package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Company;
import com.skillsync.applyr.core.models.entities.SalesAgent;
import com.skillsync.applyr.core.models.entities.TrueXeroInvoice;
import com.skillsync.applyr.core.models.entities.TrueXeroInBank;
import com.skillsync.applyr.core.models.enums.InvoiceSentStatus;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.authentication.models.ChangePassDTO;
import com.skillsync.applyr.modules.company.repositories.TrueXeroInvoiceRespository;
import com.skillsync.applyr.modules.company.repositories.TrueXeroInBankRepository;
import com.skillsync.applyr.modules.company.models.*;
import com.skillsync.applyr.modules.company.repositories.CompanyRepository;
import com.skillsync.applyr.modules.company.repositories.SalesAgentRepository;
import com.skillsync.applyr.modules.company.xero_models.TrueXeroInvoiceDTO;
import com.skillsync.applyr.modules.company.xero_models.TrueXeroInvoiceRequestDTO;
import com.skillsync.applyr.modules.company.xero_models.TrueXeroInBankDTO;
import com.skillsync.applyr.modules.company.xero_models.TrueXeroInBankRequestDTO;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import org.apache.poi.ss.usermodel.Row;                 // For Row
import org.apache.poi.ss.usermodel.Sheet;               // For Sheet
import org.apache.poi.ss.usermodel.Workbook;            // For Workbook
import org.apache.poi.xssf.usermodel.XSSFWorkbook;      // For XSSFWorkbook (if you are specifically working with .xlsx files)


import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

@Service
public class SourceOfTruthServices {

    private final SalesAgentRepository salesAgentRepository;
    private final CompanyRepository companyRepository;

    private final EmployeeProfileService employeeProfileService;

    private final TrueXeroInvoiceRespository trueXeroInvoiceRepository;

    private final TrueXeroInBankRepository trueXeroInBankRepository;

    public SourceOfTruthServices(SalesAgentRepository salesAgentRepository, CompanyRepository companyRepository, EmployeeProfileService employeeProfileService, TrueXeroInvoiceRespository trueXeroInvoiceRepository, TrueXeroInBankRepository trueXeroInBankRepository) {
        this.salesAgentRepository = salesAgentRepository;
        this.companyRepository = companyRepository;
        this.employeeProfileService = employeeProfileService;
        this.trueXeroInvoiceRepository = trueXeroInvoiceRepository;
        this.trueXeroInBankRepository = trueXeroInBankRepository;
    }


    public DashboardDTO getAdminDashboardByTargetId(Long targetId) {
        return null;
    }

    public DashboardDTO getAdminDashboard(LocalDateTime startDate, LocalDateTime endDate) {
        return null;
    }

    public SuccessResponse createXeroInvoice(TrueXeroInvoiceRequestDTO requestDTO) {
        TrueXeroInvoice trueXeroInvoice = new TrueXeroInvoice(requestDTO);
        try {
            trueXeroInvoiceRepository.save(trueXeroInvoice);
            return new SuccessResponse("Xero invoice created successfully");
        } catch (Exception e) {
            throw new AppRTException("Failed to create Xero invoice: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    private int fromMonthToInt(String month) {
        System.out.println(month);
        return switch (month) {
            case "Jan", "January" -> 1;
            case "Feb", "February" -> 2;
            case "Mar", "March" -> 3; // Added full month name for Mar
            case "Apr", "April" -> 4; // Added full month name for Apr
            case "May" -> 5;
            case "Jun", "June" -> 6; // Added full month name for Jun
            case "Jul", "July" -> 7; // Added full month name for Jul
            case "Aug", "August" -> 8; // Added full month name for Aug
            case "Sep", "September" -> 9; // Added full month name for Sep
            case "Oct", "October" -> 10; // Added full month name for Oct
            case "Nov", "November" -> 11; // Added full month name for Nov
            case "Dec", "December" -> 12; // Added full month name for Dec
            default -> LocalDateTime.now().getMonthValue();
        };
    }


    public SuccessResponse uploadXeroInvoiceData(MultipartFile file) throws Exception {
        int rowWithData = 0;
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {

            Sheet sheet = workbook.getSheetAt(0); // Assuming data is in the first sheet
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new IllegalArgumentException("The Excel file must have a header row.");
            }

            Map<String, Integer> headerMap = new HashMap<>();
            headerMap.put("Invoice Number", -1);
            headerMap.put("Contact", -1);
            headerMap.put("Invoice Date", -1);
            headerMap.put("Due Date", -1);
            headerMap.put("Reference", -1);
            headerMap.put("Gross", -1);
            headerMap.put("Balance", -1);
            headerMap.put("Status", -1);
            headerMap.put("Source", -1);
            headerMap.put("Invoice Sent", -1);
            headerMap.put("Agent Commission", -1);

            // Populate header indices
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String cellValue = cell.getStringCellValue().trim();
                    if (headerMap.containsKey(cellValue)) {
                        headerMap.put(cellValue, i);
                    }
                }
            }


            // Verify all required headers are present
            for (Map.Entry<String, Integer> entry : headerMap.entrySet()) {
                if (entry.getValue() == -1) {
                    throw new IllegalArgumentException("Missing required header: " + entry.getKey());
                }
            }

            // Date formatter for Excel date strings like "01 June 2025"
            DateTimeFormatter excelDateFormatter = DateTimeFormatter.ofPattern("dd MMMM yyyy");

            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow != null) {

                    String invoiceNumber = getStringCellValue(dataRow.getCell(headerMap.get("Invoice Number")));
                    String contact = getStringCellValue(dataRow.getCell(headerMap.get("Contact")));
                    String invoiceDateStr = getStringCellValue(dataRow.getCell(headerMap.get("Invoice Date")));
                    String dueDateStr = getStringCellValue(dataRow.getCell(headerMap.get("Due Date")));
                    String reference = getStringCellValue(dataRow.getCell(headerMap.get("Reference")));
                    String gross = getStringCellValue(dataRow.getCell(headerMap.get("Gross")));
                    String balance = getStringCellValue(dataRow.getCell(headerMap.get("Balance")));
                    String status = getStringCellValue(dataRow.getCell(headerMap.get("Status")));
                    String source = getStringCellValue(dataRow.getCell(headerMap.get("Source")));
                    String invoiceSent = getStringCellValue(dataRow.getCell(headerMap.get("Invoice Sent")));
                    String agentCommission = getStringCellValue(dataRow.getCell(headerMap.get("Agent Commission")));

                    agentCommission = agentCommission.split(" ")[0];

                    TrueXeroInvoice trueXeroInvoice = new TrueXeroInvoice();
                    trueXeroInvoice.setInvoiceNumber(invoiceNumber);
                    trueXeroInvoice.setContactName(contact);
                    // Parse dates using formatter, fallback to LocalDateTime.parse if needed
                    LocalDateTime invoiceDate = null;
                    LocalDateTime dueDate = null;
                    try {
                        if (!invoiceDateStr.isEmpty()) {
                            invoiceDate = LocalDate.parse(invoiceDateStr, excelDateFormatter).atStartOfDay();
                        }
                    } catch (Exception e) {
                        try { invoiceDate = LocalDateTime.parse(invoiceDateStr); } catch (Exception e1) {
                            System.out.println(invoiceDateStr);
                            int year = Integer.parseInt(invoiceDateStr.split(" ")[2]);
                            int day = Integer.parseInt(invoiceDateStr.split(" ")[0]);
                            System.out.println(invoiceDateStr.split(" ")[1]);
                            int month = fromMonthToInt(invoiceDateStr.split(" ")[1]);
                            invoiceDate = LocalDate.of(year, month, day).atStartOfDay();
                        }
                    }
                    try {
                        if (!dueDateStr.isEmpty()) {
                            dueDate = LocalDate.parse(dueDateStr, excelDateFormatter).atStartOfDay();
                        }
                    } catch (Exception e) {
                        try { dueDate = LocalDateTime.parse(dueDateStr); } catch (Exception e1) {
                            System.out.println(dueDateStr);
                            int year = Integer.parseInt(dueDateStr.split(" ")[2]);
                            int day = Integer.parseInt(dueDateStr.split(" ")[0]);
                            System.out.println(dueDateStr.split(" ")[1]);
                            int month = fromMonthToInt(dueDateStr.split(" ")[1]);
                            dueDate = LocalDate.of(year, month, day).atStartOfDay();
                        }
                    }

                    TrueXeroInvoice existingInvoice = trueXeroInvoiceRepository
                            .findByInvoiceNumberAndContactNameAndInvoiceDateAndInvoiceExpiryDate(invoiceNumber, contact, invoiceDate, dueDate);
                    if (existingInvoice != null) {
                        existingInvoice.setStatus(status);
                        try {
                            existingInvoice.setInvoiceSentStatus(InvoiceSentStatus.valueOf(invoiceSent.toUpperCase()));
                        } catch (Exception ignored) {
                        }
                        trueXeroInvoiceRepository.save(existingInvoice);
                        continue;
                    }

                    trueXeroInvoice.setInvoiceDate(invoiceDate);
                    trueXeroInvoice.setInvoiceExpiryDate(dueDate);
                    trueXeroInvoice.setQuoteNumber(reference);
                    trueXeroInvoice.setGross(Double.parseDouble(gross));
                    trueXeroInvoice.setBalance(Double.parseDouble(balance));
                    trueXeroInvoice.setStatus(status);
                    trueXeroInvoice.setSource(source);

                    try {
                        trueXeroInvoice.setInvoiceSentStatus(InvoiceSentStatus.valueOf(invoiceSent.toUpperCase()));
                    } catch (Exception e) {
                        trueXeroInvoice.setInvoiceSentStatus(InvoiceSentStatus.NOT_SENT);
                    }

                    Optional<SalesAgent> salesAgent = salesAgentRepository.getSalesAgentByFullNameContaining(agentCommission);
                    if (salesAgent.isPresent()) {
                        trueXeroInvoice.setAgentUsername(salesAgent.get().getUser().getUsername());
                    } else {
                        Optional<Company> company = companyRepository.getCompanyByFullNameContaining(agentCommission);
                        if (company.isPresent()) {
                            trueXeroInvoice.setAgentUsername(company.get().getUser().getUsername());
                        } else {
                            trueXeroInvoice.setAgentUsername(agentCommission + " (N/A In DB)");
                        }
                    }

                    trueXeroInvoiceRepository.save(trueXeroInvoice);
                    rowWithData++;
                }
            }

            return new SuccessResponse("Xero invoice data processed and saved successfully.");

        } catch (IOException | IllegalArgumentException e) {
            return new SuccessResponse("Saved " + rowWithData + " rows of data successfully.");
        }
    }


    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return new SimpleDateFormat("dd MMMM yyyy").format(cell.getDateCellValue());
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return "";
        }
    }

    public List<TrueXeroInvoiceDTO> getAllTrueXeroInvoiceData() {
        List<TrueXeroInvoice> trueXeroInvoices = trueXeroInvoiceRepository.findAll();
        List<TrueXeroInvoiceDTO> trueXeroInvoiceDTOs = new ArrayList<>();
        for (TrueXeroInvoice trueXeroInvoice : trueXeroInvoices) {
            TrueXeroInvoiceDTO dto = new TrueXeroInvoiceDTO();
            dto.setInvoiceNumber(trueXeroInvoice.getInvoiceNumber());
            dto.setQuoteNumber(trueXeroInvoice.getQuoteNumber());
            dto.setContactName(trueXeroInvoice.getContactName());
            dto.setGross(trueXeroInvoice.getGross());
            dto.setBalance(trueXeroInvoice.getBalance());
            dto.setStatus(trueXeroInvoice.getStatus());
            dto.setApplicationId(trueXeroInvoice.getApplicationId());
            dto.setAgent(employeeProfileService.findProfileFromUsername(trueXeroInvoice.getAgentUsername()));
            dto.setInvoiceDate(trueXeroInvoice.getInvoiceDate().toString());
            dto.setInvoiceExpiryDate(trueXeroInvoice.getInvoiceExpiryDate().toString());
            dto.setSource(trueXeroInvoice.getSource());
            dto.setInvoiceSentStatus(trueXeroInvoice.getInvoiceSentStatus().toString());
            trueXeroInvoiceDTOs.add(dto);
        }
        return trueXeroInvoiceDTOs;
    }

    public SuccessResponse createTrueXeroInBank(TrueXeroInBankRequestDTO dto) {
        TrueXeroInBank entity = mapToTrueXeroInBankEntity(dto);
        try {
            trueXeroInBankRepository.save(entity);
            return new SuccessResponse("TrueXeroInBank entry created successfully");
        } catch (Exception e) {
            throw new AppRTException("Failed to create TrueXeroInBank entry: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse uploadTrueXeroInBankExcel(MultipartFile file) throws Exception {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) throw new IllegalArgumentException("Excel file must have a header row.");

            String[] headers = {"Date", "Agent Commission", "Reference", "Contact", "Description", "Debit", "Credit", "Net", "Source"};
            int[] idx = new int[headers.length];
            for (int i = 0; i < headers.length; i++) idx[i] = -1;
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                String val = getStringCellValue(headerRow.getCell(i));
                for (int j = 0; j < headers.length; j++) {
                    if (headers[j].equalsIgnoreCase(val)) idx[j] = i;
                }
            }
            for (int i = 0; i < headers.length; i++) {
                if (idx[i] == -1) throw new IllegalArgumentException("Missing required header: " + headers[i]);
            }

            List<TrueXeroInBank> entities = new ArrayList<>();
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) continue;
                if(parseDate(row.getCell(idx[0])) == null) continue;
                TrueXeroInBank entity = new TrueXeroInBank();
                entity.setInsertDate(parseDate(row.getCell(idx[0])));

                entity.setInvoiceNumber(getStringCellValue(row.getCell(idx[2])));
                entity.setContactName(getStringCellValue(row.getCell(idx[3])));
                entity.setDescription(getStringCellValue(row.getCell(idx[4])));
                entity.setDebitAmount(getDoubleCellValue(row.getCell(idx[5])));
                entity.setCreditAmount(getDoubleCellValue(row.getCell(idx[6])));
                entity.setNetAmount(getDoubleCellValue(row.getCell(idx[7])));
                entity.setSource(getStringCellValue(row.getCell(idx[8])));


                TrueXeroInBank trueXeroInBank = trueXeroInBankRepository.findByInvoiceNumberAndInsertDateAndContactNameAndCreditAmountAndDebitAmountAndNetAmount(
                        entity.getInvoiceNumber(),
                        entity.getInsertDate(),
                        entity.getContactName(),
                        entity.getCreditAmount(),
                        entity.getDebitAmount(),
                        entity.getNetAmount()
                );
                if (trueXeroInBank != null) {
                    continue;
                }

                String agentCommission = getStringCellValue(row.getCell(idx[1]));
                agentCommission = agentCommission.split(" ")[0];


                Optional<SalesAgent> salesAgent = salesAgentRepository.getSalesAgentByFullNameContaining(agentCommission);

                if (salesAgent.isPresent()) {
                    System.out.println(salesAgent.get().getFullName() + " - " + row.getCell(idx[0]));
                    entity.setAgentUsername(salesAgent.get().getUser().getUsername());
                } else {
                    Optional<Company> company = companyRepository.getCompanyByFullNameContaining(agentCommission);
                    if (company.isPresent()) {
                        System.out.println(company.get().getFullName());
                        entity.setAgentUsername(company.get().getUser().getUsername());
                    } else {
                        entity.setAgentUsername(agentCommission + " (N/A In DB)");
                    }
                }



                entities.add(entity);
            }
            trueXeroInBankRepository.saveAll(entities);
            return new SuccessResponse("Excel data uploaded successfully");
        } catch (IOException | IllegalArgumentException e) {
            throw new AppRTException("Failed to upload Excel: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public List<TrueXeroInBankDTO> getAllTrueXeroInBank() {
        List<TrueXeroInBankDTO> dtos = new ArrayList<>();
        for (TrueXeroInBank entity : trueXeroInBankRepository.findAll()) {
            dtos.add(mapToTrueXeroInBankDTO(entity));
        }
        return dtos;
    }

    // Get KPI1 data filtered by agent username and date range
    public List<TrueXeroInvoiceDTO> getAgentXeroInvoiceData(String agentUsername, String startDate, String endDate) {
        List<TrueXeroInvoice> entities;

        if (startDate != null && endDate != null) {
            LocalDateTime start = LocalDateTime.parse(startDate);
            LocalDateTime end = LocalDateTime.parse(endDate);
            entities = trueXeroInvoiceRepository.findByAgentUsernameAndInvoiceDateBetween(agentUsername, start, end);
        } else {
            entities = trueXeroInvoiceRepository.findByAgentUsername(agentUsername);
        }

        List<TrueXeroInvoiceDTO> dtos = new ArrayList<>();
        for (TrueXeroInvoice entity : entities) {
            dtos.add(mapToTrueXeroInvoiceDTO(entity));
        }
        return dtos;
    }

    // Get KPI2 data filtered by agent username and date range
    public List<TrueXeroInBankDTO> getAgentXeroInBankData(String agentUsername, String startDate, String endDate) {
        List<TrueXeroInBank> entities;

        if (startDate != null && endDate != null) {
            LocalDateTime start = LocalDateTime.parse(startDate);
            LocalDateTime end = LocalDateTime.parse(endDate);
            entities = trueXeroInBankRepository.findByAgentUsernameAndInsertDateBetween(agentUsername, start, end);
        } else {
            entities = trueXeroInBankRepository.findByAgentUsername(agentUsername);
        }

        List<TrueXeroInBankDTO> dtos = new ArrayList<>();
        for (TrueXeroInBank entity : entities) {
            dtos.add(mapToTrueXeroInBankDTO(entity));
        }
        return dtos;
    }

    // Get performance leaderboard data
    public Object getPerformanceLeaderboard(String startDate, String endDate, Long targetId) {
        // This method will return leaderboard data similar to the admin dashboard
        // For now, return a simple structure that can be expanded
        Map<String, Object> leaderboard = new HashMap<>();

        if (targetId != null) {
            // Get data based on target ID
            // This would need to be implemented based on your existing dashboard logic
            leaderboard.put("message", "Leaderboard data for target ID: " + targetId);
        } else if (startDate != null && endDate != null) {
            // Get data based on date range
            LocalDateTime start = LocalDateTime.parse(startDate);
            LocalDateTime end = LocalDateTime.parse(endDate);

            // Get all agents' performance data for the period
            List<TrueXeroInvoice> invoices = trueXeroInvoiceRepository.findByInvoiceDateBetween(start, end);
            List<TrueXeroInBank> payments = trueXeroInBankRepository.findByInsertDateBetween(start, end);

            // Group by agent and calculate totals
            Map<String, Double> kpi1Totals = new HashMap<>();
            Map<String, Double> kpi2Totals = new HashMap<>();

            for (TrueXeroInvoice invoice : invoices) {
                kpi1Totals.merge(invoice.getAgentUsername(), invoice.getGross(), Double::sum);
            }

            for (TrueXeroInBank payment : payments) {
                kpi2Totals.merge(payment.getAgentUsername(), payment.getNetAmount(), Double::sum);
            }

            leaderboard.put("kpi1Totals", kpi1Totals);
            leaderboard.put("kpi2Totals", kpi2Totals);
        }

        return leaderboard;
    }

    private TrueXeroInBank mapToTrueXeroInBankEntity(TrueXeroInBankRequestDTO dto) {
        TrueXeroInBank entity = new TrueXeroInBank();
        entity.setAgentUsername(dto.getAgentUsername());
        entity.setInvoiceNumber(dto.getInvoiceNumber());
        entity.setContactName(dto.getContactName());
        entity.setApplicationId(dto.getApplicationId());
        entity.setInsertDate(dto.getInsertDate());
        entity.setDescription(dto.getDescription());
        entity.setDebitAmount(dto.getDebitAmount());
        entity.setCreditAmount(dto.getCreditAmount());
        entity.setNetAmount(dto.getNetAmount());
        entity.setSource(dto.getSource());
        return entity;
    }

    private TrueXeroInBankDTO mapToTrueXeroInBankDTO(TrueXeroInBank entity) {
        return new TrueXeroInBankDTO(
                entity.getId(),
                entity.getAgentUsername(),
                employeeProfileService.findProfileFromUsername(entity.getAgentUsername()),
                entity.getInvoiceNumber(),
                entity.getContactName(),
                entity.getApplicationId(),
                entity.getInsertDate(),
                entity.getDescription(),
                entity.getDebitAmount(),
                entity.getCreditAmount(),
                entity.getNetAmount(),
                entity.getSource()
        );
    }

    private TrueXeroInvoiceDTO mapToTrueXeroInvoiceDTO(TrueXeroInvoice entity) {
        TrueXeroInvoiceDTO dto = new TrueXeroInvoiceDTO();
        dto.setInvoiceNumber(entity.getInvoiceNumber());
        dto.setQuoteNumber(entity.getQuoteNumber());
        dto.setContactName(entity.getContactName());
        dto.setGross(entity.getGross());
        dto.setBalance(entity.getBalance());
        dto.setStatus(entity.getStatus());
        dto.setApplicationId(entity.getApplicationId());
        dto.setAgent(employeeProfileService.findProfileFromUsername(entity.getAgentUsername()));
        dto.setInvoiceDate(entity.getInvoiceDate().toString());
        dto.setInvoiceExpiryDate(entity.getInvoiceExpiryDate().toString());
        dto.setSource(entity.getSource());
        dto.setInvoiceSentStatus(entity.getInvoiceSentStatus().toString());
        return dto;
    }

    private double getDoubleCellValue(Cell cell) {
        if (cell == null) return 0.0;
        if (cell.getCellType() == CellType.NUMERIC) return cell.getNumericCellValue();
        if (cell.getCellType() == CellType.STRING) {
            try { return Double.parseDouble(cell.getStringCellValue().trim()); } catch (Exception e) { return 0.0; }
        }
        return 0.0;
    }

    private LocalDateTime parseDate(Cell cell) {
        if (cell == null) {
            return null;
        }

        if (cell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell)) {
            return cell.getDateCellValue().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
        } else if (cell.getCellType() == CellType.STRING) {
            String dateString = cell.getStringCellValue().trim();
            if (dateString.isEmpty()) {
                return null;
            }

            try {
                DateTimeFormatter DD_MMM_YYYY_FORMATTER = DateTimeFormatter.ofPattern("dd MMMM yyyy", Locale.ENGLISH);
                return LocalDate.parse(dateString, DD_MMM_YYYY_FORMATTER).atStartOfDay();
            } catch (DateTimeParseException e) {
                int year = Integer.parseInt(dateString.split(" ")[2]);
                int day = Integer.parseInt(dateString.split(" ")[0]);
                int month = fromMonthToInt(dateString.split(" ")[1]);
                return LocalDate.of(year, month, day).atStartOfDay();
            }
        }
        return null;
    }
}
