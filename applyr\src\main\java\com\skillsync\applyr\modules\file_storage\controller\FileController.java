package com.skillsync.applyr.modules.file_storage.controller;


import com.skillsync.applyr.modules.file_storage.services.FileStorageService;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.core.io.Resource;


@Controller
@RequestMapping("/api/v1/file")
public class FileController {

    private final FileStorageService fileStorageService;

    public FileController(FileStorageService fileStorageService) {
        this.fileStorageService = fileStorageService;
    }

    @GetMapping("/{fileName}")
    public ResponseEntity<Resource> getFile(@PathVariable String fileName) {
        return fileStorageService.getFile(fileName);
    }

    @GetMapping("/{fileName}/view")
    public ResponseEntity<Resource> viewFile(@PathVariable String fileName) {
        return fileStorageService.viewFile(fileName);
    }

}
