package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RTODataDTO {
    private Long id;
    private String code;
    private String legalName;
    private String businessName;
    private String address;
    private String rtoType;
    private String description;
    private List<RTOContactDTO> contacts = new ArrayList<>();
}
