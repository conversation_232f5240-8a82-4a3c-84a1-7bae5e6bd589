import React from "react";
import SingleLeadModal from "../modal/SingleLeadModal";
import EditLeadModal from "../modal/EditLeadModal";
import MultiStepCreateApplicationModal from "../modal/MultiStepCreateApplicationModal";

const LeadModals = ({
  // Single Lead Modal
  isSingleLeadModalOpen,
  onCloseSingleLeadModal,
  onSingleLeadSubmit,
  currentUser = null,
  isAgent = false,

  // Edit Lead Modal
  isEditLeadModalOpen,
  onCloseEditLeadModal,
  leadToEdit,
  onEditLeadSubmit,

  // Create Application Modal
  isCreateApplicationModalOpen,
  onCloseCreateApplicationModal,
  leadNumber,
  onCreateApplicationSubmit,

  // Employees data
  employees
}) => {
  return (
    <>

      {/* Single Lead Modal */}
      <SingleLeadModal
        isOpen={isSingleLeadModalOpen}
        onClose={onCloseSingleLeadModal}
        onSubmit={onSingleLeadSubmit}
        salesReps={employees.map(emp => ({ username: emp.username, name: emp.fullName }))}
        currentUser={currentUser}
        isAgent={isAgent}
      />

      {/* Edit Lead Modal */}
      <EditLeadModal
        isOpen={isEditLeadModalOpen}
        onClose={onCloseEditLeadModal}
        lead={leadToEdit}
        onSubmit={onEditLeadSubmit}
        salesReps={employees.map(emp => ({ username: emp.username, name: emp.fullName }))}
      />

      {/* Create Application Modal */}
      <MultiStepCreateApplicationModal
        isOpen={isCreateApplicationModalOpen}
        onClose={onCloseCreateApplicationModal}
        leadNumber={leadNumber}
        onSubmit={onCreateApplicationSubmit}
      />
    </>
  );
};

export default LeadModals;
