import React from "react";
import LeadTable from "./LeadTable";
import LeadMobileView from "./LeadMobileView";
import LeadSearchFilters from "./LeadSearchFilters";

const LeadTableContainer = ({
  leads,
  onProfileRedirect,
  onStatusChange,
  onEditLead,
  onOpenDrawer,
  onDeleteLead,
  sortField,
  sortDirection,
  onSort,
  leadSearch,
  setLeadSearch,
  selectedAgentFilter,
  setSelectedAgentFilter,
  leadTypeFilter,
  setLeadTypeFilter,
  agentNames,
  isFullScreen,
  setIsFullScreen,
  isAdmin = false,
  isLoading = false,
  isUpdatingStatus = false,
  error = null
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-50">
      <LeadSearchFilters
        leadSearch={leadSearch}
        setLeadSearch={setLeadSearch}
        selectedAgentFilter={selectedAgentFilter}
        setSelectedAgentFilter={setSelectedAgentFilter}
        leadTypeFilter={leadTypeFilter}
        setLeadTypeFilter={setLeadTypeFilter}
        agentNames={agentNames}
        isFullScreen={isFullScreen}
        setIsFullScreen={setIsFullScreen}
      />

      {/* Leads Table */}
      <div className="overflow-x-auto">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6E39CB]"></div>
            <span className="ml-3 text-gray-500">Loading leads...</span>
          </div>
        ) : error ? (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  Error loading leads: {error?.data?.message || error?.message || 'Unknown error'}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Mobile view for screens smaller than lg */}
            <div className="block lg:hidden p-6">
              <LeadMobileView
                leads={leads}
                onStatusChange={onStatusChange}
                onProfileRedirect={onProfileRedirect}
                onOpenDrawer={onOpenDrawer}
                onEditLead={onEditLead}
                onDeleteLead={onDeleteLead}
                isUpdatingStatus={isUpdatingStatus}
                isAdmin={isAdmin}
              />
            </div>
            {/* Desktop view for screens lg and larger */}
            <div className="hidden lg:block">
              <LeadTable
                leads={leads}
                onProfileRedirect={onProfileRedirect}
                onStatusChange={onStatusChange}
                onEditLead={onEditLead}
                onOpenDrawer={onOpenDrawer}
                onDeleteLead={onDeleteLead}
                sortField={sortField}
                sortDirection={sortDirection}
                onSort={onSort}
                isAdmin={isAdmin}
                isLoading={isLoading}
                isUpdatingStatus={isUpdatingStatus}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default LeadTableContainer;
