package com.skillsync.applyr.core.models.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DocumentType {
    PASSPORT("Passport"),
    ID_CARD("Identification Card"),
    CERTIFICATE("Certificate"),
    LICENSE("Driver's License"),
    <PERSON>THER("Other"),
    RESUME("Resume"),
    CV("Curriculum Vitae"),
    REFERENCES("References"),
    CERTIFICATE_OF_IDENTITY("Certificate of Identity issued by the Australian Government"),
    PHOTO_ID_CARD("Photo identification card issued for Australian regulatory purposes"),
    FOREIGN_DOCUMENT("Documents issued outside Australia (equivalent to Australian documents)"),
    FOREIGN_PASSPORT("Foreign Passport (current)"),
    INDIGENOUS_ORGANISATION_REFERENCE("Reference from Indigenous Organisation"),
    UTILITY_BILL("Utility Bill"),
    RATING_AUTHORITY_NOTICE("Rating Authority Notice"),
    CITIZENSHIP_CERTIFICATE("Australian Citizenship Certificate"),
    FULL_BIRTH_CERTIFICATE("Full Birth Certificate"),
    DRIVER_LICENSE("Australian Driver Licence"),
    TERTIARY_STUDENT_ID_CARD("Tertiary Student ID Card"),
    DEFENCE_ID_CARD("Defence Force Identity Card"),
    GOVERNMENT_EMPLOYEE_ID("Government Employee ID"),
    DVA_CARD("DVA Card"),
    CENTRELINK_CARD("Centrelink Card"),
    MARRIAGE_CERTIFICATE("Marriage Certificate"),
    DECREE_NISI_ABSOLUTE("Decree Nisi/Absolute"),
    CHANGE_OF_NAME_CERTIFICATE("Change of Name Certificate"),
    BANK_STATEMENT("Bank Statement"),
    LEASE_AGREEMENT("Lease Agreement"),
    TAXATION_NOTICE("Taxation Assessment Notice"),
    MORTGAGE_DOCUMENTS("Mortgage Documents"),
    BIRTH_CARD("Birth Card"),
    AUSTRALIAN_PASSPORT("Australian Passport"),
    BIRTH_CERTIFICATE_EXTRACT("Birth Certificate Extract"),
    MEDICARE_CARD("Medicare Card"),
    CREDIT_OR_ACCOUNT_CARD("Credit or Account Card"),
    ALL("All"), All("");

    private final String description;

}