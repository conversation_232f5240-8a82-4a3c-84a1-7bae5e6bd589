package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.models.enums.RequestStatus;
import com.skillsync.applyr.core.models.enums.Status;
import com.skillsync.applyr.core.response.SuccessResponse;

import com.skillsync.applyr.modules.company.repositories.ApplicationRepository;
import com.skillsync.applyr.modules.sales.models.QuoteRequestDTO;
import com.skillsync.applyr.modules.sales.models.RaiseQuoteAndInvoiceDTO;
import com.skillsync.applyr.modules.sales.models.RequestStatusChangeDTO;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class QuoteInvoiceService {

    private final ApplicationRepository applicationRepository;
    private final EmployeeProfileService employeeProfileService;
    private final LeadsService leadsService;
    private final ApplicationFileServices applicationFileServices;

    public QuoteInvoiceService(ApplicationRepository applicationRepository,
                               EmployeeProfileService employeeProfileService,
                               LeadsService leadsService, ApplicationFileServices applicationFileServices) {
        this.applicationRepository = applicationRepository;
        this.employeeProfileService = employeeProfileService;
        this.leadsService = leadsService;
        this.applicationFileServices = applicationFileServices;
    }

    public SuccessResponse updateApplicationQuoteRef(String applicationId, String quoteStr) {
        Optional<Application> application = applicationRepository.findByApplicationId(applicationId);
        if (application.isPresent()) {
            application.get().setStatus(com.skillsync.applyr.core.models.enums.Status.QUOTE_RAISED);
            application.get().setQuoteRefNumber(quoteStr);
            applicationRepository.save(application.get());
            return new SuccessResponse("Application updated successfully");
        }
        throw new AppRTException("Unable to update quote reference", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public SuccessResponse updateApplicationInvoiceRef(String applicationId, String invoiceRefStr) {
        Optional<Application> application = applicationRepository.findByApplicationId(applicationId);
        if (application.isPresent()) {
            application.get().setStatus(com.skillsync.applyr.core.models.enums.Status.INVOICE_RAISED);
            application.get().setInvoiceRefNumber(invoiceRefStr);
            applicationRepository.save(application.get());
            return new SuccessResponse("Application updated successfully");
        }
        throw new AppRTException("Unable to update invoice reference", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public SuccessResponse changeQuoteStatus(RequestStatusChangeDTO requestDTO) {
        Optional<Application> application = applicationRepository.findByApplicationId(requestDTO.getApplicationId());
        if (application.isPresent()) {
            application.get().setQuoteRefNumber(requestDTO.getRefNumber());
            application.get().setQuoteStatus(requestDTO.getStatus());
            if(requestDTO.getStatus() == RequestStatus.SENT || requestDTO.getStatus() == RequestStatus.ACCEPTED){
                application.get().setStatus(Status.QUOTE_RAISED);
            }
            applicationRepository.save(application.get());
            return new SuccessResponse("Successfully changed Quote Status");
        }
        throw new AppRTException("Failed to change Quote Status", HttpStatus.NOT_FOUND);
    }

    public SuccessResponse changeInvoiceStatus(RequestStatusChangeDTO requestDTO) {
        Optional<Application> application = applicationRepository.findByApplicationId(requestDTO.getApplicationId());
        if (application.isPresent()) {
            application.get().setInvoiceRefNumber(requestDTO.getRefNumber());
            application.get().setInvoiceStatus(requestDTO.getStatus());
            if(requestDTO.getStatus() == RequestStatus.SENT || requestDTO.getStatus() == RequestStatus.ACCEPTED){
                application.get().setStatus(Status.INVOICE_RAISED);
            }

            applicationRepository.save(application.get());
            return new SuccessResponse("Successfully changed Invoice Status");
        }
        throw new AppRTException("Failed to change Invoice Status", HttpStatus.NOT_FOUND);
    }

    public SuccessResponse raiseQuoteAndInvoices(RaiseQuoteAndInvoiceDTO requestDTO) {
        Optional<Application> application = applicationRepository.findByApplicationId(requestDTO.getApplicationId());
        if (application.isPresent()) {
            application.get().setQuoteRefNumber(requestDTO.getQuoteRefNumber());
            application.get().setQuoteStatus(requestDTO.getQuoteRequestStatus());
            application.get().setInvoiceRefNumber(requestDTO.getInvoiceRefNumber());
            application.get().setInvoiceStatus(requestDTO.getInvoiceRequestStatus());
            if(requestDTO.getQuoteRequestStatus() == RequestStatus.SENT || requestDTO.getQuoteRequestStatus() == RequestStatus.ACCEPTED){
                application.get().setStatus(Status.QUOTE_RAISED);
            }

            if(requestDTO.getInvoiceRequestStatus() == RequestStatus.SENT || requestDTO.getInvoiceRequestStatus() == RequestStatus.ACCEPTED){
                application.get().setStatus(Status.INVOICE_RAISED);
            }

            applicationFileServices.createApplicationFiles(application.get());

            applicationRepository.save(application.get());
            return new SuccessResponse("Successfully raised Quote And Invoice Status");
        }
        throw new AppRTException("Failed to raise Quote And Invoice", HttpStatus.NOT_FOUND);
    }

    public List<QuoteRequestDTO> getAllQuoteRequests() {
        List<QuoteRequestDTO> quoteRequestDTOs = new ArrayList<>();
        List<Application> applications = applicationRepository.findAll();
        for (Application application : applications) {
            QuoteRequestDTO dto = new QuoteRequestDTO(
                    application,
                    employeeProfileService.getAgentProfileByUsername(application.getAgentUsername()),
                    leadsService.getSingleLead(application.getLeadPhone())
            );
            quoteRequestDTOs.add(dto);
        }
        // Sort by createdDate descending (latest first)
        quoteRequestDTOs.sort((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()));
        return quoteRequestDTOs;
    }

    public List<QuoteRequestDTO> getPendingQuoteRequests() {
        List<QuoteRequestDTO> quoteRequestDTOs = new ArrayList<>();
        List<Application> applications = applicationRepository.findAll();
        for (Application application : applications) {
            if (application.getQuoteRefNumber() == null || application.getQuoteRefNumber().isBlank()) {
                QuoteRequestDTO dto = new QuoteRequestDTO(
                        application,
                        employeeProfileService.getAgentProfileByUsername(application.getAgentUsername()),
                        leadsService.getSingleLead(application.getLeadPhone())
                );
                quoteRequestDTOs.add(dto);
            }
        }
        // Sort by createdDate descending (latest first)
        quoteRequestDTOs.sort((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()));
        return quoteRequestDTOs;
    }

    public List<QuoteRequestDTO> getDraftedQuoteRequests() {
        List<QuoteRequestDTO> quoteRequestDTOs = new ArrayList<>();
        List<Application> applications = applicationRepository.findAll();
        for (Application application : applications) {
            if (application.getQuoteStatus() == RequestStatus.DRAFTED || application.getInvoiceStatus() == RequestStatus.DRAFTED) {
                QuoteRequestDTO dto = new QuoteRequestDTO(
                        application,
                        employeeProfileService.getAgentProfileByUsername(application.getAgentUsername()),
                        leadsService.getSingleLead(application.getLeadPhone())
                );
                quoteRequestDTOs.add(dto);
            }
        }
        // Sort by createdDate descending (latest first)
        quoteRequestDTOs.sort((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()));
        return quoteRequestDTOs;
    }
}
