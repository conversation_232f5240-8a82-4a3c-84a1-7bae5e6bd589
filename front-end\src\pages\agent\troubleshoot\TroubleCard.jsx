import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUser,
  faCalendarAlt,
  faUserCog,
  faEllipsisVertical,
  faEdit,
  faTrash,
  faComment,
  faPaperPlane,
  faSort,
} from "@fortawesome/free-solid-svg-icons";
import { formatDate, convertStatus } from "./TroubleUtils";

const TroubleCard = ({ trouble, onEdit, onDelete, onStatusChange, onAddComment }) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [openComments, setOpenComments] = useState(false);
  const [commentInput, setCommentInput] = useState("");

  const toggleDropdown = () => {
    setDropdownVisible(!dropdownVisible);
  };

  const toggleComments = () => {
    setOpenComments(!openComments);
  };

  const handleStatusChange = (e) => {
    const newStatus = e.target.value;
    onStatusChange(trouble, newStatus);
  };

  const handleSendComment = async () => {
    if (!commentInput.trim()) {
      alert("Please enter a comment.");
      return;
    }
    try {
      await onAddComment(trouble.troubleId, { answer: commentInput });
      setCommentInput("");
    } catch (err) {
      console.error("Failed to send comment:", err);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-[#DBDCDE] shadow-sm overflow-hidden">
      <div className="p-6 border-b border-[#DBDCDE]">
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#F0ECF6]">
              <FontAwesomeIcon icon={faUser} className="text-[#6E39CB]" />
            </div>
            <div>
              <h4 className="font-medium text-[#3A3541]">
                {trouble.createdBy.fullName || trouble.createdBy.username}
              </h4>
              <p className="text-xs text-[#89868D]">
                {formatDate(trouble.createdDate)}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div
              className={`rounded-full px-3 py-1 text-xs font-medium ${
                trouble.status === "RESOLVED"
                  ? "bg-[#DCFCE7] text-[#14532D]"
                  : trouble.status === "IN_PROGRESS"
                  ? "bg-[#FEF9C3] text-[#854D0E]"
                  : trouble.status === "PENDING"
                  ? "bg-[#E0F2FE] text-[#075985]"
                  : "bg-[#FEE2E2] text-[#991B1B]"
              }`}
            >
              {trouble.status === "RESOLVED"
                ? "Resolved"
                : trouble.status === "IN_PROGRESS"
                ? "In Progress"
                : trouble.status === "PENDING"
                ? "Pending"
                : "No Solution"}
            </div>
            <div className="relative">
              <button
                className="flex items-center justify-center rounded-md p-2 hover:bg-[#F4F5F9]"
                onClick={toggleDropdown}
              >
                <FontAwesomeIcon icon={faEllipsisVertical} className="text-[#89868D]" />
              </button>
              {dropdownVisible && (
                <div className="absolute right-0 top-full z-40 w-40 rounded-md border border-[#DBDCDE] bg-white py-1 shadow-sm">
                  <button
                    onClick={() => {
                      onEdit(trouble);
                      setDropdownVisible(false);
                    }}
                    className="flex w-full items-center gap-2 px-4 py-2 text-sm hover:bg-[#F4F5F9] text-[#3A3541]"
                  >
                    <FontAwesomeIcon icon={faEdit} className="text-[#6E39CB]" />
                    <span>Edit</span>
                  </button>
                  <button
                    onClick={() => {
                      onDelete(trouble.troubleId);
                      setDropdownVisible(false);
                    }}
                    className="flex w-full items-center gap-2 px-4 py-2 text-sm hover:bg-[#F4F5F9] text-[#3A3541]"
                  >
                    <FontAwesomeIcon icon={faTrash} className="text-red-500" />
                    <span>Delete</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="mb-5">
          <p className="text-[#3A3541]">{trouble.questions}</p>
        </div>

        <div className="flex flex-wrap items-center justify-between gap-3">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <FontAwesomeIcon icon={faCalendarAlt} className="text-[#89868D]" />
              <span className="text-sm text-[#3A3541]">Due: {formatDate(trouble.dueDate)}</span>
            </div>
            <div className="flex items-center gap-2">
              <FontAwesomeIcon icon={faUserCog} className="text-[#89868D]" />
              <span className="text-sm text-[#3A3541]">Assigned to:</span>
              <span className="rounded-full bg-[#F0ECF6] px-2.5 py-1 text-xs font-medium text-[#6E39CB]">
                {trouble.assignedTo.fullName || trouble.assignedTo.username}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-[#F9FAFB] p-6 border-t border-[#DBDCDE]">
        <div className="flex flex-col md:flex-row gap-4 justify-between">
          <div className="md:w-1/3">
            <label className="mb-2 block text-sm font-medium text-[#3A3541]">Change Status</label>
            <div className="relative">
              <select
                value={convertStatus(trouble.status)}
                onChange={handleStatusChange}
                className="w-full h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
              >
                <option value="resolved">Resolved</option>
                <option value="inProgress">In Progress</option>
                <option value="pending">Pending</option>
                <option value="noSolution">No Solution</option>
              </select>
              <FontAwesomeIcon icon={faSort} className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none" />
            </div>
          </div>
          <div className="flex items-center">
            <button
              onClick={toggleComments}
              className="flex items-center gap-2 rounded-md bg-[#F0ECF6] px-4 py-2 text-sm font-medium text-[#6E39CB] hover:bg-opacity-80"
            >
              <FontAwesomeIcon icon={faComment} />
              <span>{openComments ? "Hide Comments" : "View Comments"}</span>
              {trouble.answers && trouble.answers.length > 0 && (
                <span className="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-[#6E39CB] text-xs text-white">
                  {trouble.answers.length}
                </span>
              )}
            </button>
          </div>
        </div>
        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out ${openComments ? "max-h-[800px] mt-6" : "max-h-0"}`}
          style={{ transitionProperty: "max-height, margin" }}
        >
          <div className="pt-4 border-t border-[#DBDCDE]">
            <h5 className="mb-4 text-sm font-medium text-[#3A3541]">
              Comments ({trouble.answers ? trouble.answers.length : 0})
            </h5>
            <div className="space-y-4 mb-5">
              {trouble.answers && trouble.answers.length > 0 ? (
                trouble.answers.map((answer, aIndex) => (
                  <div key={aIndex} className="rounded-lg border border-[#DBDCDE] bg-white p-4">
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center gap-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#F0ECF6]">
                          <FontAwesomeIcon icon={faUser} className="text-[#6E39CB]" />
                        </div>
                        <p className="text-sm font-medium text-[#3A3541]">
                          {answer.answeredBy.fullName || answer.answeredBy.username}
                        </p>
                      </div>
                      <p className="text-xs text-[#89868D]">
                        {new Date(answer.answeredAt).toLocaleTimeString()}
                      </p>
                    </div>
                    <p className="text-sm text-[#3A3541]">{answer.answer}</p>
                  </div>
                ))
              ) : (
                <p className="text-sm text-[#89868D] bg-white p-4 rounded-lg border border-[#DBDCDE]">
                  No comments yet.
                </p>
              )}
            </div>
            <div>
              <textarea
                placeholder="Enter your comment"
                value={commentInput}
                onChange={(e) => setCommentInput(e.target.value)}
                className="w-full rounded-lg border border-[#DBDCDE] bg-white py-3 px-4 text-sm outline-none focus:border-[#6E39CB]"
                rows="3"
              ></textarea>
              <button
                onClick={handleSendComment}
                className="mt-3 flex items-center justify-center gap-2 rounded-md bg-[#6E39CB] py-2 px-4 text-sm font-medium text-white hover:bg-opacity-90"
              >
                <FontAwesomeIcon icon={faPaperPlane} />
                <span>Send Comment</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TroubleCard;
