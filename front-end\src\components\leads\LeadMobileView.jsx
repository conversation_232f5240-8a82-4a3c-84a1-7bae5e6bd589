import React from "react";
import LeadMobileCard from "./LeadMobileCard";

const LeadMobileView = ({ 
  leads, 
  onStatusChange, 
  onProfileRedirect, 
  onOpenDrawer, 
  onEditLead, 
  onDeleteLead,
  isUpdatingStatus = false,
  isAdmin = false 
}) => {
  if (leads.length === 0) {
    return (
      <div className="text-center py-8">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <p className="text-gray-500">No leads found matching your criteria.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {leads.map((lead) => (
        <LeadMobileCard
          key={lead.phone}
          lead={lead}
          onStatusChange={onStatusChange}
          onProfileRedirect={onProfileRedirect}
          onOpenDrawer={onOpenDrawer}
          onEditLead={onEditLead}
          onDeleteLead={onDeleteLead}
          isUpdatingStatus={isUpdatingStatus}
          isAdmin={isAdmin}
        />
      ))}
    </div>
  );
};

export default LeadMobileView;
