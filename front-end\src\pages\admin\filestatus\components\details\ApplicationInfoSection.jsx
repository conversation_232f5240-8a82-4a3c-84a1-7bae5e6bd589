import React from "react";
import InfoCard from "./InfoCard";

/**
 * ApplicationInfoSection component for displaying application information
 * @param {object} application - Application data
 */
const ApplicationInfoSection = ({ application }) => {
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Prepare items for InfoCard
  const items = [
    { 
      label: "Applicant Name", 
      value: application?.applicantName || "N/A",
      copyable: true
    },
    { 
      label: "Applicant Email", 
      value: application?.applicantEmail || "N/A",
      copyable: true
    },
    { 
      label: "Applicant Phone", 
      value: application?.applicantPhone || "N/A",
      copyable: true
    },
    { 
      label: "Company Name", 
      value: application?.lead?.companyName || "N/A",
      copyable: true
    },
    { 
      label: "Agent Name", 
      value: application?.createdBy?.fullName || "N/A",
      copyable: true
    },
    { 
      label: "Quote Reference", 
      value: application?.quoteRefNumber || "N/A",
      copyable: true
    },
    { 
      label: "Invoice Reference", 
      value: application?.invoiceRefNumber || "N/A",
      copyable: true
    },
    { 
      label: "Created Date", 
      value: application?.createdAt ? formatDate(application.createdAt) : "N/A",
      copyable: false
    },
    { 
      label: "Total Price", 
      value: `$${application?.totalPrice || "0.00"}`,
      copyable: false
    }
  ];

  return (
    <InfoCard 
      title="Application Information"
      items={items}
    />
  );
};

export default ApplicationInfoSection;
