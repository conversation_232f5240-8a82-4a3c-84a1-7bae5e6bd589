package com.skillsync.applyr.core.models.entities;


import com.skillsync.applyr.modules.company.models.AdminRegisterDTO;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;


@Entity
@Table(name = "sales_agent")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class SalesAgent extends Auditable<String> {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private String fullName;

    @Column(nullable = false)
    private String gender;

    @Column(nullable = false, unique = true)
    private String emailAddress;

    @Column()
    private String phoneNumber;

    @Column()
    private String address;

    @Column()
    private boolean active;

    @Column()
    private double weeklyTarget;

    @Column()
    private String currentWeek;

    @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(referencedColumnName = "id", nullable = false)
    private User user;


    @ManyToMany(mappedBy = "assignedAgents")
    private Set<Lead> leads = new HashSet<>();

    public SalesAgent(AdminRegisterDTO salesInfo) {
        this.fullName = salesInfo.getFullName();
        this.gender = salesInfo.getGender();
        this.emailAddress = salesInfo.getEmail();
        this.phoneNumber = salesInfo.getPhone();
        this.address = salesInfo.getAddress();
        this.active = true;
    }

    public void addLead(Lead lead) {
        leads.add(lead);
        lead.getAssignedAgents().add(this);
    }

    public void removeLead(Lead lead) {
        leads.remove(lead);
        lead.getAssignedAgents().remove(this);
    }
}
