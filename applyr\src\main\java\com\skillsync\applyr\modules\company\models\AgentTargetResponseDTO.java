package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class AgentTargetResponseDTO {
    private ProfileDTO profile;
    private double kpi1Target;
    private double kpi2Target;
    private double kpi1Actual;
    private double kpi2Actual;
    private double quotesRaised;
    private double invoiceRaised;
    private List<ApplicationResponseDTO> applications;
}
