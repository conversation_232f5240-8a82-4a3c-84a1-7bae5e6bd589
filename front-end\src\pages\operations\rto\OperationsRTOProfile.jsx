import React, { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowLeft,
  faSync,
  faExternalLinkAlt,
  faBuilding,
  faMapMarkerAlt,
  faIdCard,
  faInfoCircle,
  faUserPlus,
  faLink,
  faUsers
} from "@fortawesome/free-solid-svg-icons";
import {
  useGetRTOByCodeQuery,
  useReloadRTOInfoMutation,
} from "../../../services/CompanyAPIService";
import LoadingSpinner from "../../../components/common/LoadingSpinner";
import RTOContactsList from "../../admin/rto/components/RTOContactsList";
import AddContactModal from "../../admin/rto/components/AddContactModal";
import ConfirmationModal from "../../../components/modal/ConfirmationModal";

const OperationsRTOProfile = () => {
  const { rtoCode } = useParams();
  const navigate = useNavigate();
  const [isAddContactModalOpen, setIsAddContactModalOpen] = useState(false);
  const [isSyncConfirmModalOpen, setSyncConfirmModalOpen] = useState(false);

  // Fetch RTO data
  const {
    data: rto,
    isLoading,
    refetch,
  } = useGetRTOByCodeQuery(rtoCode, {
    skip: !rtoCode,
  });

  // Reload RTO info mutation
  const [reloadRTOInfo, { isLoading: isReloading }] = useReloadRTOInfoMutation();

  // Show confirmation modal before reloading RTO info
  const handleReloadInfo = () => {
    setSyncConfirmModalOpen(true);
  };

  // Actually perform the reload after confirmation
  const confirmReloadInfo = async () => {
    try {
      setSyncConfirmModalOpen(false);
      await reloadRTOInfo(rtoCode).unwrap();
      refetch();
    } catch (error) {
      console.error("Failed to reload RTO info:", error);
      alert("Failed to reload RTO information. Please try again.");
    }
  };

  // Handle external link to training.gov.au
  const handleExternalLink = () => {
    window.open(`https://training.gov.au/Organisation/Details/${rtoCode}`, "_blank");
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <LoadingSpinner />
      </div>
    );
  }

  if (!rto) {
    return (
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">RTO Not Found</h2>
          <p className="text-gray-600 mb-4">
            The RTO with code {rtoCode} could not be found. It may have been deleted or the code is incorrect.
          </p>
          <button
            onClick={() => navigate("/operations/rto")}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
            Back to RTO Management
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center">
            <button
              onClick={() => navigate("/operations/rto")}
              className="mr-4 text-gray-600 hover:text-gray-800 transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faArrowLeft} size="lg" />
            </button>
            <div>
              <h1 className="text-xl font-bold text-gray-800">{rto.legalName}</h1>
              <div className="flex items-center mt-1">
                <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded text-sm font-medium">
                  {rto.code}
                </span>
                {rto.rtoType && (
                  <span className="ml-2 text-gray-500 text-sm">
                    {rto.rtoType}
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleExternalLink}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center transition-colors duration-200"
              title="View on training.gov.au"
            >
              <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-2" />
              View on TGA
            </button>
            <button
              onClick={handleReloadInfo}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200"
              disabled={isReloading}
            >
              <FontAwesomeIcon icon={faSync} className={`mr-2 ${isReloading ? "fa-spin" : ""}`} />
              {isReloading ? "Reloading..." : "Reload Info"}
            </button>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex items-center mb-4">
          <FontAwesomeIcon icon={faUsers} className="text-blue-600 mr-2" />
          <h2 className="text-lg font-bold text-gray-800">Contact Summary</h2>
        </div>
        <div className="flex items-center bg-blue-50 p-4 rounded-lg">
          <div className="bg-blue-100 p-3 rounded-full mr-4">
            <FontAwesomeIcon icon={faUsers} className="text-blue-600" />
          </div>
          <div>
            <div className="text-sm text-gray-600">Total Contacts</div>
            <div className="text-2xl font-bold text-gray-800">{rto.contacts?.length || 0}</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* RTO Information */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm">
            <div className="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800 flex items-center">
                <FontAwesomeIcon icon={faInfoCircle} className="mr-2 text-blue-600" />
                RTO Information
              </h2>
              <button
                onClick={handleExternalLink}
                className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <FontAwesomeIcon icon={faLink} className="mr-1" />
                TGA Link
              </button>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-600 mb-1">RTO Code</label>
                    <div className="flex items-center bg-gray-50 p-3 rounded">
                      <FontAwesomeIcon icon={faIdCard} className="mr-2 text-blue-600" />
                      <span className="text-gray-800">{rto.code}</span>
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-600 mb-1">Legal Name</label>
                    <div className="flex items-center bg-gray-50 p-3 rounded">
                      <FontAwesomeIcon icon={faBuilding} className="mr-2 text-blue-600" />
                      <span className="text-gray-800">{rto.legalName}</span>
                    </div>
                  </div>
                  {rto.businessName && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-600 mb-1">
                        Business Name
                      </label>
                      <div className="flex items-center bg-gray-50 p-3 rounded">
                        <FontAwesomeIcon icon={faBuilding} className="mr-2 text-blue-600" />
                        <span className="text-gray-800">{rto.businessName}</span>
                      </div>
                    </div>
                  )}
                </div>
                <div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-600 mb-1">RTO Type</label>
                    <div className="flex items-center bg-gray-50 p-3 rounded">
                      <FontAwesomeIcon icon={faInfoCircle} className="mr-2 text-blue-600" />
                      <span className="text-gray-800">{rto.rtoType || "Registered Training Organisation"}</span>
                    </div>
                  </div>
                  {rto.address && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-600 mb-1">Address</label>
                      <div className="flex items-start bg-gray-50 p-3 rounded">
                        <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2 mt-1 text-blue-600" />
                        <span className="text-gray-800">{rto.address}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {rto.description && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-600 mb-1">Description</label>
                  <div className="bg-gray-50 p-4 rounded">
                    <p className="text-gray-800 whitespace-pre-line">{rto.description}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions Panel */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm">
            <div className="border-b border-gray-200 px-6 py-4">
              <h2 className="text-lg font-bold text-gray-800 flex items-center">
                <FontAwesomeIcon icon={faUsers} className="mr-2 text-blue-600" />
                Contact Management
              </h2>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">Add or manage contacts for this RTO.</p>
              <button
                onClick={() => setIsAddContactModalOpen(true)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center transition-colors duration-200"
              >
                <FontAwesomeIcon icon={faUserPlus} className="mr-2" />
                Add New Contact
              </button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm">
            <div className="border-b border-gray-200 px-6 py-4">
              <h2 className="text-lg font-bold text-gray-800 flex items-center">
                <FontAwesomeIcon icon={faSync} className="mr-2 text-blue-600" />
                Data Synchronization
              </h2>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">Reload data from training.gov.au to ensure you have the latest information.</p>
              <button
                onClick={handleReloadInfo}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-lg flex items-center justify-center transition-colors duration-200"
                disabled={isReloading}
              >
                <FontAwesomeIcon icon={faSync} className={`mr-2 ${isReloading ? "fa-spin" : ""}`} />
                {isReloading ? "Syncing Data..." : "Sync Data"}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Contacts Section */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
          <h2 className="text-lg font-bold text-gray-800 flex items-center">
            <FontAwesomeIcon icon={faUsers} className="mr-2 text-blue-600" />
            RTO Contacts
          </h2>
          <button
            onClick={() => setIsAddContactModalOpen(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center transition-colors duration-200"
          >
            <FontAwesomeIcon icon={faUserPlus} className="mr-2" />
            Add Contact
          </button>
        </div>
        <div className="p-6">
          <RTOContactsList
            contacts={rto.contacts || []}
            rtoCode={rtoCode}
            onContactUpdated={refetch}
          />
        </div>
      </div>

      {/* Add Contact Modal */}
      <AddContactModal
        isOpen={isAddContactModalOpen}
        onClose={() => setIsAddContactModalOpen(false)}
        rtoCode={rtoCode}
        onSuccess={() => {
          setIsAddContactModalOpen(false);
          refetch();
        }}
      />

      {/* Sync Confirmation Modal */}
      <ConfirmationModal
        isOpen={isSyncConfirmModalOpen}
        onClose={() => setSyncConfirmModalOpen(false)}
        onConfirm={confirmReloadInfo}
        title="Sync RTO Data"
        message={
          <div>
            <p className="mb-2">Are you sure you want to sync data from training.gov.au?</p>
            <p className="text-red-600 font-medium">Warning: Any manually added or modified data will be overwritten.</p>
          </div>
        }
        confirmText="Sync Data"
        confirmButtonClass="bg-blue-600 hover:bg-blue-700"
        isLoading={isReloading}
      />
    </div>
  );
};

export default OperationsRTOProfile;
