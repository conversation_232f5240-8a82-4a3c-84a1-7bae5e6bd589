import { createApi } from '@reduxjs/toolkit/query/react';
import { createCustomBaseQuery } from './customBaseQuery';

export const ProfileInfoApi = createApi({
  reducerPath: 'ProfileInfoApi',
  baseQuery: createCustomBaseQuery('/api/generic'),
  endpoints: (builder) => ({
    getLoggedUser: builder.query({
      query: () => '/agent/profile',
    }),
    getDocumentsInfo: builder.query({
      query: () => '/documents',
    }),
  }),
});

export const {
  useGetLoggedUserQuery,
 useGetDocumentsInfoQuery
} = ProfileInfoApi;