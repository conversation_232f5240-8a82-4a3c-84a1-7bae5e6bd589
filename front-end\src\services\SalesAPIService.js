import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const SalesAPIService = createApi({
  reducerPath: 'SalesAPIService',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL + '/api/sales',
    prepareHeaders: (headers) => {
      const token = localStorage.getItem('token');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Trouble'],
  endpoints: (builder) => ({
    getQualifications: builder.query({
      query: () => '/qualifications',
    }),
    addTrouble: builder.mutation({
      query: (troubleDTO) => ({
        url: '/trouble',
        method: 'POST',
        body: troubleDTO,
      }),
      invalidatesTags: ['Trouble'],
    }),
    getAllTrouble: builder.query({
      query: () => '/trouble/all',
      providesTags: ['Trouble'],
    }),
    getAllEmployees: builder.query({
      query: () => '/employee/all',
    }),
    deleteTrouble: builder.mutation({
      query: (id) => ({
        url: `/trouble/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Trouble'],
    }),
    changeStatusOfTrouble: builder.mutation({
      query: ({ id, status }) => ({
        url: `/trouble/${id}/${status}`,
        method: 'PUT',
      }),
      invalidatesTags: ['Trouble'],
    }),
    addComment: builder.mutation({
      query: ({ id, reqDTO }) => ({
        url: `/trouble/comment/${id}`,
        method: 'PUT',
        body: reqDTO,
      }),
      invalidatesTags: ['Trouble'],
    }),
    editTrouble: builder.mutation({
      query: ({ id, reqDTO }) => ({
        url: `/trouble/edit/${id}`,
        method: 'PUT',
        body: reqDTO,
      }),
      invalidatesTags: ['Trouble'],
    }),
  }),
});

export const {
  useGetQualificationsQuery,
  useAddTroubleMutation,
  useGetAllTroubleQuery,
  useGetAllEmployeesQuery,
  useDeleteTroubleMutation,
  useChangeStatusOfTroubleMutation,
  useAddCommentMutation,
  useEditTroubleMutation,
} = SalesAPIService;
