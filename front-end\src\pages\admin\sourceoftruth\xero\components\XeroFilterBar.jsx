import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faFilter, faCalendarAlt, faUser } from "@fortawesome/free-solid-svg-icons";

const XeroFilterBar = ({
  searchQuery,
  setSearchQuery,
  statusFilter,
  setStatusFilter,
  paymentStatusFilter,
  setPaymentStatusFilter,
  dateRangeFilter,
  setDateRangeFilter,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  selectedTab,
  agentFilter,
  setAgentFilter,
  uniqueAgents = []
}) => {
  // Status options for filtering
  const statusOptions = ["Paid", "Approved", "Partially Paid", "Pending"];

  // Payment status options for KPI1
  const paymentStatusOptions = ["Sent", "Not Sent"];

  return (
    <div className="bg-white rounded-lg shadow-sm p-5 mb-6 border border-[#DBDCDE]">
      <div className="flex flex-col md:flex-row gap-4 flex-wrap">
        {/* Search */}
        <div className="relative md:w-1/3">
          <input
            type="text"
            placeholder={selectedTab === "KPI1" ? "Search by invoice #, contact name, or agent..." : "Search..."}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full h-11 rounded-lg border border-[#DBDCDE] pl-10 pr-4 text-sm focus:border-[#6E39CB] focus:outline-none"
            title={selectedTab === "KPI1" ? "Search by invoice number, contact name, or agent" : "Search"}
          />
          <FontAwesomeIcon
            icon={faSearch}
            className="absolute left-3 top-3.5 text-[#89868D]"
          />
        </div>

        <div className="flex flex-wrap gap-4 md:ml-auto">
          {/* Status Filter */}
          {selectedTab === "KPI1" && (<div className="relative">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
            >
              <option value="">All Statuses</option>
              {statusOptions.map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </select>
            <FontAwesomeIcon
              icon={faFilter}
              className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none"
            />
          </div> )}
          

          {/* Payment Status Filter - Only for KPI1 */}
          {selectedTab === "KPI1" && (
            <div className="relative">
              <select
                value={paymentStatusFilter}
                onChange={(e) => setPaymentStatusFilter(e.target.value)}
                className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
              >
                <option value="">All Payment Status</option>
                {paymentStatusOptions.map((status) => (
                  <option key={status} value={status}>
                    {status}
                  </option>
                ))}
              </select>
              <FontAwesomeIcon
                icon={faFilter}
                className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none"
              />
            </div>
          )}

          {/* Agent Filter - Only for KPI1 */}
          {selectedTab === "KPI1" && (
            <div className="relative">
              <select
                value={agentFilter}
                onChange={(e) => setAgentFilter(e.target.value)}
                className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
                title="Filter by agent"
              >
                <option value="">All Agents</option>
                {uniqueAgents.sort().map((agent) => (
                  <option key={agent} value={agent}>
                    {agent}
                  </option>
                ))}
              </select>
              <FontAwesomeIcon
                icon={faUser}
                className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none"
              />
            </div>
          )}

          {/* Date Filter */}
          <div className="relative">
            <select
              value={dateRangeFilter}
              onChange={(e) => setDateRangeFilter(e.target.value)}
              className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
            >
              <option value="thisWeek">This Week</option>
              <option value="lastWeek">Last Week</option>
              <option value="thisMonth">This Month</option>
              <option value="custom">Custom Date</option>
              <option value="all">All Dates</option>
            </select>
            <FontAwesomeIcon
              icon={faCalendarAlt}
              className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none"
            />
          </div>
        </div>
      </div>

      {/* Custom Date Range */}
      {dateRangeFilter === "custom" && (
        <div className="flex items-center gap-2 mt-4">
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="h-11 rounded-lg border border-[#DBDCDE] px-4 text-sm focus:border-[#6E39CB] focus:outline-none"
          />
          <span className="text-[#89868D]">to</span>
          <input
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            className="h-11 rounded-lg border border-[#DBDCDE] px-4 text-sm focus:border-[#6E39CB] focus:outline-none"
          />
        </div>
      )}
    </div>
  );
};

export default XeroFilterBar;
