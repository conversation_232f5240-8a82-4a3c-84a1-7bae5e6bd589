package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class DashboardDTO {
    private String weekTitle;
    private double kpi1Target;
    private double kpi2Target;

    private double kpi1Actual;
    private double kpi2Actual;

    private double quotes;
    private double invoices;

    private List<AgentTargetResponseDTO> agentInfos;

    private String topPerformer;


    public void appendTitle(String title) {
        if (this.weekTitle == null) {
            this.weekTitle = title;
        } else {
            this.weekTitle += ", " + title;
        }
    }
}
