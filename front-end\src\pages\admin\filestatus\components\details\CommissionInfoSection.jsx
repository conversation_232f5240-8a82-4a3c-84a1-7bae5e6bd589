import React from "react";
import { useUpdateCommissionInfoMutation, useUpdateCommissionPaymentStatusMutation, useUpdateCommissionPaymentDateMutation } from "../../../../../services/CompanyAPIService";
import EditableField from "./EditableField";
import DateEditor from "./DateEditor";

/**
 * CommissionInfoSection component for managing commission information
 * @param {object} fileStatus - File status data
 * @param {function} showToast - Function to show toast notifications
 * @param {function} refetch - Function to refetch data
 */
const CommissionInfoSection = ({ fileStatus, showToast, refetch }) => {
  // API mutations
  const [updateCommissionInfo, { isLoading: isUpdatingCommissionInfo }] = useUpdateCommissionInfoMutation();
  const [updateCommissionPaymentStatus, { isLoading: isUpdatingPaymentStatus }] = useUpdateCommissionPaymentStatusMutation();
  const [updateCommissionPaymentDate, { isLoading: isUpdatingPaymentDate }] = useUpdateCommissionPaymentDateMutation();

  // Payment status options
  const paymentStatusOptions = [
    { value: "PENDING", label: "Pending" },
    { value: "PAID", label: "Paid" },
    { value: "NOT_REQUIRED", label: "Not Required" }
  ];

  // Handle commission name update
  const handleCommissionNameUpdate = async (value) => {
    try {
      await updateCommissionInfo({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        externalCommissionName: value,
        externalCommissionContactInfo: fileStatus.externalCommissionContactInfo || "",
        commissionAmount: fileStatus.commissionAmount || 0
      }).unwrap();
      
      showToast("Commission name updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update commission name", "error");
      console.error("Error updating commission name:", error);
    }
  };

  // Handle commission contact info update
  const handleContactInfoUpdate = async (value) => {
    try {
      await updateCommissionInfo({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        externalCommissionName: fileStatus.externalCommissionName || "",
        externalCommissionContactInfo: value,
        commissionAmount: fileStatus.commissionAmount || 0
      }).unwrap();
      
      showToast("Contact information updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update contact information", "error");
      console.error("Error updating contact information:", error);
    }
  };

  // Handle commission amount update
  const handleCommissionAmountUpdate = async (value) => {
    try {
      const amount = parseFloat(value) || 0;
      
      await updateCommissionInfo({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        externalCommissionName: fileStatus.externalCommissionName || "",
        externalCommissionContactInfo: fileStatus.externalCommissionContactInfo || "",
        commissionAmount: amount
      }).unwrap();
      
      showToast("Commission amount updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update commission amount", "error");
      console.error("Error updating commission amount:", error);
    }
  };

  // Handle payment status update
  const handlePaymentStatusUpdate = async (value) => {
    try {
      await updateCommissionPaymentStatus({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        paymentStatus: value
      }).unwrap();
      
      showToast("Payment status updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update payment status", "error");
      console.error("Error updating payment status:", error);
    }
  };

  // Handle payment date update
  const handlePaymentDateUpdate = async (date) => {
    try {
      await updateCommissionPaymentDate({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        paymentDate: date
      }).unwrap();
      
      showToast("Payment date updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update payment date", "error");
      console.error("Error updating payment date:", error);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Commission Information</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Commission Name */}
        <EditableField
          label="External Commission Name"
          value={fileStatus.externalCommissionName}
          onSave={handleCommissionNameUpdate}
          isLoading={isUpdatingCommissionInfo}
        />

        {/* Contact Information */}
        <EditableField
          label="Contact Information"
          value={fileStatus.externalCommissionContactInfo}
          onSave={handleContactInfoUpdate}
          isLoading={isUpdatingCommissionInfo}
        />

        {/* Commission Amount */}
        <EditableField
          label="Commission Amount ($)"
          value={fileStatus.commissionAmount?.toString() || "0"}
          onSave={handleCommissionAmountUpdate}
          isLoading={isUpdatingCommissionInfo}
          type="text"
        />

        {/* Payment Status */}
        <EditableField
          label="Payment Status"
          value={fileStatus.commissionPaymentStatus || "PENDING"}
          onSave={handlePaymentStatusUpdate}
          isLoading={isUpdatingPaymentStatus}
          type="select"
          options={paymentStatusOptions}
        />
      </div>

      <div className="mt-6">
        {/* Payment Date */}
        <DateEditor
          label="Commission Payment Date"
          date={fileStatus.commissionPaymentDate}
          onUpdate={handlePaymentDateUpdate}
          icon="💰"
          isLoading={isUpdatingPaymentDate}
        />
      </div>
    </div>
  );
};

export default CommissionInfoSection;
