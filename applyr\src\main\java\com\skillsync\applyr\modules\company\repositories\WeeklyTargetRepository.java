package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.WeeklyTarget;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;

public interface WeeklyTargetRepository extends JpaRepository<WeeklyTarget, Long> {
    List<WeeklyTarget> findAllByAgentUsernameAndWeeklyTargetsCombinedStartDateAfterAndWeeklyTargetsCombinedEndDateBefore(String agentUsername, LocalDateTime weeklyTargetsCombinedStartDateAfter, LocalDateTime weeklyTargetsCombinedEndDateBefore);
}
