import React, { useRef } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUpload, faFileExcel, faTimes, faSpinner } from "@fortawesome/free-solid-svg-icons";

const XeroImportModal = ({
  isOpen,
  closeModal,
  uploadFile,
  setUploadFile,
  handleUpload,
  importTab,
  isUploading,
}) => {
  const fileInputRef = useRef(null);

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
  };

  const handleFileDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) {
      setUploadFile(file);
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadFile(file);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div
        className="fixed inset-0 bg-black bg-opacity-30 z-40"
        onClick={closeModal}
      />
      <div
        className="fixed inset-0 flex items-center justify-center z-50"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl mx-4 sm:mx-0">
          <div className="flex items-center justify-between p-5 border-b border-[#DBDCDE]">
            <h3 className="text-lg font-semibold text-[#3A3541]">
              {importTab
                ? `Upload File for ${importTab === "KPI1" ? "Invoicing" : "Money in Bank"}`
                : "Upload File"}
            </h3>
            <button
              onClick={closeModal}
              className="text-[#89868D] hover:text-[#3A3541] transition-colors"
            >
              <FontAwesomeIcon icon={faTimes} className="text-xl" />
            </button>
          </div>

          <div className="p-6">
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer mb-4 transition-colors ${
                uploadFile
                  ? "border-[#6E39CB] bg-[#F0ECF6]"
                  : "border-[#DBDCDE] hover:border-[#6E39CB] hover:bg-[#F9FAFB]"
              }`}
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDrop={handleFileDrop}
              onClick={() => fileInputRef.current.click()}
            >
              <input
                type="file"
                accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                className="hidden"
                ref={fileInputRef}
                onChange={handleFileSelect}
              />

              {uploadFile ? (
                <div className="flex flex-col items-center">
                  <FontAwesomeIcon icon={faFileExcel} className="text-4xl text-[#6E39CB] mb-3" />
                  <p className="text-[#3A3541] font-medium">{uploadFile.name}</p>
                  <p className="text-[#89868D] text-sm mt-1">
                    {(uploadFile.size / 1024).toFixed(2)} KB
                  </p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <FontAwesomeIcon icon={faUpload} className="text-4xl text-[#89868D] mb-3" />
                  <p className="text-[#3A3541] font-medium">
                    Drag & drop a CSV or Excel file here
                  </p>
                  <p className="text-[#89868D] text-sm mt-1">
                    or click to browse files
                  </p>
                </div>
              )}
            </div>

            <div className="bg-[#F9FAFB] rounded-lg p-4 text-sm text-[#89868D]">
              <p className="font-medium text-[#3A3541] mb-2">File Requirements:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>Excel (.xlsx) or CSV format</li>
                <li>First row should contain column headers</li>
                <li>
                  {importTab === "KPI1"
                    ? "Must include: Invoice Number, Contact, Invoice Date, Due Date, Reference, Gross, Balance, Status"
                    : "Must include: Date, Reference, Contact, Description, Debit, Credit, Net, Source"}
                </li>
              </ul>
            </div>
          </div>

          <div className="flex items-center justify-end p-5 border-t border-[#DBDCDE]">
            <button
              onClick={closeModal}
              className="px-4 py-2 bg-[#F4F5F9] text-[#3A3541] rounded-md mr-3 hover:bg-[#DBDCDE] transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleUpload}
              disabled={!uploadFile || isUploading}
              className={`px-4 py-2 rounded-md text-white transition-colors flex items-center justify-center min-w-[120px] ${
                uploadFile && !isUploading
                  ? "bg-[#6E39CB] hover:bg-opacity-90"
                  : "bg-[#6E39CB] bg-opacity-50 cursor-not-allowed"
              }`}
            >
              {isUploading ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                  Uploading...
                </>
              ) : (
                "Upload File"
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default XeroImportModal;
