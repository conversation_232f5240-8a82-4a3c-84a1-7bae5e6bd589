package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.TrueXeroInBank;
import com.skillsync.applyr.core.models.entities.TrueXeroInvoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TrueXeroInBankRepository extends JpaRepository<TrueXeroInBank, Long> {
    TrueXeroInBank findByInvoiceNumberAndInsertDateAndContactNameAndCreditAmountAndDebitAmountAndNetAmount(String invoiceNumber, LocalDateTime insertDate, String contactName, double creditAmount, double debitAmount, double netAmount);

    List<TrueXeroInBank> findAllByInsertDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    // Agent-specific queries
    List<TrueXeroInBank> findByAgentUsername(String agentUsername);

    List<TrueXeroInBank> findByAgentUsernameAndInsertDateBetween(String agentUsername, LocalDateTime startDate, LocalDateTime endDate);

    List<TrueXeroInBank> findByInsertDateBetween(LocalDateTime startDate, LocalDateTime endDate);
}

