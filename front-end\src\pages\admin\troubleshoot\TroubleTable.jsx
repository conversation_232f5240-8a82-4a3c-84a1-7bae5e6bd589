import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUser,
  faCalendarAlt,
  faUserCog,
  faEllipsisVertical,
  faEdit,
  faTrash,
  faComment,
  faPaperPlane,
  faSort,
} from "@fortawesome/free-solid-svg-icons";
import { formatDate, convertStatus } from "./TroubleUtils";

const TroubleTable = ({ troubles, onEdit, onDelete, onStatusChange, onAddComment }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [dropdownVisible, setDropdownVisible] = useState(null);
  const [commentInputs, setCommentInputs] = useState({});

  const toggleDropdown = (troubleId) => {
    setDropdownVisible(dropdownVisible === troubleId ? null : troubleId);
  };

  const toggleExpandRow = (troubleId) => {
    setExpandedRow(expandedRow === troubleId ? null : troubleId);
  };

  const handleStatusChange = (trouble, e) => {
    const newStatus = e.target.value;
    onStatusChange(trouble, newStatus);
  };

  const handleSendComment = async (troubleId) => {
    if (!commentInputs[troubleId]?.trim()) {
      alert("Please enter a comment.");
      return;
    }
    try {
      await onAddComment(troubleId, { answer: commentInputs[troubleId] });
      setCommentInputs((prev) => ({
        ...prev,
        [troubleId]: "",
      }));
    } catch (err) {
      console.error("Failed to send comment:", err);
    }
  };

  // Function to get status color and display
  const getStatusDisplay = (status) => {
    const statusMap = {
      'RESOLVED': { color: 'bg-green-100 text-green-800', label: 'Resolved' },
      'IN_PROGRESS': { color: 'bg-purple-100 text-purple-800', label: 'In Progress' },
      'NO_SOLUTION': { color: 'bg-gray-100 text-gray-800', label: 'No Solution' },
      'PENDING': { color: 'bg-blue-100 text-blue-800', label: 'Pending' }
    };

    return statusMap[status] || { color: 'bg-gray-100 text-gray-800', label: status };
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse bg-white rounded-lg overflow-hidden">
        <thead className="bg-[#F9FAFB] border-b border-[#DBDCDE]">
          <tr>
            <th className="px-6 py-4 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider">
              Question
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider">
              Created By
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider">
              Assigned To
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider">
              Due Date
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-4 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider">
              Comments
            </th>
            <th className="px-6 py-4 text-right text-xs font-medium text-[#3A3541] uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-[#DBDCDE]">
          {troubles.map((trouble) => (
            <React.Fragment key={trouble.troubleId}>
              <tr className="hover:bg-[#F9FAFB]">
                <td className="px-6 py-4 whitespace-normal">
                  <div className="text-sm text-[#3A3541] line-clamp-2">{trouble.questions}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8 flex items-center justify-center rounded-full bg-[#F0ECF6]">
                      <FontAwesomeIcon icon={faUser} className="text-[#6E39CB]" />
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-[#3A3541]">
                        {trouble.createdBy.fullName || trouble.createdBy.username}
                      </div>
                      <div className="text-xs text-[#89868D]">{formatDate(trouble.createdDate)}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="rounded-full bg-[#F0ECF6] px-2.5 py-1 text-xs font-medium text-[#6E39CB]">
                    {trouble.assignedTo.fullName || trouble.assignedTo.username}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-[#3A3541]">{formatDate(trouble.dueDate)}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="relative">
                    <select
                      value={convertStatus(trouble.status)}
                      onChange={(e) => handleStatusChange(trouble, e)}
                      className={`h-9 appearance-none rounded-lg border border-gray-300 pl-3 pr-8 text-xs font-medium focus:outline-none focus:ring-2 focus:ring-[#6E39CB] ${getStatusDisplay(trouble.status).color}`}
                    >
                      <option value="resolved">Resolved</option>
                      <option value="inProgress">In Progress</option>
                      <option value="pending">Pending</option>
                      <option value="noSolution">No Solution</option>
                    </select>
                    <FontAwesomeIcon
                      icon={faSort}
                      className="absolute right-2 top-2.5 text-[#89868D] pointer-events-none"
                    />
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => toggleExpandRow(trouble.troubleId)}
                    className="flex items-center gap-2 rounded-md bg-[#F0ECF6] px-3 py-1.5 text-xs font-medium text-[#6E39CB] hover:bg-opacity-80"
                  >
                    <FontAwesomeIcon icon={faComment} />
                    <span>{expandedRow === trouble.troubleId ? "Hide" : "View"}</span>
                    {trouble.answers && trouble.answers.length > 0 && (
                      <span className="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-[#6E39CB] text-xs text-white">
                        {trouble.answers.length}
                      </span>
                    )}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="relative">
                    <button
                      className="flex items-center justify-center rounded-md p-2 hover:bg-[#F4F5F9]"
                      onClick={() => toggleDropdown(trouble.troubleId)}
                    >
                      <FontAwesomeIcon icon={faEllipsisVertical} className="text-[#89868D]" />
                    </button>
                    {dropdownVisible === trouble.troubleId && (
                      <div className="absolute right-0 top-full z-40 w-40 rounded-md border border-[#DBDCDE] bg-white py-1 shadow-sm">
                        <button
                          onClick={() => {
                            onEdit(trouble);
                            setDropdownVisible(null);
                          }}
                          className="flex w-full items-center gap-2 px-4 py-2 text-sm hover:bg-[#F4F5F9] text-[#3A3541]"
                        >
                          <FontAwesomeIcon icon={faEdit} className="text-[#6E39CB]" />
                          <span>Edit</span>
                        </button>
                        <button
                          onClick={() => {
                            onDelete(trouble.troubleId);
                            setDropdownVisible(null);
                          }}
                          className="flex w-full items-center gap-2 px-4 py-2 text-sm hover:bg-[#F4F5F9] text-[#3A3541]"
                        >
                          <FontAwesomeIcon icon={faTrash} className="text-red-500" />
                          <span>Delete</span>
                        </button>
                      </div>
                    )}
                  </div>
                </td>
              </tr>
              {expandedRow === trouble.troubleId && (
                <tr>
                  <td colSpan="7" className="px-6 py-4 bg-[#F9FAFB] border-t border-[#DBDCDE]">
                    <div className="space-y-4">
                      <h5 className="text-sm font-medium text-[#3A3541]">
                        Comments ({trouble.answers ? trouble.answers.length : 0})
                      </h5>
                      <div className="space-y-3 mb-4">
                        {trouble.answers && trouble.answers.length > 0 ? (
                          trouble.answers.map((answer, aIndex) => (
                            <div key={aIndex} className="rounded-lg border border-[#DBDCDE] bg-white p-3">
                              <div className="flex justify-between items-center mb-2">
                                <div className="flex items-center gap-2">
                                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#F0ECF6]">
                                    <FontAwesomeIcon icon={faUser} className="text-[#6E39CB] text-xs" />
                                  </div>
                                  <p className="text-xs font-medium text-[#3A3541]">
                                    {answer.answeredBy.fullName || answer.answeredBy.username}
                                  </p>
                                </div>
                                <p className="text-xs text-[#89868D]">
                                  {new Date(answer.answeredAt).toLocaleTimeString()}
                                </p>
                              </div>
                              <p className="text-sm text-[#3A3541]">{answer.answer}</p>
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-[#89868D] bg-white p-3 rounded-lg border border-[#DBDCDE]">
                            No comments yet.
                          </p>
                        )}
                      </div>
                      <div>
                        <textarea
                          placeholder="Enter your comment"
                          value={commentInputs[trouble.troubleId] || ""}
                          onChange={(e) =>
                            setCommentInputs((prev) => ({
                              ...prev,
                              [trouble.troubleId]: e.target.value,
                            }))
                          }
                          className="w-full rounded-lg border border-[#DBDCDE] bg-white py-2 px-3 text-sm outline-none focus:border-[#6E39CB]"
                          rows="2"
                        ></textarea>
                        <button
                          onClick={() => handleSendComment(trouble.troubleId)}
                          className="mt-2 flex items-center justify-center gap-2 rounded-md bg-[#6E39CB] py-1.5 px-4 text-xs font-medium text-white hover:bg-opacity-90"
                        >
                          <FontAwesomeIcon icon={faPaperPlane} />
                          <span>Send Comment</span>
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TroubleTable;
