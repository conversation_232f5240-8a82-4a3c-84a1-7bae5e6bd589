import React, { useRef } from "react";
import { FaTimes, FaPlus } from "react-icons/fa";

const UploadModal = ({
  isOpen,
  onClose,
  uploadFile,
  onFileChange,
  selectedSalesReps,
  onSubmit,
  isUploading,
  employees
}) => {
  const fileInputRef = useRef(null);

  if (!isOpen) return null;

  const handleDragOver = (e) => e.preventDefault();
  const handleDragEnter = (e) => e.preventDefault();
  const handleFileDrop = (e) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      const event = { target: { files: [files[0]] } };
      onFileChange(event);
    }
  };

  const toggleSalesRepSelection = (username) => {
    // This function should be passed from parent or handled differently
    // For now, we'll assume selectedSalesReps is managed by parent
  };

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-30 z-40" onClick={onClose} />
      <div className="fixed inset-0 flex items-center justify-center z-50" onClick={(e) => e.stopPropagation()}>
        <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl mx-4 sm:mx-0">
          <div className="flex items-center justify-between p-4 border-b">
            <h3 className="text-lg font-semibold">Add Leads (Bulk)</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700 text-xl">
              <FaTimes />
            </button>
          </div>
          
          <div className="p-4">
            <div
              className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center cursor-pointer mb-4"
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDrop={handleFileDrop}
              onClick={() => fileInputRef.current.click()}
            >
              <input
                type="file"
                accept=".csv, .xlsx, .xls"
                className="hidden"
                ref={fileInputRef}
                onChange={onFileChange}
              />
              {uploadFile ? (
                <p className="text-gray-700">{uploadFile.name}</p>
              ) : (
                <p className="text-gray-500">
                  Drag &amp; drop a CSV or Excel file here, or click to select
                </p>
              )}
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Assign consultants
              </label>
              <div className="border border-gray-300 rounded-md p-2">
                <div className="flex flex-wrap gap-2">
                  {selectedSalesReps && selectedSalesReps.length > 0 ? (
                    selectedSalesReps.map((rep, index) => (
                      <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center">
                        {rep}
                        <button
                          type="button"
                          className="ml-1 text-blue-500 hover:text-blue-700"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle removal - this should be managed by parent
                          }}
                        >
                          &times;
                        </button>
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-500 text-sm">No consultants selected</span>
                  )}
                </div>
              </div>
              
              <div className="mt-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md">
                {employees && employees.map(emp => (
                  <label
                    key={emp.username}
                    className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={selectedSalesReps && selectedSalesReps.includes(emp.username)}
                      onChange={() => toggleSalesRepSelection(emp.username)}
                      className="mr-2"
                    />
                    {emp.fullName} <span className="text-gray-500 ml-1">(@{emp.username})</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
          
          <div className="flex items-center justify-end p-4 border-t">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md mr-2 hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              onClick={onSubmit}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              disabled={isUploading}
            >
              {isUploading ? "Uploading..." : "Upload Leads"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default UploadModal;
