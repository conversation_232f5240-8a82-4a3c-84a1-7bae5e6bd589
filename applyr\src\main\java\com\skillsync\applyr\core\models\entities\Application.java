package com.skillsync.applyr.core.models.entities;

import com.skillsync.applyr.core.models.enums.PaidStatus;
import com.skillsync.applyr.core.models.enums.RequestStatus;
import com.skillsync.applyr.core.models.enums.Status;
import com.skillsync.applyr.modules.company.models.ApplicationRequestDTO;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Entity
@Table(name = "applications")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Application extends Auditable<String> {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    private String applicationId;

    private String applicantName;
    private String applicantEmail;
    private String applicantPhone;
    private String applicantAddress;
    private String leadPhone;
    private String otherInformation;

    @OneToMany(mappedBy = "application",
            cascade = CascadeType.ALL,
            orphanRemoval = true)
    private List<SoldQualifications> soldQualificationsList;

    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PaymentInstallment> installments = new ArrayList<>();

    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ApplicationComments> comments = new ArrayList<>();

    private String agentUsername;

    @Enumerated(EnumType.STRING)
    private Status status;

    private double price;
    private double paidAmount;

    @Enumerated(EnumType.STRING)
    private PaidStatus paidStatus;

    private String quoteRefNumber;

    @Enumerated(EnumType.STRING)
    private RequestStatus quoteStatus;

    private String invoiceRefNumber;

    @Enumerated(EnumType.STRING)
    private RequestStatus invoiceStatus;

    public Application(ApplicationRequestDTO application) {
        this.applicantName = application.getApplicantName();
        this.applicantEmail = application.getApplicantEmail();
        this.applicantPhone = application.getApplicantPhone();
        this.applicantAddress = application.getApplicantAddress();
        this.leadPhone = application.getLeadPhone();
        this.status = Status.DOCUMENT_PENDING;
        this.paidStatus = PaidStatus.PENDING;

        this.otherInformation = application.getOtherInformation();

        this.soldQualificationsList = new ArrayList<>();
        this.comments = new ArrayList<>();

        this.applicationId = generateApplicationId(application.getApplicantName());
    }

    public void calculatePaidAmount() {
        this.paidAmount = 0;
        for(PaymentInstallment installment : installments) {
            this.paidAmount += installment.getAmount();
        }
    }

    public void addInstallment(PaymentInstallment installment) {
        installment.setApplication(this);
        this.installments.add(installment);
        calculatePaidAmount();
    }

    public void removeInstallment(PaymentInstallment installment) {
        installment.setApplication(null);
        this.installments.remove(installment);
        calculatePaidAmount();
    }

    public void addComment(ApplicationComments comment) {
        comments.add(comment);
        comment.setApplication(this);
    }

    public void removeComment(ApplicationComments comment) {
        comments.remove(comment);
        comment.setApplication(null);
    }


    public void generateTotalPrice() {
        this.price = 0;
        for(SoldQualifications soldQualifications : soldQualificationsList) {
            this.price += soldQualifications.getSoldPrice();
        }
    }

    private String generateApplicationId(String name) {
        if (name == null || name.isEmpty()) {
            return "A" + this.applicantPhone.substring(1,7);
        }

        String[] nameParts = name.toUpperCase().split(" ");
        Random random = new SecureRandom();
        String alphanumeric = "0123456789";
        StringBuilder idBuilder = new StringBuilder();

        if (nameParts.length == 1) {
            idBuilder.append(nameParts[0].charAt(0));
            idBuilder.append(random.nextInt(100)); // Random 2-digit number
            idBuilder.append(nameParts[0].substring(1, Math.min(4, nameParts[0].length())));
        } else {
            idBuilder.append(nameParts[0].charAt(0)); // First letter of first name
            idBuilder.append(nameParts[1].charAt(0)); // First letter of second name
            idBuilder.append(random.nextInt(100)); // Random 2-digit number
            idBuilder.append(nameParts[1].substring(1, Math.min(4, nameParts[1].length())));
        }
        return idBuilder.toString();
    }
}
