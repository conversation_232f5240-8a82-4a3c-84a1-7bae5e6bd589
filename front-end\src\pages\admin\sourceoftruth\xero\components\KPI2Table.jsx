import React, { useState, useMemo } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faMoneyBillWave,
  faUpload,
  faSpinner,
  faUser,
  faSort,
  faSortUp,
  faSortDown
} from "@fortawesome/free-solid-svg-icons";

const KPI2Table = ({ data, isLoading, onImport, uniqueAgents = [], agentFilter, setAgentFilter }) => {
  const [sortConfig, setSortConfig] = useState({ key: 'dateInserted', direction: 'desc' }); // Default sort by date, latest first
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    // Try to parse dd MMM yyyy, fallback to Date
    const parts = dateString.split(" ");
    if (parts.length === 3) {
      const day = parseInt(parts[0]);
      const month = ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].indexOf(parts[1]);
      const year = parseInt(parts[2]);
      if (!isNaN(day) && month !== -1 && !isNaN(year)) {
        return `${day.toString().padStart(2, "0")} ${parts[1]} ${year}`;
      }
    }
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date.toLocaleDateString("en-GB", { day: "2-digit", month: "short", year: "numeric" });
    }
    return dateString;
  };

  // Format currency for display
  const formatCurrency = (amount) => {
    if (amount === undefined || amount === null) return "-";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Handle sorting
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Get sort icon
  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return faSort;
    }
    return sortConfig.direction === 'asc' ? faSortUp : faSortDown;
  };

  // Sort data
  const sortedData = useMemo(() => {
    if (!sortConfig.key || !data) return data;

    return [...data].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle different data types
      if (sortConfig.key === 'dateInserted') {
        aValue = aValue ? new Date(aValue) : new Date(0);
        bValue = bValue ? new Date(bValue) : new Date(0);
      } else if (sortConfig.key === 'debitAmount' || sortConfig.key === 'creditAmount' || sortConfig.key === 'netAmount') {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      } else {
        // For agent field, handle nested object
        if (sortConfig.key === 'agent') {
          aValue = a.agent || a.agentCommission?.name || '';
          bValue = b.agent || b.agentCommission?.name || '';
        }
        aValue = aValue ? aValue.toString().toLowerCase() : '';
        bValue = bValue ? bValue.toString().toLowerCase() : '';
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [data, sortConfig]);

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-[#3A3541]">
          <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2 text-[#6E39CB]" />
          KPI2 Money in Bank Data
        </h2>
        {/* Agent Filter for KPI2 */}
        <div className="relative min-w-[180px]">
          <select
            value={agentFilter}
            onChange={e => setAgentFilter(e.target.value)}
            className="h-10 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
            title="Filter by agent"
          >
            <option value="">All Agents</option>
            {uniqueAgents.sort().map(agent => (
              <option key={agent} value={agent}>{agent}</option>
            ))}
          </select>
          <FontAwesomeIcon icon={faUser} className="absolute right-3 top-2.5 text-[#89868D] pointer-events-none" />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-[#DBDCDE] overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full divide-y divide-[#DBDCDE]">
            <thead className="bg-[#F9FAFB]">
              <tr>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('dateInserted')}
                >
                  <div className="flex items-center gap-1">
                    Date
                    <FontAwesomeIcon icon={getSortIcon('dateInserted')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('agent')}
                >
                  <div className="flex items-center gap-1">
                    Agent
                    <FontAwesomeIcon icon={getSortIcon('agent')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('reference')}
                >
                  <div className="flex items-center gap-1">
                    Reference
                    <FontAwesomeIcon icon={getSortIcon('reference')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('contact')}
                >
                  <div className="flex items-center gap-1">
                    Contact
                    <FontAwesomeIcon icon={getSortIcon('contact')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('description')}
                >
                  <div className="flex items-center gap-1">
                    Description
                    <FontAwesomeIcon icon={getSortIcon('description')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('debitAmount')}
                >
                  <div className="flex items-center gap-1">
                    Debit
                    <FontAwesomeIcon icon={getSortIcon('debitAmount')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('creditAmount')}
                >
                  <div className="flex items-center gap-1">
                    Credit
                    <FontAwesomeIcon icon={getSortIcon('creditAmount')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('netAmount')}
                >
                  <div className="flex items-center gap-1">
                    Net
                    <FontAwesomeIcon icon={getSortIcon('netAmount')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('source')}
                >
                  <div className="flex items-center gap-1">
                    Source
                    <FontAwesomeIcon icon={getSortIcon('source')} className="text-[#89868D]" />
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-[#DBDCDE]">
              {isLoading ? (
                <tr>
                  <td colSpan="9" className="px-3 py-8 text-center text-[#89868D]">
                    <div className="flex flex-col items-center">
                      <FontAwesomeIcon icon={faSpinner} className="text-3xl mb-2 text-[#6E39CB] animate-spin" />
                      <p className="text-[#3A3541] font-medium">Loading money in bank data...</p>
                    </div>
                  </td>
                </tr>
              ) : sortedData && sortedData.length > 0 ? (
                sortedData.map((item, index) => (
                  <tr key={index} className="hover:bg-[#F9FAFB] transition-colors">
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {formatDate(item.dateInserted)}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.agent || item.agentCommission?.name || "-"}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.reference || "-"}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.contact || "-"}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.description || "-"}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {formatCurrency(item.debitAmount)}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {formatCurrency(item.creditAmount)}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {formatCurrency(item.netAmount)}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.source || "-"}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="9" className="px-3 py-8 text-center text-[#89868D]">
                    <div className="flex flex-col items-center">
                      <FontAwesomeIcon icon={faMoneyBillWave} className="text-3xl mb-2 text-[#DBDCDE]" />
                      <p className="text-[#3A3541] font-medium">No money in bank data found</p>
                      <p className="text-sm mt-1">Import data or adjust your filters</p>
                      <button
                        onClick={onImport}
                        className="mt-4 flex items-center gap-2 px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-opacity-90 transition-colors"
                      >
                        <FontAwesomeIcon icon={faUpload} />
                        Import Data
                      </button>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
};

export default KPI2Table;
