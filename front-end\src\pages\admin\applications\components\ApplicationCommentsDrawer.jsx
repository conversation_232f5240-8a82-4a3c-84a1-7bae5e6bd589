import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes, faEdit, faTrash } from "@fortawesome/free-solid-svg-icons";
import {
  useAddApplicationCommentMutation,
  useGetApplicationCommentsQuery,
  useUpdateApplicationCommentMutation,
  useDeleteApplicationCommentMutation,
} from "../../../../services/CompanyAPIService";
import { toast } from "react-toastify";

const ApplicationCommentsDrawer = ({ isOpen, onClose, application }) => {
  const [newComment, setNewComment] = useState("");
  const [editingComment, setEditingComment] = useState(null);
  const [editCommentText, setEditCommentText] = useState("");

  const [addApplicationComment] = useAddApplicationCommentMutation();
  const [updateApplicationComment] = useUpdateApplicationCommentMutation();
  const [deleteApplicationComment] = useDeleteApplicationCommentMutation();
  const { data: comments = [], refetch: refetchComments } = useGetApplicationCommentsQuery(
    application?.applicationId,
    { skip: !application?.applicationId }
  );

  const showToast = (message, type = "success") => {
    if (type === "error") {
      toast.error(message);
    } else {
      toast.success(message);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim()) return;
    try {
      await addApplicationComment({
        applicationId: application.applicationId,
        comment: newComment,
      }).unwrap();
      setNewComment("");
      refetchComments();
      showToast("Comment added successfully.");
    } catch (err) {
      console.error("Error adding comment:", err);
      showToast("Unable to add comment.", "error");
    }
  };

  const handleEditComment = (comment) => {
    setEditingComment(comment.id);
    setEditCommentText(comment.content);
  };

  const handleUpdateComment = async () => {
    if (!editCommentText.trim()) return;
    try {
      await updateApplicationComment({
        applicationId: application.applicationId,
        commentId: editingComment,
        content: editCommentText,
      }).unwrap();
      setEditingComment(null);
      setEditCommentText("");
      refetchComments();
      showToast("Comment updated successfully.");
    } catch (err) {
      console.error("Error updating comment:", err);
      showToast("Unable to update comment.", "error");
    }
  };

  const handleDeleteComment = async (commentId) => {
    if (!window.confirm("Are you sure you want to delete this comment?")) return;
    try {
      await deleteApplicationComment({
        applicationId: application.applicationId,
        commentId,
      }).unwrap();
      refetchComments();
      showToast("Comment deleted successfully.");
    } catch (err) {
      console.error("Error deleting comment:", err);
      showToast("Unable to delete comment.", "error");
    }
  };

  const cancelEdit = () => {
    setEditingComment(null);
    setEditCommentText("");
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose}></div>
      <div className="absolute right-0 top-0 h-full w-96 bg-white shadow-xl">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Comments</h2>
              <p className="text-sm text-gray-500">
                {application?.applicationId} - {application?.applicantName}
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <FontAwesomeIcon icon={faTimes} className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {/* Add Comment Section */}
            <div className="mb-6">
              <h6 className="text-md font-medium text-gray-900 mb-4">Add New Comment</h6>
              <div className="space-y-4">
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Write your comment here..."
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] resize-none"
                  rows="3"
                />
                <div className="flex justify-end">
                  <button
                    onClick={handleAddComment}
                    disabled={!newComment.trim()}
                    className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    Add Comment
                  </button>
                </div>
              </div>
            </div>

            {/* Comments List */}
            <div className="space-y-4">
              {comments.length > 0 ? (
                comments.map((comment) => (
                  <div key={comment.id} className="border border-gray-100 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center">
                          <span className="text-[#6E39CB] text-xs font-medium">
                            {comment.createdBy ? comment.createdBy.charAt(0).toUpperCase() : "?"}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-sm font-medium text-gray-900">
                              {comment.createdBy || "Unknown User"}
                            </span>
                            <span className="text-xs text-gray-500">
                              {new Date(comment.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                          {editingComment === comment.id ? (
                            <div className="space-y-3">
                              <textarea
                                value={editCommentText}
                                onChange={(e) => setEditCommentText(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] resize-none"
                                rows="2"
                              />
                              <div className="flex space-x-2">
                                <button
                                  onClick={handleUpdateComment}
                                  className="px-3 py-1 bg-[#6E39CB] text-white text-sm rounded hover:bg-[#5E2CB8] transition-colors"
                                >
                                  Save
                                </button>
                                <button
                                  onClick={cancelEdit}
                                  className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300 transition-colors"
                                >
                                  Cancel
                                </button>
                              </div>
                            </div>
                          ) : (
                            <p className="text-gray-700 text-sm leading-relaxed">{comment.content}</p>
                          )}
                        </div>
                      </div>
                      {editingComment !== comment.id && (
                        <div className="flex space-x-1 ml-2">
                          <button
                            onClick={() => handleEditComment(comment)}
                            className="p-1 text-gray-400 hover:text-[#6E39CB] transition-colors"
                            title="Edit comment"
                          >
                            <FontAwesomeIcon icon={faEdit} className="h-3 w-3" />
                          </button>
                          <button
                            onClick={() => handleDeleteComment(comment.id)}
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete comment"
                          >
                            <FontAwesomeIcon icon={faTrash} className="h-3 w-3" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <div className="bg-[#F4F5F9] p-3 rounded-full mb-4 inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <h6 className="text-md font-medium text-gray-900 mb-2">No Comments Yet</h6>
                  <p className="text-gray-500 text-sm">Be the first to add a comment.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationCommentsDrawer;
