package com.skillsync.applyr.core.models.entities;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Duration;
import java.time.LocalDateTime;

@Entity
@Table(name = "tracker")
@Getter
@Setter
@NoArgsConstructor
public class Tracker extends Auditable<String> {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    private String username;
    private LocalDateTime firstLogin;
    private LocalDateTime lastLogin;
    private double totalTime;

    public Tracker(final String username, final LocalDateTime firstLogin) {
        this.username = username;
        this.firstLogin = firstLogin;
        this.lastLogin = firstLogin;
        this.totalTime = 0.0;
    }

    public void updateTimeStamp() {
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(lastLogin, now);
        if(!(duration.toSeconds() > 300)) {
            this.totalTime += duration.toSeconds();
        }
        this.lastLogin = now;
    }
}
