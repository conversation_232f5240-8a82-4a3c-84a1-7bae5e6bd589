import React, { useState } from "react";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
} from "chart.js";
import { Pie, Bar } from "react-chartjs-2";

// Register Chart.js components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

const MarketTrend = () => {
  // State for date range filters
  const [dateRange, setDateRange] = useState("month");
  const [customDateFrom, setCustomDateFrom] = useState("");
  const [customDateTo, setCustomDateTo] = useState("");

  // State for Drawer
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [drawerContent, setDrawerContent] = useState([]);
  const [drawerTitle, setDrawerTitle] = useState("");
  const [drawerSearch, setDrawerSearch] = useState("");

  // Sample data for KPIs
  const kpis = [
    { label: "Total Leads", value: 500 },
    { label: "Converted Applications", value: 350 },
    { label: "Total Revenue", value: "$75,000" },
    { label: "Active Providers", value: 12 },
    { label: "Outstanding Invoices", value: "$5,000" },
  ];

  // Sample data for Lead Generation Trends
  const leadGenerationData = {
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
    datasets: [
      {
        label: "New Leads",
        data: [50, 75, 150, 100, 200, 175],
        backgroundColor: "#3b82f6",
      },
    ],
  };

  // Sample data for Sales Performance Metrics
  const salesPerformanceData = {
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
    datasets: [
      {
        label: "Calls Made",
        data: [200, 300, 400, 350, 500, 450],
        backgroundColor: "#10b981",
      },
      {
        label: "Successful Calls",
        data: [50, 80, 120, 100, 150, 130],
        backgroundColor: "#f59e0b",
      },
    ],
  };

  // Sample data for Qualification Demand and Revenue
  const qualificationDemandData = {
    labels: [
      "Cert III in Hairdressing",
      "Cert III in Fitness",
      "Cert IV in Fitness",
      "Cert IV in Hairdressing",
      "Diploma of Beauty Therapy",
    ],
    datasets: [
      {
        label: "Demand",
        data: [120, 90, 60, 80, 100],
        backgroundColor: "#ef4444",
      },
      {
        label: "Revenue ($)",
        data: [24000, 18000, 12000, 16000, 20000],
        backgroundColor: "#6366f1",
      },
    ],
  };

  // Sample data for Provider Performance Analysis
  const providerPerformanceData = {
    labels: ["Provider A", "Provider B", "Provider C", "Provider D"],
    datasets: [
      {
        label: "Applications Sent",
        data: [150, 200, 100, 250],
        backgroundColor: "#f472b6",
      },
      {
        label: "Success Rate (%)",
        data: [80, 75, 90, 85],
        backgroundColor: "#34d399",
      },
    ],
  };

  // Sample data for Financial Insights
  const financialInsightsData = {
    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
    datasets: [
      {
        label: "Revenue",
        data: [12000, 15000, 18000, 17000, 22000, 20000],
        borderColor: "#f97316",
        backgroundColor: "#fdba74",
        fill: true,
      },
    ],
  };

  // Sample data for Customer Demographics
  const customerDemographicsData = {
    labels: ["Sydney", "Melbourne", "Brisbane", "Perth", "Adelaide"],
    datasets: [
      {
        label: "Applicants",
        data: [200, 150, 100, 80, 70],
        backgroundColor: "#a78bfa",
      },
    ],
  };

  // Handler for date range change
  const handleDateRangeChange = (e) => {
    setDateRange(e.target.value);
    // Implement data fetching or filtering based on dateRange here
  };

  // Handlers for Drawer
  const openDrawer = (title, content) => {
    setDrawerTitle(title);
    setDrawerContent(content);
    setDrawerSearch("");
    setIsDrawerOpen(true);
  };

  const closeDrawer = () => {
    setIsDrawerOpen(false);
    setDrawerContent([]);
    setDrawerTitle("");
    setDrawerSearch("");
  };

  // Handler for Bar Chart Click
  const handleBarClick = (event, elements, chart) => {
    if (!elements.length) return;

    const element = elements[0];
    const datasetIndex = element.datasetIndex;
    const index = element.index;

    const datasetLabel = chart.data.datasets[datasetIndex].label;
    const label = chart.data.labels[index];

    // Determine what was clicked and fetch relevant data
    if (datasetLabel === "Calls Made") {
      // Example: Fetch leads associated with Calls Made in the selected month
      const month = label;
      // Replace with actual data fetching logic
      const leads = [
        { id: 1, name: "Lead A", contact: "<EMAIL>" },
        { id: 2, name: "Lead B", contact: "<EMAIL>" },
        // ... more leads
      ];
      openDrawer(`Calls Made in ${month}`, leads);
    } else if (datasetLabel === "Applications Sent") {
      // Example: Fetch applications sent to a specific provider
      const provider = label;
      // Replace with actual data fetching logic
      const applications = [
        { id: 101, applicant: "Applicant X", qualification: "Cert III in Fitness" },
        { id: 102, applicant: "Applicant Y", qualification: "Cert IV in Hairdressing" },
        // ... more applications
      ];
      openDrawer(`Applications Sent to ${provider}`, applications);
    }
  };

  // Handler for Pie Chart Click
  const handlePieClick = (event, elements, chart) => {
    if (!elements.length) return;

    const element = elements[0];
    const index = element.index;

    const label = chart.data.labels[index];

    // Example: Fetch applications from the selected city
    const city = label;
    // Replace with actual data fetching logic
    const applications = [
      { id: 201, applicant: "Applicant A", qualification: "Diploma of Beauty Therapy" },
      { id: 202, applicant: "Applicant B", qualification: "Cert IV in Fitness" },
      // ... more applications
    ];
    openDrawer(`Applications in ${city}`, applications);
  };

  // Chart options with onClick handlers and smooth transitions
  const barChartOptions = {
    responsive: true,
    plugins: {
      legend: { position: "top" },
      title: { display: false },
    },
    onClick: (event, elements, chart) => handleBarClick(event, elements, chart),
    scales: {
      y: { beginAtZero: true },
    },
    maintainAspectRatio: false,
  };

  const pieChartOptions = {
    responsive: true,
    plugins: {
      legend: { position: "right" },
      title: { display: false },
    },
    onClick: (event, elements, chart) => handlePieClick(event, elements, chart),
    maintainAspectRatio: false,
  };

  // Filter drawer content based on search
  const filteredDrawerContent = drawerContent.filter((item) =>
    Object.values(item)
      .join(" ")
      .toLowerCase()
      .includes(drawerSearch.toLowerCase())
  );

  return (
    <div className="relative">
      <div className="px-4 py-6 space-y-6">
        {/* KPIs Section */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {kpis.map((kpi, index) => (
            <div
              key={index}
              className="bg-white p-4 rounded-lg shadow-sm flex flex-col items-center"
            >
              <h3 className="text-sm text-gray-600">{kpi.label}</h3>
              <p className="text-2xl font-semibold text-blue-600">{kpi.value}</p>
            </div>
          ))}
        </div>

        {/* Date Range Filter */}
        <div className="flex flex-col md:flex-row items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-800">Market Trends</h2>
          <div className="flex items-center space-x-2 mt-4 md:mt-0">
            <label htmlFor="dateRange" className="text-sm text-gray-700">
              Date Range:
            </label>
            <select
              id="dateRange"
              value={dateRange}
              onChange={handleDateRangeChange}
              className="border border-gray-300 rounded-md p-2 text-sm"
            >
              <option value="week">Past Week</option>
              <option value="month">Past Month</option>
              <option value="quarter">Past Quarter</option>
              <option value="year">Past Year</option>
              <option value="custom">Custom</option>
            </select>
          </div>
        </div>

        {/* Conditional Custom Date Picker */}
        {dateRange === "custom" && (
          <div className="flex flex-col md:flex-row items-center space-x-2">
            <div className="flex flex-col">
              <label htmlFor="customDateFrom" className="text-sm text-gray-700">
                From:
              </label>
              <input
                type="date"
                id="customDateFrom"
                value={customDateFrom}
                onChange={(e) => setCustomDateFrom(e.target.value)}
                className="border border-gray-300 rounded-md p-2 text-sm"
              />
            </div>
            <div className="flex flex-col">
              <label htmlFor="customDateTo" className="text-sm text-gray-700">
                To:
              </label>
              <input
                type="date"
                id="customDateTo"
                value={customDateTo}
                onChange={(e) => setCustomDateTo(e.target.value)}
                className="border border-gray-300 rounded-md p-2 text-sm"
              />
            </div>
          </div>
        )}

        {/* Charts and Data Visualizations */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Lead Generation Trends */}
          <div className="bg-white p-4 rounded-lg shadow-sm h-80">
            <h3 className="text-sm font-semibold mb-2">Lead Generation Trends</h3>
            <Bar data={leadGenerationData} options={barChartOptions} />
          </div>

          {/* Sales Performance Metrics */}
          <div className="bg-white p-4 rounded-lg shadow-sm h-80">
            <h3 className="text-sm font-semibold mb-2">Sales Performance Metrics</h3>
            <Bar data={salesPerformanceData} options={barChartOptions} />
          </div>

          {/* Qualification Demand and Revenue */}
          <div className="bg-white p-4 rounded-lg shadow-sm h-80">
            <h3 className="text-sm font-semibold mb-2">Qualification Demand & Revenue</h3>
            <Bar
              data={qualificationDemandData}
              options={{
                responsive: true,
                plugins: {
                  legend: { position: "top" },
                  title: { display: false },
                },
                scales: { y: { beginAtZero: true } },
              }}
            />
          </div>

          {/* Provider Performance Analysis */}
          <div className="bg-white p-4 rounded-lg shadow-sm h-80">
            <h3 className="text-sm font-semibold mb-2">Provider Performance Analysis</h3>
            <Bar data={providerPerformanceData} options={barChartOptions} />
          </div>

          {/* Financial Insights */}
          <div className="bg-white p-4 rounded-lg shadow-sm h-80">
            <h3 className="text-sm font-semibold mb-2">Financial Insights</h3>
            <Bar
              data={financialInsightsData}
              options={{
                responsive: true,
                plugins: {
                  legend: { position: "top" },
                  title: { display: false },
                },
                scales: { y: { beginAtZero: true } },
              }}
            />
          </div>

          {/* Customer Demographics */}
          <div className="bg-white p-4 rounded-lg shadow-sm h-80">
            <h3 className="text-sm font-semibold mb-2">Customer Demographics</h3>
            <Pie data={customerDemographicsData} options={pieChartOptions} />
          </div>
        </div>
      </div>

      {/* Dark Overlay and Drawer */}
      {isDrawerOpen && (
        <>
          {/* Dark Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-30 z-40 transition-opacity duration-300"
            onClick={closeDrawer}
          ></div>

          {/* Drawer Panel */}
          <div
            className={`fixed top-0 right-0 h-full w-full sm:w-4/5 md:w-1/2 lg:w-1/3 bg-white shadow-lg z-50 transform transition-transform duration-300 ${
              isDrawerOpen ? "translate-x-0" : "translate-x-full"
            }`}
          >
            {/* Drawer Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold">{drawerTitle}</h3>
              <button
                onClick={closeDrawer}
                className="text-gray-600 hover:text-gray-800 text-xl"
              >
                &times;
              </button>
            </div>

            {/* Drawer Body */}
            <div className="p-4 h-full flex flex-col">
              {/* Search Input */}
              <div className="mb-4">
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-full p-2 border border-gray-300 rounded-md"
                  value={drawerSearch}
                  onChange={(e) => setDrawerSearch(e.target.value)}
                />
              </div>

              {/* Content List */}
              <div className="flex-1 overflow-y-auto">
                {filteredDrawerContent.length > 0 ? (
                  <table className="min-w-full table-auto">
                    <thead>
                      <tr className="bg-gray-100">
                        {Object.keys(filteredDrawerContent[0]).map((key, idx) => (
                          <th
                            key={idx}
                            className="px-4 py-2 text-left text-sm font-medium text-gray-700 capitalize"
                          >
                            {key.replace(/([A-Z])/g, " $1").trim()}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {filteredDrawerContent.map((item, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          {Object.values(item).map((value, idx) => (
                            <td key={idx} className="px-4 py-2 text-sm text-gray-700">
                              {value}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <p className="text-gray-500">No data available.</p>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default MarketTrend;
