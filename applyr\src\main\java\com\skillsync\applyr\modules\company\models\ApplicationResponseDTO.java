package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.core.models.enums.PaidStatus;
import com.skillsync.applyr.core.models.enums.Status;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ApplicationResponseDTO {
    private String applicationId;
    private String applicantName;
    private String applicantEmail;
    private String applicantPhone;
    private String applicantAddress;
    private double totalPrice;
    private Status status;
    private PaidStatus paidStatus;
    private double paidAmount;

    private List<SoldQualificationDTO> soldQualifications;
    private LeadResponseDTO lead;
    private List<ApplicationCommentDTO> comments;

    private String createdAt;
    private String quoteRefNumber;
    private String invoiceRefNumber;

    private ProfileDTO createdBy;

}
