import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faFilter, faSearch, faTable, faThLarge } from "@fortawesome/free-solid-svg-icons";
import { useGetAllApplicationFilesQuery } from "../../../services/CompanyAPIService";
import FileStatusCard from "../../../components/card/FileStatusCard";
import routes from "../../../routes";

const FileStatus = () => {
  const navigate = useNavigate();
  const { data: fileStatusData = [], isLoading, error, refetch } = useGetAllApplicationFilesQuery();

  // View mode state
  const [viewMode, setViewMode] = useState("table");

  // Filter states
  const [search, setSearch] = useState("");
  const [selectedAgentFilter, setSelectedAgentFilter] = useState("");
  const [selectedStatusFilter, setSelectedStatusFilter] = useState("");
  const [dateFilterType, setDateFilterType] = useState("thisWeek");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [filteredData, setFilteredData] = useState([]);

  // Status filter options based on FileStatus enum
  const statusOptions = [
    "DOCUMENTS_PENDING",
    "DOCUMENTS_RECEIVED",
    "LODGED_AND_PROCESSING",
    "SOFT_COPY_RECEIVED",
    "SOFT_COPY_RELEASED",
    "HARD_COPY_RECEIVED",
    "HARD_COPY_MAILED_AND_CLOSED",
    "PENDING_EVIDENCE",
    "CANCELLED",
    "STATUS_UNAVAILABLE",
    "HOLD",
  ];

  // Format status for display
  const formatStatus = (status) => {
    if (!status) return "";
    return status.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, (c) => c.toUpperCase());
  };

  // Date filtering logic
  const applyDateFilter = (dateString) => {
    if (!dateString) return true; // If no date, do not filter

    const dateToCompare = new Date(dateString);
    let filterStartDate, filterEndDate;

    if (dateFilterType === "thisWeek") {
      const now = new Date();
      const dayOfWeek = now.getDay();
      const diffToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      filterStartDate = new Date(now);
      filterStartDate.setDate(now.getDate() - diffToMonday);
      filterStartDate.setHours(0, 0, 0, 0);
      filterEndDate = new Date(filterStartDate);
      filterEndDate.setDate(filterStartDate.getDate() + 6);
      filterEndDate.setHours(23, 59, 59, 999);
    } else if (dateFilterType === "lastWeek") {
      const now = new Date();
      const dayOfWeek = now.getDay();
      const diffToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      const currentMonday = new Date(now);
      currentMonday.setDate(now.getDate() - diffToMonday);
      currentMonday.setHours(0, 0, 0, 0);
      filterStartDate = new Date(currentMonday);
      filterStartDate.setDate(currentMonday.getDate() - 7);
      filterEndDate = new Date(currentMonday);
      filterEndDate.setDate(currentMonday.getDate() - 1);
      filterEndDate.setHours(23, 59, 59, 999);
    } else if (dateFilterType === "thisMonth") {
      const now = new Date();
      filterStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
      filterEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      filterEndDate.setHours(23, 59, 59, 999);
    } else if (dateFilterType === "lastMonth") {
      const now = new Date();
      filterStartDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      filterEndDate = new Date(now.getFullYear(), now.getMonth(), 0);
      filterEndDate.setHours(23, 59, 59, 999);
    } else if (dateFilterType === "custom") {
      if (!startDate || !endDate) return true; // If no date range, do not filter
      filterStartDate = new Date(startDate);
      filterEndDate = new Date(endDate);
      filterEndDate.setHours(23, 59, 59, 999);
    }

    return dateToCompare >= filterStartDate && dateToCompare <= filterEndDate;
  };

  // Apply filters to data
  useEffect(() => {
    if (!fileStatusData) return;

    const filtered = fileStatusData.filter((file) => {
      const clientName = file.application?.lead?.companyName || "";
      const candidateName = file.application?.applicantName || "";
      const agentName = file.application?.createdBy?.fullName || "";
      const fileStatus = file.fileStatus || "";
      const lodgedDate = file.lodgedDate;
      const softCopyReleasedDate = file.softCopyReleasedDate;

      const searchMatch =
        clientName.toLowerCase().includes(search.toLowerCase()) ||
        candidateName.toLowerCase().includes(search.toLowerCase());

      const agentMatch = selectedAgentFilter ? agentName === selectedAgentFilter : true;
      const statusMatch = selectedStatusFilter ? fileStatus === selectedStatusFilter : true;
      const dateMatch =
        applyDateFilter(lodgedDate) ||
        applyDateFilter(softCopyReleasedDate);

      return searchMatch && agentMatch && statusMatch && dateMatch;
    });

    setFilteredData(filtered);
  }, [fileStatusData, search, selectedAgentFilter, selectedStatusFilter, dateFilterType, startDate, endDate]);

  // Get unique agent names for filter
  const agentOptions = fileStatusData
    ? [...new Set(fileStatusData.map(file => file.application?.createdBy?.fullName).filter(Boolean))]
    : [];

  // Handle view details click
  const handleViewDetails = (applicationId, qualificationId) => {
    navigate(`/operations/filestatus/${applicationId}/${qualificationId}`);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">File Status</h1>
          <p className="text-sm text-gray-500">View and manage file status information</p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode("card")}
            className={`p-2 rounded ${viewMode === "card" ? "bg-[#6E39CB] text-white" : "bg-gray-200 text-gray-600"}`}
          >
            <FontAwesomeIcon icon={faThLarge} />
          </button>
          <button
            onClick={() => setViewMode("table")}
            className={`p-2 rounded ${viewMode === "table" ? "bg-[#6E39CB] text-white" : "bg-gray-200 text-gray-600"}`}
          >
            <FontAwesomeIcon icon={faTable} />
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6 border border-gray-100">
        <div className="flex flex-wrap items-center gap-4">
          {/* Search Filter */}
          <div className="relative flex-grow max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search by client or candidate name..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
            />
          </div>

          {/* Agent Filter */}
          <select
            value={selectedAgentFilter}
            onChange={(e) => setSelectedAgentFilter(e.target.value)}
            className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
          >
            <option value="">All Agents</option>
            {agentOptions.map((agent) => (
              <option key={agent} value={agent}>
                {agent}
              </option>
            ))}
          </select>

          {/* Status Filter */}
          <select
            value={selectedStatusFilter}
            onChange={(e) => setSelectedStatusFilter(e.target.value)}
            className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
          >
            <option value="">All Statuses</option>
            {statusOptions.map((status) => (
              <option key={status} value={status}>
                {formatStatus(status)}
              </option>
            ))}
          </select>

          {/* Date Filter */}
          <select
            value={dateFilterType}
            onChange={(e) => setDateFilterType(e.target.value)}
            className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
          >
            <option value="thisWeek">This Week</option>
            <option value="lastWeek">Last Week</option>
            <option value="thisMonth">This Month</option>
            <option value="lastMonth">Last Month</option>
            <option value="custom">Custom Date</option>
          </select>

          {/* Custom Date Range */}
          {dateFilterType === "custom" && (
            <div className="flex items-center space-x-2">
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
              />
              <span>to</span>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
              />
            </div>
          )}
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-10">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#6E39CB]"></div>
          <p className="mt-2 text-gray-500">Loading file status data...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          <p>Error loading file status data. Please try again later.</p>
        </div>
      )}

      {/* No Results */}
      {!isLoading && !error && filteredData.length === 0 && (
        <div className="bg-white border border-gray-100 rounded-lg shadow-sm p-10 text-center">
          <p className="text-gray-500">No file status records found matching your filters.</p>
        </div>
      )}

      {/* Card View */}
      {!isLoading && !error && viewMode === "card" && filteredData.length > 0 && (
        <div className="grid grid-cols-1 gap-4">
          {filteredData.map((file) => (
            <div key={`${file.application?.applicationId}-${file.qualificationId}`} className="relative">
              <FileStatusCard
                clientName={file.application?.lead?.companyName || "N/A"}
                agentName={file.application?.createdBy?.fullName || "N/A"}
                invoiceNumber={file.application?.invoiceRefNumber || "N/A"}
                certificationName={file.qualificationName || "N/A"}
                status={file.fileStatus}
                lodgementDate={file.lodgedDate ? new Date(file.lodgedDate).toISOString().split('T')[0] : "N/A"}
                softCopyReleaseDate={file.softCopyReleasedDate ? new Date(file.softCopyReleasedDate).toISOString().split('T')[0] : "N/A"}
                hardCopyReleaseDate={file.hardCopyMailedDate ? new Date(file.hardCopyMailedDate).toISOString().split('T')[0] : "N/A"}
              />
              <button
                onClick={() => handleViewDetails(file.application?.applicationId, file.qualificationCode)}
                className="absolute top-4 right-4 bg-blue-500 text-white p-2 rounded-full hover:bg-blue-600 transition-colors"
                title="View Details"
              >
                <FontAwesomeIcon icon={faEye} />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Table View */}
      {!isLoading && !error && viewMode === "table" && filteredData.length > 0 && (
        <div className="overflow-x-auto bg-white rounded-lg border border-gray-100 shadow-sm">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-[#F4F5F9]">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client/Candidate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Agent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Qualification
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Lodged Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredData.map((file) => (
                <tr key={`${file.application?.applicationId}-${file.qualificationCode}`} className="hover:bg-[#F4F5F9]">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col">
                      <div className="text-sm font-medium text-gray-900">{file.application?.lead?.companyName || "N/A"}</div>
                      <div className="text-sm text-gray-500">{file.application?.applicantName || "N/A"}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{file.application?.createdBy?.fullName || "N/A"}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-normal">
                    <div className="text-sm text-gray-900">{file.qualificationName || "N/A"}</div>
                    <div className="text-xs text-gray-500">{file.qualificationCode || "N/A"}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      {formatStatus(file.fileStatus)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {file.lodgedDate ? new Date(file.lodgedDate).toLocaleDateString() : "N/A"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleViewDetails(file.application?.applicationId, file.qualificationCode)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default FileStatus;
