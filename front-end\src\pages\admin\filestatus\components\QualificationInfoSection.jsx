import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheck, faSpinner } from "@fortawesome/free-solid-svg-icons";
import { useUpdateFileStatusMutation } from "../../../../services/CompanyAPIService";

const QualificationInfoSection = ({
  qualificationCode,
  qualificationName,
  fileStatus,
  onStatusUpdate,
  applicationId,
  showToast
}) => {
  const [selectedStatus, setSelectedStatus] = useState(fileStatus);
  const [updateFileStatus, { isLoading: isUpdatingStatus }] = useUpdateFileStatusMutation();

  // Handle status change
  const handleStatusChange = (e) => {
    setSelectedStatus(e.target.value);
  };

  // Handle status update
  const handleStatusUpdate = async () => {
    try {
      await updateFileStatus({
        applicationId,
        qualificationId: qualificationCode,
        status: selectedStatus
      }).unwrap();

      showToast("File status updated successfully");
    } catch (error) {
      showToast("Failed to update file status", "error");
      console.error("Error updating file status:", error);
    }
  };
  // Status options based on FileStatus enum
  const statusOptions = [
    "DOCUMENTS_PENDING",
    "DOCUMENTS_RECEIVED",
    "LODGED_AND_PROCESSING",
    "SOFT_COPY_RECEIVED",
    "SOFT_COPY_RELEASED",
    "HARD_COPY_RECEIVED",
    "HARD_COPY_MAILED_AND_CLOSED",
    "PENDING_EVIDENCE",
    "CANCELLED",
    "STATUS_UNAVAILABLE",
    "HOLD",
  ];

  // Format status for display
  const formatStatus = (status) => {
    if (!status) return "";
    return status.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, (c) => c.toUpperCase());
  };

  return (
    <div className="lg:col-span-1 bg-white rounded-lg border border-gray-100 shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Qualification Information</h3>

      <div className="space-y-4">
        {/* Qualification Name */}
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-500 mb-1">Qualification Name:</span>
          <span className="text-sm text-gray-900">{qualificationName || "N/A"}</span>
        </div>

        {/* Qualification Code */}
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-500 mb-1">Qualification Code:</span>
          <span className="text-sm text-gray-900">{qualificationCode || "N/A"}</span>
        </div>

        {/* Current Status */}
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-500 mb-1">Current Status:</span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {formatStatus(fileStatus)}
          </span>
        </div>

        {/* Status Update */}
        <div className="flex flex-col mt-6">
          <label htmlFor="statusUpdate" className="text-sm font-medium text-gray-500 mb-1">
            Update Status:
          </label>
          <div className="flex space-x-2">
            <select
              id="statusUpdate"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm rounded-md"
              value={selectedStatus}
              onChange={handleStatusChange}
            >
              {statusOptions.map((status) => (
                <option key={status} value={status}>
                  {formatStatus(status)}
                </option>
              ))}
            </select>
            <button
              type="button"
              onClick={handleStatusUpdate}
              disabled={isUpdatingStatus || selectedStatus === fileStatus}
              className="mt-1 inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#6E39CB] hover:bg-[#5E2CB8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUpdatingStatus ? (
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
              ) : (
                <FontAwesomeIcon icon={faCheck} />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QualificationInfoSection;
