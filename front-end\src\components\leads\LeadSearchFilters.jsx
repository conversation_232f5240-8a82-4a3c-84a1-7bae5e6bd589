import React from "react";
import { FaExpand, FaCompress } from "react-icons/fa";

const LeadSearchFilters = ({
  leadSearch,
  setLeadSearch,
  selectedAgentFilter,
  setSelectedAgentFilter,
  leadTypeFilter,
  setLeadTypeFilter,
  agent<PERSON><PERSON>s,
  isFullScreen,
  setIsFullScreen
}) => {
  return (
    <div className="px-6 py-4">
      <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
        <div className="w-full md:w-1/2 relative">
          <input
            type="text"
            placeholder="Search leads by name, company, email..."
            className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            value={leadSearch}
            onChange={(e) => setLeadSearch(e.target.value)}
          />
          <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <div className="flex items-center space-x-3 w-full md:w-auto">
          {!isFullScreen && (
            <button
              onClick={() => setIsFullScreen(!isFullScreen)}
              className="flex items-center text-gray-700 bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              title="Toggle Full Screen"
            >
              <FaExpand className="mr-2" /> Fullscreen
            </button>
          )}
          <select
            className="border border-gray-200 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            value={selectedAgentFilter}
            onChange={(e) => setSelectedAgentFilter(e.target.value)}
          >
            <option value="">All Agents</option>
            {agentNames.map((name) => (
              <option key={name} value={name}>{name}</option>
            ))}
          </select>
          <select
            className="border border-gray-200 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            value={leadTypeFilter}
            onChange={(e) => setLeadTypeFilter(e.target.value)}
          >
            <option value="all">All Types</option>
            <option value="B2B">Company</option>
            <option value="Direct">Individual</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default LeadSearchFilters;
