import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes, faSpinner } from "@fortawesome/free-solid-svg-icons";
import { useUpdateRTOInfoMutation } from "../../../../../services/CompanyAPIService";

/**
 * RTOAssignModal component for assigning a file to an RTO
 * @param {boolean} isOpen - Whether the modal is open
 * @param {function} onClose - Function to close the modal
 * @param {object} fileStatus - File status data
 * @param {function} showToast - Function to show toast notifications
 * @param {function} refetch - Function to refetch data
 */
const RTOAssignModal = ({ isOpen, onClose, fileStatus, showToast, refetch }) => {
  const [formData, setFormData] = useState({
    rtoCode: fileStatus.rtoData?.code || "",
    rtoCharge: fileStatus.rtoCharge?.toString() || "0",
    paymentStatus: fileStatus.rtoPaymentStatus || "UNPAID"
  });

  // API mutation
  const [updateRTOInfo, { isLoading }] = useUpdateRTOInfoMutation();

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.rtoCode.trim()) {
      showToast("RTO Code is required", "error");
      return;
    }

    try {
      await updateRTOInfo({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        rtoCode: formData.rtoCode,
        rtoCharge: parseFloat(formData.rtoCharge) || 0,
        paymentStatus: formData.paymentStatus
      }).unwrap();

      showToast(fileStatus.rtoData ? "RTO information updated successfully" : "File assigned to RTO successfully");
      refetch();
      onClose();
    } catch (error) {
      showToast(fileStatus.rtoData ? "Failed to update RTO information" : "Failed to assign file to RTO", "error");
      console.error("Error assigning file to RTO:", error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Modal Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            {fileStatus.rtoData ? "Update RTO Information" : "Assign File to RTO"}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        {/* Modal Body */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* RTO Code */}
            <div>
              <label htmlFor="rtoCode" className="block text-sm font-medium text-gray-700 mb-1">
                RTO Code <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="rtoCode"
                name="rtoCode"
                value={formData.rtoCode}
                onChange={handleChange}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
                required
              />
            </div>

            {/* RTO Charge */}
            <div>
              <label htmlFor="rtoCharge" className="block text-sm font-medium text-gray-700 mb-1">
                RTO Charge ($)
              </label>
              <input
                type="number"
                id="rtoCharge"
                name="rtoCharge"
                value={formData.rtoCharge}
                onChange={handleChange}
                min="0"
                step="0.01"
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              />
            </div>

            {/* Payment Status */}
            <div>
              <label htmlFor="paymentStatus" className="block text-sm font-medium text-gray-700 mb-1">
                Payment Status
              </label>
              <select
                id="paymentStatus"
                name="paymentStatus"
                value={formData.paymentStatus}
                onChange={handleChange}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              >
                <option value="UNPAID">Unpaid</option>
                <option value="PAID">Paid</option>
              </select>
            </div>
          </div>

          {/* Modal Footer */}
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-[#5E2CB8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
            >
              {isLoading ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
                  Assigning...
                </>
              ) : (
                fileStatus.rtoData ? "Update RTO" : "Assign to RTO"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RTOAssignModal;
