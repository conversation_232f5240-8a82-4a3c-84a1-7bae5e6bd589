package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.core.utills.UserUtils;
import com.skillsync.applyr.modules.company.models.DetailedProfileDTO;
import com.skillsync.applyr.modules.company.models.ProfileDTO;
import com.skillsync.applyr.modules.company.repositories.ApplicationRepository;
import com.skillsync.applyr.modules.company.repositories.CompanyRepository;
import com.skillsync.applyr.modules.company.repositories.SalesAgentRepository;
import com.skillsync.applyr.modules.sales.models.ApplicationDTO;
import com.skillsync.applyr.core.models.entities.Company;
import com.skillsync.applyr.core.models.entities.OperationsAgent;
import com.skillsync.applyr.core.models.entities.SalesAgent;
import com.skillsync.applyr.modules.sales.repositories.OperationsAgentRepository;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class EmployeeProfileService {

    private final CompanyRepository companyRepository;
    private final SalesAgentRepository salesAgentRepository;
    private final OperationsAgentRepository operationsAgentRepository;
    private final ApplicationRepository applicationRepository;

    public EmployeeProfileService(CompanyRepository companyRepository,
                                  SalesAgentRepository salesAgentRepository,
                                  OperationsAgentRepository operationsAgentRepository,
                                  ApplicationRepository applicationRepository) {
        this.companyRepository = companyRepository;
        this.salesAgentRepository = salesAgentRepository;
        this.operationsAgentRepository = operationsAgentRepository;
        this.applicationRepository = applicationRepository;
    }

    public List<ProfileDTO> getAllEmployees() {
        List<ProfileDTO> employees = new ArrayList<>();

        List<Company> companies = companyRepository.findAll();
        for (Company company : companies) {
            employees.add(fromCompanyToProfileDTO(company));
        }

        List<SalesAgent> agents = salesAgentRepository.findAll();
        for (SalesAgent salesAgent : agents) {
            employees.add(fromAgentToProfileDTO(salesAgent));
        }

        List<OperationsAgent> operationsAgents = operationsAgentRepository.findAll();
        for (OperationsAgent operationsAgent : operationsAgents) {
            employees.add(fromOperationsAgentToProfileDTO(operationsAgent));
        }

        return employees;
    }

    public ProfileDTO findProfileFromUsername(String username) {
        Optional<SalesAgent> salesAgent = salesAgentRepository.getSalesAgentByUserUsername(username);
        if (salesAgent.isPresent()) {
            return fromAgentToProfileDTO(salesAgent.get());
        } else {
            Optional<Company> company = companyRepository.getCompanyByUserUsername(username);
            if (company.isPresent()) {
                return fromCompanyToProfileDTO(company.get());
            } else {
                return new ProfileDTO("N/A", username, "N/A", "N/A", "N/A", "N/A", 0, "N/A");
            }
        }
    }

    public ProfileDTO getProfile() {
        String username = UserUtils.getUsernameFromToken();
        Optional<Company> company = companyRepository.getCompanyByUserUsername(username);
        if (company.isPresent()) {
            return fromCompanyToProfileDTO(company.get());
        }
        throw new AppRTException("Unable to fetch Admin Profile", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ProfileDTO getAgentProfile() {
        String username = UserUtils.getUsernameFromToken();

        Optional<SalesAgent> agent = salesAgentRepository.getSalesAgentByUserUsername(username);
        if (agent.isPresent()) {
            return fromAgentToProfileDTO(agent.get());
        }
        throw new AppRTException("Unable to fetch Sales Profile", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ProfileDTO getAgentProfileByUsername(String username) {
        Optional<SalesAgent> agent = salesAgentRepository.getSalesAgentByUserUsername(username);
        if (agent.isPresent()) {
            return fromAgentToProfileDTO(agent.get());
        } else {
            Optional<Company> company = companyRepository.getCompanyByUserUsername(username);
            if (company.isPresent()) {
                return fromCompanyToProfileDTO(company.get());
            }
        }
        throw new AppRTException(username + " - Unable to fetch Sales Profile", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public DetailedProfileDTO getDetailedProfile(String username) {
        Optional<SalesAgent> agent = salesAgentRepository.getSalesAgentByUserUsername(username);
        DetailedProfileDTO detailedProfileDTO = new DetailedProfileDTO();
        ProfileDTO profile = new ProfileDTO();
        if (agent.isPresent()) {
            // Here you might also add leads details if needed.
            profile = fromAgentToProfileDTO(agent.get());
        } else {
            Optional<Company> company = companyRepository.getCompanyByUserUsername(username);
            if (company.isPresent()) {
                profile = fromCompanyToProfileDTO(company.get());
            }
        }
        detailedProfileDTO.setProfile(profile);
        List<Application> applications = applicationRepository.findAllByAgentUsername(username);
        List<ApplicationDTO> applicationDTOs = new ArrayList<>();
        double lifetimeEarning = 0;
        for (Application application : applications) {
            ApplicationDTO applicationDTO = new ApplicationDTO(application);
            applicationDTOs.add(applicationDTO);
            lifetimeEarning += application.getPrice();
        }
        detailedProfileDTO.setLifetimeRevenue(lifetimeEarning);
        detailedProfileDTO.setApplications(applicationDTOs);
        return detailedProfileDTO;
    }

    public SuccessResponse deleteEmployee(String username) {
        String currentUser = UserUtils.getUsernameFromToken();
        if (currentUser.equals(username)) {
            throw new AppRTException("You cannot delete yourself from the company", HttpStatus.BAD_REQUEST);
        }
        Optional<SalesAgent> salesAgent = salesAgentRepository.getSalesAgentByUserUsername(username);
        if (salesAgent.isPresent()) {
            salesAgentRepository.delete(salesAgent.get());
            return new SuccessResponse("Successfully deleted Employee from the company");
        } else {
            Optional<Company> company = companyRepository.getCompanyByUserUsername(username);
            if (company.isPresent()) {
                companyRepository.delete(company.get());
                return new SuccessResponse("Successfully deleted Employee from the company");
            } else {
                throw new AppRTException("The user you are trying to delete does not exist", HttpStatus.NOT_FOUND);
            }
        }
    }

    private ProfileDTO fromCompanyToProfileDTO(Company company) {
        return new ProfileDTO(
                company.getUser().getUsername(),
                company.getFullName(),
                company.getEmailAddress(),
                company.getPhoneNumber(),
                company.getAddress(),
                company.getGender(),
                0,
                "ADMIN"
        );
    }

    private ProfileDTO fromAgentToProfileDTO(SalesAgent agent) {
        return new ProfileDTO(
                agent.getUser().getUsername(),
                agent.getFullName(),
                agent.getEmailAddress(),
                agent.getPhoneNumber(),
                agent.getAddress(),
                agent.getGender(),
                agent.getWeeklyTarget(),
                "SALES AGENT"
        );
    }

    private ProfileDTO fromOperationsAgentToProfileDTO(OperationsAgent operationsAgent) {
        return new ProfileDTO(
                operationsAgent.getUser().getUsername(),
                operationsAgent.getFullName(),
                operationsAgent.getEmailAddress(),
                operationsAgent.getPhoneNumber(),
                operationsAgent.getAddress(),
                operationsAgent.getGender(),
                0,
                "OPERATIONS"
        );
    }
}
