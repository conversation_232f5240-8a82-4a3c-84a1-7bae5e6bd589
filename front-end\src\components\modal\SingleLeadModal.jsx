import React, { useState, useRef, useEffect } from "react";
import { FaTimes, FaChevronDown } from "react-icons/fa";

const SingleLeadModal = ({
  isOpen,
  onClose,
  onSubmit,
  salesReps = [],
  currentUser = null,
  isAgent = false,
}) => {
  const [leadType, setLeadType] = useState("Company");
  const [companyName, setCompanyName] = useState("");
  const [leadName, setLeadName] = useState("");
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [address, setAddress] = useState("");
  const [status, setStatus] = useState("FRESH");
  const [countryCode, setCountryCode] = useState("+61");
  const [selectedRep, setSelectedRep] = useState(null);
  const [salesRepSearch, setSalesRepSearch] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const dropdownRef = useRef(null);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setLeadType("Company");
      setCompanyName("");
      setLeadName("");
      setPhone("");
      setEmail("");
      setAddress("");
      setStatus("FRESH");
      setCountryCode("+61");

      // For agents, preselect current user
      if (isAgent && currentUser) {
        const currentUserRep = salesReps.find(rep => rep.username === currentUser.username);
        if (currentUserRep) {
          setSelectedRep(currentUserRep);
          setSalesRepSearch(currentUserRep.name);
        }
      } else {
        setSelectedRep(null);
        setSalesRepSearch("");
      }

      setIsDropdownOpen(false);
    }
  }, [isOpen, isAgent, currentUser, salesReps]);

  // Handle type change
  const handleTypeChange = (value) => {
    setLeadType(value);
    setCompanyName(value === "Individual" ? "N/A" : "");
  };

  // Handle sales rep selection
  const handleSalesRepSelect = (rep) => {
    setSelectedRep(rep);
    setSalesRepSearch(rep.name);
    setIsDropdownOpen(false);
  };

  // Filter sales reps based on search
  const filteredSalesReps = salesReps.filter(rep =>
    rep.name.toLowerCase().includes(salesRepSearch.toLowerCase())
  );

  // Validation functions
  const validatePhone = (phoneNumber) => {
    // Remove any non-digit characters for validation
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    return cleanPhone.length >= 8 && cleanPhone.length <= 15; // Reasonable phone number length
  };

  const validateEmail = (emailAddress) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(emailAddress);
  };

  // Handle phone input - only allow numbers
  const handlePhoneChange = (e) => {
    const value = e.target.value;
    // Only allow digits - remove all non-numeric characters
    let cleanValue = value.replace(/[^\d]/g, '');

    // Remove leading 61 if present to prevent duplication with country code
    if (cleanValue.startsWith('61') && cleanValue.length > 2) {
      cleanValue = cleanValue.substring(2);
    }

    setPhone(cleanValue);
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!leadName.trim() || !phone.trim() || !email.trim()) {
      return;
    }

    // Validate phone number
    if (!validatePhone(phone)) {
      alert("Please enter a valid phone number (8-15 digits)");
      return;
    }

    // Validate email
    if (!validateEmail(email)) {
      alert("Please enter a valid email address");
      return;
    }

    const finalCompanyName = leadType === "Individual" ? "N/A" : companyName;
    const formattedPhone = phone.startsWith('+') ? phone : `${countryCode}${phone}`;

    const payload = {
      companyName: finalCompanyName,
      leadName: leadName,
      phone: formattedPhone,
      email: email,
      address: address,
      status: status,
      assignedAgents: selectedRep ? [selectedRep] : [],
    };

    onSubmit(payload);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={onClose} />
      <div className="fixed inset-0 flex items-center justify-center z-50 p-4" onClick={(e) => e.stopPropagation()}>
        <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl mx-auto transform transition-all">
          <div className="flex items-center justify-between p-6 border-b border-gray-100">
            <div>
              <h3 className="text-xl font-semibold text-gray-900">Add Single Lead</h3>
              <p className="text-sm text-gray-500 mt-1">Create a new lead in your system</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 p-2 rounded-lg transition-colors"
            >
              <FaTimes className="w-5 h-5" />
            </button>
          </div>
          <div className="p-6 space-y-6">
            {/* Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Lead Type</label>
              <select
                className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] transition-all duration-200 bg-white"
                value={leadType}
                onChange={(e) => handleTypeChange(e.target.value)}
              >
                <option value="Company">Company</option>
                <option value="Individual">Individual</option>
              </select>
            </div>

            {/* Company Name and Lead Name Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Company Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                <input
                  type="text"
                  className={`w-full border border-gray-300 rounded-lg px-4 py-3 transition-all duration-200 ${
                    leadType === "Individual"
                      ? 'bg-gray-50 text-gray-500 cursor-not-allowed'
                      : 'focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]'
                  }`}
                  value={leadType === "Individual" ? "N/A" : companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  disabled={leadType === "Individual"}
                  placeholder={leadType === "Individual" ? "Not applicable for individual leads" : "Enter company name"}
                />
              </div>

              {/* Lead Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lead Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] transition-all duration-200"
                  value={leadName}
                  onChange={(e) => setLeadName(e.target.value)}
                  placeholder="Enter lead name"
                />
              </div>
            </div>

            {/* Phone and Email Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone <span className="text-red-500">*</span>
                </label>
                <div className="flex">
                  <select
                    className="border border-gray-300 rounded-l-lg px-3 py-3 bg-gray-50 focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] transition-all duration-200"
                    value={countryCode}
                    onChange={(e) => setCountryCode(e.target.value)}
                  >
                    <option value="+61">+61</option>
                    <option value="+1">+1</option>
                    <option value="+44">+44</option>
                  </select>
                  <input
                    type="text"
                    className="border border-gray-300 rounded-r-lg px-4 py-3 flex-1 focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] transition-all duration-200"
                    value={phone}
                    onChange={handlePhoneChange}
                    placeholder="Enter phone number (digits only)"
                  />
                </div>
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] transition-all duration-200"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter email address"
                />
              </div>
            </div>

            {/* Address and Status Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Address */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                <textarea
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] transition-all duration-200 resize-none"
                  rows={3}
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  placeholder="Enter address (optional)"
                />
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Lead Status</label>
                <select
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] transition-all duration-200 bg-white"
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                >
                  <option value="HOT">🔥 HOT</option>
                  <option value="WARM">🌡️ WARM</option>
                  <option value="COLD">❄️ COLD</option>
                  <option value="FRESH">✨ FRESH</option>
                  <option value="CLOSED">🔒 CLOSED</option>
                </select>
              </div>
            </div>

            {/* Assign Consultants */}
            <div className="relative" ref={dropdownRef}>
              <label className="block text-sm font-medium text-gray-700 mb-2">Assign Consultant</label>
              <div
                className={`border border-gray-300 rounded-lg px-4 py-3 flex items-center justify-between transition-all duration-200 ${
                  isAgent
                    ? 'bg-gray-50 cursor-not-allowed border-gray-200'
                    : 'cursor-pointer hover:border-[#6E39CB] focus-within:ring-2 focus-within:ring-[#6E39CB] focus-within:border-[#6E39CB]'
                }`}
                onClick={() => !isAgent && setIsDropdownOpen(!isDropdownOpen)}
              >
                <span className={`${isAgent ? 'text-gray-500' : 'text-gray-700'}`}>
                  {selectedRep ? selectedRep.name : "Select consultant"}
                </span>
                {!isAgent && (
                  <FaChevronDown className={`transition-transform text-gray-400 ${isDropdownOpen ? "rotate-180" : ""}`} />
                )}
              </div>
              {isDropdownOpen && !isAgent && (
                <div className="absolute z-10 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 overflow-y-auto">
                  <input
                    type="text"
                    className="w-full p-3 border-b border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                    placeholder="Search consultants..."
                    value={salesRepSearch}
                    onChange={(e) => setSalesRepSearch(e.target.value)}
                  />
                  {filteredSalesReps.map((rep) => (
                    <div
                      key={rep.username}
                      className="p-3 hover:bg-gray-50 cursor-pointer transition-colors border-b border-gray-100 last:border-b-0"
                      onClick={() => handleSalesRepSelect(rep)}
                    >
                      {rep.name}
                    </div>
                  ))}
                </div>
              )}
              {isAgent && (
                <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-700 flex items-center">
                    <span className="mr-2">ℹ️</span>
                    As an agent, you are automatically assigned to leads you create.
                  </p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-100">
              <button
                onClick={onClose}
                className="px-6 py-3 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                className="px-6 py-3 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors font-medium shadow-sm"
              >
                Add Lead
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SingleLeadModal;
