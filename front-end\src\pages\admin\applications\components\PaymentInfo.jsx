import React from "react";

const PaymentInfo = ({ 
  application, 
  selectedPaymentStatus, 
  duePayment, 
  canEditStatus, 
  setModalType, 
  setShowRefModal 
}) => {
  return (
    <div
      className="p-6"
      role="tabpanel"
      id="paymentInfo-panel"
      aria-labelledby="paymentInfo-tab"
    >
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-1">Payment Information</h2>
        <p className="text-sm text-gray-500">
          View and manage payment details for this application
        </p>
      </div>

      {/* Payment Progress Bar */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-2">
          <h5 className="text-md font-medium text-gray-900">Payment Progress</h5>
          <div className="flex items-center">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                selectedPaymentStatus === "FULLY_PAID"
                  ? "bg-green-100 text-green-800"
                  : selectedPaymentStatus === "PARTIALLY_PAID"
                  ? "bg-yellow-100 text-yellow-800"
                  : selectedPaymentStatus === "INVOICE_EXPIRED"
                  ? "bg-red-100 text-red-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {selectedPaymentStatus.replace("_", " ")}
            </span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
          <div
            className={`h-2.5 rounded-full ${
              selectedPaymentStatus === "FULLY_PAID" ? "bg-green-600" : "bg-[#6E39CB]"
            }`}
            style={{ width: `${Math.min(100, ((application.paidAmount || 0) / (application.totalPrice || 1)) * 100)}%` }}
          ></div>
        </div>

        <div className="flex justify-between text-xs text-gray-500">
          <span>$0</span>
          <span>${application.totalPrice || 0}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payment Summary Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          <h5 className="text-md font-medium text-gray-900 mb-4 pb-2 border-b border-gray-100">Payment Summary</h5>

          <div className="space-y-6">
            <div className="text-center p-4 bg-[#F4F5F9] rounded-lg">
              <p className="text-sm text-gray-500 mb-1">Total Amount</p>
              <p className="text-2xl font-bold text-[#6E39CB]">${application.totalPrice || 0}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <p className="text-xs text-gray-500 mb-1">Paid</p>
                <p className="text-lg font-semibold text-green-600">${application.paidAmount || 0}</p>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <p className="text-xs text-gray-500 mb-1">Due</p>
                <p className="text-lg font-semibold text-red-600">${duePayment.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Reference Numbers Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          <h5 className="text-md font-medium text-gray-900 mb-4 pb-2 border-b border-gray-100">Reference Numbers</h5>

          <div className="space-y-4">
            {/* Quote Reference */}
            <div className="p-4 border border-gray-100 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center">
                  <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Quote Reference</p>
                    <p className="text-sm font-medium">
                      {application.quoteRefNumber || application.quoteRefNumber ? (
                        application.quoteRefNumber || application.quoteRefNumber
                      ) : (
                        <span className="text-gray-400">Not available</span>
                      )}
                    </p>
                  </div>
                </div>
                {!application.quoteRefNum && !application.quote && canEditStatus && (
                  <button
                    onClick={() => {
                      setModalType("quote");
                      setShowRefModal(true);
                    }}
                    className="text-xs text-[#6E39CB] hover:text-[#5E2CB8] font-medium"
                  >
                    Add
                  </button>
                )}
              </div>
            </div>

            {/* Invoice Reference */}
            <div className="p-4 border border-gray-100 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center">
                  <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Invoice Reference</p>
                    <p className="text-sm font-medium">
                      {application.invoiceRefNumber || application.invoiceRefNumber ? (
                        application.invoiceRefNumber || application.invoiceRefNumber
                      ) : (
                        <span className="text-gray-400">Not available</span>
                      )}
                    </p>
                  </div>
                </div>
                {!application.invoiceRefNum && !application.invoice && canEditStatus && (
                  <button
                    onClick={() => {
                      setModalType("invoice");
                      setShowRefModal(true);
                    }}
                    className="text-xs text-[#6E39CB] hover:text-[#5E2CB8] font-medium"
                  >
                    Add
                  </button>
                )}
              </div>
              {(application.invoiceRefNum || application.invoice) && (
                <div className="mt-2 text-xs text-gray-500">
                  <span className="inline-flex items-center px-2 py-0.5 rounded bg-blue-100 text-blue-800">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                    Added on {new Date().toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Payment Timeline Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          <h5 className="text-md font-medium text-gray-900 mb-4 pb-2 border-b border-gray-100">Payment Timeline</h5>

          <div className="relative pl-8 space-y-6 before:absolute before:top-2 before:bottom-0 before:left-3 before:w-0.5 before:bg-gray-200">
            {/* Status Change Events */}
            <div className="relative">
              <div className="absolute -left-8 top-0 flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 ring-8 ring-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900">Application Created</p>
                <p className="text-xs text-gray-500">
                  {new Date(application.createdAt || Date.now()).toLocaleDateString()} at {new Date(application.createdAt || Date.now()).toLocaleTimeString()}
                </p>
              </div>
            </div>

            {(application.quoteRefNum || application.quote) && (
              <div className="relative">
                <div className="absolute -left-8 top-0 flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 text-purple-600 ring-8 ring-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900">Quote Raised</p>
                  <p className="text-xs text-gray-500">Quote #{application.quoteRefNum || application.quote}</p>
                </div>
              </div>
            )}

            {(application.invoiceRefNum || application.invoice) && (
              <div className="relative">
                <div className="absolute -left-8 top-0 flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-600 ring-8 ring-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900">Invoice Raised</p>
                  <p className="text-xs text-gray-500">Invoice #{application.invoiceRefNum || application.invoice}</p>
                </div>
              </div>
            )}

            {application.paidAmount > 0 && (
              <div className="relative">
                <div className="absolute -left-8 top-0 flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-600 ring-8 ring-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {selectedPaymentStatus === "FULLY_PAID" ? "Payment Completed" : "Partial Payment Received"}
                  </p>
                  <p className="text-xs text-gray-500">
                    ${application.paidAmount} {selectedPaymentStatus === "FULLY_PAID" ? "paid in full" : `of $${application.totalPrice} paid`}
                  </p>
                </div>
              </div>
            )}

            {selectedPaymentStatus === "INVOICE_EXPIRED" && (
              <div className="relative">
                <div className="absolute -left-8 top-0 flex items-center justify-center w-6 h-6 rounded-full bg-red-100 text-red-600 ring-8 ring-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900">Invoice Expired</p>
                  <p className="text-xs text-gray-500">Payment deadline passed</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentInfo;
