package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.core.models.entities.ApplicationComments;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@AllArgsConstructor
@Setter
@Getter
@NoArgsConstructor
public class ApplicationCommentDTO {
    private Long id;
    private String content;

    private String createdAt;
    private String updatedAt;
    private String createdBy;
    private String updatedBy;

    public ApplicationCommentDTO(ApplicationComments comment) {
        this.id = comment.getId();
        this.content = comment.getContent();
        this.createdBy = comment.getCreatedBy();
        this.createdAt = (comment.getCreatedDate() != null)
                ? comment.getCreatedDate().toString()
                : LocalDateTime.now().toString();
        this.updatedBy = comment.getLastModifiedBy();
        this.updatedAt = (comment.getLastModifiedDate() != null)
                ? comment.getLastModifiedDate().toString()
                : LocalDateTime.now().toString();
    }
}
