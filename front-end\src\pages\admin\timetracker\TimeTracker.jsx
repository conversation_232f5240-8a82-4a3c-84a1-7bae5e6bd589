import React, { useContext, useState } from "react";
import TimeTrackerContext from "../../../services/TimeTrackerContext";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useGetTodayTimeTrackerQuery, useGetTrackersByMonthYearQuery } from "../../../services/CompanyAPIService";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faClock,
  faUserClock,
  faCalendarAlt,
  faSearch,
  faFilter,
  faChartLine,
  faEye,
  faArrowUp,
  faArrowDown,
  faSignInAlt,
  faSignOutAlt,
  faHourglass
} from "@fortawesome/free-solid-svg-icons";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const TimeTracker = () => {
  // Use TimeTrackerContext to track user activity
  useContext(TimeTrackerContext);

  // State for filtering and sorting
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRole, setSelectedRole] = useState("All");
  const [showModal, setShowModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: 'totalSeconds', direction: 'desc' });
  const [dateFilter, setDateFilter] = useState(new Date());
  const [viewMode, setViewMode] = useState("table"); // "table" or "card"

  // Fetch time tracking data
  const { data: todayTrackers = [], isLoading } = useGetTodayTimeTrackerQuery();

  // Calculate summary statistics
  const totalHoursToday = todayTrackers.reduce((total, tracker) => total + (tracker.totalSeconds || 0), 0) / 3600;
  const averageHoursPerPerson = todayTrackers.length > 0 ? totalHoursToday / todayTrackers.length : 0;
  const earliestLogin = todayTrackers.length > 0 ?
    todayTrackers.reduce((earliest, tracker) => {
      if (!tracker.firstLogin) return earliest;
      const loginTime = new Date(tracker.firstLogin);
      return !earliest || loginTime < earliest ? loginTime : earliest;
    }, null) : null;

  // Filter employees based on search and role
  const filteredEmployees = todayTrackers.filter((tracker) => {
    const matchesSearch = tracker.profile.fullName
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesRole =
      selectedRole === "All" || tracker.profile.role === selectedRole;
    return matchesSearch && matchesRole;
  });

  // Sort employees based on sort configuration
  const sortedEmployees = [...filteredEmployees].sort((a, b) => {
    if (sortConfig.key === 'name') {
      const aValue = a.profile.fullName.toLowerCase();
      const bValue = b.profile.fullName.toLowerCase();
      return sortConfig.direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    } else if (sortConfig.key === 'firstLogin') {
      const aValue = a.firstLogin ? new Date(a.firstLogin).getTime() : 0;
      const bValue = b.firstLogin ? new Date(b.firstLogin).getTime() : 0;
      return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
    } else if (sortConfig.key === 'lastTime') {
      const aValue = a.lastTime ? new Date(a.lastTime).getTime() : 0;
      const bValue = b.lastTime ? new Date(b.lastTime).getTime() : 0;
      return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
    } else if (sortConfig.key === 'totalSeconds') {
      const aValue = a.totalSeconds || 0;
      const bValue = b.totalSeconds || 0;
      return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
    }
    return 0;
  });

  // Get unique roles for filter dropdown
  const roles = [
    "All",
    ...new Set(todayTrackers.map((tracker) => tracker.profile.role)),
  ];

  // Handle sort request
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Format time from seconds to hours and minutes
  const formatTime = (seconds) => {
    if (!seconds) return "0h 0m";
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    return `${hrs}h ${mins}m`;
  };

  // Format time for display with AM/PM
  const formatTimeDisplay = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Time Tracking</h1>
          <p className="text-sm text-gray-500 mt-1">Monitor employee work hours and activity</p>
        </div>
        <div className="mt-4 md:mt-0">
          <DatePicker
            selected={dateFilter}
            onChange={(date) => setDateFilter(date)}
            dateFormat="MMMM d, yyyy"
            className="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            customInput={
              <button className="flex items-center bg-white px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-[#6E39CB]" />
                {dateFilter.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
              </button>
            }
          />
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {/* Total Hours Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          <div className="flex items-center mb-4">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faClock} className="h-5 w-5 text-[#6E39CB]" />
            </div>
            <h3 className="text-sm font-medium text-gray-700">Total Hours Today</h3>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold text-gray-900">{totalHoursToday.toFixed(1)}h</p>
            <p className="text-sm text-gray-500">{todayTrackers.length} employees</p>
          </div>
        </div>

        {/* Average Hours Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          <div className="flex items-center mb-4">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faChartLine} className="h-5 w-5 text-[#6E39CB]" />
            </div>
            <h3 className="text-sm font-medium text-gray-700">Average Per Person</h3>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold text-gray-900">{averageHoursPerPerson.toFixed(1)}h</p>
            <p className="text-sm text-gray-500">per employee</p>
          </div>
        </div>

        {/* Earliest Login Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          <div className="flex items-center mb-4">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faSignInAlt} className="h-5 w-5 text-[#6E39CB]" />
            </div>
            <h3 className="text-sm font-medium text-gray-700">Earliest Login</h3>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold text-gray-900">
              {earliestLogin ? earliestLogin.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'N/A'}
            </p>
            <p className="text-sm text-gray-500">today</p>
          </div>
        </div>

        {/* Active Employees Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          <div className="flex items-center mb-4">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faUserClock} className="h-5 w-5 text-[#6E39CB]" />
            </div>
            <h3 className="text-sm font-medium text-gray-700">Active Employees</h3>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-2xl font-bold text-gray-900">{todayTrackers.length}</p>
            <p className="text-sm text-gray-500">logged in today</p>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 w-full md:w-auto">
            <div className="relative w-full sm:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search by name"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
            </div>

            <div className="relative w-full sm:w-48">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faFilter} className="text-gray-400" />
              </div>
              <select
                className="pl-10 pr-4 py-2 w-full border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] appearance-none"
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
              >
                {roles.map((role, index) => (
                  <option key={index} value={role}>
                    {role}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faArrowDown} className="text-gray-400 h-3 w-3" />
              </div>
            </div>
          </div>

          {/* View Toggle */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">View:</span>
            <div className="flex border border-gray-200 rounded-lg overflow-hidden">
              <button
                className={`px-3 py-1.5 text-sm ${viewMode === 'table' ? 'bg-[#6E39CB] text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                onClick={() => setViewMode('table')}
              >
                Table
              </button>
              <button
                className={`px-3 py-1.5 text-sm ${viewMode === 'card' ? 'bg-[#6E39CB] text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                onClick={() => setViewMode('card')}
              >
                Cards
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#6E39CB] mb-4"></div>
          <p className="text-gray-500">Loading time tracking data...</p>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && sortedEmployees.length === 0 && (
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-8 text-center">
          <div className="bg-[#F4F5F9] p-4 rounded-full inline-block mb-4">
            <FontAwesomeIcon icon={faUserClock} className="h-8 w-8 text-[#6E39CB]" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Time Tracking Data</h3>
          <p className="text-gray-500 mb-4">There are no employees with time tracking data for the selected filters.</p>
          <button
            onClick={() => {
              setSearchQuery('');
              setSelectedRole('All');
            }}
            className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
          >
            Clear Filters
          </button>
        </div>
      )}

      {/* Table View */}
      {!isLoading && sortedEmployees.length > 0 && viewMode === 'table' && (
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th
                  className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('name')}
                >
                  <div className="flex items-center">
                    <span>Name</span>
                    {sortConfig.key === 'name' && (
                      <FontAwesomeIcon
                        icon={sortConfig.direction === 'asc' ? faArrowUp : faArrowDown}
                        className="ml-1 h-3 w-3 text-[#6E39CB]"
                      />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('firstLogin')}
                >
                  <div className="flex items-center">
                    <span>First In</span>
                    {sortConfig.key === 'firstLogin' && (
                      <FontAwesomeIcon
                        icon={sortConfig.direction === 'asc' ? faArrowUp : faArrowDown}
                        className="ml-1 h-3 w-3 text-[#6E39CB]"
                      />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('lastTime')}
                >
                  <div className="flex items-center">
                    <span>Last Out</span>
                    {sortConfig.key === 'lastTime' && (
                      <FontAwesomeIcon
                        icon={sortConfig.direction === 'asc' ? faArrowUp : faArrowDown}
                        className="ml-1 h-3 w-3 text-[#6E39CB]"
                      />
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('totalSeconds')}
                >
                  <div className="flex items-center">
                    <span>Duration</span>
                    {sortConfig.key === 'totalSeconds' && (
                      <FontAwesomeIcon
                        icon={sortConfig.direction === 'asc' ? faArrowUp : faArrowDown}
                        className="ml-1 h-3 w-3 text-[#6E39CB]"
                      />
                    )}
                  </div>
                </th>
                <th className="px-6 py-3 bg-[#F4F5F9] text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {sortedEmployees.map((tracker, index) => (
                <tr key={index} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-[#6E39CB] font-medium">
                          {tracker.profile.fullName.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{tracker.profile.fullName}</p>
                        <p className="text-xs text-gray-500">{tracker.profile.role}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faSignInAlt} className="text-green-500 mr-2" />
                      <span>{formatTimeDisplay(tracker.firstLogin)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faSignOutAlt} className="text-red-500 mr-2" />
                      <span>{formatTimeDisplay(tracker.lastTime)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="bg-[#F4F5F9] px-2 py-1 rounded-full">
                        <span className="font-medium text-[#6E39CB]">
                          {formatTime(tracker.totalSeconds)}
                        </span>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-center">
                    <button
                      onClick={() => {
                        setSelectedUser(tracker);
                        setShowModal(true);
                      }}
                      className="inline-flex items-center px-3 py-1.5 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
                    >
                      <FontAwesomeIcon icon={faEye} className="mr-1" />
                      Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Card View */}
      {!isLoading && sortedEmployees.length > 0 && viewMode === 'card' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedEmployees.map((tracker, index) => (
            <div key={index} className="bg-white rounded-lg border border-gray-100 shadow-sm overflow-hidden hover:shadow-md transition-shadow">
              <div className="border-b border-gray-100 bg-[#F4F5F9] px-6 py-4">
                <div className="flex items-center">
                  <div className="bg-white rounded-full h-10 w-10 flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                    <span className="text-[#6E39CB] text-lg font-medium">
                      {tracker.profile.fullName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{tracker.profile.fullName}</p>
                    <p className="text-xs text-gray-500">{tracker.profile.role}</p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <p className="text-xs text-gray-500 mb-1">First Login</p>
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faSignInAlt} className="text-green-500 mr-2" />
                      <p className="text-sm font-medium">{formatTimeDisplay(tracker.firstLogin)}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Last Activity</p>
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faSignOutAlt} className="text-red-500 mr-2" />
                      <p className="text-sm font-medium">{formatTimeDisplay(tracker.lastTime)}</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Total Duration</p>
                    <div className="bg-[#F4F5F9] px-3 py-1 rounded-full">
                      <span className="font-medium text-[#6E39CB]">
                        {formatTime(tracker.totalSeconds)}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setSelectedUser(tracker);
                      setShowModal(true);
                    }}
                    className="inline-flex items-center px-3 py-1.5 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
                  >
                    <FontAwesomeIcon icon={faEye} className="mr-1" />
                    Details
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal Section */}
      {showModal && selectedUser && (
        <TimeTrackerModal
          selectedUser={selectedUser}
          onClose={() => setShowModal(false)}
        />
      )}
    </div>
  );
};

const TimeTrackerModal = ({ selectedUser, onClose }) => {
  const [monthYearFilter, setMonthYearFilter] = useState(new Date());
  const [activeTab, setActiveTab] = useState('calendar'); // 'calendar', 'timeline', or 'analytics'

  // Format time function
  const formatTime = (seconds) => {
    if (!seconds) return "0h 0m";
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    return `${hrs}h ${mins}m`;
  };

  // Format time for display with AM/PM
  const formatTimeDisplay = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Fetch monthly data
  const { data: monthYearData = [], isFetching } =
    useGetTrackersByMonthYearQuery(
      {
        username: selectedUser.profile.username,
        month: monthYearFilter.getMonth() + 1,
        year: monthYearFilter.getFullYear(),
      },
      {
        skip: !selectedUser,
      }
    );

  // Calculate monthly statistics
  const totalMonthlyHours = monthYearData.reduce((total, data) => total + (data.totalSeconds || 0), 0) / 3600;
  const averageDailyHours = monthYearData.length > 0 ? totalMonthlyHours / monthYearData.length : 0;
  const daysWorked = monthYearData.filter(data => data.totalSeconds > 0).length;

  // Sort data by date (most recent first)
  const sortedData = [...monthYearData].sort((a, b) => {
    return new Date(b.captureDate) - new Date(a.captureDate);
  });

  // Sort data by date (chronological for charts)
  const chronologicalData = [...monthYearData].sort((a, b) => {
    return new Date(a.captureDate) - new Date(b.captureDate);
  });

  // Prepare chart data
  const prepareChartData = () => {
    // Get all days in the month
    const year = monthYearFilter.getFullYear();
    const month = monthYearFilter.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // Create arrays for labels and data
    const labels = [];
    const hoursData = [];
    const loginTimeData = [];

    // Create a map of date to data for quick lookup
    const dateDataMap = {};
    chronologicalData.forEach(data => {
      const date = new Date(data.captureDate);
      dateDataMap[date.getDate()] = data;
    });

    // Fill in data for all days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month, i);
      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
      labels.push(`${dayName} ${i}`);

      const dayData = dateDataMap[i];
      if (dayData && dayData.totalSeconds) {
        // Convert seconds to hours
        hoursData.push((dayData.totalSeconds / 3600).toFixed(1));

        // Get login time hour (for login time pattern)
        if (dayData.firstLogin) {
          const loginDate = new Date(dayData.firstLogin);
          const loginHour = loginDate.getHours() + (loginDate.getMinutes() / 60);
          loginTimeData.push(loginHour);
        } else {
          loginTimeData.push(null);
        }
      } else {
        hoursData.push(0);
        loginTimeData.push(null);
      }
    }

    return { labels, hoursData, loginTimeData };
  };

  // Get chart data
  const { labels, hoursData, loginTimeData } = prepareChartData();

  // Chart options and data
  const hoursChartData = {
    labels,
    datasets: [
      {
        label: 'Hours Worked',
        data: hoursData,
        backgroundColor: 'rgba(110, 57, 203, 0.2)',
        borderColor: 'rgba(110, 57, 203, 1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgba(110, 57, 203, 1)',
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };

  const hoursChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Daily Hours Worked',
        font: {
          size: 16,
        },
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.raw} hours`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Hours',
        },
      },
      x: {
        title: {
          display: true,
          text: 'Day of Month',
        },
      },
    },
  };

  const loginTimeChartData = {
    labels,
    datasets: [
      {
        label: 'Login Time',
        data: loginTimeData,
        backgroundColor: 'rgba(46, 184, 92, 0.2)',
        borderColor: 'rgba(46, 184, 92, 1)',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointBackgroundColor: 'rgba(46, 184, 92, 1)',
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };

  const loginTimeChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Login Time Pattern',
        font: {
          size: 16,
        },
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const hour = Math.floor(context.raw);
            const minute = Math.round((context.raw - hour) * 60);
            return `Login at ${hour}:${minute < 10 ? '0' + minute : minute}`;
          }
        }
      }
    },
    scales: {
      y: {
        min: 6,  // Start at 6 AM
        max: 12, // End at 12 PM
        title: {
          display: true,
          text: 'Time of Day',
        },
        ticks: {
          callback: function(value) {
            return `${value}:00`;
          }
        }
      },
      x: {
        title: {
          display: true,
          text: 'Day of Month',
        },
      },
    },
  };

  const handleMonthYearChange = (date) => {
    setMonthYearFilter(date);
  };

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Modal Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-100">
          <div className="flex items-center">
            <div className="bg-[#F4F5F9] rounded-full h-10 w-10 flex items-center justify-center mr-3">
              <span className="text-[#6E39CB] font-medium">
                {selectedUser.profile.fullName.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {selectedUser.profile.fullName}
              </h2>
              <p className="text-sm text-gray-500">{selectedUser.profile.role}</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <DatePicker
              selected={monthYearFilter}
              onChange={handleMonthYearChange}
              showMonthYearPicker
              dateFormat="MMMM yyyy"
              className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              customInput={
                <button className="flex items-center bg-white px-3 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-[#6E39CB]" />
                  {monthYearFilter.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </button>
              }
            />

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-6">
          <div className="bg-[#F4F5F9] rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FontAwesomeIcon icon={faClock} className="text-[#6E39CB] mr-2" />
              <p className="text-sm font-medium text-gray-700">Total Hours</p>
            </div>
            <p className="text-2xl font-bold text-[#6E39CB]">{totalMonthlyHours.toFixed(1)}h</p>
            <p className="text-xs text-gray-500 mt-1">This month</p>
          </div>

          <div className="bg-[#F4F5F9] rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FontAwesomeIcon icon={faChartLine} className="text-[#6E39CB] mr-2" />
              <p className="text-sm font-medium text-gray-700">Daily Average</p>
            </div>
            <p className="text-2xl font-bold text-[#6E39CB]">{averageDailyHours.toFixed(1)}h</p>
            <p className="text-xs text-gray-500 mt-1">Per working day</p>
          </div>

          <div className="bg-[#F4F5F9] rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FontAwesomeIcon icon={faCalendarAlt} className="text-[#6E39CB] mr-2" />
              <p className="text-sm font-medium text-gray-700">Days Worked</p>
            </div>
            <p className="text-2xl font-bold text-[#6E39CB]">{daysWorked}</p>
            <p className="text-xs text-gray-500 mt-1">This month</p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="px-6 border-b border-gray-100">
          <nav className="flex space-x-6" aria-label="Tabs">
            <button
              className={`py-3 border-b-2 font-medium text-sm ${activeTab === 'calendar' ? 'border-[#6E39CB] text-[#6E39CB]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              onClick={() => setActiveTab('calendar')}
            >
              <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
              Calendar View
            </button>
            <button
              className={`py-3 border-b-2 font-medium text-sm ${activeTab === 'timeline' ? 'border-[#6E39CB] text-[#6E39CB]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              onClick={() => setActiveTab('timeline')}
            >
              <FontAwesomeIcon icon={faHourglass} className="mr-2" />
              Timeline View
            </button>
            <button
              className={`py-3 border-b-2 font-medium text-sm ${activeTab === 'analytics' ? 'border-[#6E39CB] text-[#6E39CB]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
              onClick={() => setActiveTab('analytics')}
            >
              <FontAwesomeIcon icon={faChartLine} className="mr-2" />
              Analytics
            </button>
          </nav>
        </div>

        {/* Content Area */}
        <div className="p-6 overflow-y-auto flex-grow">
          {/* Loading State */}
          {isFetching && (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="w-12 h-12 border-4 border-[#6E39CB] border-t-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-gray-500">Loading time tracking data...</p>
            </div>
          )}

          {/* Empty State */}
          {!isFetching && monthYearData.length === 0 && (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="bg-[#F4F5F9] p-4 rounded-full mb-4">
                <FontAwesomeIcon icon={faUserClock} className="h-8 w-8 text-[#6E39CB]" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Time Tracking Data</h3>
              <p className="text-gray-500 max-w-md">
                There is no time tracking data available for {selectedUser.profile.fullName} in {monthYearFilter.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}.
              </p>
            </div>
          )}

          {/* Calendar View */}
          {!isFetching && monthYearData.length > 0 && activeTab === 'calendar' && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      First Login
                    </th>
                    <th className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Activity
                    </th>
                    <th className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sortedData.map((data, index) => (
                    <tr key={index} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-100 transition-colors`}>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <div className={`w-2 h-2 rounded-full mr-2 ${data.totalSeconds > 0 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          <span className="font-medium">
                            {new Date(data.captureDate).toLocaleDateString('en-US', {
                              weekday: 'short',
                              month: 'short',
                              day: 'numeric',
                            })}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <FontAwesomeIcon icon={faSignInAlt} className="text-green-500 mr-2" />
                          <span>{formatTimeDisplay(data.firstLogin)}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <FontAwesomeIcon icon={faSignOutAlt} className="text-red-500 mr-2" />
                          <span>{formatTimeDisplay(data.lastTime)}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <div className="bg-[#F4F5F9] px-2 py-1 rounded-full">
                            <span className="font-medium text-[#6E39CB]">
                              {formatTime(data.totalSeconds)}
                            </span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Timeline View */}
          {!isFetching && monthYearData.length > 0 && activeTab === 'timeline' && (
            <div className="relative pl-8 space-y-8 before:absolute before:top-2 before:bottom-0 before:left-3 before:w-0.5 before:bg-gray-200">
              {sortedData.filter(data => data.totalSeconds > 0).map((data, index) => (
                <div key={index} className="relative">
                  <div className="absolute -left-8 top-0 flex items-center justify-center w-6 h-6 rounded-full bg-[#F4F5F9] text-[#6E39CB] ring-8 ring-white">
                    <FontAwesomeIcon icon={faUserClock} className="h-3 w-3" />
                  </div>
                  <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h5 className="text-md font-medium text-gray-900">
                        {new Date(data.captureDate).toLocaleDateString('en-US', {
                          weekday: 'long',
                          month: 'long',
                          day: 'numeric',
                          year: 'numeric',
                        })}
                      </h5>
                      <div className="bg-[#F4F5F9] px-3 py-1 rounded-full">
                        <span className="font-medium text-[#6E39CB]">
                          {formatTime(data.totalSeconds)}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <div className="bg-green-100 p-2 rounded-full mr-3">
                          <FontAwesomeIcon icon={faSignInAlt} className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">First Login</p>
                          <p className="text-sm font-medium">{formatTimeDisplay(data.firstLogin)}</p>
                        </div>
                      </div>

                      <div className="flex items-center">
                        <div className="bg-red-100 p-2 rounded-full mr-3">
                          <FontAwesomeIcon icon={faSignOutAlt} className="h-4 w-4 text-red-600" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Last Activity</p>
                          <p className="text-sm font-medium">{formatTimeDisplay(data.lastTime)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {sortedData.filter(data => data.totalSeconds > 0).length === 0 && (
                <div className="text-center py-4 text-sm text-gray-500">
                  No activity recorded in this month.
                </div>
              )}
            </div>
          )}

          {/* Analytics View */}
          {!isFetching && monthYearData.length > 0 && activeTab === 'analytics' && (
            <div>
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Work Hours Analysis</h3>
                <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4">
                  <div className="h-80">
                    <Line data={hoursChartData} options={hoursChartOptions} />
                  </div>
                </div>
                <p className="text-sm text-gray-500 mt-2 text-center">
                  Daily work hours for {monthYearFilter.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </p>
              </div>

              

              <div className="mt-8 bg-[#F4F5F9] rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Analytics Insights</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <div className="bg-[#6E39CB] rounded-full h-2 w-2 mt-1.5 mr-2 flex-shrink-0"></div>
                    <span>Average daily work hours: <strong>{averageDailyHours.toFixed(1)} hours</strong></span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-[#6E39CB] rounded-full h-2 w-2 mt-1.5 mr-2 flex-shrink-0"></div>
                    <span>Days worked this month: <strong>{daysWorked} days</strong> ({Math.round((daysWorked / labels.length) * 100)}% of month)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-[#6E39CB] rounded-full h-2 w-2 mt-1.5 mr-2 flex-shrink-0"></div>
                    <span>Total hours this month: <strong>{totalMonthlyHours.toFixed(1)} hours</strong></span>
                  </li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-100 p-4 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default TimeTracker;
