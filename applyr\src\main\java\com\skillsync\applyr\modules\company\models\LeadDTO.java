package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.models.enums.LeadsStatus;
import com.skillsync.applyr.modules.sales.models.ApplicationDTO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


@AllArgsConstructor
@Setter
@Getter
@ToString
public class LeadDTO {
    private String companyName;
    private String leadName;
    private String phone;
    private String email;
    private String address;
    private LeadsStatus status;
    private int applicationCount;
    private LocalDateTime createdDate;

    private List<CommentDTO> comments;
    private List<ActivityDTO> activities;
    private List<SalesAgentDTO> assignedAgents;
    private List<ApplicationDTO> applications;

    public LeadDTO() {
        comments = new ArrayList<>();
        activities = new ArrayList<>();
        assignedAgents = new ArrayList<>();
        applications = new ArrayList<>();
    }

    public String getPhone() {
        return this.phone.trim();
    }
}