import React, { useState } from "react";
import {
  useRegisterAdminMutation,
  useRegisterSalesMutation,
  useRegisterOperationsMutation,
} from "../../services/AdminAPIService"; // Adjust path if needed
import { showValidationError, showErrorToast } from "../../utils/toastUtils";

const AddEmployeeModal = ({ onClose, onEmployeeAdded }) => {
  const [registerAdmin, { isLoading: isAddingAdmin }] =
    useRegisterAdminMutation();
  const [registerSales, { isLoading: isAddingSales }] =
    useRegisterSalesMutation();
  const [registerOperations, { isLoading: isAddingOperations }] =
    useRegisterOperationsMutation();

  const [formData, setFormData] = useState({
    username: "",
    role: "",
    fullName: "",
    gender: "",
    email: "",
    phone: "",
    address: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const payload = {
      registerRequestDTO: {
        username: formData.email,
        role: formData.role,
      },
      fullName: formData.fullName,
      gender: formData.gender,
      email: formData.email,
      phone: formData.phone,
      address: formData.address,
    };

    try {
      if (formData.role === "ADMIN") {
        await registerAdmin(payload).unwrap();
      } else if (formData.role === "SALES") {
        await registerSales(payload).unwrap();
      } else if (formData.role === "OPERATIONS") {
        await registerOperations(payload).unwrap();
      } else {
        showValidationError("Please select a valid role (ADMIN, SALES, or OPERATIONS).");
        return;
      }

      // Close modal on success and refetch employee list
      onEmployeeAdded(); // Trigger refetch to update employee list
      onClose(); // Close modal
    } catch (error) {
      console.error("Error registering employee:", error);
      showErrorToast(error, "Failed to register employee. Check console for details.");
    }
  };

  return (
    // Modal backdrop
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      {/* Modal container */}
      <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Add Employee</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">Full Name</label>
            <input
              type="text"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              required
            />
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">Role</label>
            <select
              name="role"
              value={formData.role}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              required
            >
              <option value="" disabled>
                Select role
              </option>
              <option value="ADMIN">ADMIN</option>
              <option value="SALES">SALES</option>
              <option value="OPERATIONS">OPERATIONS</option>
            </select>
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              required
            />
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">Gender</label>
            <select
              name="gender"
              value={formData.gender}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              required
            >
              <option value="" disabled>
                Select Gender
              </option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
            </select>
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">Phone</label>
            <input
              type="text"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
          </div>

          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">Address</label>
            <input
              type="text"
              name="address"
              value={formData.address}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
          </div>

          <div className="col-span-1 md:col-span-2 flex justify-end gap-3 mt-4">
            <button
              type="button"
              onClick={onClose}
              className="border border-gray-300 text-gray-700 px-5 py-2.5 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isAddingAdmin || isAddingSales || isAddingOperations}
              className="bg-[#6E39CB] text-white px-5 py-2.5 rounded-lg hover:bg-[#5E2CB8] transition-colors flex items-center"
            >
              {isAddingAdmin || isAddingSales || isAddingOperations ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                "Add Employee"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddEmployeeModal;
