import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowDown,
  faListAlt,
  faCheckCircle,
  faClock,
  faHourglass,
  faFileAlt,
  faEye,
} from "@fortawesome/free-solid-svg-icons";

const ApplicationsTab = ({
  targetData,
  agentFilter,
  setAgentFilter,
  applicationStatusFilter,
  setApplicationStatusFilter,
}) => {
  // Initialize the navigate function for routing
  const navigate = useNavigate();
  // Map raw status to display category (COMPLETED, IN_PROGRESS, PENDING)
  const getStatusCategory = (status) => {
    if (["SOFT_COPY_READY", "SOFT_COPY_SENT", "HARD_COPY_READY", "HARD_COPY_SENT"].includes(status)) {
      return "COMPLETED";
    } else if (status === "IN_PROGRESS") {
      return "IN_PROGRESS";
    } else if (["DOCUMENT_PENDING", "QUOTE_RAISED", "INVOICE_RAISED"].includes(status)) {
      return "PENDING";
    } else {
      return "PENDING"; // Default to pending for unknown statuses
    }
  };

  // Get a user-friendly display name for the status
  const getStatusDisplayName = (status) => {
    // Replace underscores with spaces and capitalize each word
    return status.replace(/_/g, " ").replace(/\b\w/g, c => c.toUpperCase());
  };

  // Get status icon based on application status category
  const getStatusIcon = (status) => {
    const category = getStatusCategory(status);
    switch(category) {
      case "COMPLETED":
        return <FontAwesomeIcon icon={faCheckCircle} className="mr-2 text-green-600" />;
      case "IN_PROGRESS":
        return <FontAwesomeIcon icon={faClock} className="mr-2 text-blue-600" />;
      case "PENDING":
        return <FontAwesomeIcon icon={faHourglass} className="mr-2 text-amber-600" />;
      default:
        return <FontAwesomeIcon icon={faListAlt} className="mr-2 text-gray-600" />;
    }
  };

  // Calculate application statistics
  const stats = useMemo(() => {
    const allApplications = targetData.targets.flatMap(t => t.applications || []);
    const totalCount = allApplications.length;
    const completedCount = allApplications.filter(app => getStatusCategory(app.status) === "COMPLETED").length;
    const inProgressCount = allApplications.filter(app => getStatusCategory(app.status) === "IN_PROGRESS").length;
    const pendingCount = allApplications.filter(app => getStatusCategory(app.status) === "PENDING").length;

    return {
      total: totalCount,
      completed: completedCount,
      inProgress: inProgressCount,
      pending: pendingCount,
      completedPercentage: totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0,
      inProgressPercentage: totalCount > 0 ? Math.round((inProgressCount / totalCount) * 100) : 0,
      pendingPercentage: totalCount > 0 ? Math.round((pendingCount / totalCount) * 100) : 0
    };
  }, [targetData]);

  // Get filtered agents with their applications
  const filteredAgents = useMemo(() => {
    return targetData.targets
      .filter(t => agentFilter === "ALL" || t.profile?.username === agentFilter)
      .map(agent => ({
        ...agent,
        filteredApplications: (agent.applications || [])
          .filter(app => applicationStatusFilter === "ALL" || getStatusCategory(app.status) === applicationStatusFilter)
          .sort((a, b) => {
            // Sort by status: completed first, then in progress, then pending
            const statusOrder = { COMPLETED: 1, IN_PROGRESS: 2, PENDING: 3 };
            return (statusOrder[getStatusCategory(a.status)] || 4) - (statusOrder[getStatusCategory(b.status)] || 4);
          })
      }))
      // Only include agents that have applications matching the filter
      .filter(agent => agent.filteredApplications.length > 0);
  }, [targetData, agentFilter, applicationStatusFilter]);

  // Total filtered applications count
  const filteredApplicationsCount = useMemo(() => {
    return filteredAgents.reduce((sum, agent) => sum + agent.filteredApplications.length, 0);
  }, [filteredAgents]);

  return (
    <>
      {/* Summary Cards - Simplified */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div className="bg-white rounded-lg border border-gray-200 p-4 flex-1 min-w-[200px]">
          <div className="flex items-center gap-3">
            <div className="bg-[#F4F5F9] p-2 rounded-lg">
              <FontAwesomeIcon icon={faFileAlt} className="h-5 w-5 text-[#6E39CB]" />
            </div>
            <div>
              <h3 className="text-base font-medium text-gray-700">Total Applications</h3>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-2xl font-semibold text-gray-900">{stats.total}</span>
                <span className="text-sm text-gray-500">
                  from {targetData.targets.filter(t => (t.applications || []).length > 0).length} agents
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 flex-1 min-w-[600px]">
          <div className="bg-white rounded-lg border border-gray-200 p-4 flex-1 min-w-[180px]">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={faCheckCircle} className="h-4 w-4 text-green-600" />
                <span className="text-base font-medium text-gray-700">Completed</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.completed}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full"
                style={{ width: `${stats.completedPercentage}%` }}
              ></div>
            </div>
            <div className="text-right mt-1">
              <span className="text-sm text-gray-500">{stats.completedPercentage}%</span>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4 flex-1 min-w-[180px]">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={faClock} className="h-4 w-4 text-blue-600" />
                <span className="text-base font-medium text-gray-700">In Progress</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.inProgress}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full"
                style={{ width: `${stats.inProgressPercentage}%` }}
              ></div>
            </div>
            <div className="text-right mt-1">
              <span className="text-sm text-gray-500">{stats.inProgressPercentage}%</span>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-4 flex-1 min-w-[180px]">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={faHourglass} className="h-4 w-4 text-yellow-600" />
                <span className="text-base font-medium text-gray-700">Pending</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.pending}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-yellow-500 h-2 rounded-full"
                style={{ width: `${stats.pendingPercentage}%` }}
              ></div>
            </div>
            <div className="text-right mt-1">
              <span className="text-sm text-gray-500">{stats.pendingPercentage}%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Applications Filter Controls - Simplified */}
      <div className="bg-white rounded-lg border border-gray-200 p-5 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Applications</h3>
            <p className="text-sm text-gray-500 mt-1">
              Showing {filteredApplicationsCount} of {stats.total} applications from {filteredAgents.length} agents
            </p>
          </div>

          <div className="flex flex-wrap items-center gap-3">
            <div className="relative">
              <select
                value={agentFilter}
                onChange={(e) => setAgentFilter(e.target.value)}
                className="text-sm border border-gray-300 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-1 focus:ring-[#6E39CB] focus:border-[#6E39CB] bg-white"
              >
                <option value="ALL">All Agents</option>
                {targetData.targets.map((t, i) => (
                  <option key={i} value={t.profile?.username}>
                    {t.profile?.fullName || t.profile?.username || "Agent"}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <FontAwesomeIcon icon={faArrowDown} className="text-gray-500 h-3 w-3" />
              </div>
            </div>

            <div className="relative">
              <select
                value={applicationStatusFilter}
                onChange={(e) => setApplicationStatusFilter(e.target.value)}
                className="text-sm border border-gray-300 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-1 focus:ring-[#6E39CB] focus:border-[#6E39CB] bg-white"
              >
                <option value="ALL">All Statuses</option>
                <option value="COMPLETED">Completed</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="PENDING">Pending</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <FontAwesomeIcon icon={faArrowDown} className="text-gray-500 h-3 w-3" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* No Applications Message - Simplified */}
      {filteredAgents.length === 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-10 text-center">
          <div className="flex flex-col items-center justify-center max-w-md mx-auto">
            <div className="bg-[#F4F5F9] p-4 rounded-lg mb-4">
              <FontAwesomeIcon icon={faListAlt} className="h-8 w-8 text-[#6E39CB]" />
            </div>
            <h3 className="text-xl font-medium text-gray-900 mb-3">
              No Applications Found
            </h3>
            <p className="text-base text-gray-600 mb-5">
              {stats.total > 0
                ? "No applications match your current filter criteria. Try adjusting your filters."
                : "There are no applications associated with this target period."}
            </p>
            {stats.total > 0 && (
              <button
                onClick={() => {
                  setAgentFilter("ALL");
                  setApplicationStatusFilter("ALL");
                }}
                className="px-5 py-2.5 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5D2EAE] transition-colors font-medium"
              >
                Show All Applications
              </button>
            )}
          </div>
        </div>
      )}

      {/* Applications by Agent - Simplified */}
      {filteredAgents.map((agent, agentIndex) => (
        <div key={agentIndex} className="bg-white rounded-lg border border-gray-200 mb-6 overflow-hidden">
          {/* Agent Header */}
          <div className="flex items-center p-4 bg-gray-50 border-b border-gray-200">
            <div className="bg-white rounded-full h-10 w-10 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200">
              <span className="text-[#6E39CB] font-medium text-lg">
                {agent.profile?.fullName
                  ? agent.profile.fullName.charAt(0).toUpperCase()
                  : "A"}
              </span>
            </div>
            <div className="flex-grow">
              <h3 className="text-lg font-medium text-gray-900">
                {agent.profile?.fullName || agent.profile?.username || "Agent"}
              </h3>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-gray-600">
                  {agent.filteredApplications.length} application{agent.filteredApplications.length !== 1 ? 's' : ''}
                </span>

                <div className="flex items-center gap-3 ml-2">
                  <span className="inline-flex items-center text-sm text-green-700">
                    <FontAwesomeIcon icon={faCheckCircle} className="mr-1.5 h-3.5 w-3.5 text-green-600" />
                    {agent.filteredApplications.filter(app => getStatusCategory(app.status) === "COMPLETED").length}
                  </span>
                  <span className="inline-flex items-center text-sm text-blue-700">
                    <FontAwesomeIcon icon={faClock} className="mr-1.5 h-3.5 w-3.5 text-blue-600" />
                    {agent.filteredApplications.filter(app => getStatusCategory(app.status) === "IN_PROGRESS").length}
                  </span>
                  <span className="inline-flex items-center text-sm text-amber-700">
                    <FontAwesomeIcon icon={faHourglass} className="mr-1.5 h-3.5 w-3.5 text-amber-600" />
                    {agent.filteredApplications.filter(app => getStatusCategory(app.status) === "PENDING").length}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Agent's Applications Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-600">
                    Application ID
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-600">
                    Client
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-600">
                    Candidate
                  </th>
                  <th className="px-6 py-3 text-center text-sm font-medium text-gray-600">
                    Status
                  </th>
                  <th className="px-6 py-3 text-center text-sm font-medium text-gray-600">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {agent.filteredApplications.map((app, idx) => {
                  // Determine row styling based on status category
                  const statusCategory = getStatusCategory(app.status);
                  // Use a more subtle styling approach with just a left border indicator
                  let rowClass = "hover:bg-gray-50 transition-colors border-b border-gray-200";

                  // Add a subtle left border indicator instead of full background color
                  if (statusCategory === "COMPLETED") {
                    rowClass += " border-l-4 border-l-green-200";
                  } else if (statusCategory === "IN_PROGRESS") {
                    rowClass += " border-l-4 border-l-blue-200";
                  } else if (statusCategory === "PENDING") {
                    rowClass += " border-l-4 border-l-yellow-200";
                  }

                  return (
                    <tr key={idx} className={rowClass}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-[#6E39CB]">
                          <FontAwesomeIcon icon={faFileAlt} className="mr-2" />
                          {app.applicationId}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="bg-white rounded-full h-9 w-9 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200">
                            <span className="text-[#6E39CB] font-medium">
                              {app.lead?.leadName ? app.lead.leadName.charAt(0).toUpperCase() : "C"}
                            </span>
                          </div>
                          <div>
                            <span className="font-medium text-sm block text-gray-900">{app.lead?.leadName || "N/A"}</span>
                            {app.lead?.phone && (
                              <span className="text-gray-500">{app.lead.email}</span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="bg-white rounded-full h-9 w-9 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200">
                            <span className="text-[#6E39CB] font-medium">
                              {app.applicantName ? app.applicantName.charAt(0).toUpperCase() : "C"}
                            </span>
                          </div>
                          <div>
                            <span className="font-medium block text-sm text-gray-900">{app.applicantName || "N/A"}</span>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span
                          className={`inline-flex items-center px-3 py-1 rounded-md text-sm ${
                            statusCategory === "COMPLETED"
                              ? "text-green-700"
                              : statusCategory === "IN_PROGRESS"
                              ? "text-blue-700"
                              : statusCategory === "PENDING"
                              ? "text-amber-700"
                              : "text-gray-700"
                          }`}
                        >
                          {getStatusIcon(app.status)}
                          {getStatusDisplayName(app.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <button
                          onClick={() => navigate(`/admin/application/profile/${app.applicationId}`)}
                          className="text-[#6E39CB] hover:text-[#5D2EAE] transition-colors p-2 rounded-full hover:bg-[#F4F5F9]"
                          title="View Application Profile"
                        >
                          <FontAwesomeIcon icon={faEye} className="h-5 w-5" />
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      ))}
    </>
  );
};

export default ApplicationsTab;
