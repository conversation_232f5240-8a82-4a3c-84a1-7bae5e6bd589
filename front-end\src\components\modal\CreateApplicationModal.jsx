// src/components/modal/CreateApplicationModal.jsx

import React, { useState, useEffect } from "react";

const CreateApplicationModal = ({
  isOpen,
  onClose,
  onCreate,
  qualifications,
}) => {
  const [formData, setFormData] = useState({
    applicantName: "",
    email: "",
    phone: "",
    address: "",
    usiNumber: "",
    dateOfBirth: "",
    gender: "",
    qualificationCode: "",
    quoteNumber: "",
    leadName: "",
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (isOpen) {
      // Reset form when modal opens
      setFormData({
        applicantName: "",
        email: "",
        phone: "",
        address: "",
        usiNumber: "",
        dateOfBirth: "",
        gender: "",
        qualificationCode: "",
        quoteNumber: "",
        leadName: "",
      });
      setErrors({});
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleChange = (e) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Remove error message upon input change
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: "",
    }));
  };

  const validate = () => {
    const newErrors = {};
    if (!formData.applicantName.trim())
      newErrors.applicantName = "Applicant Name is required.";
    if (!formData.email.trim()) {
      newErrors.email = "Email is required.";
    } else {
      // Simple email regex for validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        newErrors.email = "Invalid email format.";
      }
    }
    if (!formData.phone.trim()) newErrors.phone = "Phone number is required.";
    if (!formData.address.trim()) newErrors.address = "Address is required.";
    if (!formData.usiNumber.trim())
      newErrors.usiNumber = "USI Number is required.";
    if (!formData.dateOfBirth.trim())
      newErrors.dateOfBirth = "Date of Birth is required.";
    if (!formData.gender.trim()) newErrors.gender = "Gender is required.";
    if (!formData.qualificationCode)
      newErrors.qualificationCode = "Qualification is required.";
    if (!formData.quoteNumber.trim())
      newErrors.quoteNumber = "Quote Number is required.";
    if (!formData.leadName.trim())
      newErrors.leadName = "Lead Name is required.";

    setErrors(newErrors);

    // Return true if no errors
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validate()) {
      onCreate(formData);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 overflow-auto">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl mx-4">
        <h2 className="text-2xl font-semibold mb-4">Create New Application</h2>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Applicant Name */}
            <div>
              <label
                htmlFor="applicantName"
                className="block text-gray-700 mb-2"
              >
                Applicant Name<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="applicantName"
                name="applicantName"
                value={formData.applicantName}
                onChange={handleChange}
                className={`w-full border ${
                  errors.applicantName ? "border-red-500" : "border-gray-300"
                } rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter applicant name"
              />
              {errors.applicantName && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.applicantName}
                </p>
              )}
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-gray-700 mb-2">
                Email<span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full border ${
                  errors.email ? "border-red-500" : "border-gray-300"
                } rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter email address"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email}</p>
              )}
            </div>

            {/* Phone */}
            <div>
              <label htmlFor="phone" className="block text-gray-700 mb-2">
                Phone<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className={`w-full border ${
                  errors.phone ? "border-red-500" : "border-gray-300"
                } rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter phone number"
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
              )}
            </div>

            {/* Address */}
            <div className="sm:col-span-2">
              <label htmlFor="address" className="block text-gray-700 mb-2">
                Address<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                className={`w-full border ${
                  errors.address ? "border-red-500" : "border-gray-300"
                } rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter address"
              />
              {errors.address && (
                <p className="text-red-500 text-sm mt-1">{errors.address}</p>
              )}
            </div>

            {/* USI Number */}
            <div>
              <label htmlFor="usiNumber" className="block text-gray-700 mb-2">
                USI Number<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="usiNumber"
                name="usiNumber"
                value={formData.usiNumber}
                onChange={handleChange}
                className={`w-full border ${
                  errors.usiNumber ? "border-red-500" : "border-gray-300"
                } rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter USI Number"
              />
              {errors.usiNumber && (
                <p className="text-red-500 text-sm mt-1">{errors.usiNumber}</p>
              )}
            </div>

            {/* Date of Birth */}
            <div>
              <label htmlFor="dateOfBirth" className="block text-gray-700 mb-2">
                Date of Birth<span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="dateOfBirth"
                name="dateOfBirth"
                value={formData.dateOfBirth}
                onChange={handleChange}
                className={`w-full border ${
                  errors.dateOfBirth ? "border-red-500" : "border-gray-300"
                } rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {errors.dateOfBirth && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.dateOfBirth}
                </p>
              )}
            </div>

            {/* Gender */}
            <div>
              <label htmlFor="gender" className="block text-gray-700 mb-2">
                Gender<span className="text-red-500">*</span>
              </label>
              <select
                id="gender"
                name="gender"
                value={formData.gender}
                onChange={handleChange}
                className={`w-full border ${
                  errors.gender ? "border-red-500" : "border-gray-300"
                } rounded-lg p-2 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="">Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </select>
              {errors.gender && (
                <p className="text-red-500 text-sm mt-1">{errors.gender}</p>
              )}
            </div>

            {/* Qualification */}
            <div>
              <label
                htmlFor="qualificationCode"
                className="block text-gray-700 mb-2"
              >
                Qualification<span className="text-red-500">*</span>
              </label>
              <select
                id="qualificationCode"
                name="qualificationCode"
                value={formData.qualificationCode}
                onChange={handleChange}
                className={`w-full border ${
                  errors.qualificationCode
                    ? "border-red-500"
                    : "border-gray-300"
                } rounded-lg p-2 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="">Select Qualification</option>
                {qualifications.map((q) => (
                  <option key={q.code} value={q.code}>
                    {q.name}
                  </option>
                ))}
              </select>
              {errors.qualificationCode && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.qualificationCode}
                </p>
              )}
            </div>

            {/* Quote Number */}
            <div>
              <label htmlFor="quoteNumber" className="block text-gray-700 mb-2">
                Quote Number<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="quoteNumber"
                name="quoteNumber"
                value={formData.quoteNumber}
                onChange={handleChange}
                className={`w-full border ${
                  errors.quoteNumber ? "border-red-500" : "border-gray-300"
                } rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter Quote Number"
              />
              {errors.quoteNumber && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.quoteNumber}
                </p>
              )}
            </div>

            {/* Lead Name */}
            <div>
              <label htmlFor="leadName" className="block text-gray-700 mb-2">
                Lead Name<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="leadName"
                name="leadName"
                value={formData.leadName}
                onChange={handleChange}
                className={`w-full border ${
                  errors.leadName ? "border-red-500" : "border-gray-300"
                } rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter Lead Name"
              />
              {errors.leadName && (
                <p className="text-red-500 text-sm mt-1">{errors.leadName}</p>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 mt-6">
            <button
              type="button"
              onClick={() => onClose()}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Create Application
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateApplicationModal;
