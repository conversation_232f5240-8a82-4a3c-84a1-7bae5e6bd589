package com.skillsync.applyr.core.models.entities;


import com.skillsync.applyr.core.models.enums.InvoiceSentStatus;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "xero_invoice")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class XeroInvoice {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    private String applicationId;

    private LocalDateTime invoiceDate;
    private LocalDateTime invoiceExpiryDate;
    private String source;

    private String status;

    @Enumerated(EnumType.STRING)
    private InvoiceSentStatus invoiceSentStatus;


}
