package com.skillsync.applyr.core.models.entities;

import com.skillsync.applyr.core.models.enums.LeadsStatus;
import com.skillsync.applyr.core.models.enums.Status;
import com.skillsync.applyr.modules.company.models.LeadDTO;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "leads")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class Lead extends Auditable<String> {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    private String companyName;
    private String leadName;
    private String phone;
    private String email;
    private String address;

    @Enumerated(EnumType.STRING)
    private LeadsStatus status;

    private int applicationCount;

    @OneToMany(mappedBy = "lead", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Comment> comments = new ArrayList<>();

    @ManyToMany
    @JoinTable(
            name = "lead_sales_agent",
            joinColumns = @JoinColumn(name = "lead_id"),
            inverseJoinColumns = @JoinColumn(name = "sales_agent_id")
    )
    private Set<SalesAgent> assignedAgents = new HashSet<>();

    @OneToMany(mappedBy = "lead", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Activity> activities = new ArrayList<>();

    public Lead(LeadDTO leadDTO) {
        super();
        this.companyName = leadDTO.getCompanyName();
        this.leadName = leadDTO.getLeadName();
        this.phone = leadDTO.getPhone();
        this.email = leadDTO.getEmail();
        this.address = leadDTO.getAddress();
        this.status = leadDTO.getStatus() != null ? leadDTO.getStatus() : LeadsStatus.FRESH;
        this.applicationCount = 0;

        this.assignedAgents = new HashSet<>();
        this.activities = new ArrayList<>();
        this.comments = new ArrayList<>();
    }

    public void addComment(Comment comment) {
        comments.add(comment);
        comment.setLead(this);
    }

    public void removeComment(Comment comment) {
        comments.remove(comment);
        comment.setLead(null);
    }

    public void addSalesAgent(SalesAgent salesAgent) {
        assignedAgents = new HashSet<>();
        assignedAgents.add(salesAgent);
        salesAgent.getLeads().add(this);
    }

    public void removeSalesAgent(SalesAgent salesAgent) {
        assignedAgents.remove(salesAgent);
        salesAgent.getLeads().remove(this);
    }

    // Helper methods for Activities
    public void addActivity(Activity activity) {
        activities.add(activity);
        activity.setLead(this);
    }

    public void removeActivity(Activity activity) {
        activities.remove(activity);
        activity.setLead(null);
    }
}
