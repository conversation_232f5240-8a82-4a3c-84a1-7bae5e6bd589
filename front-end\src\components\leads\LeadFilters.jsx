import React, { useRef, useEffect } from "react";
import { Fa<PERSON><PERSON>ch, Fa<PERSON>ilter, FaChevronDown, FaExpand, FaTh, FaList } from "react-icons/fa";

const LeadFilters = ({
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  salesRepFilter,
  setSalesRepFilter,
  salesRepSearch,
  setSalesRepSearch,
  isSalesRepDropdownOpen,
  setIsSalesRepDropdownOpen,
  selectedSalesReps,
  setSelectedSalesReps,
  employees,
  viewMode,
  setViewMode,
  isFullScreen,
  setIsFullScreen,
  sortField,
  setSortField,
  sortDirection,
  setSortDirection,
  isAdmin = false
}) => {
  const salesRepDropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (salesRepDropdownRef.current && !salesRepDropdownRef.current.contains(event.target)) {
        setIsSalesRepDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [setIsSalesRepDropdownOpen]);

  const handleSalesRepSelect = (rep) => {
    if (selectedSalesReps.some(selected => selected.username === rep.username)) {
      setSelectedSalesReps(selectedSalesReps.filter(selected => selected.username !== rep.username));
    } else {
      setSelectedSalesReps([...selectedSalesReps, rep]);
    }
  };

  const filteredSalesReps = employees.filter(rep =>
    rep.fullName.toLowerCase().includes(salesRepSearch.toLowerCase())
  );

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  return (
    <div className="space-y-4">
      {/* Search and View Controls */}
      <div className="flex flex-col md:flex-row items-center justify-between space-y-2 md:space-y-0">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsFullScreen(!isFullScreen)}
            className="flex items-center text-gray-700 bg-white border border-gray-300 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <FaExpand className="mr-2" />
            {isFullScreen ? "Exit Fullscreen" : "Fullscreen"}
          </button>
          
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode("grid")}
              className={`flex items-center px-3 py-1 rounded-md transition-colors ${
                viewMode === "grid" ? "bg-white shadow-sm" : "text-gray-600"
              }`}
            >
              <FaTh className="mr-1" />
              Grid
            </button>
            <button
              onClick={() => setViewMode("table")}
              className={`flex items-center px-3 py-1 rounded-md transition-colors ${
                viewMode === "table" ? "bg-white shadow-sm" : "text-gray-600"
              }`}
            >
              <FaList className="mr-1" />
              Table
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Search */}
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search leads..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
          >
            <option value="">All Status</option>
            <option value="FRESH">Fresh</option>
            <option value="COLD">Cold</option>
            <option value="WARM">Warm</option>
            <option value="HOT">Hot</option>
            <option value="CLOSED">Closed</option>
          </select>

          {/* Sales Rep Filter */}
          {isAdmin && (
            <div className="relative" ref={salesRepDropdownRef}>
              <button
                onClick={() => setIsSalesRepDropdownOpen(!isSalesRepDropdownOpen)}
                className="flex items-center justify-between border border-gray-300 rounded-lg px-3 py-2 bg-white hover:bg-gray-50 transition-colors min-w-[150px]"
              >
                <span className="text-gray-700">
                  {selectedSalesReps.length > 0 
                    ? `${selectedSalesReps.length} selected` 
                    : "All Agents"}
                </span>
                <FaChevronDown className={`ml-2 transition-transform ${isSalesRepDropdownOpen ? "rotate-180" : ""}`} />
              </button>
              
              {isSalesRepDropdownOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  <div className="p-2 border-b border-gray-200">
                    <input
                      type="text"
                      placeholder="Search agents..."
                      value={salesRepSearch}
                      onChange={(e) => setSalesRepSearch(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
                    />
                  </div>
                  
                  <div className="max-h-40 overflow-y-auto">
                    {filteredSalesReps.map((rep) => (
                      <label
                        key={rep.username}
                        className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={selectedSalesReps.some(selected => selected.username === rep.username)}
                          onChange={() => handleSalesRepSelect(rep)}
                          className="mr-3 rounded border-gray-300 text-[#6E39CB] focus:ring-[#6E39CB]"
                        />
                        <span className="text-sm text-gray-700">{rep.fullName}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Table Sorting Controls (only show in table view) */}
      {viewMode === "table" && (
        <div className="flex items-center space-x-4 text-sm">
          <span className="text-gray-600">Sort by:</span>
          
          <button
            onClick={() => handleSort('name')}
            className={`flex items-center space-x-1 px-2 py-1 rounded ${
              sortField === 'name' ? 'bg-[#6E39CB] text-white' : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <span>Name</span>
            {sortField === 'name' && (
              <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
          
          <button
            onClick={() => handleSort('phone')}
            className={`flex items-center space-x-1 px-2 py-1 rounded ${
              sortField === 'phone' ? 'bg-[#6E39CB] text-white' : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <span>Phone</span>
            {sortField === 'phone' && (
              <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
          
          <button
            onClick={() => handleSort('email')}
            className={`flex items-center space-x-1 px-2 py-1 rounded ${
              sortField === 'email' ? 'bg-[#6E39CB] text-white' : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <span>Email</span>
            {sortField === 'email' && (
              <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
          
          <button
            onClick={() => handleSort('createdAt')}
            className={`flex items-center space-x-1 px-2 py-1 rounded ${
              sortField === 'createdAt' ? 'bg-[#6E39CB] text-white' : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <span>Date</span>
            {sortField === 'createdAt' && (
              <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default LeadFilters;
