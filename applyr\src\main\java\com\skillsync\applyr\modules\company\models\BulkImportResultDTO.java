package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class BulkImportResultDTO {
    private int totalProcessed;
    private int successCount;
    private int updateCount;
    private int createCount;
    private int errorCount;
    private List<String> errors;
    private String message;

    public BulkImportResultDTO(int totalProcessed, int successCount, int updateCount, int createCount, int errorCount, List<String> errors) {
        this.totalProcessed = totalProcessed;
        this.successCount = successCount;
        this.updateCount = updateCount;
        this.createCount = createCount;
        this.errorCount = errorCount;
        this.errors = errors;
        this.message = String.format("Import completed: %d total, %d successful (%d created, %d updated), %d errors", 
                                    totalProcessed, successCount, createCount, updateCount, errorCount);
    }
}
