package com.skillsync.applyr.modules.sales.models;


import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.models.entities.SoldQualifications;
import com.skillsync.applyr.core.models.enums.RequestStatus;
import com.skillsync.applyr.core.models.enums.Status;
import com.skillsync.applyr.modules.company.models.LeadDTO;
import com.skillsync.applyr.modules.company.models.ProfileDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class QuoteRequestDTO {
    private String applicationId;
    private Status applicationStatus;
    private ProfileDTO salesAgent;
    private String clientName;
    private String clientEmail;
    private String clientPhone;

    private String candidateName;
    private String candidatePhone;
    private String candidateEmail;
    private String otherInformation;

    private double price;

    private String quoteRequestDetails;

    private String quoteRefNumber;
    private RequestStatus quoteStatus;

    private String invoiceRefNumber;
    private RequestStatus invoiceStatus;

    private LocalDateTime createdAt;





    public QuoteRequestDTO(Application application, ProfileDTO agent, LeadDTO lead) {
        this.applicationId = application.getApplicationId();
        this.applicationStatus = application.getStatus();
        this.salesAgent = agent;
        this.clientName = lead.getLeadName();
        this.clientEmail = lead.getEmail();
        this.clientPhone = lead.getPhone();

        this.candidateName = application.getApplicantName();
        this.candidatePhone = application.getApplicantPhone();
        this.candidateEmail = application.getApplicantEmail();
        if(application.getOtherInformation() == null) {
            this.otherInformation = "N/A";
        } else {
            this.otherInformation = application.getOtherInformation();
        }

        this.price = application.getPrice();

        setQuoteRequestDetails(application.getSoldQualificationsList());

        this.quoteRefNumber = application.getQuoteRefNumber();
        this.quoteStatus = application.getQuoteStatus();
        this.invoiceRefNumber = application.getInvoiceRefNumber();
        this.invoiceStatus = application.getInvoiceStatus();
        this.createdAt = application.getCreatedDate();

    }

    private void setQuoteRequestDetails(List<SoldQualifications> soldQualifications) {
        StringBuilder quoteRequestDetailsBuilder = new StringBuilder();
        int size = soldQualifications.size();

        for (int i = 0; i < size; i++) {
            SoldQualifications soldQualification = soldQualifications.get(i);
            String qualificationString = soldQualification.getQualification().getQualificationId() + " - " +
                    soldQualification.getQualification().getQualificationName() + " ($" +
                    soldQualification.getSoldPrice() + ")";

            if (i == size - 2) {
                quoteRequestDetailsBuilder.append(qualificationString).append(" and ");
            } else if (i == size - 1) {
                quoteRequestDetailsBuilder.append(qualificationString);
            } else {
                quoteRequestDetailsBuilder.append(qualificationString).append(", ");
            }
        }

        this.quoteRequestDetails = quoteRequestDetailsBuilder.toString();
    }
}
