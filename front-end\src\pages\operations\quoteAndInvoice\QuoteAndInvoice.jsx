import React, { useState, useEffect, useRef } from "react";
import QuoteCard from "../../../components/card/QuoteCard";
import {
  useGetAllQuoteRequestsQuery,
  useGetDraftedQuoteRequestsQuery,
  useGetRequestedQuoteRequestsQuery
} from "../../../services/CompanyAPIService";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faFilter, faKeyboard } from "@fortawesome/free-solid-svg-icons";

const QuoteAndInvoice = () => {
  // Tab states and filter states
  const [selectedTab, setSelectedTab] = useState("requests"); // "requests", "drafted", "all"
  const [search, setSearch] = useState("");
  const [refNumberSearch, setRefNumberSearch] = useState(""); // New search for quote/invoice numbers
  const [selectedAgentFilter, setSelectedAgentFilter] = useState("");
  const [selectedStatusFilter, setSelectedStatusFilter] = useState("");
  const [dateFilterType, setDateFilterType] = useState("today"); // Changed default to today
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  // Only using card view
  const [showFilters, setShowFilters] = useState(true);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const searchInputRef = useRef(null);
  const refNumberSearchInputRef = useRef(null);

  // State to store the current data
  const [quoteRequests, setQuoteRequests] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get the query hooks
  const allQuotesQuery = useGetAllQuoteRequestsQuery();
  const draftedQuotesQuery = useGetDraftedQuoteRequestsQuery();
  const requestedQuotesQuery = useGetRequestedQuoteRequestsQuery();

  // Function to completely reset and fetch data
  const resetAndFetch = async () => {
    // Clear the current data
    setQuoteRequests([]);
    setIsLoading(true);

    try {
      let result;

      // Fetch fresh data based on the selected tab
      if (selectedTab === "all") {
        // Force a refetch by using the refetch method
        await allQuotesQuery.refetch();
        result = allQuotesQuery.data;
      } else if (selectedTab === "drafted") {
        await draftedQuotesQuery.refetch();
        result = draftedQuotesQuery.data;
      } else if (selectedTab === "requests") {
        await requestedQuotesQuery.refetch();
        result = requestedQuotesQuery.data;
      }

      // Update the state with the new data
      setQuoteRequests(result || []);
    } catch (error) {
      console.error("Error fetching data:", error);
      setQuoteRequests([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to reset and fetch data when tab changes
  useEffect(() => {
    resetAndFetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTab]);

  // Date filter change logic for custom date selection
  const handleDateFilterChange = (type) => {
    setDateFilterType(type);
    if (type === "custom") {
      setStartDate("");
      setEndDate("");
    }
  };

  // Function to get date range based on filter type
  const getDateRange = () => {
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));

    switch (dateFilterType) {
      case "today":
        return {
          start: startOfDay,
          end: new Date(today.setHours(23, 59, 59, 999))
        };
      case "thisWeek": {
        const start = new Date(today);
        start.setDate(start.getDate() - start.getDay()); // Start of week (Sunday)
        start.setHours(0, 0, 0, 0);

        const end = new Date(start);
        end.setDate(end.getDate() + 6); // End of week (Saturday)
        end.setHours(23, 59, 59, 999);

        return { start, end };
      }
      case "lastWeek": {
        const start = new Date(today);
        start.setDate(start.getDate() - start.getDay() - 7); // Start of last week
        start.setHours(0, 0, 0, 0);

        const end = new Date(start);
        end.setDate(end.getDate() + 6); // End of last week
        end.setHours(23, 59, 59, 999);

        return { start, end };
      }
      case "thisMonth": {
        const start = new Date(today.getFullYear(), today.getMonth(), 1);
        const end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        end.setHours(23, 59, 59, 999);

        return { start, end };
      }
      case "lastMonth": {
        const start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const end = new Date(today.getFullYear(), today.getMonth(), 0);
        end.setHours(23, 59, 59, 999);

        return { start, end };
      }
      case "custom":
        return {
          start: startDate ? new Date(startDate) : null,
          end: endDate ? new Date(endDate + "T23:59:59.999") : null
        };
      default:
        return { start: null, end: null };
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl/Cmd + / to show keyboard shortcuts
      if ((e.ctrlKey || e.metaKey) && e.key === "/") {
        e.preventDefault();
        setShowKeyboardShortcuts(prev => !prev);
      }
      // Ctrl/Cmd + F to focus search
      else if ((e.ctrlKey || e.metaKey) && e.key === "f") {
        e.preventDefault();
        searchInputRef.current?.focus();
      }
      // No view mode toggle needed
      // Escape to clear search
      else if (e.key === "Escape" && document.activeElement === searchInputRef.current) {
        e.preventDefault();
        setSearch("");
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Make a copy of the quotes to avoid mutation issues
  const quotes = quoteRequests ? [...quoteRequests] : [];

  // Apply all other filters (no need for tab filtering since we're using separate API endpoints)
  const filteredQuotes = quotes.filter(quote => {
    // Search filter: checking in clientName, candidateName, or salesAgent.fullName
    const searchTerm = search.toLowerCase();
    if (
      searchTerm &&
      !(
        (quote.clientName || "").toLowerCase().includes(searchTerm) ||
        (quote.candidateName || "").toLowerCase().includes(searchTerm) ||
        (quote.salesAgent?.fullName || "").toLowerCase().includes(searchTerm)
      )
    ) {
      return false;
    }

    // Quote/Invoice number search
    const refSearchTerm = refNumberSearch.toLowerCase();
    if (
      refSearchTerm &&
      !(
        ((quote.quoteRefNumber || "").toLowerCase().includes(refSearchTerm)) ||
        ((quote.invoiceRefNumber || "").toLowerCase().includes(refSearchTerm))
      )
    ) {
      return false;
    }

    // Agent filter
    if (selectedAgentFilter && quote.salesAgent?.fullName !== selectedAgentFilter) {
      return false;
    }

    // Status filter (checks both quote and invoice statuses)
    if (
      selectedStatusFilter &&
      quote.quoteStatus !== selectedStatusFilter &&
      quote.invoiceStatus !== selectedStatusFilter
    ) {
      return false;
    }

    // Date filter
    if (dateFilterType !== "all") {
      const { start, end } = getDateRange();
      const createdAt = quote.createdAt ? new Date(quote.createdAt) : null;

      if (createdAt && start && end) {
        if (createdAt < start || createdAt > end) {
          return false;
        }
      } else if (dateFilterType === "custom" && (startDate || endDate)) {
        // If custom date range is selected but the quote has no date, filter it out
        if (!createdAt) return false;
      }
    }

    return true;
  })
  // Finally, sort by createdAt date (newest first)
  .sort((a, b) => {
    const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
    const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
    return dateB - dateA; // Descending order (newest first)
  });

  if (isLoading) return <div className="p-4 text-center">Loading...</div>;
  if (!quoteRequests) return <div className="p-4 text-center">No data available</div>;

  // Simplified debugging
  console.log(`Tab: ${selectedTab}, Filtered Quotes: ${filteredQuotes.length}`);

  // Log a sample quote if available
  if (quotes.length > 0) {
    const sampleQuote = quotes[0];
    console.log("Sample Quote Properties:", {
      quoteRefNumber: sampleQuote.quoteRefNumber,
      quoteStatus: sampleQuote.quoteStatus,
      invoiceStatus: sampleQuote.invoiceStatus,
      applicationId: sampleQuote.applicationId
    });
  }

  return (
    <div className="container mx-auto p-4">
      {/* Header with action buttons */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-xl font-semibold">Quote & Invoice Requests</h1>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-md transition-colors ${showFilters ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'}`}
            title="Toggle filters"
          >
            <FontAwesomeIcon icon={faFilter} />
          </button>
          <button
            onClick={() => setShowKeyboardShortcuts(!showKeyboardShortcuts)}
            className={`p-2 rounded-md transition-colors ${showKeyboardShortcuts ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'}`}
            title="Keyboard shortcuts"
          >
            <FontAwesomeIcon icon={faKeyboard} />
          </button>

        </div>
      </div>

      {/* Search bars - always visible and prominent */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
          </div>
          <input
            ref={searchInputRef}
            type="text"
            value={search}
            placeholder="Search by client, candidate or agent name... (Ctrl+F)"
            onChange={(e) => setSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
          />
          {search && (
            <button
              onClick={() => setSearch('')}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          )}
        </div>

        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
          </div>
          <input
            ref={refNumberSearchInputRef}
            type="text"
            value={refNumberSearch}
            placeholder="Search by quote or invoice number..."
            onChange={(e) => setRefNumberSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
          />
          {refNumberSearch && (
            <button
              onClick={() => setRefNumberSearch('')}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          )}
        </div>
      </div>

      {/* Tabs - simplified */}
      <div className="flex mb-4 bg-gray-100 rounded-lg p-1">
        <button
          className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${selectedTab === "requests" ? "bg-white shadow-sm text-blue-600" : "text-gray-600 hover:text-gray-800"}`}
          onClick={() => {
            // First set the tab
            setSelectedTab("requests");
            // Reset and fetch will happen automatically via useEffect
          }}
        >
          Requests
        </button>
        <button
          className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${selectedTab === "drafted" ? "bg-white shadow-sm text-blue-600" : "text-gray-600 hover:text-gray-800"}`}
          onClick={() => {
            // First set the tab
            setSelectedTab("drafted");
            // Reset and fetch will happen automatically via useEffect
          }}
        >
          Drafted
        </button>
        <button
          className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${selectedTab === "all" ? "bg-white shadow-sm text-blue-600" : "text-gray-600 hover:text-gray-800"}`}
          onClick={() => {
            // First set the tab
            setSelectedTab("all");
            // Reset and fetch will happen automatically via useEffect
          }}
        >
          All
        </button>
      </div>

      {/* Collapsible Filters */}
      {showFilters && (
        <div className="bg-white rounded-lg shadow-sm p-3 mb-4 border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
            <select
              value={selectedAgentFilter}
              onChange={(e) => setSelectedAgentFilter(e.target.value)}
              className="p-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Agents</option>
              <option value="Quenne">Quenne</option>
              <option value="Anjal Karki">Anjal Karki</option>
            </select>

            <select
              value={selectedStatusFilter}
              onChange={(e) => setSelectedStatusFilter(e.target.value)}
              className="p-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="SENT">Sent</option>
              <option value="DRAFTED">Drafted</option>
              <option value="ACCEPTED">Accepted</option>
            </select>

            <select
              value={dateFilterType}
              onChange={(e) => handleDateFilterChange(e.target.value)}
              className="p-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Dates</option>
              <option value="today">Today</option>
              <option value="thisWeek">This Week</option>
              <option value="lastWeek">Last Week</option>
              <option value="thisMonth">This Month</option>
              <option value="lastMonth">Last Month</option>
              <option value="custom">Custom Date</option>
            </select>

            {dateFilterType === "custom" && (
              <div className="flex items-center space-x-2">
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="border border-gray-300 rounded-md p-2 text-sm flex-1 focus:ring-blue-500 focus:border-blue-500"
                />
                <span className="text-gray-500">to</span>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="border border-gray-300 rounded-md p-2 text-sm flex-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Keyboard shortcuts modal */}
      {showKeyboardShortcuts && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50" onClick={() => setShowKeyboardShortcuts(false)}>
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full" onClick={e => e.stopPropagation()}>
            <h3 className="text-lg font-semibold mb-4">Keyboard Shortcuts</h3>
            <div className="grid grid-cols-2 gap-y-2 text-sm">
              <div className="font-medium">Ctrl+F</div>
              <div>Focus search</div>

              <div className="font-medium">Ctrl+/</div>
              <div>Show/hide shortcuts</div>
              <div className="font-medium">Esc</div>
              <div>Clear search</div>
            </div>
            <button
              className="mt-4 w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium"
              onClick={() => setShowKeyboardShortcuts(false)}
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Card View */}
        <div className="grid grid-cols-1 gap-4">
          {filteredQuotes.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center text-gray-500 border border-gray-200">
              No quotes found matching your criteria
            </div>
          ) : (
            filteredQuotes.map((quote) => (
              <QuoteCard
                key={quote.quoteRefNumber || quote.applicationId}
                onQuoteUpdated={resetAndFetch} // Pass the resetAndFetch function
                applicationId={quote.applicationId}
                agentName={quote.salesAgent.fullName}
                clientName={quote.clientName}
                clientEmail={quote.clientEmail}
                clientPhone={quote.clientPhone}
                candidateName={quote.candidateName}
                phoneNumber={quote.candidatePhone}
                email={quote.candidateEmail}
                quoteRequestDetail={quote.quoteRequestDetails}
                price={`$${quote.price}`}
                otherInformation={quote.otherInformation}
                quoteNumber={quote.quoteRefNumber}
                quoteStatus={quote.quoteStatus}
                invoiceNumber={quote.invoiceRefNumber}
                invoiceStatus={quote.invoiceStatus}
                quoteRefNumber={quote.quoteRefNumber}
              />
            ))
          )}
        </div>
    </div>
  );
};

export default QuoteAndInvoice;
