import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft } from "@fortawesome/free-solid-svg-icons";

const FileStatusHeader = ({ navigate, fileStatus }) => {
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between">
      <div className="flex items-center mb-4 md:mb-0">
        <button
          onClick={() => navigate(-1)}
          className="mr-4 p-2 rounded-full text-gray-500 hover:bg-gray-100 transition-colors"
        >
          <FontAwesomeIcon icon={faArrowLeft} />
        </button>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">File Status Details</h1>
          <p className="text-sm text-gray-500">
            {fileStatus.application?.applicantName || "N/A"} - {fileStatus.qualificationName || "N/A"}
          </p>
        </div>
      </div>
      
      <div className="flex items-center">
        <div className="px-3 py-1 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
          {formatFileStatus(fileStatus.fileStatus)}
        </div>
      </div>
    </div>
  );
};

// Helper function to format file status for display
const formatFileStatus = (status) => {
  if (!status) return "Status Unavailable";
  return status.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, (c) => c.toUpperCase());
};

export default FileStatusHeader;
