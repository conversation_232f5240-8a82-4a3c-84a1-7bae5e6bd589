import React, { useState } from "react";
import { useChangeApplicationStatusMutation } from "../../services/CompanyAPIService";

const ApplicationStatusModal = ({ isOpen, onClose, applicationId, currentStatus, onSuccess }) => {
  const [status, setStatus] = useState(currentStatus || "DOCUMENT_PENDING");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // Use the mutation hook
  const [changeApplicationStatus, { isLoading }] = useChangeApplicationStatusMutation();

  if (!isOpen) return null;

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setError("");

    try {
      await changeApplicationStatus({
        applicationId,
        status
      }).unwrap();

      // Call onSuccess callback if provided
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess();
      }

      // Close the modal after successful update
      onClose();
    } catch (err) {
      console.error(`Failed to update application status:`, err);
      setError(err.data?.message || `Failed to update application status. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white w-full max-w-md p-6 rounded shadow-lg">
        <div className="sticky top-0 bg-white z-10 p-4">
          <h2 className="text-xl font-bold mb-4">
            Update Application Status
          </h2>
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
        </div>

        {/* Application ID Display */}
        <div className="mb-4">
          <label className="block font-semibold text-sm mb-1">
            Application ID
          </label>
          <div className="p-2 bg-gray-100 rounded border border-gray-200">
            {applicationId}
          </div>
        </div>

        {/* Status Selection */}
        <div className="mb-4">
          <label className="block font-semibold text-sm mb-1">Status</label>
          <select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            className="border p-2 rounded w-full"
          >
            <option value="DOCUMENT_PENDING">Document Pending</option>
            <option value="QUOTE_RAISED">Quote Raised</option>
            <option value="INVOICE_RAISED">Invoice Raised</option>
            <option value="IN_PROGRESS">In Progress</option>
            <option value="SOFT_COPY_READY">Soft Copy Ready</option>
            <option value="SOFT_COPY_SENT">Soft Copy Sent</option>
            <option value="HARD_COPY_READY">Hard Copy Ready</option>
            <option value="HARD_COPY_SENT">Hard Copy Sent</option>
            <option value="CLOSED">Closed</option>
            <option value="FALLOUT">Fallout</option>
          </select>
        </div>

        <div className="flex justify-end mt-4">
          <button
            onClick={() => onClose()}
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded mr-2"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || isSubmitting}
            className={`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded ${
              isLoading || isSubmitting ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isLoading || isSubmitting ? "Processing..." : "Update Status"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ApplicationStatusModal;
