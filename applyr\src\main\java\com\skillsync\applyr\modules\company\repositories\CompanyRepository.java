package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.Company;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface CompanyRepository extends JpaRepository<Company, Long> {
    Optional<Company> getCompanyByUserUsername(String username);

    Optional<Company> getCompanyByFullNameContaining(String word);
}
