// components/modal/UpdateWeeklyTargetModal.jsx

import React, { useState } from "react";
import { useUpdateWeeklyTargetMutation } from "../../services/AdminAPIService";

const UpdateWeeklyTargetModal = ({ employee, onClose }) => {
  // We assume `employee` includes a property `weeklyTarget` from the server.
  // If not, you can fetch it separately or remove references.
  const [newTarget, setNewTarget] = useState(employee.weeklyTarget || 0);

  // The RTK Query mutation hook
  const [updateWeeklyTarget, { isLoading, isError, error }] = useUpdateWeeklyTargetMutation();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await updateWeeklyTarget({ username: employee.username, amount: newTarget }).unwrap();
      // After successful update, close the modal (RTK Query will auto-re-fetch if set up)
      onClose();
    } catch (err) {
      // If there's any error, it will be handled in isError / error
      console.error("Error updating weekly target:", err);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white p-6 rounded-md shadow-md w-full max-w-md relative">
        <h2 className="text-xl font-bold mb-4">Update Weekly Target</h2>
        <p className="mb-2">
          <strong>Employee:</strong> {employee.fullName} ({employee.username})
        </p>
        {isError && (
          <div className="text-red-500 mb-2">
            {error?.data?.message || error?.message || "Error updating target."}
          </div>
        )}
        <form onSubmit={handleSubmit}>
          <label className="block mb-2">
            Current Target: <strong>$ {employee.weeklyTarget ?? "Not Set"}</strong>
          </label>
          <label className="block mb-4">
            New Target:
            <input
              type="number"
              className="border border-gray-300 rounded-md p-2 w-full mt-1"
              value={newTarget}
              onChange={(e) => setNewTarget(e.target.value)}
            />
          </label>

          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={onClose}
              disabled={isLoading}
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
            >
              {isLoading ? "Updating..." : "Update"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateWeeklyTargetModal;
