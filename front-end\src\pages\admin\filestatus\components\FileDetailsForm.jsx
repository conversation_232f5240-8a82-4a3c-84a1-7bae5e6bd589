import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSave, faSpinner } from "@fortawesome/free-solid-svg-icons";
import { useUpdateFileDetailsMutation } from "../../../../services/CompanyAPIService";

const FileDetailsForm = ({ fileStatus, onSubmit, isLoading, showToast }) => {
  const [formData, setFormData] = useState({
    fileSource: "",
    visaStatus: "",
    hardCopyTrackingNumber: ""
  });

  // API mutation
  const [updateFileDetails, { isLoading: isUpdatingDetails }] = useUpdateFileDetailsMutation();

  // Initialize form with existing data
  useEffect(() => {
    if (fileStatus) {
      setFormData({
        fileSource: fileStatus.fileSource || "",
        visaStatus: fileStatus.visaStatus || "",
        hardCopyTrackingNumber: fileStatus.hardCopyTrackingNumber || ""
      });
    }
  }, [fileStatus]);

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      await updateFileDetails({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        ...formData
      }).unwrap();

      showToast("File details updated successfully");
    } catch (error) {
      showToast("Failed to update file details", "error");
      console.error("Error updating file details:", error);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">File Details</h3>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* File Source */}
          <div className="flex flex-col">
            <label htmlFor="fileSource" className="text-sm font-medium text-gray-500 mb-1">
              File Source:
            </label>
            <input
              type="text"
              id="fileSource"
              name="fileSource"
              value={formData.fileSource}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              placeholder="Enter file source"
            />
          </div>

          {/* Visa Status */}
          <div className="flex flex-col">
            <label htmlFor="visaStatus" className="text-sm font-medium text-gray-500 mb-1">
              Visa Status:
            </label>
            <input
              type="text"
              id="visaStatus"
              name="visaStatus"
              value={formData.visaStatus}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              placeholder="Enter visa status"
            />
          </div>

          {/* Hard Copy Tracking Number */}
          <div className="flex flex-col md:col-span-2">
            <label htmlFor="hardCopyTrackingNumber" className="text-sm font-medium text-gray-500 mb-1">
              Hard Copy Tracking Number:
            </label>
            <input
              type="text"
              id="hardCopyTrackingNumber"
              name="hardCopyTrackingNumber"
              value={formData.hardCopyTrackingNumber}
              onChange={handleChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              placeholder="Enter tracking number"
            />
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <button
            type="submit"
            disabled={isLoading || isUpdatingDetails}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#6E39CB] hover:bg-[#5E2CB8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB] disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading || isUpdatingDetails ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                Updating...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="mr-2" />
                Update Details
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default FileDetailsForm;
