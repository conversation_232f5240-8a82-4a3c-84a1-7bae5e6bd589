package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.models.entities.PaymentInstallment;
import com.skillsync.applyr.core.models.entities.XeroInBank;
import com.skillsync.applyr.core.models.entities.XeroInvoice;
import com.skillsync.applyr.core.models.enums.InvoiceSentStatus;
import com.skillsync.applyr.core.models.enums.PaidStatus;
import com.skillsync.applyr.core.models.enums.Status;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.company.models.ApplicationResponseDTO;
import com.skillsync.applyr.modules.company.models.KPI1XeroDTO;
import com.skillsync.applyr.modules.company.models.KPI2XeroDTO;
import com.skillsync.applyr.modules.company.repositories.ApplicationRepository;
import com.skillsync.applyr.modules.company.repositories.XeroInBankRepository;
import com.skillsync.applyr.modules.company.repositories.XeroInvoiceRepository;
import com.skillsync.applyr.modules.sales.repositories.PaymentInstallmentRepository;
import org.apache.poi.ss.usermodel.*;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class XeroDataService {

    private final XeroInvoiceRepository xeroInvoiceRepository;
    private final ApplicationRepository applicationRepository;
    private final ApplicationService applicationService;
    private final XeroInBankRepository xeroInBankRepository;
    private final PaymentInstallmentRepository paymentInstallmentRepository;

    public XeroDataService(XeroInvoiceRepository xeroInvoiceRepository, ApplicationRepository applicationRepository,
                           ApplicationService applicationService, XeroInBankRepository xeroInBankRepository, PaymentInstallmentRepository paymentInstallmentRepository) {
        this.xeroInvoiceRepository = xeroInvoiceRepository;
        this.applicationRepository = applicationRepository;
        this.applicationService = applicationService;
        this.xeroInBankRepository = xeroInBankRepository;
        this.paymentInstallmentRepository = paymentInstallmentRepository;
    }

    public List<KPI1XeroDTO> getAllXeroKPI1Data() {
        List<XeroInvoice> xeroInvoices = xeroInvoiceRepository.findAll();
        List<KPI1XeroDTO> kpi1XeroDTOs = new ArrayList<>();
        for (XeroInvoice xeroInvoice : xeroInvoices) {
            ApplicationResponseDTO appDTO = applicationService.getSingleApplications(xeroInvoice.getApplicationId());
            KPI1XeroDTO dto = new KPI1XeroDTO();
            dto.setApplication(appDTO);
            dto.setInvoiceDate(xeroInvoice.getInvoiceDate());
            dto.setInvoiceExpiryDate(xeroInvoice.getInvoiceExpiryDate());
            dto.setSource(xeroInvoice.getSource());
            dto.setStatus(xeroInvoice.getStatus());
            dto.setInvoiceStatus(xeroInvoice.getInvoiceSentStatus());
            kpi1XeroDTOs.add(dto);
        }
        return kpi1XeroDTOs;
    }

    public List<KPI2XeroDTO> getAllXeroKPI2Data() {
        List<XeroInBank> xeroInBanks = xeroInBankRepository.findAll();
        List<KPI2XeroDTO> kpi2XeroDTOs = new ArrayList<>();
        
        for (XeroInBank xeroInBank : xeroInBanks) {
            ApplicationResponseDTO appDTO = applicationService.getSingleApplications(xeroInBank.getApplicationId());
            KPI2XeroDTO dto = new KPI2XeroDTO();
            dto.setApplicationResponse(appDTO);
            dto.setDateInserted(xeroInBank.getInsertDate());
            dto.setReference(xeroInBank.getReference());
            dto.setDescription(xeroInBank.getDescription());
            dto.setDebitAmount(xeroInBank.getDebitAmount());
            dto.setCreditAmount(xeroInBank.getCreditAmount());
            dto.setNetAmount(xeroInBank.getNetAmount());
            dto.setSource(xeroInBank.getSource());
            kpi2XeroDTOs.add(dto);
        }
        
        return kpi2XeroDTOs;
    }

    public SuccessResponse uploadXeroInvoiceData(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("Uploaded file is empty");
        }

        String fileName = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));
        List<String[]> rows = new ArrayList<>();

        try {
            // Determine file type and process accordingly
            if (fileName.toLowerCase().endsWith(".csv")) {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    boolean isHeader = true;
                    while ((line = reader.readLine()) != null) {
                        // Skip header line
                        if (isHeader) {
                            isHeader = false;
                            continue;
                        }
                        // Split on comma (adjust if your CSV values may contain commas)
                        String[] columns = line.split(",");
                        rows.add(columns);
                    }
                }
            } else if (fileName.toLowerCase().endsWith(".xls") || fileName.toLowerCase().endsWith(".xlsx")) {
                // Use Apache POI to parse Excel files
                Workbook workbook = WorkbookFactory.create(file.getInputStream());
                Sheet sheet = workbook.getSheetAt(0);
                boolean isHeader = true;
                for (Row row : sheet) {
                    if (isHeader) {
                        isHeader = false;
                        continue;
                    }
                    int lastCell = row.getLastCellNum();
                    String[] columns = new String[lastCell];
                    for (int i = 0; i < lastCell; i++) {
                        Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        // Check the cell type before converting
                        if (cell.getCellType() == CellType.NUMERIC) {
                            if (DateUtil.isCellDateFormatted(cell)) {
                                // Convert the numeric cell to a formatted date string using the "dd MMM yyyy" pattern
                                LocalDate date = cell.getDateCellValue().toInstant()
                                        .atZone(ZoneId.systemDefault()).toLocalDate();
                                columns[i] = date.format(DateTimeFormatter.ofPattern("dd MMM yyyy"));
                            } else {
                                // It is a numeric value that is not a date, so convert to string
                                columns[i] = String.valueOf(cell.getNumericCellValue());
                            }
                        } else {
                            columns[i] = cell.getStringCellValue();
                        }
                    }
                    rows.add(columns);
                }
                workbook.close();
            } else {
                throw new AppRTException("Unsupported file type. Only CSV and Excel files are supported.", HttpStatus.BAD_REQUEST);
            }

            // Prepare DateTimeFormatter to parse "dd MMM yyyy" (e.g., "03 Mar 2025")
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");

            // Process each row; columns are expected in the order:
            // 0: Invoice Number, 1: Contact, 2: Invoice Date, 3: Due Date,
            // 4: Reference, 5: Gross, 6: Balance, 7: Status, 8: Source,
            // 9: Invoice Sent, 10: Agent Commission

            for (String[] cols : rows) {

                if (cols.length < 11) {
                    // Log an error or skip if the number of columns is insufficient
                    continue;
                }



                String invoiceNumber = cols[0].trim();
                String contact = cols[1].trim();
                String invoiceDateStr = cols[2].trim();
                String dueDateStr = cols[3].trim();
                String reference = cols[4].trim();
                // Columns "Gross", "Balance" can be parsed if needed:
                // String grossStr = cols[5].trim();
                // String balanceStr = cols[6].trim();
                String statusFromFile = cols[7].trim();
                String source = cols[8].trim();
                String invoiceSentStr = cols[9].trim();
                // "Agent Commission" in cols[10] can be ignored per current requirements

                LocalDateTime invoiceDate = LocalDate.parse(invoiceDateStr, formatter).atStartOfDay();
                LocalDateTime dueDate = LocalDate.parse(dueDateStr, formatter).atStartOfDay();

                // Find the application using the invoice reference (assumed to match invoiceRefNumber)
                Optional<Application> application = applicationRepository.findByInvoiceRefNumber(invoiceNumber);
                if (application.isPresent()) {
                    application.get().setStatus(Status.IN_PROGRESS);
                    applicationRepository.save(application.get());
                } else {
                    continue;
                }

                Optional<XeroInvoice> xeroInvoice = xeroInvoiceRepository.findByApplicationId(application.get().getApplicationId());

                if (xeroInvoice.isPresent()) {
                    xeroInvoice.get().setInvoiceDate(invoiceDate);
                    xeroInvoice.get().setApplicationId(application.get().getApplicationId());
                    xeroInvoice.get().setInvoiceExpiryDate(dueDate);
                    xeroInvoice.get().setSource(source);
                    xeroInvoice.get().setStatus(statusFromFile);

                    // Convert the Invoice Sent column to the InvoiceSentStatus enum.
                    // Assumes the enum names match the string values (after converting to upper case).
                    try {
                        InvoiceSentStatus invoiceSentStatus = InvoiceSentStatus.valueOf(invoiceSentStr.trim().toUpperCase());
                        xeroInvoice.get().setInvoiceSentStatus(invoiceSentStatus);
                    } catch (IllegalArgumentException e) {
                        xeroInvoice.get().setInvoiceSentStatus(InvoiceSentStatus.SENT);
                    }
                    xeroInvoiceRepository.save(xeroInvoice.get());
                } else {
                    // Create and populate a new XeroInvoice
                    XeroInvoice newXeroInvoice = new XeroInvoice();
                    newXeroInvoice.setApplicationId(application.get().getApplicationId());
                    newXeroInvoice.setInvoiceDate(invoiceDate);
                    newXeroInvoice.setInvoiceExpiryDate(dueDate);
                    newXeroInvoice.setSource(source);
                    newXeroInvoice.setStatus(statusFromFile);

                    // Convert the Invoice Sent column to the InvoiceSentStatus enum.
                    // Assumes the enum names match the string values (after converting to upper case).
                    try {
                        InvoiceSentStatus invoiceSentStatus = InvoiceSentStatus.valueOf(invoiceSentStr.trim().toUpperCase());
                        newXeroInvoice.setInvoiceSentStatus(invoiceSentStatus);
                    } catch (IllegalArgumentException e) {
                        newXeroInvoice.setInvoiceSentStatus(InvoiceSentStatus.SENT);
                    }
                    System.out.println(newXeroInvoice);
                    xeroInvoiceRepository.save(newXeroInvoice);
                }
            }

            return new SuccessResponse("Successfully uploaded Xero Invoice data");

        } catch (Exception e) {
            throw new AppRTException("Failed to parse and upload CSV/Excel file: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse uploadXeroInBankData(MultipartFile file) {

        if (file.isEmpty()) {
            throw new IllegalArgumentException("Uploaded file is empty");
        }

        String fileName = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));
        List<String[]> rows = new ArrayList<>();

        try {
            // Determine file type and process accordingly
            if (fileName.toLowerCase().endsWith(".csv")) {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    boolean isHeader = true;
                    while ((line = reader.readLine()) != null) {
                        // Skip header line
                        if (isHeader) {
                            isHeader = false;
                            continue;
                        }
                        // Split on comma (adjust if your CSV values may contain commas)
                        String[] columns = line.split(",");
                        rows.add(columns);
                    }
                }
            } else if (fileName.toLowerCase().endsWith(".xls") || fileName.toLowerCase().endsWith(".xlsx")) {
                // Use Apache POI to parse Excel files
                Workbook workbook = WorkbookFactory.create(file.getInputStream());
                Sheet sheet = workbook.getSheetAt(0);
                boolean isHeader = true;
                for (Row row : sheet) {
                    if (isHeader) {
                        isHeader = false;
                        continue;
                    }
                    int lastCell = row.getLastCellNum();
                    String[] columns = new String[lastCell];
                    for (int i = 0; i < lastCell; i++) {
                        Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        // Check the cell type before converting
                        if (cell.getCellType() == CellType.NUMERIC) {
                            if (DateUtil.isCellDateFormatted(cell)) {
                                // Convert the numeric cell to a formatted date string using the "dd MMM yyyy" pattern
                                LocalDate date = cell.getDateCellValue().toInstant()
                                        .atZone(ZoneId.systemDefault()).toLocalDate();
                                columns[i] = date.format(DateTimeFormatter.ofPattern("dd MMM yyyy"));
                            } else {
                                // It is a numeric value that is not a date, so convert to string
                                columns[i] = String.valueOf(cell.getNumericCellValue());
                            }
                        } else {
                            columns[i] = cell.getStringCellValue();
                        }
                    }
                    rows.add(columns);
                }
                workbook.close();
            } else {
                throw new AppRTException("Unsupported file type. Only CSV and Excel files are supported.", HttpStatus.BAD_REQUEST);
            }

            // Prepare DateTimeFormatter to parse "dd MMM yyyy" (e.g., "03 Mar 2025")
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");

            for (String[] cols : rows) {
                if (cols.length < 9) {
                    continue;
                }

                String paymentDateStr = cols[0].trim();
                String reference = cols[2].trim();
                String description = cols[4].trim();
                String debitAmountStr = cols[5].trim();
                String creditAmountStr = cols[6].trim();
                String netAmountStr = cols[7].trim();
                String source = cols[8].trim();

                LocalDateTime paymentDate = LocalDate.parse(paymentDateStr, formatter).atStartOfDay();
                double debitAmount = Double.parseDouble(debitAmountStr);
                double creditAmount = Double.parseDouble(creditAmountStr);
                double netAmount = Double.parseDouble(netAmountStr);

                Optional<XeroInBank> xeroInBank = xeroInBankRepository.findByReference(reference);

                if (xeroInBank.isPresent()
                        && Objects.equals(xeroInBank.get().getInsertDate(), paymentDate)
                        && xeroInBank.get().getDebitAmount() == debitAmount
                        && xeroInBank.get().getCreditAmount() == creditAmount
                        && xeroInBank.get().getNetAmount() == netAmount) {

                } else {
                    Optional<Application> application = applicationRepository.findByInvoiceRefNumber(reference);

                    if (application.isPresent()) {
                        XeroInBank newXeroInBank = new XeroInBank();
                        newXeroInBank.setApplicationId(application.get().getApplicationId());
                        newXeroInBank.setInsertDate(paymentDate);
                        newXeroInBank.setReference(reference);
                        newXeroInBank.setDescription(description);
                        newXeroInBank.setDebitAmount(debitAmount);
                        newXeroInBank.setCreditAmount(creditAmount);
                        newXeroInBank.setNetAmount(netAmount);
                        newXeroInBank.setSource(source);
                        xeroInBankRepository.save(newXeroInBank);

                        PaymentInstallment installment = new PaymentInstallment();
                        installment.setAmount(debitAmount);
                        installment.setApplication(application.get());
                        installment = paymentInstallmentRepository.save(installment);
                        application.get().addInstallment(installment);

                        if (application.get().getPaidAmount() >= application.get().getPrice()) {
                            application.get().setPaidStatus(PaidStatus.FULLY_PAID);
                        } else {
                            application.get().setPaidStatus(PaidStatus.PARTIALLY_PAID);
                        }
                        applicationRepository.save(application.get());
                    }
                }

            }

            return new SuccessResponse("Successfully uploaded Xero In Bank data");

        } catch (Exception e) {
            throw new AppRTException("Failed to parse and upload CSV/Excel file: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
