import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { handleLogout } from './LocalStorageService';
import { toast } from 'react-toastify';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Custom base query that handles JWT token expiration
export const createCustomBaseQuery = (baseUrl) => {
  const baseQuery = fetchBaseQuery({
    baseUrl: API_BASE_URL + baseUrl,
    prepareHeaders: (headers) => {
      const token = localStorage.getItem('token');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  });

  return async (args, api, extraOptions) => {
    const result = await baseQuery(args, api, extraOptions);

    // Check if the response indicates an expired or invalid token
    if (result.error && (result.error.status === 401 || result.error.status === 403)) {
      // Check if the error message indicates token expiration
      const errorMessage = result.error.data?.message || '';
      const isTokenExpired =
        errorMessage.toLowerCase().includes('token') ||
        errorMessage.toLowerCase().includes('expired') ||
        errorMessage.toLowerCase().includes('unauthorized') ||
        result.error.status === 401;

      if (isTokenExpired) {
        console.log('JWT token expired or invalid, logging out user');

        // Show toast notification
        toast.error('Your session has expired. Please log in again.', {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });

        // Handle logout after a brief delay to show the toast
        setTimeout(() => {
          handleLogout();
        }, 1000);
      }
    }

    return result;
  };
};

// Default base query for services that don't specify a custom base URL
export const defaultCustomBaseQuery = createCustomBaseQuery('/api/');
