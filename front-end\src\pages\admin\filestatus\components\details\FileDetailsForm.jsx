import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSave, faSpinner } from "@fortawesome/free-solid-svg-icons";
import { useUpdateFileDetailsMutation } from "../../../../../services/CompanyAPIService";
import EditableField from "./EditableField";

/**
 * FileDetailsForm component for editing file details
 * @param {object} fileStatus - File status data
 * @param {function} showToast - Function to show toast notifications
 * @param {function} refetch - Function to refetch data
 */
const FileDetailsForm = ({ fileStatus, showToast, refetch }) => {
  const [formData, setFormData] = useState({
    fileSource: "",
    visaStatus: "",
    hardCopyTrackingNumber: ""
  });

  // API mutation
  const [updateFileDetails, { isLoading: isUpdatingDetails }] = useUpdateFileDetailsMutation();

  // Initialize form with existing data
  useEffect(() => {
    if (fileStatus) {
      setFormData({
        fileSource: fileStatus.fileSource || "",
        visaStatus: fileStatus.visaStatus || "",
        hardCopyTrackingNumber: fileStatus.hardCopyTrackingNumber || ""
      });
    }
  }, [fileStatus]);

  // Handle file source update
  const handleFileSourceUpdate = async (value) => {
    try {
      await updateFileDetails({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        fileSource: value,
        visaStatus: formData.visaStatus,
        hardCopyTrackingNumber: formData.hardCopyTrackingNumber
      }).unwrap();
      
      setFormData(prev => ({ ...prev, fileSource: value }));
      showToast("File source updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update file source", "error");
      console.error("Error updating file source:", error);
    }
  };

  // Handle visa status update
  const handleVisaStatusUpdate = async (value) => {
    try {
      await updateFileDetails({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        fileSource: formData.fileSource,
        visaStatus: value,
        hardCopyTrackingNumber: formData.hardCopyTrackingNumber
      }).unwrap();
      
      setFormData(prev => ({ ...prev, visaStatus: value }));
      showToast("Visa status updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update visa status", "error");
      console.error("Error updating visa status:", error);
    }
  };

  // Handle tracking number update
  const handleTrackingNumberUpdate = async (value) => {
    try {
      await updateFileDetails({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        fileSource: formData.fileSource,
        visaStatus: formData.visaStatus,
        hardCopyTrackingNumber: value
      }).unwrap();
      
      setFormData(prev => ({ ...prev, hardCopyTrackingNumber: value }));
      showToast("Tracking number updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update tracking number", "error");
      console.error("Error updating tracking number:", error);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">File Details</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* File Source */}
        <EditableField
          label="File Source"
          value={formData.fileSource}
          onSave={handleFileSourceUpdate}
          isLoading={isUpdatingDetails}
        />

        {/* Visa Status */}
        <EditableField
          label="Visa Status"
          value={formData.visaStatus}
          onSave={handleVisaStatusUpdate}
          isLoading={isUpdatingDetails}
        />

        {/* Tracking Number */}
        <EditableField
          label="Hard Copy Tracking Number"
          value={formData.hardCopyTrackingNumber}
          onSave={handleTrackingNumberUpdate}
          isLoading={isUpdatingDetails}
        />
      </div>
    </div>
  );
};

export default FileDetailsForm;
