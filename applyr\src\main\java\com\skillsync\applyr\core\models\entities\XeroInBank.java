package com.skillsync.applyr.core.models.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "xero_in_bank")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class XeroInBank {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    // one application can have multiple XeroInBank Statement
    private String applicationId;

    private LocalDateTime insertDate;

    private String reference;
    private String description;

    private double debitAmount;
    private double creditAmount;
    private double netAmount;

    private String source;

}
