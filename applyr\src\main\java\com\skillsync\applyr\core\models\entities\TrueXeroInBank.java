package com.skillsync.applyr.core.models.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "true_xero_in_bank")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TrueXeroInBank {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    private String agentUsername;

    // in the excel file it is called "Reference"
    private String invoiceNumber;

    private String contactName;
    private String applicationId;

    private LocalDateTime insertDate;

    private String description;

    private double debitAmount;
    private double creditAmount;
    private double netAmount;

    private String source;

}
