import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUser,
  faUserTie,
  faFileInvoiceDollar,
  faPhone,
  faEnvelope,
  faMoneyBillWave,
  faCalendarAlt,
  faCheckCircle,
  faEdit,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";
import { formatCurrency } from "../../utils/formatters";
import CommissionStatusBadge from "./CommissionStatusBadge";
import { getToken } from "../../services/LocalStorageService";
import { jwtDecode } from "jwt-decode";

const CommissionCard = ({ commission, onStatusChange, onEdit, onDelete }) => {
  const [userRole, setUserRole] = useState("");
  const [canChangeStatus, setCanChangeStatus] = useState(false);

  useEffect(() => {
    const token = getToken();
    if (token) {
      try {
        const decodedToken = jwtDecode(token);
        const roles = decodedToken.roles || [];
        if (roles.includes("ROLE_SALES")) {
          setUserRole("AGENT");
          setCanChangeStatus(false);
        } else if (roles.includes("ROLE_OPERATIONS")) {
          setUserRole("OPERATIONS");
          setCanChangeStatus(true);
        } else if (roles.includes("ROLE_ADMIN")) {
          setUserRole("ADMIN");
          setCanChangeStatus(true);
        }
      } catch (error) {
        console.error("Error decoding token:", error);
      }
    }
  }, []);

  // Format date if it exists
  const formattedDate = commission.paymentDate
    ? new Date(commission.paymentDate).toLocaleDateString()
    : "Not paid yet";

  // Determine if contact info is email or phone
  const isEmail = commission.externalCommissionContactInfo &&
    commission.externalCommissionContactInfo.includes("@");

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
      <div className="p-5">
        {/* Header with Invoice Number */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="text-blue-500 mr-2" />
            <div>
              <div className="text-sm text-gray-500">Invoice Number</div>
              <div className="font-bold text-gray-800">
                {commission.application.invoiceRefNumber || "No Invoice"}
              </div>
            </div>
          </div>
          <CommissionStatusBadge status={commission.paymentStatus} />
        </div>

        {/* Main Content */}
        <div className="space-y-3 mb-4">
          {/* Internal Team Member */}
          <div className="flex items-center">
            <FontAwesomeIcon icon={faUserTie} className="text-blue-500 w-5 mr-3" />
            <div>
              <div className="text-sm text-gray-500">Internal Team Member</div>
              <div className="font-medium text-gray-800">
                {commission.application.createdBy.fullName}
              </div>
            </div>
          </div>

          {/* Applicant Name */}
          <div className="flex items-center">
            <FontAwesomeIcon icon={faUser} className="text-blue-500 w-5 mr-3" />
            <div>
              <div className="text-sm text-gray-500">Applicant</div>
              <div className="font-medium text-gray-800">
                {commission.application.applicantName}
              </div>
            </div>
          </div>

          {/* External Referral Name */}
          <div className="flex items-center">
            <FontAwesomeIcon icon={faUser} className="text-blue-500 w-5 mr-3" />
            <div>
              <div className="text-sm text-gray-500">External Referral</div>
              <div className="font-medium text-gray-800">
                {commission.externalCommissionName}
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="flex items-center">
            <FontAwesomeIcon
              icon={isEmail ? faEnvelope : faPhone}
              className="text-blue-500 w-5 mr-3"
            />
            <div>
              <div className="text-sm text-gray-500">Contact Info</div>
              <div className="font-medium text-gray-800">
                {commission.externalCommissionContactInfo}
              </div>
            </div>
          </div>

          {/* Commission Amount */}
          <div className="flex items-center">
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-blue-500 w-5 mr-3" />
            <div>
              <div className="text-sm text-gray-500">Commission Amount</div>
              <div className="font-medium text-green-600">
                {formatCurrency(commission.commissionAmount)}
              </div>
            </div>
          </div>

          {/* Payment Date */}
          <div className="flex items-center">
            <FontAwesomeIcon icon={faCalendarAlt} className="text-blue-500 w-5 mr-3" />
            <div>
              <div className="text-sm text-gray-500">Payment Date</div>
              <div className="font-medium text-gray-800">
                {formattedDate}
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="border-t border-gray-100 pt-4 mt-2 flex justify-between">
          {canChangeStatus && (
            <select
              value={commission.paymentStatus}
              onChange={(e) => onStatusChange(commission.application.applicationId, e.target.value)}
              className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm font-medium border border-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="PENDING_CHECKED">Pending Checked</option>
              <option value="CHECKED_AND_PAID">Checked & Paid</option>
              <option value="RECONCILED_AND_COMPLETED">Reconciled & Completed</option>
            </select>
          )}

          <div className="flex space-x-2">
            <button
              onClick={() => onEdit(commission)}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faEdit} className="mr-1" />
              Edit
            </button>
            <button
              onClick={() => onDelete(commission.application.applicationId)}
              className="bg-red-50 hover:bg-red-100 text-red-600 px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faTrash} className="mr-1" />
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommissionCard;
