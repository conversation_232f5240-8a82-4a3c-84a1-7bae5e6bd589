import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import { userAuth<PERSON>pi } from "../services/userAuthApi";
import { agent<PERSON><PERSON>pi } from "../services/agentIDApi";
import { StudentAPIService } from "../services/studentApiService";
import { ProfileInfoApi } from "../services/profileInfoApi";
import { AgentAPIService } from "../services/AgentAPIService";
import { ProviderApiService } from "../services/ProviderApiService";
import { SalesAPIService } from "../services/SalesAPIService";
import { CompanyAPIService } from "../services/CompanyAPIService";
import { AdminAPIService } from "../services/AdminAPIService";
import { SourceOfTruthApiService } from "../services/SourceOfTruthApiService";
export const store = configureStore({
  reducer: {
    [CompanyAPIService.reducerPath]: CompanyAPIService.reducer,
    [userAuthApi.reducerPath]: userAuthApi.reducer,
    [agentIDApi.reducerPath]: agentIDApi.reducer,
    [StudentAPIService.reducerPath]: StudentAPIService.reducer,
    [ProfileInfoApi.reducerPath]: ProfileInfoApi.reducer,
    [AgentAPIService.reducerPath]: AgentAPIService.reducer,
    [ProviderApiService.reducerPath]: ProviderApiService.reducer,
    [SalesAPIService.reducerPath]: SalesAPIService.reducer,
    [AdminAPIService.reducerPath]: AdminAPIService.reducer,
    [SourceOfTruthApiService.reducerPath]: SourceOfTruthApiService.reducer,
  },

  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      userAuthApi.middleware,
      agentIDApi.middleware,
      StudentAPIService.middleware,
      ProfileInfoApi.middleware,
      AgentAPIService.middleware,
      ProviderApiService.middleware,
      SalesAPIService.middleware,
      CompanyAPIService.middleware,
      AdminAPIService.middleware,
      SourceOfTruthApiService.middleware,
    ),
});

setupListeners(store.dispatch);
