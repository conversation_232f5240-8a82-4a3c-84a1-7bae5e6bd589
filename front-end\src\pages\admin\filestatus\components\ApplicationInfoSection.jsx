import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCopy, faCheck } from "@fortawesome/free-solid-svg-icons";

const ApplicationInfoSection = ({ application, formatDate }) => {
  const [copied, setCopied] = useState(null);

  // Copy text to clipboard
  const copyToClipboard = (text, field) => {
    navigator.clipboard.writeText(text);
    setCopied(field);
    setTimeout(() => setCopied(null), 2000);
  };

  return (
    <div className="lg:col-span-1 bg-white rounded-lg border border-gray-100 shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Application Information</h3>
      
      <div className="space-y-4">
        {/* Applicant Name */}
        <InfoItem 
          label="Applicant Name" 
          value={application?.applicantName || "N/A"} 
          onCopy={() => copyToClipboard(application?.applicantName, "applicantName")}
          copied={copied === "applicantName"}
        />
        
        {/* Applicant Email */}
        <InfoItem 
          label="Applicant Email" 
          value={application?.applicantEmail || "N/A"} 
          onCopy={() => copyToClipboard(application?.applicantEmail, "applicantEmail")}
          copied={copied === "applicantEmail"}
        />
        
        {/* Applicant Phone */}
        <InfoItem 
          label="Applicant Phone" 
          value={application?.applicantPhone || "N/A"} 
          onCopy={() => copyToClipboard(application?.applicantPhone, "applicantPhone")}
          copied={copied === "applicantPhone"}
        />
        
        {/* Company Name */}
        <InfoItem 
          label="Company Name" 
          value={application?.lead?.companyName || "N/A"} 
          onCopy={() => copyToClipboard(application?.lead?.companyName, "companyName")}
          copied={copied === "companyName"}
        />
        
        {/* Agent Name */}
        <InfoItem 
          label="Agent Name" 
          value={application?.createdBy?.fullName || "N/A"} 
          onCopy={() => copyToClipboard(application?.createdBy?.fullName, "agentName")}
          copied={copied === "agentName"}
        />
        
        {/* Quote Reference */}
        <InfoItem 
          label="Quote Reference" 
          value={application?.quoteRefNumber || "N/A"} 
          onCopy={() => copyToClipboard(application?.quoteRefNumber, "quoteRef")}
          copied={copied === "quoteRef"}
        />
        
        {/* Invoice Reference */}
        <InfoItem 
          label="Invoice Reference" 
          value={application?.invoiceRefNumber || "N/A"} 
          onCopy={() => copyToClipboard(application?.invoiceRefNumber, "invoiceRef")}
          copied={copied === "invoiceRef"}
        />
        
        {/* Created Date */}
        <div className="flex justify-between">
          <span className="text-sm font-medium text-gray-500">Created Date:</span>
          <span className="text-sm text-gray-900">
            {application?.createdAt ? formatDate(application.createdAt) : "N/A"}
          </span>
        </div>
        
        {/* Total Price */}
        <div className="flex justify-between">
          <span className="text-sm font-medium text-gray-500">Total Price:</span>
          <span className="text-sm text-gray-900">
            ${application?.totalPrice || "0.00"}
          </span>
        </div>
        
        {/* Paid Status */}
        <div className="flex justify-between">
          <span className="text-sm font-medium text-gray-500">Payment Status:</span>
          <span className={`text-sm font-medium px-2 py-0.5 rounded-full ${
            application?.paidStatus === "FULLY_PAID" 
              ? "bg-green-100 text-green-800" 
              : application?.paidStatus === "PARTIALLY_PAID"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-red-100 text-red-800"
          }`}>
            {formatPaidStatus(application?.paidStatus)}
          </span>
        </div>
      </div>
    </div>
  );
};

// Helper component for info items with copy functionality
const InfoItem = ({ label, value, onCopy, copied }) => {
  return (
    <div className="flex justify-between items-center">
      <span className="text-sm font-medium text-gray-500">{label}:</span>
      <div className="flex items-center">
        <span className="text-sm text-gray-900 mr-2">{value}</span>
        <button 
          onClick={onCopy}
          className="text-gray-400 hover:text-gray-600"
          title="Copy to clipboard"
        >
          <FontAwesomeIcon icon={copied ? faCheck : faCopy} className={copied ? "text-green-500" : ""} />
        </button>
      </div>
    </div>
  );
};

// Helper function to format paid status
const formatPaidStatus = (status) => {
  if (!status) return "Pending";
  
  switch (status) {
    case "FULLY_PAID":
      return "Fully Paid";
    case "PARTIALLY_PAID":
      return "Partially Paid";
    case "INVOICE_EXPIRED":
      return "Invoice Expired";
    default:
      return "Pending";
  }
};

export default ApplicationInfoSection;
