package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class WeeklyTargetsRequestDTO {
    private long id;
    private String title;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private List<WeeklyTargetDTO> targets;
}

