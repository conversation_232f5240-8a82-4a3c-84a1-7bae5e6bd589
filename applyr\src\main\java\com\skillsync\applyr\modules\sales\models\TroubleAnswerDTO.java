package com.skillsync.applyr.modules.sales.models;


import com.skillsync.applyr.modules.company.models.ProfileDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TroubleAnswerDTO {
    private String answerId;
    private String answer;
    private ProfileDTO answeredBy;
    private LocalDateTime answeredAt;

}
