package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.models.entities.ApplicationFile;
import com.skillsync.applyr.core.models.entities.ExternalCommission;
import com.skillsync.applyr.core.models.entities.SoldQualifications;
import com.skillsync.applyr.core.models.enums.CommissionPaymentStatus;
import com.skillsync.applyr.core.models.enums.FileStatus;
import com.skillsync.applyr.core.models.enums.RTOPaymentStatus;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.company.models.ExternalCommissionRequestDTO;
import com.skillsync.applyr.modules.company.models.ExternalCommissionResponseDTO;
import com.skillsync.applyr.modules.company.models.FileStatusDTO;
import com.skillsync.applyr.modules.company.repositories.ApplicationFileRepository;
import com.skillsync.applyr.modules.company.repositories.ApplicationRepository;
import com.skillsync.applyr.modules.company.repositories.ExternalCommissionRepository;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ApplicationFileServices {
    
    private final ApplicationRepository applicationRepository;
    private final ExternalCommissionRepository externalCommissionRepository;
    private final ApplicationService applicationService;
    private final ApplicationFileRepository applicationFileRepository;
    private final RTOServices rtoServices;


    public ApplicationFileServices(ApplicationRepository applicationRepository, ExternalCommissionRepository externalCommissionRepository, ApplicationService applicationService, ApplicationFileRepository applicationFileRepository, RTOServices rtoServices) {
        this.applicationRepository = applicationRepository;
        this.externalCommissionRepository = externalCommissionRepository;
        this.applicationService = applicationService;
        this.applicationFileRepository = applicationFileRepository;
        this.rtoServices = rtoServices;
    }
    
    public SuccessResponse createExternalCommissionForApplication(ExternalCommissionRequestDTO externalCommissionRequestDTO) {
        try {
            Optional<Application> applicationOptional = applicationRepository.findByApplicationId(externalCommissionRequestDTO.getApplicationId());
            if (applicationOptional.isEmpty()) {
                throw new AppRTException("Application not found", HttpStatus.NOT_FOUND);
            }
            
            ExternalCommission commission = new ExternalCommission();
            commission.setName(externalCommissionRequestDTO.getName());
            commission.setContactInfo(externalCommissionRequestDTO.getContactInfo());
            commission.setCommissionAmount(externalCommissionRequestDTO.getCommissionAmount());
            commission.setApplicationId(applicationOptional.get().getApplicationId());
            commission.setPaymentStatus(CommissionPaymentStatus.PENDING_CHECKED);
            
            externalCommissionRepository.save(commission);
            
            return new SuccessResponse("External commission created successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to create external commission: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse changeCommissionStatus(String applicationId, CommissionPaymentStatus newStatus) {
        try {
            ExternalCommission commission = externalCommissionRepository.findByApplicationId(applicationId)
                .orElseThrow(() -> new AppRTException("Commission not found for application: " + applicationId, HttpStatus.NOT_FOUND));
            
            commission.setPaymentStatus(newStatus);
            
            // If status is PAID, set payment date to now
            if (newStatus == CommissionPaymentStatus.CHECKED_AND_PAID) {
                commission.setPaymentDate(LocalDateTime.now());
            }
            
            externalCommissionRepository.save(commission);
            return new SuccessResponse("Commission status updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update commission status: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse editCommission(String applicationId, ExternalCommissionRequestDTO updateDTO) {
        try {
            ExternalCommission commission = externalCommissionRepository.findByApplicationId(applicationId)
                .orElseThrow(() -> new AppRTException("Commission not found for application: " + applicationId, HttpStatus.NOT_FOUND));
            
            commission.setName(updateDTO.getName());
            commission.setContactInfo(updateDTO.getContactInfo());
            commission.setCommissionAmount(updateDTO.getCommissionAmount());
            
            externalCommissionRepository.save(commission);
            return new SuccessResponse("Commission updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update commission: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse deleteCommission(String applicationId) {
        try {
            ExternalCommission commission = externalCommissionRepository.findByApplicationId(applicationId)
                .orElseThrow(() -> new AppRTException("Commission not found for application: " + applicationId, HttpStatus.NOT_FOUND));
            
            externalCommissionRepository.delete(commission);
            return new SuccessResponse("Commission deleted successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to delete commission: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public List<ExternalCommissionResponseDTO> getAllCommissions() {
        try {
            List<ExternalCommission> commissions = externalCommissionRepository.findAll();
            return commissions.stream()
                    .map(this::mapToResponseDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new AppRTException("Failed to retrieve commissions: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private ExternalCommissionResponseDTO mapToResponseDTO(ExternalCommission commission) {
        ExternalCommissionResponseDTO dto = new ExternalCommissionResponseDTO();
        dto.setExternalCommissionName(commission.getName());
        dto.setExternalCommissionContactInfo(commission.getContactInfo());
        dto.setApplication(applicationService.getSingleApplications(commission.getApplicationId()));
        dto.setCommissionAmount(commission.getCommissionAmount());
        dto.setPaymentStatus(commission.getPaymentStatus());
        dto.setPaymentDate(commission.getPaymentDate());
        return dto;
    }


    public void createApplicationFiles(Application application) {
        for(SoldQualifications soldQualification : application.getSoldQualificationsList()) {
            Optional<ApplicationFile> exData = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(application.getApplicationId(), soldQualification.getQualification().getQualificationId());
            if (exData.isPresent()) {
                continue;
            }
            ApplicationFile applicationFile = new ApplicationFile();
            applicationFile.setApplicationId(application.getApplicationId());
            applicationFile.setQualification(soldQualification.getQualification());
            applicationFile.setFileStatus(FileStatus.DOCUMENTS_PENDING);

            applicationFileRepository.save(applicationFile);
        }
    }

    public List<FileStatusDTO> getAllApplicationFiles() {
        List<ApplicationFile> applicationFiles = applicationFileRepository.findAll();
        return applicationFiles.stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }
    
    public FileStatusDTO getSingleApplicationFile(String applicationId, String qualificationId) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            return mapToDTO(applicationFile);
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to retrieve application file: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    private FileStatusDTO mapToDTO(ApplicationFile applicationFile) {
        FileStatusDTO dto = new FileStatusDTO();

        dto.setApplication(applicationService.getSingleApplications(applicationFile.getApplicationId()));
        dto.setFileSource(applicationFile.getFileSource());
        dto.setVisaStatus(applicationFile.getVisaStatus());

        dto.setQualificationCode(applicationFile.getQualification().getQualificationId());
        dto.setQualificationName(applicationFile.getQualification().getQualificationName());

        dto.setFileStatus(applicationFile.getFileStatus());

        try {
            dto.setRtoData(rtoServices.getRTOByCode(applicationFile.getRtoCode()));
        } catch (AppRTException e) {
            dto.setRtoData(null);
        }
        dto.setRtoCharge(applicationFile.getRtoCharge());
        dto.setRtoPaymentStatus(applicationFile.getRtoPaymentStatus());
        dto.setRtoPaymentDate(applicationFile.getRtoPaymentDate());
        dto.setLodgedToRTO(applicationFile.isLodgedToRTO());
        dto.setLodgedDate(applicationFile.getLodgedDate());


        dto.setHardCopyTrackingNumber(applicationFile.getHardCopyTrackingNumber());

        dto.setHardCopyMailedDate(applicationFile.getHardCopyMailedDate());
        dto.setHardCopyReceivedDate(applicationFile.getHardCopyReceivedDate());
        dto.setSoftCopyReleasedDate(applicationFile.getSoftCopyReleasedDate());
        dto.setSoftCopyReceivedDate(applicationFile.getSoftCopyReceivedDate());
        dto.setDocumentReceivedDate(applicationFile.getDocumentReceivedDate());

        Optional<ExternalCommission> commission = externalCommissionRepository.findByApplicationId(applicationFile.getApplicationId());
        if (commission.isPresent()) {
            dto.setExternalCommissionName(commission.get().getName());
            dto.setExternalCommissionContactInfo(commission.get().getContactInfo());
            dto.setCommissionAmount(commission.get().getCommissionAmount());
            dto.setCommissionPaymentStatus(commission.get().getPaymentStatus());
            dto.setCommissionPaymentDate(commission.get().getPaymentDate());
        } else {
            dto.setExternalCommissionName(null);
            dto.setExternalCommissionContactInfo(null);
            dto.setCommissionAmount(0.0);
            dto.setCommissionPaymentStatus(null);
            dto.setCommissionPaymentDate(null);
        }

        return dto;
    }

    // Update file status
    public SuccessResponse updateFileStatus(String applicationId, String qualificationId, FileStatus newStatus) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setFileStatus(newStatus);
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("File status updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update file status: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Update RTO information
    public SuccessResponse updateRTOInfo(String applicationId, String qualificationId, String rtoCode, double rtoCharge, RTOPaymentStatus paymentStatus) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setRtoCode(rtoCode);
            applicationFile.setRtoCharge(rtoCharge);
            applicationFile.setRtoPaymentStatus(paymentStatus);
            if(paymentStatus == RTOPaymentStatus.PAID){
                applicationFile.setRtoPaymentDate(LocalDateTime.now());
            }
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("RTO information updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update RTO information: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Update RTO payment date
    public SuccessResponse updateRTOPaymentDate(String applicationId, String qualificationId, LocalDateTime paymentDate) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setRtoPaymentDate(paymentDate);
            applicationFile.setRtoPaymentStatus(RTOPaymentStatus.PAID);
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("RTO payment date updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update RTO payment date: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Update lodged to RTO status and date
    public SuccessResponse updateLodgedToRTO(String applicationId, String qualificationId, boolean lodgedToRTO, LocalDateTime lodgedDate) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setLodgedToRTO(lodgedToRTO);
            if(lodgedToRTO){
                applicationFile.setFileStatus(FileStatus.LODGED_AND_PROCESSING);
            }else{
                applicationFile.setLodgedDate(null);
            }
            applicationFile.setLodgedDate(lodgedDate);
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("Lodged to RTO status updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update lodged to RTO status: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Update document received date
    public SuccessResponse updateDocumentReceivedDate(String applicationId, String qualificationId, LocalDateTime documentReceivedDate) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setDocumentReceivedDate(documentReceivedDate);
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("Document received date updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update document received date: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Update soft copy received date
    public SuccessResponse updateSoftCopyReceivedDate(String applicationId, String qualificationId, LocalDateTime softCopyReceivedDate) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setSoftCopyReceivedDate(softCopyReceivedDate);
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("Soft copy received date updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update soft copy received date: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Update soft copy released date
    public SuccessResponse updateSoftCopyReleasedDate(String applicationId, String qualificationId, LocalDateTime softCopyReleasedDate) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setSoftCopyReleasedDate(softCopyReleasedDate);
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("Soft copy released date updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update soft copy released date: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Update hard copy received date
    public SuccessResponse updateHardCopyReceivedDate(String applicationId, String qualificationId, LocalDateTime hardCopyReceivedDate) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setHardCopyReceivedDate(hardCopyReceivedDate);
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("Hard copy received date updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update hard copy received date: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Update hard copy mailed date
    public SuccessResponse updateHardCopyMailedDate(String applicationId, String qualificationId, LocalDateTime hardCopyMailedDate) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setHardCopyMailedDate(hardCopyMailedDate);
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("Hard copy mailed date updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update hard copy mailed date: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Update file source, visa status, and tracking number
    public SuccessResponse updateFileDetails(String applicationId, String qualificationId, String fileSource, String visaStatus, String hardCopyTrackingNumber) {
        try {
            ApplicationFile applicationFile = applicationFileRepository.findByApplicationIdAndQualificationQualificationId(applicationId, qualificationId)
                .orElseThrow(() -> new AppRTException("Application file not found", HttpStatus.NOT_FOUND));
            
            applicationFile.setFileSource(fileSource);
            applicationFile.setVisaStatus(visaStatus);
            applicationFile.setHardCopyTrackingNumber(hardCopyTrackingNumber);
            applicationFileRepository.save(applicationFile);
            
            return new SuccessResponse("File details updated successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Failed to update file details: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
