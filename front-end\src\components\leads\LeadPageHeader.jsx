import React from "react";

const LeadPageHeader = ({ 
  onOpenBulkModal, 
  onOpenSingleModal,
  isAdmin = false 
}) => {
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-end mb-8">
      <div className="flex items-center space-x-3">
        <button
          onClick={onOpenBulkModal}
          className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Import Leads (CSV)
        </button>
        <button
          onClick={onOpenSingleModal}
          className="flex items-center bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Add Single Lead
        </button>
      </div>
    </div>
  );
};

export default LeadPageHeader;
