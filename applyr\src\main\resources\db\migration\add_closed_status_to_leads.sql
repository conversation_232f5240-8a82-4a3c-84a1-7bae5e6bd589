-- Migration script to add CLOSED status to leads table constraint
-- This script updates the check constraint to include the new CLOSED status

-- Drop the existing constraint
ALTER TABLE leads DROP CONSTRAINT IF EXISTS leads_status_check;

-- Add the new constraint with CLOSED status included
ALTER TABLE leads ADD CONSTRAINT leads_status_check 
CHECK (status::text = ANY (ARRAY['HOT'::character varying, 'WARM'::character varying, 'COLD'::character varying, 'FRESH'::character varying, 'CLOSED'::character varying]::text[]));
