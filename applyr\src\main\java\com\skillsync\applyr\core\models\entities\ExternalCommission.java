package com.skillsync.applyr.core.models.entities;

import com.skillsync.applyr.core.models.enums.CommissionPaymentStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "external_commissions")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ExternalCommission extends Auditable<String> {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    
    private String name;
    private String contactInfo;
    private double commissionAmount;
    

    private String applicationId;

    @Enumerated(EnumType.STRING)
    private CommissionPaymentStatus paymentStatus;
    
    private LocalDateTime paymentDate;
}
