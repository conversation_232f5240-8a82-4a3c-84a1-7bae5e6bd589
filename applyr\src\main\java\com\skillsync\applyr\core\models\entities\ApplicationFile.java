package com.skillsync.applyr.core.models.entities;

import com.skillsync.applyr.core.models.enums.FileStatus;
import com.skillsync.applyr.core.models.enums.RTOPaymentStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Table(name = "application_files")
@Getter
@Setter
public class ApplicationFile extends Auditable<String> {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    
    private String applicationId;

    private String fileSource;
    private String visaStatus;
    private String hardCopyTrackingNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "qualification_id")
    private Qualification qualification;

    @Enumerated(EnumType.STRING)
    private FileStatus fileStatus;

    private String rtoCode;
    private double rtoCharge;
    @Enumerated(EnumType.STRING)
    private RTOPaymentStatus rtoPaymentStatus;
    private boolean lodgedToRTO;
    private LocalDateTime lodgedDate;
    private LocalDateTime rtoPaymentDate;

    private LocalDateTime documentReceivedDate;
    private LocalDateTime softCopyReceivedDate;
    private LocalDateTime softCopyReleasedDate;
    private LocalDateTime hardCopyReceivedDate;
    private LocalDateTime hardCopyMailedDate;
}
