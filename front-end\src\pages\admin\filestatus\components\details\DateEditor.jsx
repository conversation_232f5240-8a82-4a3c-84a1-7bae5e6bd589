import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faCheck, faTimes, faSpinner } from "@fortawesome/free-solid-svg-icons";

/**
 * DateEditor component for editing date fields
 * @param {string} label - Field label
 * @param {string} date - Current date value
 * @param {function} onUpdate - Function to call when date is updated
 * @param {string} icon - Icon to display
 * @param {boolean} isLoading - Loading state
 */
const DateEditor = ({ label, date, onUpdate, icon, isLoading = false }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newDate, setNewDate] = useState(date ? new Date(date).toISOString().split('T')[0] : '');

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setNewDate(date ? new Date(date).toISOString().split('T')[0] : '');
  };

  const handleSave = async () => {
    if (!newDate) {
      handleCancel();
      return;
    }

    try {
      // Format date for backend (add time component)
      const formattedDate = `${newDate}T00:00:00`;
      await onUpdate(formattedDate);
      setIsEditing(false);
    } catch (error) {
      console.error("Error updating date:", error);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Not set";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4">
      <div className="flex items-center mb-2">
        <span className="text-2xl mr-3">{icon}</span>
        <h5 className="text-md font-medium text-gray-900">{label}</h5>
        {!isEditing && (
          <button
            onClick={handleEdit}
            className="ml-auto text-gray-400 hover:text-[#6E39CB] transition-colors"
            title="Edit date"
          >
            <FontAwesomeIcon icon={faEdit} />
          </button>
        )}
      </div>

      {isEditing ? (
        <div className="mt-2">
          <div className="flex items-center">
            <input
              type="date"
              value={newDate}
              onChange={(e) => setNewDate(e.target.value)}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              disabled={isLoading}
            />
            <div className="flex ml-2">
              <button
                onClick={handleSave}
                className="text-green-500 hover:text-green-700 mr-2"
                disabled={isLoading}
                title="Save"
              >
                {isLoading ? (
                  <FontAwesomeIcon icon={faSpinner} spin />
                ) : (
                  <FontAwesomeIcon icon={faCheck} />
                )}
              </button>
              <button
                onClick={handleCancel}
                className="text-red-500 hover:text-red-700"
                disabled={isLoading}
                title="Cancel"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
          </div>
        </div>
      ) : (
        <p className="text-sm text-gray-700 mt-2">
          {date ? formatDate(date) : "Not available"}
        </p>
      )}
    </div>
  );
};

export default DateEditor;
