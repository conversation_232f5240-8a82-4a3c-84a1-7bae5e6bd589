package com.skillsync.applyr.core.utills;

import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import java.security.SecureRandom;

public class UserUtils {
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();

    public static String generatePassword() {
        StringBuilder password = new StringBuilder(8);
        for (int i = 0; i < 8; i++) {
            int index = RANDOM.nextInt(CHARACTERS.length());
            password.append(CHARACTERS.charAt(index));
        }
        return "applyr#01A";
    }

    public static String getUsernameFromToken() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetails) {
            return ((UserDetails) principal).getUsername();
        } else {
            return principal.toString();
        }
    }

    public static String getRoleFromToken() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetails) {
            return ((UserDetails) principal).getAuthorities().iterator().next().getAuthority();
        } else {
            return principal.toString();
        }
    }

    public static String generateAccountCreationEmailBody(String name, String username, String password) {
        String logoUrl = "https://skillsync.com.au/wp-content/uploads/2024/11/cropped-cropped-logo-bw-1.png";

        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <title>Welcome to Skill Sync</title>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            font-family: Arial, sans-serif;\n" +
                "            background-color: #f4f4f4;\n" +
                "            margin: 0;\n" +
                "            padding: 0;\n" +
                "        }\n" +
                "        .email-container {\n" +
                "            background-color: #ffffff;\n" +
                "            margin: 20px auto;\n" +
                "            padding: 20px;\n" +
                "            max-width: 600px;\n" +
                "            border-radius: 8px;\n" +
                "            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\n" +
                "        }\n" +
                "        .logo {\n" +
                "            text-align: center;\n" +
                "            margin-bottom: 20px;\n" +
                "        }\n" +
                "        .content {\n" +
                "            font-size: 16px;\n" +
                "            line-height: 1.5;\n" +
                "            color: #333333;\n" +
                "        }\n" +
                "        .content p {\n" +
                "            margin: 10px 0;\n" +
                "        }\n" +
                "        .highlight {\n" +
                "            font-weight: bold;\n" +
                "            color: #000000;\n" +
                "        }\n" +
                "        .footer {\n" +
                "            margin-top: 20px;\n" +
                "            font-size: 12px;\n" +
                "            color: #777777;\n" +
                "            text-align: center;\n" +
                "        }\n" +
                "        .footer a {\n" +
                "            color: #777777;\n" +
                "            text-decoration: none;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"email-container\">\n" +
                "        <div class=\"logo\">\n" +
                "            <img src=\"" + logoUrl + "\" alt=\"Skill Sync Logo\" width=\"150\">\n" +
                "        </div>\n" +
                "        <div class=\"content\">\n" +
                "            <p>Dear " + escapeHtml(name) + ",</p>\n" +
                "            <p>Welcome to <strong>Skill Sync Pty Ltd</strong>! Your account has been successfully created. Below are your login credentials:</p>\n" +
                "            <p>Username: <span class=\"highlight\">" + escapeHtml(username) + "</span></p>\n" +
                "            <p>Password: <span class=\"highlight\">" + escapeHtml(password) + "</span></p>\n" +
                "            <p>Please log in to your account and change your password as soon as possible to ensure your account's security.</p>\n" +
                "            <p>If you have any questions or need assistance, feel free to contact our support team.</p>\n" +
                "            <p>Best Regards,<br>Skill Sync Team</p>\n" +
                "        </div>\n" +
                "        <div class=\"footer\">\n" +
                "            <p>&copy; Skill Sync Pty Ltd. All rights reserved.</p>\n" +
                "            <p><a href=\"https://skillsync.com.au\">Visit our website</a></p>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }

    private static String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#x27;")
                .replace("/", "&#x2F;");
    }
}
