import React, { useState } from "react";

const StatusUpdateSection = ({ currentStatus, onStatusUpdate }) => {
  const [status, setStatus] = useState(currentStatus);
  
  // Status options based on FileStatus enum
  const statusOptions = [
    "DOCUMENTS_PENDING",
    "DOCUMENTS_RECEIVED",
    "LODGED_AND_PROCESSING",
    "SOFT_COPY_RECEIVED",
    "SOFT_COPY_RELEASED",
    "HARD_COPY_RECEIVED",
    "HARD_COPY_MAILED_AND_CLOSED",
    "PENDING_EVIDENCE",
    "CANCELLED",
    "STATUS_UNAVAILABLE",
    "HOLD",
  ];

  // Format status for display
  const formatStatus = (status) => {
    if (!status) return "";
    return status.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, (c) => c.toUpperCase());
  };
  
  // Handle status change
  const handleStatusChange = (e) => {
    setStatus(e.target.value);
  };
  
  // Handle update button click
  const handleUpdate = () => {
    onStatusUpdate(status);
  };
  
  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Update Status</h3>
      
      <div className="space-y-4">
        <div className="flex flex-col">
          <span className="text-sm font-medium text-gray-500 mb-1">Current Status:</span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {formatStatus(currentStatus)}
          </span>
        </div>
        
        <div className="flex flex-col">
          <label htmlFor="statusUpdate" className="text-sm font-medium text-gray-500 mb-1">
            New Status:
          </label>
          <select
            id="statusUpdate"
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm rounded-md"
            value={status}
            onChange={handleStatusChange}
          >
            {statusOptions.map((option) => (
              <option key={option} value={option}>
                {formatStatus(option)}
              </option>
            ))}
          </select>
        </div>
        
        <div className="flex justify-end">
          <button
            onClick={handleUpdate}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#6E39CB] hover:bg-[#5E2CB8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
          >
            Update Status
          </button>
        </div>
      </div>
    </div>
  );
};

export default StatusUpdateSection;
