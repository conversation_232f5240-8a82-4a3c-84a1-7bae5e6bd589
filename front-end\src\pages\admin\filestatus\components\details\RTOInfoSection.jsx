import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faCheck, faTimes, faSpinner, faPlus, faBuilding } from "@fortawesome/free-solid-svg-icons";
import { useUpdateRTOInfoMutation, useUpdateRTOPaymentDateMutation, useUpdateLodgedToRTOMutation } from "../../../../../services/CompanyAPIService";
import EditableField from "./EditableField";
import DateEditor from "./DateEditor";
import RTOAssignModal from "./RTOAssignModal";

/**
 * RTOInfoSection component for managing RTO information
 * @param {object} fileStatus - File status data
 * @param {function} showToast - Function to show toast notifications
 * @param {function} refetch - Function to refetch data
 */
const RTOInfoSection = ({ fileStatus, showToast, refetch }) => {
  // State for modal
  const [isModalOpen, setIsModalOpen] = useState(false);

  // API mutations
  const [updateRTOInfo, { isLoading: isUpdatingRTOInfo }] = useUpdateRTOInfoMutation();
  const [updateRTOPaymentDate, { isLoading: isUpdatingPaymentDate }] = useUpdateRTOPaymentDateMutation();
  const [updateLodgedToRTO, { isLoading: isUpdatingLodgedStatus }] = useUpdateLodgedToRTOMutation();

  // Check if RTO data exists
  const hasRTOData = !!fileStatus.rtoData;

  // Payment status options based on RTOPaymentStatus enum
  const paymentStatusOptions = [
    { value: "UNPAID", label: "Unpaid" },
    { value: "PAID", label: "Paid" }
  ];

  // Handle RTO code update
  const handleRTOCodeUpdate = async (value) => {
    try {
      await updateRTOInfo({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        rtoCode: value,
        rtoCharge: fileStatus.rtoCharge || 0,
        paymentStatus: fileStatus.rtoPaymentStatus || "UNPAID"
      }).unwrap();

      showToast("RTO code updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update RTO code", "error");
      console.error("Error updating RTO code:", error);
    }
  };

  // Handle RTO charge update
  const handleRTOChargeUpdate = async (value) => {
    try {
      const charge = parseFloat(value) || 0;

      await updateRTOInfo({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        rtoCode: fileStatus.rtoData?.code || "",
        rtoCharge: charge,
        paymentStatus: fileStatus.rtoPaymentStatus || "UNPAID"
      }).unwrap();

      showToast("RTO charge updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update RTO charge", "error");
      console.error("Error updating RTO charge:", error);
    }
  };

  // Handle payment status update
  const handlePaymentStatusUpdate = async (value) => {
    try {
      await updateRTOInfo({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        rtoCode: fileStatus.rtoData?.code || "",
        rtoCharge: fileStatus.rtoCharge || 0,
        paymentStatus: value
      }).unwrap();

      showToast("Payment status updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update payment status", "error");
      console.error("Error updating payment status:", error);
    }
  };

  // Handle payment date update
  const handlePaymentDateUpdate = async (date) => {
    try {
      await updateRTOPaymentDate({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        paymentDate: date
      }).unwrap();

      showToast("Payment date updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update payment date", "error");
      console.error("Error updating payment date:", error);
    }
  };

  // Handle lodged status update
  const handleLodgedStatusUpdate = async (value) => {
    try {
      await updateLodgedToRTO({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        lodgedToRTO: value === "true",
        lodgedDate: fileStatus.lodgedDate || null
      }).unwrap();

      showToast("Lodged status updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update lodged status", "error");
      console.error("Error updating lodged status:", error);
    }
  };

  // Handle lodged date update
  const handleLodgedDateUpdate = async (date) => {
    try {
      await updateLodgedToRTO({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        lodgedToRTO: fileStatus.lodgedToRTO || false,
        lodgedDate: date
      }).unwrap();

      showToast("Lodged date updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update lodged date", "error");
      console.error("Error updating lodged date:", error);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">RTO Information</h3>

      {!hasRTOData ? (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="bg-gray-100 rounded-full p-6 mb-4">
            <FontAwesomeIcon icon={faBuilding} className="text-4xl text-gray-400" />
          </div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">No RTO Assigned</h4>
          <p className="text-gray-500 text-center mb-6 max-w-md">
            This file has not been assigned to an RTO yet. Assign it to an RTO to track RTO-related information.
          </p>
          <button
            onClick={() => setIsModalOpen(true)}
            className="inline-flex items-center px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-[#5E2CB8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
          >
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Assign File to RTO
          </button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-6">
            {/* RTO Information */}
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
              <div className="flex justify-between items-center mb-3">
                <h5 className="text-md font-medium text-gray-900">RTO Details</h5>
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="text-xs px-2 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded transition-colors"
                >
                  Update RTO
                </button>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">RTO Code:</span>
                  <span className="text-sm text-gray-900">{fileStatus.rtoData?.code || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Legal Name:</span>
                  <span className="text-sm text-gray-900">{fileStatus.rtoData?.legalName || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Business Name:</span>
                  <span className="text-sm text-gray-900">{fileStatus.rtoData?.businessName || "N/A"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Address:</span>
                  <span className="text-sm text-gray-900">{fileStatus.rtoData?.address || "N/A"}</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* RTO Charge */}
              <EditableField
                label="RTO Charge ($)"
                value={fileStatus.rtoCharge?.toString() || "0"}
                onSave={handleRTOChargeUpdate}
                isLoading={isUpdatingRTOInfo}
                type="text"
              />

              {/* Payment Status */}
              <EditableField
                label="Payment Status"
                value={fileStatus.rtoPaymentStatus || "UNPAID"}
                onSave={handlePaymentStatusUpdate}
                isLoading={isUpdatingRTOInfo}
                type="select"
                options={paymentStatusOptions}
              />

              {/* Lodged Status */}
              <EditableField
                label="Lodged to RTO"
                value={fileStatus.lodgedToRTO ? "true" : "false"}
                onSave={handleLodgedStatusUpdate}
                isLoading={isUpdatingLodgedStatus}
                type="select"
                options={[
                  { value: "true", label: "Yes" },
                  { value: "false", label: "No" }
                ]}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            {/* Payment Date */}
            <DateEditor
              label="RTO Payment Date"
              date={fileStatus.rtoPaymentDate}
              onUpdate={handlePaymentDateUpdate}
              icon="💰"
              isLoading={isUpdatingPaymentDate}
            />

            {/* Lodged Date */}
            <DateEditor
              label="Lodged Date"
              date={fileStatus.lodgedDate}
              onUpdate={handleLodgedDateUpdate}
              icon="📅"
              isLoading={isUpdatingLodgedStatus}
            />
          </div>

          {/* RTO Contacts */}
          {fileStatus.rtoData?.contacts && fileStatus.rtoData.contacts.length > 0 && (
            <div className="mt-6">
              <h5 className="text-md font-medium text-gray-900 mb-3">RTO Contacts</h5>
              <div className="bg-white rounded-lg border border-gray-100 shadow-sm overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {fileStatus.rtoData.contacts.map((contact, index) => (
                        <tr key={contact.id || index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {contact.firstName} {contact.lastName}
                            {contact.jobTitle && <div className="text-xs text-gray-500">{contact.jobTitle}</div>}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{contact.role || 'N/A'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{contact.phone || 'N/A'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <a href={`mailto:${contact.email}`} className="text-blue-600 hover:text-blue-800">
                              {contact.email || 'N/A'}
                            </a>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* RTO Assign Modal */}
      <RTOAssignModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        fileStatus={fileStatus}
        showToast={showToast}
        refetch={refetch}
      />
    </div>
  );
};

export default RTOInfoSection;
