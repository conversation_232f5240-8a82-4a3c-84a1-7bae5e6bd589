import React from "react";
import { FaCommentDots, FaExternalLinkAlt, FaEdit, FaTrash } from "react-icons/fa";
import { getStatusClass, formatDate } from "../../utils/leadUtils";

const LeadMobileCard = ({ 
  lead, 
  onStatusChange, 
  onProfileRedirect, 
  onOpenDrawer, 
  onEditLead, 
  onDeleteLead,
  isUpdatingStatus = false,
  isAdmin = false 
}) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <h3 className="font-medium text-gray-900 text-sm">{lead.leadName}</h3>
          <p className="text-xs text-gray-500 mt-1">
            {lead.companyName && lead.companyName.toLowerCase() !== "n/a" ? lead.companyName : "Individual Lead"}
          </p>
        </div>
        <select
          className={`rounded-full px-2 py-1 text-xs font-medium focus:outline-none focus:ring-2 focus:ring-[#6E39CB] ${getStatusClass(lead.status)}`}
          value={lead.status}
          onChange={(e) => onStatusChange(lead.phone, e.target.value)}
          disabled={isUpdatingStatus}
        >
          <option value="HOT">HOT</option>
          <option value="WARM">WARM</option>
          <option value="COLD">COLD</option>
          <option value="FRESH">FRESH</option>
          <option value="CLOSED">CLOSED</option>
        </select>
      </div>

      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-500">Phone:</span>
          <span className="text-gray-900">{lead.phone}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500">Email:</span>
          <span className="text-gray-900 truncate ml-2">{lead.email}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500">Applications:</span>
          <span className="text-gray-900">
            {lead.applicationCount > 0 ? (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full bg-green-100 text-green-800 text-xs">
                {lead.applicationCount}
              </span>
            ) : (
              <span className="text-gray-500">0</span>
            )}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500">Created At:</span>
          <span className="text-gray-900">{formatDate(lead.createdDate)}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-500">Lead Owner:</span>
          <span className="text-gray-900 truncate ml-2">
            {lead.assignedAgents && lead.assignedAgents.length > 0 ? (
              lead.assignedAgents.length === 1 ? (
                lead.assignedAgents[0].fullName
              ) : (
                `${lead.assignedAgents.length} agents`
              )
            ) : (
              <span className="text-gray-400 italic">Unassigned</span>
            )}
          </span>
        </div>
      </div>

      <div className="flex justify-between items-center mt-4 pt-3 border-t border-gray-100">
        <div className="text-xs text-gray-500">
          {lead.comments && lead.comments.length > 0 ? (
            <span className="truncate">Last: {lead.comments[lead.comments.length - 1].content}</span>
          ) : (
            <span>No comments</span>
          )}
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => onProfileRedirect(lead.phone)}
            className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100"
            title="Open Profile"
          >
            <FaExternalLinkAlt size={14} />
          </button>
          <button
            onClick={() => {
              onOpenDrawer(lead);
            }}
            className={`p-1 rounded-full hover:bg-[#F4F5F9] ${
              lead.comments && lead.comments.length > 0
                ? "text-[#6E39CB] hover:text-[#5E2CB8]"
                : "text-gray-400 hover:text-[#6E39CB]"
            }`}
            title={`${lead.comments && lead.comments.length > 0 ? 'View' : 'Add'} Comments`}
          >
            <FaCommentDots size={14} />
          </button>
          <button
            onClick={() => onEditLead(lead)}
            className="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
            title="Edit Lead"
          >
            <FaEdit size={14} />
          </button>
          {isAdmin && onDeleteLead && (
            <button
              onClick={() => onDeleteLead(lead)}
              className="text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-50"
              title="Delete Lead"
            >
              <FaTrash size={14} />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default LeadMobileCard;
