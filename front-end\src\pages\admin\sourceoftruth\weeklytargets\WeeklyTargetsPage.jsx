import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCalendarAlt,
  faListAlt,
  faSyncAlt,
  faSearch,
  faFilter,
  faEye,
  faEdit,
  faChartLine
} from "@fortawesome/free-solid-svg-icons";
import { format } from "date-fns";
import { toast } from "react-toastify";

// Import API hooks
import {
  useGetAllTargetsQuery,
  useCreateTargetMutation,
  useUpdateTargetMutation,
} from "../../../../services/CompanyAPIService";
import { useGetAllAgentsQuery } from "../../../../services/AdminAPIService";

// Import modal components
import DetailsModal from "../../weeklytarget/DetailsModal";
import CreateTargetModal from "../../weeklytarget/CreateTargetModal";
import EditTargetModal from "../../weeklytarget/EditTargetModal";

const WeeklyTargetsPage = () => {
  // Fetch weekly targets and agents
  const {
    data: weeklyTargets,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetAllTargetsQuery();
  
  const {
    data: agents,
    isLoading: agentsLoading,
    isError: agentsError,
  } = useGetAllAgentsQuery();

  // Mutations for create and update
  const [createTarget] = useCreateTargetMutation();
  const [updateTarget] = useUpdateTargetMutation();

  // Local state for modals and details
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [detailsTarget, setDetailsTarget] = useState(null);
  const [editTarget, setEditTarget] = useState(null);

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState("");
  const [dateFilter, setDateFilter] = useState(null);

  // Handlers for modal toggling
  const handleOpenCreateModal = () => setShowCreateModal(true);
  const handleCloseCreateModal = () => setShowCreateModal(false);

  const handleOpenDetails = (target) => {
    setDetailsTarget(target);
    setShowDetailsModal(true);
  };
  
  const handleCloseDetails = () => {
    setDetailsTarget(null);
    setShowDetailsModal(false);
  };

  const handleOpenEdit = (target) => {
    setEditTarget(target);
    setShowEditModal(true);
  };
  
  const handleCloseEdit = () => {
    setEditTarget(null);
    setShowEditModal(false);
  };

  // Callbacks for create and update mutations
  const handleCreateTarget = async (requestBody) => {
    try {
      await createTarget(requestBody).unwrap();
      setShowCreateModal(false);
      toast.success("Weekly target created successfully");
      refetch();
    } catch (err) {
      console.error("Failed to create target", err);
      toast.error("Failed to create weekly target");
    }
  };

  const handleUpdateTarget = async (updatedTarget) => {
    try {
      await updateTarget(updatedTarget).unwrap();
      setShowEditModal(false);
      toast.success("Weekly target updated successfully");
      refetch();
    } catch (err) {
      console.error("Failed to update target", err);
      toast.error("Failed to update weekly target");
    }
  };

  // Filter targets based on search query and date filter
  const filteredTargets = weeklyTargets
    ? weeklyTargets.filter((wt) => {
        // Apply search filter
        if (
          searchQuery &&
          !wt.title.toLowerCase().includes(searchQuery.toLowerCase())
        ) {
          return false;
        }

        // Apply date filter
        if (dateFilter) {
          const targetStart = new Date(wt.startDate);
          const targetEnd = new Date(wt.endDate);
          const filterDate = new Date(dateFilter);
          return filterDate >= targetStart && filterDate <= targetEnd;
        }

        return true;
      })
    : [];

  // Calculate overall statistics
  const overallStats = weeklyTargets
    ? {
        totalTargets: weeklyTargets.length,
        activeTargets: weeklyTargets.filter((t) => new Date(t.endDate) >= new Date()).length,
        completedTargets: weeklyTargets.filter((t) => new Date(t.endDate) < new Date()).length,
        totalAgents: agents?.length || 0,
      }
    : {};

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Weekly Targets</h1>
          <p className="text-sm text-gray-500 mt-1">
            Manage and track employee performance targets
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <button
            onClick={() => refetch()}
            className="flex items-center bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
          >
            <FontAwesomeIcon icon={faSyncAlt} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={handleOpenCreateModal}
            className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
          >
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
            Create Target
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faListAlt} className="text-[#6E39CB]" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Targets</p>
              <p className="text-xl font-bold text-gray-900">{overallStats.totalTargets || 0}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faCalendarAlt} className="text-green-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Active Targets</p>
              <p className="text-xl font-bold text-gray-900">{overallStats.activeTargets || 0}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faChartLine} className="text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Completed Targets</p>
              <p className="text-xl font-bold text-gray-900">{overallStats.completedTargets || 0}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faListAlt} className="text-orange-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Agents</p>
              <p className="text-xl font-bold text-gray-900">{overallStats.totalAgents || 0}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <input
              type="text"
              placeholder="Search targets..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
            <FontAwesomeIcon
              icon={faSearch}
              className="absolute left-3 top-3 text-gray-400"
            />
          </div>
          <div className="relative">
            <input
              type="date"
              value={dateFilter || ""}
              onChange={(e) => setDateFilter(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
            <FontAwesomeIcon
              icon={faFilter}
              className="absolute left-3 top-3 text-gray-400"
            />
          </div>
          {(searchQuery || dateFilter) && (
            <button
              onClick={() => {
                setSearchQuery("");
                setDateFilter(null);
              }}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>

      {/* Targets Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Target
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Start Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                End Date
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                KPI1 Target
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                KPI1 Actual
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                KPI2 Target
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                KPI2 Actual
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {isLoading ? (
              <tr>
                <td colSpan="8" className="px-6 py-4 text-center text-gray-500">
                  Loading weekly targets...
                </td>
              </tr>
            ) : isError ? (
              <tr>
                <td colSpan="8" className="px-6 py-4 text-center text-red-500">
                  Error loading targets: {error?.message || "Unknown error"}
                </td>
              </tr>
            ) : filteredTargets.length > 0 ? (
              filteredTargets.map((wt, idx) => {
                const isActive = new Date(wt.endDate) >= new Date();
                const start = format(new Date(wt.startDate), "MMM d, yyyy");
                const end = format(new Date(wt.endDate), "MMM d, yyyy");
                
                // Calculate totals for this target
                const totalKPI1Target = wt.targets?.reduce((sum, t) => sum + (t.kpi1Target || 0), 0) || 0;
                const totalKPI1Actual = wt.targets?.reduce((sum, t) => sum + (t.kpi1Actual || 0), 0) || 0;
                const totalKPI2Target = wt.targets?.reduce((sum, t) => sum + (t.kpi2Target || 0), 0) || 0;
                const totalKPI2Actual = wt.targets?.reduce((sum, t) => sum + (t.kpi2Actual || 0), 0) || 0;
                
                return (
                  <tr key={idx} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className={`h-2.5 w-2.5 rounded-full ${isActive ? "bg-green-500" : "bg-gray-300"} mr-2`}></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{wt.title}</p>
                          <p className="text-xs text-gray-500">
                            {wt.targets?.length || 0} agents
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{start}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{end}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      ${totalKPI1Target.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div>
                        <p className="text-sm text-gray-900">${totalKPI1Actual.toLocaleString()}</p>
                        <div className="flex items-center justify-end">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className={`h-2 rounded-full ${
                                totalKPI1Actual >= totalKPI1Target
                                  ? "bg-green-500"
                                  : "bg-blue-500"
                              }`}
                              style={{
                                width: `${
                                  totalKPI1Target > 0
                                    ? Math.min(
                                        (totalKPI1Actual / totalKPI1Target) * 100,
                                        100
                                      )
                                    : 0
                                }%`,
                              }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-500">
                            {totalKPI1Target > 0
                              ? `${Math.round(
                                  (totalKPI1Actual / totalKPI1Target) * 100
                                )}%`
                              : "0%"}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      ${totalKPI2Target.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div>
                        <p className="text-sm text-gray-900">${totalKPI2Actual.toLocaleString()}</p>
                        <div className="flex items-center justify-end">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className={`h-2 rounded-full ${
                                totalKPI2Actual >= totalKPI2Target
                                  ? "bg-green-500"
                                  : "bg-purple-500"
                              }`}
                              style={{
                                width: `${
                                  totalKPI2Target > 0
                                    ? Math.min(
                                        (totalKPI2Actual / totalKPI2Target) * 100,
                                        100
                                      )
                                    : 0
                                }%`,
                              }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-500">
                            {totalKPI2Target > 0
                              ? `${Math.round(
                                  (totalKPI2Actual / totalKPI2Target) * 100
                                )}%`
                              : "0%"}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleOpenDetails(wt)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </button>
                      <button
                        onClick={() => handleOpenEdit(wt)}
                        className="text-green-600 hover:text-green-900"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </button>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan="8" className="px-6 py-8 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <div className="bg-[#F4F5F9] p-4 rounded-full mb-4">
                      <FontAwesomeIcon icon={faListAlt} className="h-8 w-8 text-[#6E39CB]" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Targets Found</h3>
                    <p className="text-gray-500 mb-4 max-w-md text-center">
                      {searchQuery || dateFilter
                        ? "No targets match your search criteria. Try adjusting your filters."
                        : "No weekly targets found. Create one to get started."}
                    </p>
                    {(searchQuery || dateFilter) && (
                      <button
                        onClick={() => {
                          setSearchQuery("");
                          setDateFilter(null);
                        }}
                        className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        Clear Filters
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Modals */}
      {showCreateModal && (
        <CreateTargetModal
          agents={agents}
          agentsLoading={agentsLoading}
          agentsError={agentsError}
          onClose={handleCloseCreateModal}
          onCreate={handleCreateTarget}
        />
      )}

      {showEditModal && editTarget && (
        <EditTargetModal
          target={editTarget}
          agents={agents}
          agentsLoading={agentsLoading}
          agentsError={agentsError}
          onClose={handleCloseEdit}
          onUpdate={handleUpdateTarget}
        />
      )}

      {showDetailsModal && detailsTarget && (
        <DetailsModal targetData={detailsTarget} onClose={handleCloseDetails} />
      )}
    </div>
  );
};

export default WeeklyTargetsPage;
