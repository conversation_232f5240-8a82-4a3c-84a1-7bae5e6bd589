import React, { useState } from "react";
import { useGetAllEmployeeQuery, useDeleteEmployeeMutation } from "../../../services/AdminAPIService";
import AddEmployeeModal from "../../../components/modal/CreateEmployeeModal"; // Keep this separate if already built

// Inline Notification Modal Component
const NotificationModal = ({ message, type, onClose }) => (
  <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
    <div className="bg-white rounded-lg p-6 w-11/12 max-w-md shadow-xl">
      <div className="flex items-center mb-4">
        {type === "success" ? (
          <div className="bg-green-100 p-2 rounded-full mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        ) : (
          <div className="bg-red-100 p-2 rounded-full mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        )}
        <h2 className="text-xl font-bold">
          {type === "success" ? "Success" : "Error"}
        </h2>
      </div>
      <p className="mb-6 text-gray-600 pl-11">{message}</p>
      <div className="flex justify-end">
        <button
          onClick={onClose}
          className="bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  </div>
);

const Employees = () => {
  const {
    data: employees,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetAllEmployeeQuery();

  // State for Add Employee modal
  const [isAddEmployeeModalOpen, setIsAddEmployeeModalOpen] = useState(false);
  const handleOpenAddEmployeeModal = () => setIsAddEmployeeModalOpen(true);
  const handleCloseAddEmployeeModal = () => setIsAddEmployeeModalOpen(false);

  // State for search functionality
  const [searchTerm, setSearchTerm] = useState("");

  // Filtered employees based on search term
  const filteredEmployees = employees
    ? employees.filter(
        (employee) =>
          employee.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          employee.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (employee.phone && employee.phone.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : [];

  // State for Notification Modal
  const [modalInfo, setModalInfo] = useState({
    isOpen: false,
    message: "",
    type: "", // "success" or "error"
  });

  const closeModal = () => setModalInfo({ ...modalInfo, isOpen: false });

  // Delete mutation for employee removal
  const [deleteEmployee, { isLoading: isDeleting }] = useDeleteEmployeeMutation();

  const handleRemoveEmployee = async (employee) => {
    const confirmed = window.confirm(
      `Are you sure you want to remove ${employee.fullName} from the company?`
    );
    if (confirmed) {
      try {
        await deleteEmployee(employee.username).unwrap();
        setModalInfo({
          isOpen: true,
          message: "Employee removed successfully!",
          type: "success",
        });
        refetch();
      } catch (err) {
        setModalInfo({
          isOpen: true,
          message: "Failed to remove employee: " + (err?.data?.message || err?.message),
          type: "error",
        });
      }
    }
  };

  if (isLoading) return (
    <div className="flex justify-center items-center h-64">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6E39CB]"></div>
      <span className="ml-3 text-gray-500">Loading employees...</span>
    </div>
  );

  if (isError)
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-700">
              Error fetching employees: {error?.data?.message || error?.message}
            </p>
          </div>
        </div>
      </div>
    );

  return (
    <div className="w-full py-6">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Employees</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage company employees and their roles
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <button
            onClick={handleOpenAddEmployeeModal}
            className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add Employee
          </button>
        </div>
      </div>

      {/* Employees Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-50 overflow-x-auto">
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Employee List</h2>
            <div className="relative">
              <input
                type="text"
                placeholder="Search employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
              <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr className="bg-[#F4F5F9]">
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Email
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Phone
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-100">
            {!filteredEmployees || filteredEmployees.length === 0 ? (
              <tr>
                <td
                  colSpan={5}
                  className="px-6 py-8 whitespace-nowrap text-sm text-gray-500 text-center"
                >
                  <div className="flex flex-col items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <p>{searchTerm ? "No employees match your search." : "No employees added yet."}</p>
                  </div>
                </td>
              </tr>
            ) : (
              filteredEmployees.map((employee) => (
                <tr key={employee.username} className="hover:bg-[#F4F5F9] transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-[#F4F5F9] rounded-full flex items-center justify-center">
                        <span className="text-[#6E39CB] font-medium text-lg">{employee.fullName.charAt(0)}</span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{employee.fullName}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {employee.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${employee.role === 'ADMIN' ? 'bg-purple-100 text-purple-800' : employee.role === 'SALES' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>
                      {employee.role}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {employee.phone}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => handleRemoveEmployee(employee)}
                      className="flex items-center text-red-600 hover:text-red-900 transition-colors"
                      disabled={isDeleting}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Remove
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Add Employee Modal */}
      {isAddEmployeeModalOpen && (
        <AddEmployeeModal
          onClose={handleCloseAddEmployeeModal}
          onEmployeeAdded={refetch}
        />
      )}

      {/* Notification Modal */}
      {modalInfo.isOpen && (
        <NotificationModal
          message={modalInfo.message}
          type={modalInfo.type}
          onClose={closeModal}
        />
      )}
    </div>
  );
};

export default Employees;
