import { createApi } from "@reduxjs/toolkit/query/react";
import { createCustomBaseQuery } from "./customBaseQuery";

export const SourceOfTruthApiService = createApi({
  reducerPath: "SourceOfTruthApiService",
  baseQuery: createCustomBaseQuery("/api/truth"),
  tagTypes: ["XeroData"],
  endpoints: (builder) => ({
    // KPI1 - Xero Invoice endpoints
    createXeroInvoice: builder.mutation({
      query: (invoiceData) => ({
        url: "/xero/invoice/invoice",
        method: "POST",
        body: invoiceData,
      }),
      invalidatesTags: ["XeroData"],
    }),
    
    uploadXeroInvoice: builder.mutation({
      query: (file) => {
        const formData = new FormData();
        formData.append('file', file);
        return {
          url: '/xero/invoice/import',
          method: 'POST',
          body: formData,
          formData: true,
        };
      },
      invalidatesTags: ["XeroData"],
    }),
    
    getAllXeroInvoices: builder.query({
      query: () => '/xero/invoice/all',
      providesTags: ["XeroData"],
    }),
    
    // KPI2 - Xero In Bank endpoints
    createXeroInBank: builder.mutation({
      query: (inBankData) => ({
        url: "/xero/inbank",
        method: "POST",
        body: inBankData,
      }),
      invalidatesTags: ["XeroData"],
    }),
    
    uploadXeroInBank: builder.mutation({
      query: (file) => {
        const formData = new FormData();
        formData.append('file', file);
        return {
          url: '/xero/inbank/import',
          method: 'POST',
          body: formData,
          formData: true,
        };
      },
      invalidatesTags: ["XeroData"],
    }),
    
    getAllXeroInBank: builder.query({
      query: () => '/xero/inbank/all',
      providesTags: ["XeroData"],
    }),

    // Agent-specific endpoints
    getAgentXeroInvoices: builder.query({
      query: ({ agentUsername, startDate, endDate }) => {
        let url = `/xero/invoice/agent/${agentUsername}`;
        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        if (params.toString()) url += `?${params.toString()}`;
        return url;
      },
      providesTags: ["XeroData"],
    }),

    getAgentXeroInBank: builder.query({
      query: ({ agentUsername, startDate, endDate }) => {
        let url = `/xero/inbank/agent/${agentUsername}`;
        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        if (params.toString()) url += `?${params.toString()}`;
        return url;
      },
      providesTags: ["XeroData"],
    }),

    getPerformanceLeaderboard: builder.query({
      query: ({ startDate, endDate, targetId }) => {
        let url = '/performance/leaderboard';
        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        if (targetId) params.append('targetId', targetId);
        if (params.toString()) url += `?${params.toString()}`;
        return url;
      },
      providesTags: ["XeroData"],
    }),
  }),
});

export const {
  useCreateXeroInvoiceMutation,
  useUploadXeroInvoiceMutation,
  useGetAllXeroInvoicesQuery,
  useCreateXeroInBankMutation,
  useUploadXeroInBankMutation,
  useGetAllXeroInBankQuery,
  useGetAgentXeroInvoicesQuery,
  useGetAgentXeroInBankQuery,
  useGetPerformanceLeaderboardQuery,
} = SourceOfTruthApiService;
