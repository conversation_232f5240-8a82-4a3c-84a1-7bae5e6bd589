import React, { useState, useRef } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBan } from "@fortawesome/free-solid-svg-icons";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement
} from "chart.js";
import { Pie, Bar, Line } from "react-chartjs-2";

ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement
);

const EmployeeProfileWithTabs = () => {
  const [activeTab, setActiveTab] = useState("applications");
  
  // For "Applications" tab
  const [filter, setFilter] = useState("");
  const [sortOrder, setSortOrder] = useState("asc");

  // For "Activity" tab
  const [searchTerm, setSearchTerm] = useState("");

  // For the "Analysis" tab
  const [dateRange, setDateRange] = useState("week");
  const [customDateFrom, setCustomDateFrom] = useState("");
  const [customDateTo, setCustomDateTo] = useState("");

  // For Dump Leads Modal
  const [showDumpLeadsModal, setShowDumpLeadsModal] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);

  const fileInputRef = useRef(null);

  // Sample data for the "Applications" tab
  const staticStudents = [
    {
      fullName: "John Doe",
      emailAddress: "<EMAIL>",
      phoneNumber: "************",
      usiNumber: "USI12345",
      gender: "Male",
    },
    {
      fullName: "Jane Smith",
      emailAddress: "<EMAIL>",
      phoneNumber: "************",
      usiNumber: "USI54321",
      gender: "Female",
    },
    {
      fullName: "Alice Johnson",
      emailAddress: "<EMAIL>",
      phoneNumber: "************",
      usiNumber: "USI67890",
      gender: "Female",
    },
  ];

  // Sample data for the "Activity" tab
  const activities = [
    {
      date: "09/16/2019, 06:27PM",
      change: "Changed status of Lead (Rohit Sharma) to Warm Lead",
    },
    {
      date: "09/16/2019, 06:27PM",
      change:
        "Created an application for Certificate III - Basics of Something for an Applicant (Kyle Smith)",
    },
    {
      date: "09/16/2019, 06:27PM",
      change: "Downloaded Passport file of Applicant (Khaleda Zia)",
    },
  ];

  // Sort handler for "Applications" tab
  const handleSortChange = () => {
    setSortOrder((prevOrder) => (prevOrder === "asc" ? "desc" : "asc"));
  };

  // Filter students by user input
  const filteredStudents = staticStudents
    .filter((student) => {
      if (!filter) return true;
      const lowerFilter = filter.toLowerCase();
      return (
        student.fullName.toLowerCase().includes(lowerFilter) ||
        student.emailAddress.toLowerCase().includes(lowerFilter) ||
        student.phoneNumber.toLowerCase().includes(lowerFilter) ||
        student.usiNumber.toLowerCase().includes(lowerFilter)
      );
    })
    .sort((a, b) => {
      if (sortOrder === "asc") {
        return a.fullName.localeCompare(b.fullName);
      } else {
        return b.fullName.localeCompare(a.fullName);
      }
    });

  // Filter activities by user input
  const filteredActivities = activities.filter((activity) => {
    const lowerSearch = searchTerm.toLowerCase();
    return (
      activity.change.toLowerCase().includes(lowerSearch) ||
      activity.date.toLowerCase().includes(lowerSearch)
    );
  });

  // ---------------------
  // ANALYSIS TAB CHART DATA
  // ---------------------
  // Example: Pie chart for distribution of leads (Hot, Warm, Cold, Fresh)
  const leadsData = {
    labels: ["Hot", "Warm", "Cold", "Fresh"],
    datasets: [
      {
        label: "# of Leads",
        data: [25, 40, 20, 15], // example data
        backgroundColor: ["#ef4444", "#f59e0b", "#3b82f6", "#10b981"],
        hoverOffset: 4,
      },
    ],
  };

  // Example: Bar chart for top-selling Qualifications
  const qualificationsData = {
    labels: [
      "SHB30416 Cert III in Hairdressing",
      "SIS30321 Cert III in Fitness",
      "SIS40221 Cert IV in Fitness",
      "SHB40216 Cert IV in Hairdressing",
      "SHB40121 Cert IV in Beauty Therapy",
      "SHB50121 Diploma of Beauty Therapy",
      "HLT52015 Diploma of Remedial Massage",
      "SHB50216 Diploma of Salon Management",
    ],
    datasets: [
      {
        label: "Enrollments Sold",
        data: [12, 19, 8, 5, 14, 10, 7, 4], // example data
        backgroundColor: "#3b82f6",
      },
    ],
  };

  // Example: Line chart for Applications Over Time
  const applicationsData = {
    labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    datasets: [
      {
        label: "Applications",
        data: [10, 15, 8, 20, 12, 9, 4], // example data
        borderColor: "#3b82f6",
        backgroundColor: "#bfdbfe",
        fill: true,
      },
    ],
  };

  // Handle date range change
  const handleDateRangeChange = (e) => {
    setDateRange(e.target.value);
    // If you need to filter or fetch data based on dateRange, do it here.
  };

  // ---------------------
  // DUMP LEADS MODAL HANDLERS
  // ---------------------
  const handleDumpLeadsClick = () => {
    setShowDumpLeadsModal(true);
  };

  const handleModalClose = () => {
    setShowDumpLeadsModal(false);
    setSelectedFile(null);
    setDragActive(false);
  };

  // Handle file selection via input
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  // Handle drag events
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!dragActive) {
      setDragActive(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setSelectedFile(e.dataTransfer.files[0]);
    }
  };

  // Handle "Dump Now" button
  const handleDumpNow = () => {
    // Implement the actual uploading/dumping logic here
    // For example, send `selectedFile` to your backend API
    alert("Dumping leads with file: " + (selectedFile?.name || "No file"));
    // Close modal after completion
    handleModalClose();
  };

  return (
    <div className="px-4 py-6 space-y-6">
      {/* Combined Employee Info + Overview */}
      <div className="bg-white p-4 rounded-lg shadow-sm w-full">
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:justify-between md:items-start">
          {/* Left side: Employee Info */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Sadman Shabab
            </h2>
            <p className="text-sm text-gray-500">
              <span className="font-medium text-gray-700">Sales</span>
            </p>

            <div className="mt-4 space-y-1 text-sm text-gray-600">
              <div>
                <span className="font-medium text-gray-700">Created At: </span>
                28th Feb, 2024
              </div>
              <div>
                <span className="font-medium text-gray-700">Location: </span>
                Sydney, Australia
              </div>
              <div>
                <span className="font-medium text-gray-700">Email: </span>
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 underline ml-1"
                >
                  <EMAIL>
                </a>
              </div>
              <div>
                <span className="font-medium text-gray-700">Phone: </span>
                +880 11914 21477
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex flex-col mt-4 gap-2 md:flex-row">
              <button className="flex items-center justify-center gap-2 border border-blue-600 px-4 py-2 text-blue-600 text-sm font-medium rounded-md hover:bg-blue-50">
                <FontAwesomeIcon icon={faBan} />
                Suspend Access
              </button>

              {/* Dump Leads button triggers the modal */}
              <button
                className="flex items-center justify-center gap-2 bg-blue-600 px-4 py-2 text-white text-sm font-medium rounded-md hover:bg-blue-700"
                onClick={handleDumpLeadsClick}
              >
                Dump Leads
              </button>
            </div>
          </div>

          {/* Right side: Overview metrics */}
          <div className="grid grid-cols-2 gap-4 mt-6 md:mt-0 md:grid-cols-2">
            <div className="p-3 border rounded-md text-center">
              <h3 className="text-sm text-gray-600">Total Applications</h3>
              <p className="text-lg font-semibold text-blue-600">300</p>
            </div>
            <div className="p-3 border rounded-md text-center">
              <h3 className="text-sm text-gray-600">Completed</h3>
              <p className="text-lg font-semibold text-blue-600">200</p>
            </div>
            <div className="p-3 border rounded-md text-center">
              <h3 className="text-sm text-gray-600">Pending</h3>
              <p className="text-lg font-semibold text-blue-600">100</p>
            </div>
            <div className="p-3 border rounded-md text-center">
              <h3 className="text-sm text-gray-600">Active Leads</h3>
              <p className="text-lg font-semibold text-blue-600">50</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-8 border-b pb-1">
        <button
          className={`text-sm font-semibold ${
            activeTab === "applications"
              ? "text-blue-500 border-b-2 border-blue-500 pb-1"
              : "text-gray-600 hover:text-blue-500"
          }`}
          onClick={() => setActiveTab("applications")}
        >
          Applications
        </button>
        <button
          className={`text-sm font-semibold ${
            activeTab === "Activity"
              ? "text-blue-500 border-b-2 border-blue-500 pb-1"
              : "text-gray-600 hover:text-blue-500"
          }`}
          onClick={() => setActiveTab("Activity")}
        >
          Activity
        </button>
        <button
          className={`text-sm font-semibold ${
            activeTab === "leads"
              ? "text-blue-500 border-b-2 border-blue-500 pb-1"
              : "text-gray-600 hover:text-blue-500"
          }`}
          onClick={() => setActiveTab("leads")}
        >
          Leads
        </button>
        <button
          className={`text-sm font-semibold ${
            activeTab === "analysis"
              ? "text-blue-500 border-b-2 border-blue-500 pb-1"
              : "text-gray-600 hover:text-blue-500"
          }`}
          onClick={() => setActiveTab("analysis")}
        >
          Analysis
        </button>
      </div>

      {/* Tab Contents */}

      {/* APPLICATIONS TAB */}
      {activeTab === "applications" && (
        <div className="bg-white rounded-md shadow-sm p-4">
          {/* Filter & Sort */}
          <div className="flex flex-col md:flex-row items-center justify-between mb-4 gap-2">
            <input
              type="text"
              placeholder="Search by Name, Email, Phone, or USI Number"
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-md p-2 w-full md:w-1/2 text-sm"
            />
            <button
              onClick={handleSortChange}
              className="bg-blue-500 text-white px-4 py-2 text-sm rounded-md hover:bg-blue-600"
            >
              Sort by Name ({sortOrder === "asc" ? "A-Z" : "Z-A"})
            </button>
          </div>

          {/* Applications Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm text-left">
              <thead>
                <tr className="border-b text-gray-600">
                  <th className="py-2 px-4">Name</th>
                  <th className="py-2 px-4">Email</th>
                  <th className="py-2 px-4">Phone</th>
                  <th className="py-2 px-4">USI Number</th>
                  <th className="py-2 px-4">Gender</th>
                </tr>
              </thead>
              <tbody>
                {filteredStudents.length === 0 ? (
                  <tr>
                    <td
                      colSpan={5}
                      className="py-4 px-4 text-center text-gray-500"
                    >
                      There are no students connected with your account.
                    </td>
                  </tr>
                ) : (
                  filteredStudents.map((student, idx) => (
                    <tr
                      key={idx}
                      className="border-b hover:bg-gray-50 transition-colors"
                    >
                      <td className="py-2 px-4 font-medium">
                        {student.fullName}
                      </td>
                      <td className="py-2 px-4">{student.emailAddress}</td>
                      <td className="py-2 px-4">{student.phoneNumber}</td>
                      <td className="py-2 px-4">{student.usiNumber}</td>
                      <td className="py-2 px-4">{student.gender}</td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* ACTIVITY TAB */}
      {activeTab === "Activity" && (
        <div className="bg-white rounded-md shadow-sm p-4">
          <h1 className="text-base font-semibold mb-4">Activity Log</h1>
          <div className="flex justify-between items-center mb-4">
            <input
              type="text"
              placeholder="Search activity..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="border border-gray-300 rounded-md p-2 text-sm w-full md:w-1/3"
            />
          </div>
          <ul className="space-y-2">
            {filteredActivities.map((activity, index) => (
              <li
                key={index}
                className="p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <p className="text-xs text-gray-500">{activity.date}</p>
                <p className="text-sm text-gray-800 mt-1">{activity.change}</p>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* LEADS TAB */}
      {activeTab === "leads" && (
        <div className="bg-white rounded-md shadow-sm p-4">
          <h2 className="text-base font-semibold mb-2">Leads coming soon</h2>
          {/* Replace or expand with relevant leads info */}
        </div>
      )}

      {/* ANALYSIS TAB */}
      {activeTab === "analysis" && (
        <div className="bg-white rounded-md shadow-sm p-4">
          {/* Date Filters */}
          <div className="flex flex-col md:flex-row items-center gap-2 mb-4">
            <select
              value={dateRange}
              onChange={handleDateRangeChange}
              className="border border-gray-300 rounded-md p-2 text-sm"
            >
              <option value="week">Past Week</option>
              <option value="month">Past Month</option>
              <option value="year">Past Year</option>
              <option value="custom">Custom</option>
            </select>
            {dateRange === "custom" && (
              <div className="flex flex-col md:flex-row items-center gap-2">
                <input
                  type="date"
                  value={customDateFrom}
                  onChange={(e) => setCustomDateFrom(e.target.value)}
                  className="border border-gray-300 rounded-md p-2 text-sm"
                />
                <span className="text-sm text-gray-500">to</span>
                <input
                  type="date"
                  value={customDateTo}
                  onChange={(e) => setCustomDateTo(e.target.value)}
                  className="border border-gray-300 rounded-md p-2 text-sm"
                />
              </div>
            )}
          </div>

          {/* Charts Section */}
          {/* Leads Distribution & Applications Over Time side by side */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Pie Chart for Leads */}
            <div className="border rounded-md p-4">
              <h3 className="text-sm font-semibold mb-2">Leads Distribution</h3>
              <Pie data={leadsData} />
            </div>

            {/* Applications Over Time (Line Chart) */}
            <div className="border rounded-md p-4">
              <h3 className="text-sm font-semibold mb-2">
                Applications Over Time
              </h3>
              <div className="h-72">
                <Line
                  data={applicationsData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: "top",
                      },
                    },
                    scales: {
                      x: {
                        display: true,
                      },
                      y: {
                        display: true,
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>

          {/* Bar Chart for Top-Selling Qualifications (bottom) */}
          <div className="border rounded-md p-4">
            <h3 className="text-sm font-semibold mb-2">
              Top-Selling Qualifications
            </h3>
            <div className="h-64">
              <Bar
                data={qualificationsData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: { display: false },
                  },
                  scales: {
                    x: {
                      ticks: {
                        maxRotation: 45,
                        minRotation: 0,
                        font: {
                          size: 10,
                        },
                      },
                    },
                  },
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* DUMP LEADS MODAL */}
      {showDumpLeadsModal && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <div
            className="bg-white w-full max-w-md p-6 rounded shadow-lg relative"
            onDragEnter={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            {/* Close button */}
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
              onClick={handleModalClose}
            >
              <span className="sr-only">Close</span>
              &times;
            </button>

            <h2 className="text-lg font-semibold mb-4">Dump Leads</h2>

            <div
              className={`flex flex-col items-center justify-center border-2 border-dashed rounded-md p-6 mb-4 transition ${
                dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300"
              }`}
            >
              <p className="text-sm text-gray-500 mb-2">
                {selectedFile
                  ? `Selected File: ${selectedFile.name}`
                  : "Drag & drop an Excel or CSV file here"}
              </p>
              {!selectedFile && (
                <>
                  <p className="text-xs text-gray-400 mb-2">
                    (or click below to select a file)
                  </p>
                  <button
                    onClick={() => fileInputRef.current.click()}
                    className="bg-blue-600 text-white text-sm px-3 py-1 rounded hover:bg-blue-700"
                  >
                    Browse
                  </button>
                </>
              )}
              {/* Hidden file input */}
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                className="hidden"
                onChange={handleFileChange}
              />
            </div>

            {/* If file is selected, show it. Otherwise, instructions remain */}
            {selectedFile && (
              <div className="mb-4 text-sm text-green-600">
                File ready to upload: {selectedFile.name}
              </div>
            )}

            {/* Buttons */}
            <div className="flex justify-end gap-3">
              <button
                className="text-sm px-4 py-2 rounded border border-gray-300 hover:bg-gray-50"
                onClick={handleModalClose}
              >
                Cancel
              </button>
              <button
                className="text-sm px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700"
                onClick={handleDumpNow}
              >
                Dump Now
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeeProfileWithTabs;
