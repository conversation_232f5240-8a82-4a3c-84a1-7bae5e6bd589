import React from "react";
import TroubleCard from "./TroubleCard";
import TroubleTable from "./TroubleTable";

const TroubleList = ({
  troubles,
  viewMode,
  employees,
  onEdit,
  onDelete,
  onStatusChange,
  onAddComment,
}) => {
  if (viewMode === "table") {
    return (
      <div className="bg-white rounded-lg border border-[#DBDCDE] shadow-sm overflow-hidden">
        <TroubleTable
          troubles={troubles}
          employees={employees}
          onEdit={onEdit}
          onDelete={onDelete}
          onStatusChange={onStatusChange}
          onAddComment={onAddComment}
        />
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-6">
      {troubles.map((trouble, index) => (
        <TroubleCard
          key={trouble.troubleId || index}
          trouble={trouble}
          employees={employees}
          onEdit={onEdit}
          onDelete={onDelete}
          onStatusChange={onStatusChange}
          onAddComment={onAddComment}
        />
      ))}
    </div>
  );
};

export default TroubleList;
