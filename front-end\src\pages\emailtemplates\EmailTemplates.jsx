import React, { useState } from "react";
import Modal from "react-modal";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

// Set the app element for accessibility
Modal.setAppElement("#root");

const EmailTemplates = () => {
  const [filter, setFilter] = useState("");
  const [sortOrder, setSortOrder] = useState("asc");
  const [templates, setTemplates] = useState([
    {
      id: 1,
      name: "Candidate Welcome Email",
      createdAt: "2025-01-01",
      content: `
        <p>Dear {{CandidateName}},</p>
        <p>Welcome to Skill Sync Pty Ltd! We are excited to assist you in your career journey.</p>
        <p>Our team will be in touch shortly to discuss your qualifications and match you with suitable opportunities.</p>
        <p>Best regards,<br/>Skill Sync Team</p>
      `,
    },
    {
      id: 2,
      name: "Educational Partner Introduction",
      createdAt: "2025-01-05",
      content: `
        <p>Dear {{PartnerName}},</p>
        <p>We are pleased to introduce Skill Sync Pty Ltd, your trusted partner in Recruitment Process Outsourcing (RPO).</p>
        <p>Our services are designed to bridge the gap between educational providers and industry demands, ensuring that your graduates are industry-ready.</p>
        <p>Let's schedule a meeting to discuss how we can collaborate effectively.</p>
        <p>Best regards,<br/>{{YourName}}<br/>Skill Sync Pty Ltd</p>
      `,
    },
    {
      id: 3,
      name: "Monthly Performance Report",
      createdAt: "2025-01-10",
      content: `
        <p>Dear {{RecipientName}},</p>
        <p>Please find attached the monthly performance report for January 2025.</p>
        <p>This report includes key metrics on candidate placements, client engagements, and partnership developments.</p>
        <p>Feel free to reach out if you have any questions or need further details.</p>
        <p>Best regards,<br/>{{YourName}}<br/>Skill Sync Pty Ltd</p>
      `,
    },
  ]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({ name: "", content: "" });

  // Filter and sort logic
  const filteredTemplates = templates
    .filter((template) => {
      if (!filter) return true;
      return template.name.toLowerCase().includes(filter.toLowerCase());
    })
    .sort((a, b) => {
      if (sortOrder === "asc") {
        return a.name.localeCompare(b.name);
      } else {
        return b.name.localeCompare(a.name);
      }
    });

  const handleSortChange = () => {
    setSortOrder((prevOrder) => (prevOrder === "asc" ? "desc" : "asc"));
  };

  const handleEdit = (template) => {
    setIsEditMode(true);
    setFormData({ name: template.name, content: template.content });
    setCurrentTemplate(template);
    setIsAddModalOpen(true);
  };

  const handleDelete = (id) => {
    if (window.confirm("Are you sure you want to delete this template?")) {
      setTemplates((prevTemplates) =>
        prevTemplates.filter((template) => template.id !== id)
      );
    }
  };

  const openAddModal = () => {
    setIsEditMode(false);
    setFormData({ name: "", content: "" });
    setIsAddModalOpen(true);
  };

  const closeAddModal = () => {
    setIsAddModalOpen(false);
    setFormData({ name: "", content: "" });
    setCurrentTemplate(null);
  };

  const openViewModal = (template) => {
    setCurrentTemplate(template);
    setIsViewModalOpen(true);
  };

  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setCurrentTemplate(null);
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    if (isEditMode) {
      // Update existing template
      setTemplates((prevTemplates) =>
        prevTemplates.map((template) =>
          template.id === currentTemplate.id
            ? { ...template, name: formData.name, content: formData.content }
            : template
        )
      );
      alert("Template updated successfully!");
    } else {
      // Add new template
      const newTemplate = {
        id: Date.now(),
        name: formData.name,
        createdAt: new Date().toISOString().split("T")[0],
        content: formData.content,
      };
      setTemplates((prevTemplates) => [...prevTemplates, newTemplate]);
      alert("Template added successfully!");
    }
    closeAddModal();
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-4 space-x-2">
        <input
          type="text"
          placeholder="Search Templates"
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="border border-gray-300 rounded-lg p-2 w-1/2"
        />
        <div className="flex space-x-2">
          <button
            onClick={handleSortChange}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600"
          >
            Sort by Name ({sortOrder === "asc" ? "A-Z" : "Z-A"})
          </button>
          <button
            onClick={openAddModal}
            className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600"
          >
            Add Email Template
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created At
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredTemplates.length === 0 ? (
              <tr>
                <td
                  colSpan={3}
                  className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center"
                >
                  No templates found.
                </td>
              </tr>
            ) : (
              filteredTemplates.map((template) => (
                <tr
                  key={template.id}
                  className="hover:bg-gray-100 cursor-pointer"
                  onClick={() => openViewModal(template)}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {template.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {template.createdAt}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEdit(template);
                      }}
                      className="text-blue-600 hover:underline mr-4"
                    >
                      Edit
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(template.id);
                      }}
                      className="text-red-600 hover:underline"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onRequestClose={closeAddModal}
        contentLabel={isEditMode ? "Edit Email Template" : "Add Email Template"}
        className="max-w-3xl mx-auto mt-20 bg-white p-6 rounded-lg shadow-lg overflow-auto"
        overlayClassName="fixed inset-0 bg-gray-500 bg-opacity-75 flex justify-center items-start z-50"
      >
        <h2 className="text-2xl mb-4">
          {isEditMode ? "Edit Email Template" : "Add Email Template"}
        </h2>
        <form onSubmit={handleFormSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Template Name
            </label>
            <input
              type="text"
              value={formData.name}
              required
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="e.g., Candidate Welcome Email"
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Template Content
            </label>
            <ReactQuill
              theme="snow"
              value={formData.content}
              onChange={(content) =>
                setFormData({ ...formData, content })
              }
              className="mt-1"
              modules={{
                toolbar: [
                  [{ header: [1, 2, 3, false] }],
                  ["bold", "italic", "underline", "strike"],
                  [{ list: "ordered" }, { list: "bullet" }],
                  ["link", "image"],
                  ["clean"],
                ],
              }}
              formats={[
                "header",
                "bold",
                "italic",
                "underline",
                "strike",
                "list",
                "bullet",
                "link",
                "image",
              ]}
            />
            <p className="text-xs text-gray-500 mt-2">
              Use placeholders like <code>{`{{CandidateName}}`}</code>,{" "}
              <code>{`{{PartnerName}}`}</code>, or{" "}
              <code>{`{{YourName}}`}</code> for dynamic content.
            </p>
          </div>
          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={closeAddModal}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              {isEditMode ? "Update" : "Add"}
            </button>
          </div>
        </form>
      </Modal>

      {/* View Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onRequestClose={closeViewModal}
        contentLabel="View Email Template"
        className="max-w-3xl mx-auto mt-20 bg-white p-6 rounded-lg shadow-lg overflow-auto"
        overlayClassName="fixed inset-0 bg-gray-500 bg-opacity-75 flex justify-center items-start z-50"
      >
        {currentTemplate && (
          <>
            <h2 className="text-2xl mb-4">{currentTemplate.name}</h2>
            <p className="text-gray-500 mb-2">Created At: {currentTemplate.createdAt}</p>
            <div
              className="prose max-w-none mb-4"
              dangerouslySetInnerHTML={{ __html: currentTemplate.content }}
            ></div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  handleEdit(currentTemplate);
                }}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Edit
              </button>
              <button
                onClick={closeViewModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </>
        )}
      </Modal>
    </div>
  );
};

export default EmailTemplates;
