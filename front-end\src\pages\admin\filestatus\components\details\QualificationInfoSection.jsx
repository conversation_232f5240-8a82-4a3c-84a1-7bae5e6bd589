import React from "react";
import InfoCard from "./InfoCard";

/**
 * QualificationInfoSection component for displaying qualification information
 * @param {string} qualificationCode - Qualification code
 * @param {string} qualificationName - Qualification name
 */
const QualificationInfoSection = ({ qualificationCode, qualificationName }) => {
  // Prepare items for InfoCard
  const items = [
    { 
      label: "Qualification Name", 
      value: qualificationName || "N/A",
      copyable: true
    },
    { 
      label: "Qualification Code", 
      value: qualificationCode || "N/A",
      copyable: true
    }
  ];

  return (
    <InfoCard 
      title="Qualification Information"
      items={items}
    />
  );
};

export default QualificationInfoSection;
