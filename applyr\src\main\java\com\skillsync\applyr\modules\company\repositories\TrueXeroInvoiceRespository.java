package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.TrueXeroInvoice;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;

public interface TrueXeroInvoiceRespository extends JpaRepository<TrueXeroInvoice, Long> {

    List<TrueXeroInvoice> findAllByInvoiceDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    TrueXeroInvoice findByInvoiceNumberAndContactNameAndInvoiceDateAndInvoiceExpiryDate(String invoiceNumber, String contact, LocalDateTime invoiceDate, LocalDateTime dueDate);

    // Agent-specific queries
    List<TrueXeroInvoice> findByAgentUsername(String agentUsername);

    List<TrueXeroInvoice> findByAgentUsernameAndInvoiceDateBetween(String agentUsername, LocalDateTime startDate, LocalDateTime endDate);

    List<TrueXeroInvoice> findByInvoiceDateBetween(LocalDateTime startDate, LocalDateTime endDate);
}
