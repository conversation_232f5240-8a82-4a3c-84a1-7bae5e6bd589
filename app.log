nohup: ignoring input
[[1;34mINFO[m] Scanning for projects...
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-metadata.xml
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/maven-metadata.xml
Progress (1): 2.8/14 kB
Progress (2): 2.8/14 kB | 2.8/21 kB
Progress (2): 5.5/14 kB | 2.8/21 kB
Progress (2): 5.5/14 kB | 5.5/21 kB
Progress (2): 8.3/14 kB | 5.5/21 kB
Progress (2): 8.3/14 kB | 8.3/21 kB
Progress (2): 8.3/14 kB | 11/21 kB 
Progress (2): 11/14 kB | 11/21 kB 
Progress (2): 11/14 kB | 14/21 kB
Progress (2): 14/14 kB | 14/21 kB
Progress (2): 14 kB | 14/21 kB   
Progress (2): 14 kB | 17/21 kB
Progress (2): 14 kB | 19/21 kB
Progress (2): 14 kB | 21 kB   
                           
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/maven-metadata.xml (21 kB at 36 kB/s)
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-metadata.xml (14 kB at 25 kB/s)
[[1;34mINFO[m] [1m------------------------------------------------------------------------[m
[[1;34mINFO[m] [1;31mBUILD FAILURE[m
[[1;34mINFO[m] [1m------------------------------------------------------------------------[m
[[1;34mINFO[m] Total time:  1.760 s
[[1;34mINFO[m] Finished at: 2025-03-13T03:51:35Z
[[1;34mINFO[m] [1m------------------------------------------------------------------------[m
[[1;31mERROR[m] No plugin found for prefix 'spring-boot' in the current project and in the plugin groups [org.apache.maven.plugins, org.codehaus.mojo] available from the repositories [local (/root/.m2/repository), central (https://repo.maven.apache.org/maven2)] -> [1m[Help 1][m
[[1;31mERROR[m] 
[[1;31mERROR[m] To see the full stack trace of the errors, re-run Maven with the [1m-e[m switch.
[[1;31mERROR[m] Re-run Maven using the [1m-X[m switch to enable full debug logging.
[[1;31mERROR[m] 
[[1;31mERROR[m] For more information about the errors and possible solutions, please read the following articles:
[[1;31mERROR[m] [1m[Help 1][m http://cwiki.apache.org/confluence/display/MAVEN/NoPluginFoundForPrefixException
