import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faSort, faUser, faUserCog, faCalendarAlt } from "@fortawesome/free-solid-svg-icons";

const TroubleFilters = ({
  searchText,
  setSearchText,
  selectedStatus,
  setSelectedStatus,
  createdByFilter,
  setCreatedByFilter,
  staffFilter,
  setStaffFilter,
  dateFilterType,
  setDateFilterType,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  employees,
}) => {
  return (
    <div className="bg-white p-4 border border-[#DBDCDE] border-t-0 rounded-b-lg mb-6">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search Bar */}
        <div className="relative md:w-1/3">
          <input
            type="text"
            placeholder="Search questions..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="w-full h-11 rounded-lg border border-[#DBDCDE] pl-10 pr-4 text-sm focus:border-[#6E39CB] focus:outline-none"
          />
          <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-3.5 text-[#89868D]" />
        </div>
        {/* Filters */}
        <div className="flex flex-wrap gap-4 md:ml-auto">
          {/* Status Filter */}
          <div className="relative">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
            >
              <option value="">All Status</option>
              <option value="resolved">Resolved</option>
              <option value="pending">Pending</option>
              <option value="inProgress">In Progress</option>
              <option value="noSolution">No Solution</option>
            </select>
            <FontAwesomeIcon icon={faSort} className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none" />
          </div>
          {/* Created By Filter */}
          <div className="relative">
            <select
              value={createdByFilter}
              onChange={(e) => setCreatedByFilter(e.target.value)}
              className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
            >
              <option value="all">All Creators</option>
              {employees &&
                employees.map((employee) => (
                  <option key={employee.username} value={employee.username}>
                    {employee.fullName}
                  </option>
                ))}
            </select>
            <FontAwesomeIcon icon={faUser} className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none" />
          </div>
          {/* Assigned To Filter */}
          <div className="relative">
            <select
              value={staffFilter}
              onChange={(e) => setStaffFilter(e.target.value)}
              className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
            >
              <option value="all">All Assignees</option>
              {employees &&
                employees.map((employee) => (
                  <option key={employee.username} value={employee.username}>
                    {employee.fullName}
                  </option>
                ))}
            </select>
            <FontAwesomeIcon icon={faUserCog} className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none" />
          </div>
          {/* Date Filter */}
          <div className="relative">
            <select
              value={dateFilterType}
              onChange={(e) => setDateFilterType(e.target.value)}
              className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
            >
              <option value="thisWeek">This Week</option>
              <option value="thisMonth">This Month</option>
              <option value="custom">Custom Date</option>
              <option value="all">All Dates</option>
            </select>
            <FontAwesomeIcon icon={faCalendarAlt} className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none" />
          </div>
        </div>
      </div>
      {dateFilterType === "custom" && (
        <div className="flex items-center gap-2 mt-4">
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="h-11 rounded-lg border border-[#DBDCDE] px-4 text-sm focus:border-[#6E39CB] focus:outline-none"
          />
          <span className="text-[#89868D]">to</span>
          <input
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            className="h-11 rounded-lg border border-[#DBDCDE] px-4 text-sm focus:border-[#6E39CB] focus:outline-none"
          />
        </div>
      )}
    </div>
  );
};

export default TroubleFilters;
