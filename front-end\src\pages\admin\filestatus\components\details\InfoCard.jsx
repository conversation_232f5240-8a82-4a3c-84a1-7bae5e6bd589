import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCopy, faCheck } from "@fortawesome/free-solid-svg-icons";

/**
 * InfoCard component for displaying information in a card format with copy functionality
 * @param {string} title - Card title
 * @param {array} items - Array of items to display (label, value)
 * @param {string} className - Additional CSS classes
 */
const InfoCard = ({ title, items, className = "" }) => {
  const [copied, setCopied] = useState(null);

  // Copy text to clipboard
  const copyToClipboard = (text, field) => {
    navigator.clipboard.writeText(text);
    setCopied(field);
    setTimeout(() => setCopied(null), 2000);
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-100 shadow-sm p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      
      <div className="space-y-4">
        {items.map((item, index) => (
          <InfoItem 
            key={index}
            label={item.label} 
            value={item.value || "N/A"} 
            onCopy={() => copyToClipboard(item.value, item.label)}
            copied={copied === item.label}
            copyable={item.copyable !== false}
          />
        ))}
      </div>
    </div>
  );
};

// Helper component for info items with copy functionality
const InfoItem = ({ label, value, onCopy, copied, copyable = true }) => {
  return (
    <div className="flex justify-between items-center">
      <span className="text-sm font-medium text-gray-500">{label}:</span>
      <div className="flex items-center">
        <span className="text-sm text-gray-900 mr-2">{value}</span>
        {copyable && (
          <button 
            onClick={onCopy}
            className="text-gray-400 hover:text-gray-600"
            title="Copy to clipboard"
          >
            <FontAwesomeIcon icon={copied ? faCheck : faCopy} className={copied ? "text-green-500" : ""} />
          </button>
        )}
      </div>
    </div>
  );
};

export default InfoCard;
