import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSpinner } from "@fortawesome/free-solid-svg-icons";

const LoadingSpinner = ({ size = "lg", message = "Loading..." }) => {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <FontAwesomeIcon icon={faSpinner} spin size={size} className="text-blue-600 mb-4" />
      <p className="text-gray-600">{message}</p>
    </div>
  );
};

export default LoadingSpinner;
