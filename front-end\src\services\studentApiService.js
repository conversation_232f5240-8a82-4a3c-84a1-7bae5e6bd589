// StudentAPIService.js

import { createApi } from '@reduxjs/toolkit/query/react';
import { createCustomBaseQuery } from './customBaseQuery';

export const StudentAPIService = createApi({
  reducerPath: 'StudentAPIService',
  baseQuery: createCustomBaseQuery('/api/'),
  endpoints: (builder) => ({
    getDocumentTypes: builder.query({
      query: () => 'student/document/types',
    }),
    uploadDocuments: builder.mutation({
      query: (formData) => ({
        url: 'student/upload/documents',
        method: 'PUT',
        body: formData,
      }),
    }),
    submitApplication: builder.mutation({
      query: () => ({
        url: 'student/application/submit', // URL for the submit application endpoint
        method: 'PUT', // HTTP method is PUT
      }),
    }),
  }),
});

export const {
  useGetDocumentTypesQuery,
  useUploadDocumentsMutation,
  useSubmitApplicationMutation,
} = StudentAPIService;
