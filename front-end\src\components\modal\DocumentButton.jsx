// src/components/DocumentButton.jsx

import React from "react";
import buttonImg from "../../assets/file.png"; // Corrected Path


const DocumentButton = ({ title, date, fileLink, handleOpenModal }) => {
  return (
    <button
      className="flex items-center bg-white p-4 rounded-lg shadow-sm border border-gray-300 w-full cursor-pointer hover:bg-gray-100 transition-colors"
      onClick={() => handleOpenModal({ documentName: title, fileLink, issues: [] })} // Pass issues if available
    >
      <img src={buttonImg} alt="Document" className="w-14 h-14 mr-4" />
      <div className="h-full w-px bg-gray-300 mr-4"></div>
      <div className="flex flex-col text-left">
        <p className="font-semibold text-gray-700">{title}</p>
        <p className="text-gray-500 text-sm">{date}</p>
      </div>
    </button>
  );
};

export default DocumentButton;
