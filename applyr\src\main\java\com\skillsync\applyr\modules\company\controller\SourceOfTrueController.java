package com.skillsync.applyr.modules.company.controller;


import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.company.models.*;
import com.skillsync.applyr.modules.company.services.SourceOfTruthServices;
import com.skillsync.applyr.modules.company.xero_models.TrueXeroInvoiceDTO;
import com.skillsync.applyr.modules.company.xero_models.TrueXeroInvoiceRequestDTO;
import com.skillsync.applyr.modules.company.xero_models.TrueXeroInBankDTO;
import com.skillsync.applyr.modules.company.xero_models.TrueXeroInBankRequestDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/truth")
@PreAuthorize("hasRole('ADMIN')")
public class SourceOfTrueController {

    private final SourceOfTruthServices sourceOfTruthServices;

    public SourceOfTrueController(SourceOfTruthServices sourceOfTruthServices) {
        this.sourceOfTruthServices = sourceOfTruthServices;
    }


    // a method that takes in truexeroinvoicerequestdto and creates an entry in the database
    @PostMapping("/xero/invoice/invoice")
    public ResponseEntity<SuccessResponse> createXeroInvoice(@RequestBody TrueXeroInvoiceRequestDTO requestDTO) {
        return ResponseEntity.ok(sourceOfTruthServices.createXeroInvoice(requestDTO));
    }

    // a method that takes in a excel file containing xero invoice data and uploads it to the database
    @PostMapping("/xero/invoice/import")
    public ResponseEntity<SuccessResponse> uploadXeroInvoice(@RequestParam("file") MultipartFile file) throws Exception {
        return ResponseEntity.ok(sourceOfTruthServices.uploadXeroInvoiceData(file));
    }

    // Create a single TrueXeroInBank entry
    @PostMapping("/xero/inbank")
    public ResponseEntity<SuccessResponse> createTrueXeroInBank(@RequestBody TrueXeroInBankRequestDTO requestDTO) {
        return ResponseEntity.ok(sourceOfTruthServices.createTrueXeroInBank(requestDTO));
    }

    // Upload TrueXeroInBank entries from Excel
    @PostMapping("/xero/inbank/import")
    public ResponseEntity<SuccessResponse> uploadTrueXeroInBankExcel(@RequestParam("file") MultipartFile file) throws Exception {
        return ResponseEntity.ok(sourceOfTruthServices.uploadTrueXeroInBankExcel(file));
    }

    // Get all TrueXeroInBank entries
    @GetMapping("/xero/inbank/all")
    public ResponseEntity<List<TrueXeroInBankDTO>> getAllTrueXeroInBank() {
        return ResponseEntity.ok(sourceOfTruthServices.getAllTrueXeroInBank());
    }

    @GetMapping("/xero/invoice/all")
    public ResponseEntity<List<TrueXeroInvoiceDTO>> getAllTrueXeroInvoiceData() {
        return ResponseEntity.ok(sourceOfTruthServices.getAllTrueXeroInvoiceData());
    }

    // Get KPI1 data filtered by agent username and date range
    @GetMapping("/xero/invoice/agent/{agentUsername}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SALES')")
    public ResponseEntity<List<TrueXeroInvoiceDTO>> getAgentXeroInvoiceData(
            @PathVariable String agentUsername,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        return ResponseEntity.ok(sourceOfTruthServices.getAgentXeroInvoiceData(agentUsername, startDate, endDate));
    }

    // Get KPI2 data filtered by agent username and date range
    @GetMapping("/xero/inbank/agent/{agentUsername}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SALES')")
    public ResponseEntity<List<TrueXeroInBankDTO>> getAgentXeroInBankData(
            @PathVariable String agentUsername,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        return ResponseEntity.ok(sourceOfTruthServices.getAgentXeroInBankData(agentUsername, startDate, endDate));
    }

    // Get agent performance data for leaderboard
    @GetMapping("/performance/leaderboard")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SALES')")
    public ResponseEntity<Object> getPerformanceLeaderboard(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Long targetId) {
        return ResponseEntity.ok(sourceOfTruthServices.getPerformanceLeaderboard(startDate, endDate, targetId));
    }

}
