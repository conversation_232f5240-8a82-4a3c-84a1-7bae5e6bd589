import React from "react";
import { <PERSON>hn<PERSON>, <PERSON> } from "react-chartjs-2";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCalendarAlt,
  faChartPie,
  faChartLine,
  faListAlt,
  faUsers,
  faPrint,
  faCheckCircle,
  faTrophy,
  faLightbulb,
  faMoneyBillWave,
  faFileInvoiceDollar,
  faTasks,
} from "@fortawesome/free-solid-svg-icons";

const OverviewTab = ({
  targetData,
  formattedStart,
  formattedEnd,
  totals,
  sortedPerformers,
  kpi1DonutData,
  kpi2DonutData,
  donutOptions,
  barData,
  barOptions,
}) => {
  return (
    <>
      {/* Executive Summary Banner */}
      <div className="bg-gradient-to-r from-[#F4F5F9] to-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">Executive Summary</h2>
            <p className="text-sm text-gray-600 mb-2">
              Period: {formattedStart} - {formattedEnd} (
              {Math.ceil(
                (new Date(targetData.endDate) - new Date(targetData.startDate)) /
                  (1000 * 60 * 60 * 24)
              )}{" "}
              days)
            </p>
            <div className="flex flex-wrap gap-3 mt-2">
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  totals.kpi1Progress >= 100
                    ? "bg-green-100 text-green-800"
                    : totals.kpi1Progress >= 75
                    ? "bg-blue-100 text-blue-800"
                    : "bg-orange-100 text-orange-800"
                }`}
              >
                KPI1: {totals.kpi1Progress}% of target
              </span>
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  totals.kpi2Progress >= 100
                    ? "bg-green-100 text-green-800"
                    : totals.kpi2Progress >= 75
                    ? "bg-blue-100 text-blue-800"
                    : "bg-orange-100 text-orange-800"
                }`}
              >
                KPI2: {totals.kpi2Progress}% of target
              </span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                {targetData.targets.length} Agents
              </span>
            </div>
          </div>
          <div className="mt-4 md:mt-0 flex items-center">
            {/* Export Report button is managed in the main header */}
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {/* KPI1 Progress Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faMoneyBillWave} className="h-5 w-5 text-[#6E39CB]" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700">KPI1 Progress</h3>
              <p className="text-xs text-gray-500">Revenue Generation</p>
            </div>
          </div>
          <div className="flex items-end justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                ${totals.totalKPI1Actual.toLocaleString()}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                of ${totals.totalKPI1Target.toLocaleString()} target
              </p>
            </div>
            <div className="flex flex-col items-end">
              <div className="flex items-center">
                {totals.kpi1Progress >= 100 ? (
                  <span className="flex items-center text-green-600 text-sm font-medium">
                    <FontAwesomeIcon icon={faCheckCircle} className="mr-1" />
                    Achieved
                  </span>
                ) : (
                  <span
                    className={`flex items-center text-sm font-medium ${
                      totals.kpi1Progress >= 75 ? "text-blue-600" : "text-orange-500"
                    }`}
                  >
                    {totals.kpi1Progress}%
                  </span>
                )}
              </div>
              <span className="text-xs text-gray-500 mt-1">
                {totals.kpi1Progress >= 100 ? "Target exceeded" : `${100 - totals.kpi1Progress}% remaining`}
              </span>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full ${
                  totals.kpi1Progress >= 100 ? "bg-green-500" : totals.kpi1Progress >= 75 ? "bg-[#6E39CB]" : "bg-orange-500"
                }`}
                style={{ width: `${Math.min(100, totals.kpi1Progress)}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* KPI2 Progress Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-5 w-5 text-[#6E39CB]" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700">KPI2 Progress</h3>
              <p className="text-xs text-gray-500">Invoicing</p>
            </div>
          </div>
          <div className="flex items-end justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                ${totals.totalKPI2Actual.toLocaleString()}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                of ${totals.totalKPI2Target.toLocaleString()} target
              </p>
            </div>
            <div className="flex flex-col items-end">
              <div className="flex items-center">
                {totals.kpi2Progress >= 100 ? (
                  <span className="flex items-center text-green-600 text-sm font-medium">
                    <FontAwesomeIcon icon={faCheckCircle} className="mr-1" />
                    Achieved
                  </span>
                ) : (
                  <span
                    className={`flex items-center text-sm font-medium ${
                      totals.kpi2Progress >= 75 ? "text-blue-600" : "text-orange-500"
                    }`}
                  >
                    {totals.kpi2Progress}%
                  </span>
                )}
              </div>
              <span className="text-xs text-gray-500 mt-1">
                {totals.kpi2Progress >= 100 ? "Target exceeded" : `${100 - totals.kpi2Progress}% remaining`}
              </span>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full ${
                  totals.kpi2Progress >= 100 ? "bg-green-500" : totals.kpi2Progress >= 75 ? "bg-[#6E39CB]" : "bg-orange-500"
                }`}
                style={{ width: `${Math.min(100, totals.kpi2Progress)}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Agents Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faUsers} className="h-5 w-5 text-[#6E39CB]" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700">Team Performance</h3>
              <p className="text-xs text-gray-500">Agent Activity</p>
            </div>
          </div>
          <div className="flex items-end justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {targetData.targets.length}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Total agents assigned
              </p>
            </div>
            <div className="flex flex-col items-end">
              <div className="flex items-center">
                <span className="flex items-center text-sm font-medium text-blue-600">
                  {targetData.targets.filter((t) => t.kpi1Actual > 0 || t.kpi2Actual > 0).length} Active
                </span>
              </div>
              <span className="text-xs text-gray-500 mt-1">
                {Math.round(
                  (targetData.targets.filter((t) => t.kpi1Actual > 0 || t.kpi2Actual > 0).length /
                    targetData.targets.length) *
                    100
                )}
                % participation
              </span>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-blue-500 h-3 rounded-full"
                style={{
                  width: `${Math.round(
                    (targetData.targets.filter((t) => t.kpi1Actual > 0 || t.kpi2Actual > 0).length /
                      targetData.targets.length) *
                      100
                  )}%`,
                }}
              ></div>
            </div>
          </div>
        </div>

        {/* Applications Card */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center mb-4">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
              <FontAwesomeIcon icon={faTasks} className="h-5 w-5 text-[#6E39CB]" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700">Applications</h3>
              <p className="text-xs text-gray-500">Submission Status</p>
            </div>
          </div>
          <div className="flex items-end justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {targetData.targets.reduce(
                  (sum, t) => sum + (t.applications?.length || 0),
                  0
                )}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Total applications
              </p>
            </div>
            <div className="flex flex-col items-end">
              <div className="flex items-center">
                <span className="flex items-center text-sm font-medium text-green-600">
                  {targetData.targets.reduce(
                    (sum, t) => sum + ((t.applications || []).filter(a => a.status === 'COMPLETED').length),
                    0
                  )} Completed
                </span>
              </div>
              <span className="text-xs text-gray-500 mt-1">
                {targetData.targets.reduce(
                  (sum, t) => sum + ((t.applications || []).filter(a => a.status === 'IN_PROGRESS').length),
                  0
                )} in progress
              </span>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-green-500 h-3 rounded-full"
                style={{
                  width: `${Math.round(
                    (targetData.targets.reduce(
                      (sum, t) => sum + ((t.applications || []).filter(a => a.status === 'COMPLETED').length),
                      0
                    ) /
                      Math.max(1, targetData.targets.reduce(
                        (sum, t) => sum + (t.applications?.length || 0),
                        0
                      ))) * 100
                  )}%`,
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Data Visualization Section */}
      <div className="mb-6">
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          <div className="flex flex-col md:flex-row justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Performance Dashboard</h3>
            <div className="flex space-x-2 mt-2 md:mt-0">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#F4F5F9] text-[#6E39CB]">
                <FontAwesomeIcon icon={faChartPie} className="mr-1" /> KPI Metrics
              </span>
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#F4F5F9] text-[#6E39CB]">
                <FontAwesomeIcon icon={faUsers} className="mr-1" /> {targetData.targets.length} Agents
              </span>
            </div>
          </div>

          {/* KPI Comparison Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* KPI1 Performance Chart */}
            <div className="bg-[#F9FAFB] rounded-lg p-4 border border-gray-100">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-sm font-medium text-gray-700">KPI1 Performance</h4>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${totals.kpi1Progress >= 100 ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>
                  ${totals.totalKPI1Actual.toLocaleString()} / ${totals.totalKPI1Target.toLocaleString()}
                </span>
              </div>
              <div className="flex flex-col md:flex-row items-center">
                <div className="w-40 h-40 relative mb-4 md:mb-0">
                  <Doughnut data={kpi1DonutData} options={donutOptions} />
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <span className="text-2xl font-bold text-[#6E39CB]">
                      {totals.kpi1Progress}%
                    </span>
                    <span className="text-xs text-gray-500">Complete</span>
                  </div>
                </div>
                <div className="md:ml-6 flex-grow">
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-xs font-medium text-gray-500">Target</span>
                        <span className="text-xs font-medium text-gray-700">
                          ${totals.totalKPI1Target.toLocaleString()}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div className="bg-gray-400 h-1.5 rounded-full" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-xs font-medium text-gray-500">Actual</span>
                        <span className="text-xs font-medium text-gray-700">
                          ${totals.totalKPI1Actual.toLocaleString()}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div
                          className={`h-1.5 rounded-full ${totals.kpi1Progress >= 100 ? 'bg-green-500' : 'bg-[#6E39CB]'}`}
                          style={{ width: `${Math.min(100, totals.kpi1Progress)}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Top Performer for KPI1 */}
                    {sortedPerformers.topKPI1Performers.length > 0 && (
                      <div className="mt-4 pt-3 border-t border-gray-100">
                        <p className="text-xs font-medium text-gray-500 mb-2">Top Performer</p>
                        <div className="flex items-center">
                          <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-2 flex-shrink-0">
                            <span className="text-[#6E39CB] font-medium text-xs">
                              {sortedPerformers.topKPI1Performers[0].profile?.fullName
                                ? sortedPerformers.topKPI1Performers[0].profile.fullName.charAt(0).toUpperCase()
                                : "?"}
                            </span>
                          </div>
                          <div className="flex-grow">
                            <div className="flex justify-between items-center">
                              <span className="text-xs font-medium">{sortedPerformers.topKPI1Performers[0].profile?.fullName}</span>
                              <span className="text-xs font-medium">
                                ${sortedPerformers.topKPI1Performers[0].kpi1Actual.toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* KPI2 Performance Chart */}
            <div className="bg-[#F9FAFB] rounded-lg p-4 border border-gray-100">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-sm font-medium text-gray-700">KPI2 Performance</h4>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${totals.kpi2Progress >= 100 ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>
                  ${totals.totalKPI2Actual.toLocaleString()} / ${totals.totalKPI2Target.toLocaleString()}
                </span>
              </div>
              <div className="flex flex-col md:flex-row items-center">
                <div className="w-40 h-40 relative mb-4 md:mb-0">
                  <Doughnut data={kpi2DonutData} options={donutOptions} />
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <span className="text-2xl font-bold text-[#6E39CB]">
                      {totals.kpi2Progress}%
                    </span>
                    <span className="text-xs text-gray-500">Complete</span>
                  </div>
                </div>
                <div className="md:ml-6 flex-grow">
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-xs font-medium text-gray-500">Target</span>
                        <span className="text-xs font-medium text-gray-700">
                          ${totals.totalKPI2Target.toLocaleString()}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div className="bg-gray-400 h-1.5 rounded-full" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-xs font-medium text-gray-500">Actual</span>
                        <span className="text-xs font-medium text-gray-700">
                          ${totals.totalKPI2Actual.toLocaleString()}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div
                          className={`h-1.5 rounded-full ${totals.kpi2Progress >= 100 ? 'bg-green-500' : 'bg-[#6E39CB]'}`}
                          style={{ width: `${Math.min(100, totals.kpi2Progress)}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Top Performer for KPI2 */}
                    {sortedPerformers.topKPI2Performers.length > 0 && (
                      <div className="mt-4 pt-3 border-t border-gray-100">
                        <p className="text-xs font-medium text-gray-500 mb-2">Top Performer</p>
                        <div className="flex items-center">
                          <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-2 flex-shrink-0">
                            <span className="text-[#6E39CB] font-medium text-xs">
                              {sortedPerformers.topKPI2Performers[0].profile?.fullName
                                ? sortedPerformers.topKPI2Performers[0].profile.fullName.charAt(0).toUpperCase()
                                : "?"}
                            </span>
                          </div>
                          <div className="flex-grow">
                            <div className="flex justify-between items-center">
                              <span className="text-xs font-medium">{sortedPerformers.topKPI2Performers[0].profile?.fullName}</span>
                              <span className="text-xs font-medium">
                                ${sortedPerformers.topKPI2Performers[0].kpi2Actual.toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Team Performance Chart */}
          <div className="bg-[#F9FAFB] rounded-lg p-4 border border-gray-100">
            <div className="flex justify-between items-center mb-4">
              <h4 className="text-sm font-medium text-gray-700">Team Contribution</h4>
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {targetData.targets.filter((t) => t.kpi1Actual > 0 || t.kpi2Actual > 0).length} Active Agents
              </span>
            </div>
            <div className="h-64">
              <Bar data={barData} options={barOptions} />
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">
              Comparison of target vs. actual values for all agents
            </p>
          </div>
        </div>
      </div>

      {/* Team Leaderboard */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Team Leaderboard</h3>
          <div className="flex space-x-2 mt-2 md:mt-0">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#F4F5F9] text-[#6E39CB]">
              <FontAwesomeIcon icon={faTrophy} className="mr-1" /> Top Performers
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* KPI1 Leaderboard */}
          <div>
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-sm font-medium text-gray-700">KPI1 Top Performers</h4>
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Revenue Generation
              </span>
            </div>

            <div className="bg-[#F9FAFB] rounded-lg p-4 border border-gray-100">
              <div className="space-y-4">
                {sortedPerformers.topKPI1Performers.slice(0, 5).map((agent, index) => (
                  <div key={index} className="flex items-center">
                    <div className="flex-shrink-0 w-8 text-center">
                      <span
                        className={`inline-flex items-center justify-center h-6 w-6 rounded-full text-xs font-bold ${
                          index === 0
                            ? "bg-yellow-100 text-yellow-800"
                            : index === 1
                            ? "bg-gray-200 text-gray-700"
                            : index === 2
                            ? "bg-orange-100 text-orange-800"
                            : "bg-gray-100 text-gray-600"
                        }`}
                      >
                        {index + 1}
                      </span>
                    </div>
                    <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mx-2 flex-shrink-0">
                      <span className="text-[#6E39CB] font-medium text-xs">
                        {agent.profile?.fullName ? agent.profile.fullName.charAt(0).toUpperCase() : "?"}
                      </span>
                    </div>
                    <div className="flex-grow">
                      <div className="flex justify-between items-center">
                        <span className="text-xs font-medium truncate max-w-[120px]">{agent.profile?.fullName}</span>
                        <div className="flex items-center">
                          <span className="text-xs font-medium">
                            ${agent.kpi1Actual.toLocaleString()}
                          </span>
                          <span className={`ml-1 text-xs ${agent.kpi1Actual >= agent.kpi1Target ? "text-green-600" : "text-orange-500"}`}>
                            ({Math.round((agent.kpi1Actual / agent.kpi1Target) * 100)}%)
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                        <div
                          className={`h-1.5 rounded-full ${agent.kpi1Actual >= agent.kpi1Target ? "bg-green-500" : "bg-[#6E39CB]"}`}
                          style={{ width: `${Math.min(100, Math.round((agent.kpi1Actual / agent.kpi1Target) * 100))}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}

                {sortedPerformers.topKPI1Performers.length === 0 && (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">No data available</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* KPI2 Leaderboard */}
          <div>
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-sm font-medium text-gray-700">KPI2 Top Performers</h4>
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Invoicing
              </span>
            </div>

            <div className="bg-[#F9FAFB] rounded-lg p-4 border border-gray-100">
              <div className="space-y-4">
                {sortedPerformers.topKPI2Performers.slice(0, 5).map((agent, index) => (
                  <div key={index} className="flex items-center">
                    <div className="flex-shrink-0 w-8 text-center">
                      <span
                        className={`inline-flex items-center justify-center h-6 w-6 rounded-full text-xs font-bold ${
                          index === 0
                            ? "bg-yellow-100 text-yellow-800"
                            : index === 1
                            ? "bg-gray-200 text-gray-700"
                            : index === 2
                            ? "bg-orange-100 text-orange-800"
                            : "bg-gray-100 text-gray-600"
                        }`}
                      >
                        {index + 1}
                      </span>
                    </div>
                    <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mx-2 flex-shrink-0">
                      <span className="text-[#6E39CB] font-medium text-xs">
                        {agent.profile?.fullName ? agent.profile.fullName.charAt(0).toUpperCase() : "?"}
                      </span>
                    </div>
                    <div className="flex-grow">
                      <div className="flex justify-between items-center">
                        <span className="text-xs font-medium truncate max-w-[120px]">{agent.profile?.fullName}</span>
                        <div className="flex items-center">
                          <span className="text-xs font-medium">
                            ${agent.kpi2Actual.toLocaleString()}
                          </span>
                          <span className={`ml-1 text-xs ${agent.kpi2Actual >= agent.kpi2Target ? "text-green-600" : "text-orange-500"}`}>
                            ({Math.round((agent.kpi2Actual / agent.kpi2Target) * 100)}%)
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                        <div
                          className={`h-1.5 rounded-full ${agent.kpi2Actual >= agent.kpi2Target ? "bg-green-500" : "bg-[#6E39CB]"}`}
                          style={{ width: `${Math.min(100, Math.round((agent.kpi2Actual / agent.kpi2Target) * 100))}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}

                {sortedPerformers.topKPI2Performers.length === 0 && (
                  <div className="text-center py-4">
                    <p className="text-sm text-gray-500">No data available</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Executive Insights */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Executive Insights</h3>
        <div className="bg-[#F9FAFB] rounded-lg p-4 border border-gray-100">
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="bg-[#6E39CB] bg-opacity-10 p-2 rounded-full mr-3 mt-0.5">
                <FontAwesomeIcon icon={faChartLine} className="h-4 w-4 text-[#6E39CB]" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">Performance Summary</h4>
                <p className="text-sm text-gray-600 mt-1">
                  {totals.kpi1Progress >= 100 && totals.kpi2Progress >= 100
                    ? "All KPI targets have been achieved for this period. Team performance is excellent."
                    : totals.kpi1Progress >= 75 && totals.kpi2Progress >= 75
                    ? "Team is on track to meet targets with strong performance across both KPIs."
                    : totals.kpi1Progress < 50 || totals.kpi2Progress < 50
                    ? "Some KPI targets are significantly behind schedule. Immediate attention required."
                    : "Team is making progress but needs to accelerate to meet all targets by the end date."}
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-blue-100 p-2 rounded-full mr-3 mt-0.5">
                <FontAwesomeIcon icon={faUsers} className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">Team Engagement</h4>
                <p className="text-sm text-gray-600 mt-1">
                  {targetData.targets.filter((t) => t.kpi1Actual > 0 || t.kpi2Actual > 0).length ===
                  targetData.targets.length
                    ? "All team members are actively contributing to targets."
                    : targetData.targets.filter((t) => t.kpi1Actual > 0 || t.kpi2Actual > 0).length >=
                      targetData.targets.length * 0.75
                    ? `${targetData.targets.filter((t) => t.kpi1Actual > 0 || t.kpi2Actual > 0).length} of ${targetData.targets.length} team members are actively engaged.`
                    : `Only ${targetData.targets.filter((t) => t.kpi1Actual > 0 || t.kpi2Actual > 0).length} of ${targetData.targets.length} team members are showing activity. Engagement needs improvement.`}
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-green-100 p-2 rounded-full mr-3 mt-0.5">
                <FontAwesomeIcon icon={faTasks} className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">Application Status</h4>
                <p className="text-sm text-gray-600 mt-1">
                  {targetData.targets.reduce((sum, t) => sum + (t.applications?.length || 0), 0) === 0
                    ? "No applications have been submitted for this period."
                    : `${targetData.targets.reduce((sum, t) => sum + ((t.applications || []).filter(a => a.status === 'COMPLETED').length), 0)} of ${targetData.targets.reduce((sum, t) => sum + (t.applications?.length || 0), 0)} applications have been completed (${Math.round(
                        (targetData.targets.reduce((sum, t) => sum + ((t.applications || []).filter(a => a.status === 'COMPLETED').length), 0) /
                          Math.max(1, targetData.targets.reduce((sum, t) => sum + (t.applications?.length || 0), 0))) *
                          100
                      )}%).`}
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-purple-100 p-2 rounded-full mr-3 mt-0.5">
                <FontAwesomeIcon icon={faLightbulb} className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">Recommendations</h4>
                <p className="text-sm text-gray-600 mt-1">
                  {totals.kpi1Progress >= 90 && totals.kpi2Progress >= 90
                    ? "Consider setting more ambitious targets for the next period to challenge the team."
                    : totals.kpi1Progress < 50 || totals.kpi2Progress < 50
                    ? "Schedule a team meeting to address performance gaps and provide additional support where needed."
                    : targetData.targets.filter((t) => t.kpi1Actual === 0 && t.kpi2Actual === 0).length > 0
                    ? `Follow up with the ${targetData.targets.filter((t) => t.kpi1Actual === 0 && t.kpi2Actual === 0).length} inactive team members to ensure they have the resources needed to contribute.`
                    : "Continue monitoring progress and provide regular feedback to maintain momentum."}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default OverviewTab;
