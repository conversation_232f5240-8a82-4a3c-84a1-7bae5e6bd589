import React from "react";
import { FaCompress } from "react-icons/fa";
import LeadStatusTabs from "./LeadStatusTabs";
import LeadTable from "./LeadTable";

const LeadFullScreenView = ({
  isOpen,
  onClose,
  leads,
  leadTab,
  onTabChange,
  totalLeads,
  leadsWithApplications,
  statusCounts,
  onOpenModal,
  onOpenSingleLeadModal,
  onProfileRedirect,
  onStatusChange,
  onEditLead,
  onOpenDrawer,
  isAdmin = false,
  isLoading = false
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-white p-6 z-50 overflow-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Leads Management (Fullscreen)</h2>
        <div className="flex items-center space-x-3">
          <button
            onClick={onOpenModal}
            className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Import Leads (CSV)
          </button>
          <button
            onClick={onOpenSingleLeadModal}
            className="flex items-center bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add Single Lead
          </button>
          <button
            onClick={onClose}
            className="flex items-center text-gray-700 bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <FaCompress className="mr-2" /> Exit Fullscreen
          </button>
        </div>
      </div>
      
      {/* Status Tabs in Fullscreen */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-50 mb-6">
        <div className="border-b border-gray-100 px-6 py-4">
          <div className="flex flex-wrap -mb-px">
            {[
              { label: "All Leads", value: "All" },
              { label: "Hot Leads", value: "HOT", color: "text-red-600" },
              { label: "Warm Leads", value: "WARM", color: "text-yellow-600" },
              { label: "Cold/Fresh Leads", value: "COLD_FRESH", color: "text-blue-600" },
              { label: "Leads with Applications", value: "Leads with Applications", color: "text-indigo-600" },
              { label: "Closed", value: "CLOSED", color: "text-gray-600" },
            ].map(({ label, value, color }) => (
              <button
                key={value}
                onClick={() => onTabChange(value)}
                className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                  leadTab === value
                    ? `border-[#6E39CB] ${color || "text-[#6E39CB]"}`
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                } whitespace-nowrap mr-4`}
              >
                {label}
                <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${leadTab === value ? "bg-[#F4F5F9]" : "bg-gray-100"}`}>
                  {value === "All" ? totalLeads :
                   value === "Leads with Applications" ? leadsWithApplications :
                   value === "COLD_FRESH" ? (statusCounts["COLD"] || 0) + (statusCounts["FRESH"] || 0) :
                   value === "CLOSED" ? statusCounts["CLOSED"] || 0 :
                   statusCounts[value] || 0}
                </span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Full Screen Table */}
      <LeadTable
        leads={leads}
        onProfileRedirect={onProfileRedirect}
        onStatusChange={onStatusChange}
        onEditLead={onEditLead}
        onOpenDrawer={onOpenDrawer}
        isAdmin={isAdmin}
        isLoading={isLoading}
      />
    </div>
  );
};

export default LeadFullScreenView;
