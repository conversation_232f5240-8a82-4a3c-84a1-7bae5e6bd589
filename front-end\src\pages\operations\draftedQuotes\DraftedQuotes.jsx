import React, { useState, useEffect, useRef } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faSearch,
  faFilter,
  faKeyboard,
  faCalendarAlt,
  faFileInvoiceDollar,
  faUser,
  faBuilding,
  faSort,
  faTable,
  faThLarge,
  faClipboard,
  faCheck,
  faComment,
  faCopy
} from "@fortawesome/free-solid-svg-icons";
import EnhancedQuoteCard from "../../../components/card/EnhancedQuoteCard";
import { useGetDraftedQuoteRequestsQuery } from "../../../services/CompanyAPIService";
import { useNavigate } from "react-router-dom";

const DraftedQuotes = () => {
  const navigate = useNavigate();

  // State for view mode
  const [viewMode, setViewMode] = useState("card"); // "card" or "table"

  // State for search and filters
  const [search, setSearch] = useState("");
  const [refNumberSearch, setRefNumberSearch] = useState("");
  const [selectedAgentFilter, setSelectedAgentFilter] = useState("");
  const [selectedStatusFilter, setSelectedStatusFilter] = useState("");
  const [dateFilterType, setDateFilterType] = useState("today");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [copiedField, setCopiedField] = useState(null);
  const searchInputRef = useRef(null);
  const refNumberSearchInputRef = useRef(null);

  // State to store the current data
  const [quoteRequests, setQuoteRequests] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get the query hook for drafted quotes
  const draftedQuotesQuery = useGetDraftedQuoteRequestsQuery();

  // Function to completely reset and fetch data
  const resetAndFetch = async () => {
    // Clear the current data
    setQuoteRequests([]);
    setIsLoading(true);

    try {
      // Force a refetch by using the refetch method
      await draftedQuotesQuery.refetch();
      const result = draftedQuotesQuery.data;

      // Update the state with the new data
      setQuoteRequests(result || []);
    } catch (error) {
      console.error("Error fetching data:", error);
      setQuoteRequests([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    resetAndFetch();
  }, []);

  // Update data when query results change
  useEffect(() => {
    if (!draftedQuotesQuery.isLoading) {
      setQuoteRequests(draftedQuotesQuery.data || []);
      setIsLoading(false);
    }
  }, [draftedQuotesQuery.data, draftedQuotesQuery.isLoading]);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearch(e.target.value);
  };

  // Handle reference number search input change
  const handleRefNumberSearchChange = (e) => {
    setRefNumberSearch(e.target.value);
  };

  // Handle agent filter change
  const handleAgentFilterChange = (e) => {
    setSelectedAgentFilter(e.target.value);
  };

  // Handle status filter change
  const handleStatusFilterChange = (e) => {
    setSelectedStatusFilter(e.target.value);
  };

  // Handle date filter change
  const handleDateFilterChange = (e) => {
    setDateFilterType(e.target.value);
    if (e.target.value !== "custom") {
      setStartDate("");
      setEndDate("");
    }
  };

  // Handle start date change
  const handleStartDateChange = (e) => {
    setStartDate(e.target.value);
  };

  // Handle end date change
  const handleEndDateChange = (e) => {
    setEndDate(e.target.value);
  };

  // Toggle view mode between card and table
  const toggleViewMode = () => {
    setViewMode(viewMode === "card" ? "table" : "card");
  };

  // Navigate to application profile
  const navigateToApplicationProfile = (applicationId) => {
    navigate(`/admin/application/profile/${applicationId}`);
  };

  // Copy to clipboard function
  const copyToClipboard = (text, field) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    });
  };

  // Format date for display
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge color
  const getStatusBadgeColor = (status, type) => {
    const baseColors = {
      quote: {
        PENDING: "bg-yellow-100 text-yellow-800",
        SENT: "bg-green-100 text-green-800",
        DRAFT: "bg-gray-100 text-gray-800"
      },
      invoice: {
        PENDING: "bg-blue-100 text-blue-800",
        SENT: "bg-purple-100 text-purple-800",
        DRAFT: "bg-gray-100 text-gray-800"
      }
    };

    return baseColors[type][status] || "bg-gray-100 text-gray-800";
  };

  // Make a copy of the quotes to avoid mutation issues
  const quotes = quoteRequests ? [...quoteRequests] : [];

  // Apply all filters
  const filteredQuotes = quotes.filter(quote => {
    // Search filter: checking in clientName, candidateName, or salesAgent.fullName
    const searchTerm = search.toLowerCase();
    if (
      searchTerm &&
      !(
        (quote.clientName || "").toLowerCase().includes(searchTerm) ||
        (quote.candidateName || "").toLowerCase().includes(searchTerm) ||
        (quote.salesAgent?.fullName || "").toLowerCase().includes(searchTerm)
      )
    ) {
      return false;
    }

    // Reference number search
    if (
      refNumberSearch &&
      !(
        (quote.quoteRefNumber || "").toLowerCase().includes(refNumberSearch.toLowerCase()) ||
        (quote.invoiceRefNumber || "").toLowerCase().includes(refNumberSearch.toLowerCase())
      )
    ) {
      return false;
    }

    // Agent filter
    if (selectedAgentFilter && quote.salesAgent?.fullName !== selectedAgentFilter) {
      return false;
    }

    // Status filter
    if (selectedStatusFilter) {
      if (selectedStatusFilter === "QUOTE_PENDING" && quote.quoteStatus !== "PENDING") {
        return false;
      }
      if (selectedStatusFilter === "QUOTE_SENT" && quote.quoteStatus !== "SENT") {
        return false;
      }
      if (selectedStatusFilter === "INVOICE_PENDING" && quote.invoiceStatus !== "PENDING") {
        return false;
      }
      if (selectedStatusFilter === "INVOICE_SENT" && quote.invoiceStatus !== "SENT") {
        return false;
      }
    }

    // Date filter
    if (dateFilterType === "custom" && (startDate || endDate)) {
      const quoteDate = new Date(quote.createdAt);

      if (startDate) {
        const filterStartDate = new Date(startDate);
        if (quoteDate < filterStartDate) {
          return false;
        }
      }

      if (endDate) {
        const filterEndDate = new Date(endDate);
        filterEndDate.setHours(23, 59, 59, 999); // End of the day
        if (quoteDate > filterEndDate) {
          return false;
        }
      }
    } else if (dateFilterType === "today") {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const quoteDate = new Date(quote.createdAt);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      if (quoteDate < today || quoteDate >= tomorrow) {
        return false;
      }
    } else if (dateFilterType === "thisWeek") {
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)); // Start from Monday
      startOfWeek.setHours(0, 0, 0, 0);

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6); // End on Sunday
      endOfWeek.setHours(23, 59, 59, 999);

      const quoteDate = new Date(quote.createdAt);

      if (quoteDate < startOfWeek || quoteDate > endOfWeek) {
        return false;
      }
    } else if (dateFilterType === "thisMonth") {
      const today = new Date();
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59, 999);

      const quoteDate = new Date(quote.createdAt);

      if (quoteDate < startOfMonth || quoteDate > endOfMonth) {
        return false;
      }
    }

    return true;
  });

  // Get unique agent names for the filter dropdown
  const uniqueAgents = [...new Set(quotes.map(quote => quote.salesAgent?.fullName).filter(Boolean))];

  if (isLoading) return (
    <div className="flex justify-center items-center h-64">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6E39CB]"></div>
    </div>
  );

  return (
    <div className="w-full">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Drafted Quotes & Invoices</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage all drafted quotes and invoices
          </p>
        </div>

        <div className="mt-4 md:mt-0 flex items-center gap-3">
          {/* View mode toggle */}
          <button
            onClick={toggleViewMode}
            className="p-2 rounded-md bg-white border border-gray-200 text-gray-600 hover:bg-gray-50 transition-colors"
            title={viewMode === "card" ? "Switch to table view" : "Switch to card view"}
          >
            <FontAwesomeIcon icon={viewMode === "card" ? faTable : faThLarge} />
          </button>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-md transition-colors ${showFilters ? 'bg-[#F4F5F9] text-[#6E39CB]' : 'text-gray-600 hover:text-[#6E39CB] hover:bg-[#F4F5F9]'}`}
            title="Toggle filters"
          >
            <FontAwesomeIcon icon={faFilter} />
          </button>
          <button
            onClick={() => setShowKeyboardShortcuts(!showKeyboardShortcuts)}
            className={`p-2 rounded-md transition-colors ${showKeyboardShortcuts ? 'bg-[#F4F5F9] text-[#6E39CB]' : 'text-gray-600 hover:text-[#6E39CB] hover:bg-[#F4F5F9]'}`}
            title="Keyboard shortcuts"
          >
            <FontAwesomeIcon icon={faKeyboard} />
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6 border border-gray-50">
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          {/* Name/Client/Agent Search */}
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
            </div>
            <input
              ref={searchInputRef}
              type="text"
              value={search}
              onChange={handleSearchChange}
              placeholder="Search by name, client, or agent..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
          </div>

          {/* Quote/Invoice Number Search */}
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="text-gray-400" />
            </div>
            <input
              ref={refNumberSearchInputRef}
              type="text"
              value={refNumberSearch}
              onChange={handleRefNumberSearchChange}
              placeholder="Search by quote or invoice number..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
          </div>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4 pt-4 border-t border-gray-100">
            {/* Agent Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Agent</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FontAwesomeIcon icon={faUser} className="text-gray-400" />
                </div>
                <select
                  value={selectedAgentFilter}
                  onChange={handleAgentFilterChange}
                  className="block w-full pl-10 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] appearance-none"
                >
                  <option value="">All Agents</option>
                  {uniqueAgents.map((agent, index) => (
                    <option key={index} value={agent}>{agent}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <FontAwesomeIcon icon={faSort} className="text-gray-400" />
                </div>
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FontAwesomeIcon icon={faBuilding} className="text-gray-400" />
                </div>
                <select
                  value={selectedStatusFilter}
                  onChange={handleStatusFilterChange}
                  className="block w-full pl-10 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] appearance-none"
                >
                  <option value="">All Statuses</option>
                  <option value="QUOTE_PENDING">Quote Pending</option>
                  <option value="QUOTE_SENT">Quote Sent</option>
                  <option value="INVOICE_PENDING">Invoice Pending</option>
                  <option value="INVOICE_SENT">Invoice Sent</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <FontAwesomeIcon icon={faSort} className="text-gray-400" />
                </div>
              </div>
            </div>

            {/* Date Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FontAwesomeIcon icon={faCalendarAlt} className="text-gray-400" />
                </div>
                <select
                  value={dateFilterType}
                  onChange={handleDateFilterChange}
                  className="block w-full pl-10 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] appearance-none"
                >
                  <option value="allDates">All Dates</option>
                  <option value="today">Today</option>
                  <option value="thisWeek">This Week</option>
                  <option value="thisMonth">This Month</option>
                  <option value="custom">Custom Range</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <FontAwesomeIcon icon={faSort} className="text-gray-400" />
                </div>
              </div>
            </div>

            {/* Custom Date Range */}
            {dateFilterType === "custom" && (
              <div className="md:col-span-2 lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">Custom Range</label>
                <div className="flex space-x-2">
                  <input
                    type="date"
                    value={startDate}
                    onChange={handleStartDateChange}
                    className="block w-1/2 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  />
                  <input
                    type="date"
                    value={endDate}
                    onChange={handleEndDateChange}
                    className="block w-1/2 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Keyboard Shortcuts Info */}
        {showKeyboardShortcuts && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Keyboard Shortcuts</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="flex items-center">
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Ctrl+F</kbd>
                <span className="ml-2 text-sm text-gray-600">Focus search</span>
              </div>
              <div className="flex items-center">
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Ctrl+R</kbd>
                <span className="ml-2 text-sm text-gray-600">Focus reference search</span>
              </div>
              <div className="flex items-center">
                <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Esc</kbd>
                <span className="ml-2 text-sm text-gray-600">Clear search</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Results Count and View Toggle */}
      <div className="flex justify-between items-center mb-4">
        <p className="text-sm text-gray-500">
          Showing <span className="font-medium text-gray-700">{filteredQuotes.length}</span> drafted quotes and invoices
        </p>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setViewMode("card")}
            className={`p-2 rounded ${viewMode === "card" ? 'bg-[#F4F5F9] text-[#6E39CB]' : 'text-gray-600 hover:bg-gray-100'}`}
            title="Card view"
          >
            <FontAwesomeIcon icon={faThLarge} />
          </button>
          <button
            onClick={() => setViewMode("table")}
            className={`p-2 rounded ${viewMode === "table" ? 'bg-[#F4F5F9] text-[#6E39CB]' : 'text-gray-600 hover:bg-gray-100'}`}
            title="Table view"
          >
            <FontAwesomeIcon icon={faTable} />
          </button>
        </div>
      </div>

      {/* No Results Message */}
      {filteredQuotes.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center text-gray-500 border border-gray-200">
          No drafted quotes or invoices found matching your criteria
        </div>
      )}

      {/* Card View */}
      {viewMode === "card" && filteredQuotes.length > 0 && (
        <div className="grid grid-cols-1 gap-4">
          {filteredQuotes.map((quote) => (
            <EnhancedQuoteCard
              key={quote.quoteRefNumber || quote.applicationId}
              onQuoteUpdated={resetAndFetch}
              applicationId={quote.applicationId}
              agentName={quote.salesAgent?.fullName || "Unknown"}
              clientName={quote.clientName}
              clientEmail={quote.clientEmail}
              clientPhone={quote.clientPhone}
              candidateName={quote.candidateName}
              phoneNumber={quote.candidatePhone}
              email={quote.candidateEmail}
              quoteRequestDetail={quote.quoteRequestDetails}
              price={`$${quote.price}`}
              otherInformation={quote.otherInformation}
              quoteNumber={quote.quoteRefNumber}
              invoiceNumber={quote.invoiceRefNumber}
              quoteStatus={quote.quoteStatus}
              invoiceStatus={quote.invoiceStatus}
              createdAt={quote.createdAt}
            />
          ))}
        </div>
      )}

      {/* Table View */}
      {viewMode === "table" && filteredQuotes.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-50 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Client/Candidate
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Agent
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quote #
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quote Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Invoice #
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Invoice Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredQuotes.map((quote) => (
                  <tr key={quote.applicationId} className="hover:bg-[#F4F5F9] transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <div className="text-sm font-medium text-gray-900">{quote.clientName}</div>
                        <div className="text-sm text-gray-500">{quote.candidateName}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{quote.salesAgent?.fullName || "Unknown"}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-sm text-gray-900 mr-2">{quote.quoteRefNumber || "-"}</span>
                        {quote.quoteRefNumber && (
                          <button
                            onClick={() => copyToClipboard(quote.quoteRefNumber, `quote-${quote.applicationId}`)}
                            className="text-gray-400 hover:text-gray-600"
                            title="Copy quote reference"
                          >
                            {copiedField === `quote-${quote.applicationId}` ? (
                              <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                            ) : (
                              <FontAwesomeIcon icon={faCopy} />
                            )}
                          </button>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {quote.quoteStatus ? (
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(quote.quoteStatus, 'quote')}`}>
                          {quote.quoteStatus}
                        </span>
                      ) : (
                        <span className="text-sm text-gray-500">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-sm text-gray-900 mr-2">{quote.invoiceRefNumber || "-"}</span>
                        {quote.invoiceRefNumber && (
                          <button
                            onClick={() => copyToClipboard(quote.invoiceRefNumber, `invoice-${quote.applicationId}`)}
                            className="text-gray-400 hover:text-gray-600"
                            title="Copy invoice reference"
                          >
                            {copiedField === `invoice-${quote.applicationId}` ? (
                              <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                            ) : (
                              <FontAwesomeIcon icon={faCopy} />
                            )}
                          </button>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {quote.invoiceStatus ? (
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(quote.invoiceStatus, 'invoice')}`}>
                          {quote.invoiceStatus}
                        </span>
                      ) : (
                        <span className="text-sm text-gray-500">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">${quote.price}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{formatDate(quote.createdAt)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => navigateToApplicationProfile(quote.applicationId)}
                        className="text-[#6E39CB] hover:text-[#5930a8] mr-3"
                        title="View Application"
                      >
                        <FontAwesomeIcon icon={faComment} />
                      </button>
                      <button
                        onClick={() => copyToClipboard(JSON.stringify(quote, null, 2), `full-${quote.applicationId}`)}
                        className="text-gray-500 hover:text-gray-700"
                        title="Copy all information"
                      >
                        {copiedField === `full-${quote.applicationId}` ? (
                          <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                        ) : (
                          <FontAwesomeIcon icon={faClipboard} />
                        )}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default DraftedQuotes;
