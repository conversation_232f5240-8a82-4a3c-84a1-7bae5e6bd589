package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.ApplicationComments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ApplicationCommentsRepository extends JpaRepository<ApplicationComments, Long> {
    
    List<ApplicationComments> findByApplicationApplicationIdOrderByCreatedDateDesc(String applicationId);
    
    Optional<ApplicationComments> findByIdAndApplicationApplicationId(Long commentId, String applicationId);
}
