import React, { useState, useEffect } from "react";
import { showValidationError } from "../../utils/toastUtils";

const QuestionEditModal = ({
  isOpen,
  onClose,
  onSave,
  employees = [],
  initialData = null,
}) => {
  const [question, setQuestion] = useState(initialData?.question || "");
  const [dueDate, setDueDate] = useState(initialData?.dueDate || "");
  const [assignedTo, setAssignedTo] = useState(initialData?.assignedTo || "");
  const [emailAssigned, setEmailAssigned] = useState(
    initialData?.emailAssigned || false
  );

  // Populate form with initial data when modal is opened
  useEffect(() => {
    if (initialData) {
      setQuestion(initialData.question);
      setDueDate(initialData.dueDate);
      setAssignedTo(initialData.assignedTo);
      setEmailAssigned(initialData.emailAssigned);
    }
  }, [initialData]);

  if (!isOpen) return null;

  const handleSubmit = () => {
    if (!question || !dueDate || !assignedTo) {
      showValidationError("Please fill in all fields.");
      return;
    }
    const updatedQuestion = {
      question,
      dueDate,
      assignedTo,
      emailAssigned, // defaults to false unless changed
    };
    onSave(updatedQuestion); // Pass the updated data to the parent
    // Reset form values
    setQuestion("");
    setDueDate("");
    setAssignedTo("");
    setEmailAssigned(false);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white w-full max-w-lg p-6 rounded shadow-lg">
        <div className="sticky top-0 bg-white z-10 p-4">
          <h2 className="text-xl font-bold mb-4 text-left">Edit Question</h2>
        </div>

        <div className="mb-3">
          <label className="block font-semibold text-sm mb-1 text-left">
            Question
          </label>
          <textarea
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            className="border p-2 rounded w-full"
            placeholder="Enter your question"
          />
        </div>

        <div className="mb-3">
          <label className="block font-semibold text-sm mb-1 text-left">
            Due Date for Response
          </label>
          <input
            type="datetime-local"
            value={dueDate}
            onChange={(e) => setDueDate(e.target.value)}
            className="border p-2 rounded w-full"
          />
        </div>

        <div className="mb-3">
          <label className="block font-semibold text-sm mb-1 text-left">
            Assign To
          </label>
          <select
            value={assignedTo}
            onChange={(e) => setAssignedTo(e.target.value)}
            className="border p-2 rounded w-full"
          >
            <option value="">Select Staff</option>
            {employees && employees.length > 0 ? (
              employees.map((employee) => (
                <option key={employee.username} value={employee.username}>
                  {employee.fullName || employee.username}
                </option>
              ))
            ) : (
              <option value="">No staff available</option>
            )}
          </select>
        </div>

        {/* Optionally, you could add a checkbox for email notification
        <div className="mb-3 flex items-center">
          <input
            type="checkbox"
            id="emailAssigned"
            checked={emailAssigned}
            onChange={(e) => setEmailAssigned(e.target.checked)}
            className="mr-2"
          />
          <label htmlFor="emailAssigned" className="text-sm">
            Email Assigned Staff?
          </label>
        </div> */}

        <div className="flex justify-end mt-4">
          <button
            onClick={onClose}
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded mr-2"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuestionEditModal;
