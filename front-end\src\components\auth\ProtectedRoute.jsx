import React, { useEffect, useState } from "react";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { jwtDecode } from "jwt-decode";
import routes from "../../routes";

/**
 * A component that protects routes based on user roles
 * @param {Object} props
 * @param {Array} props.allowedRoles - Array of roles allowed to access the route
 * @returns {JSX.Element} - The protected route
 */
const ProtectedRoute = ({ allowedRoles }) => {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const location = useLocation();

  useEffect(() => {
    const checkAuth = () => {
      const token = localStorage.getItem("token");
      
      if (!token) {
        setIsAuthorized(false);
        setIsLoading(false);
        return;
      }

      try {
        const decodedToken = jwtDecode(token);
        const userRoles = decodedToken.roles || [];
        
        // Check if user has any of the allowed roles
        const hasAllowedRole = allowedRoles.some(role => 
          userRoles.includes(role)
        );
        
        setIsAuthorized(hasAllowedRole);
        setIsLoading(false);
      } catch (error) {
        console.error("Error decoding token:", error);
        setIsAuthorized(false);
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [allowedRoles, location.pathname]);

  if (isLoading) {
    // You could show a loading spinner here
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6E39CB]"></div>
      </div>
    );
  }

  return isAuthorized ? (
    <Outlet />
  ) : (
    <Navigate to={routes.accessDenied} state={{ from: location }} replace />
  );
};

export default ProtectedRoute;
