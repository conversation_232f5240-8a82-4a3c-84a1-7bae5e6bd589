package com.skillsync.applyr.modules.file_storage.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.modules.file_storage.configs.FileStorageProperties;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Service
public class FileStorageService {

    private final FileStorageProperties fileStorageProperties;

    public FileStorageService(FileStorageProperties fileStorageProperties) {
        this.fileStorageProperties = fileStorageProperties;
    }

    public String storeFile(MultipartFile file, String studentName, String documentType) throws IOException {
        String uploadDir = fileStorageProperties.getUploadDir();

        Path uploadPath = Paths.get(uploadDir);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }

        String fileName = System.currentTimeMillis() + "_" + studentName + "_" +
                documentType + "_" + file.getOriginalFilename() + file.getOriginalFilename();

        Path filePath = uploadPath.resolve(fileName);
        file.transferTo(filePath);

        return fileName;
    }

    public ResponseEntity<Resource> getFile(String fileName) {
        try {
            Path filePath = Paths.get(fileStorageProperties.getUploadDir()).resolve(fileName).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            if (!resource.exists() || !resource.isReadable()) {
                throw new AppRTException("File not found: " + fileName, HttpStatus.NOT_FOUND);
            }
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);
        } catch (MalformedURLException e) {
            throw new AppRTException("Error fetching file from database.", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public ResponseEntity<Resource> viewFile(String fileName) {
        try {
            Path filePath = Paths.get(fileStorageProperties.getUploadDir()).resolve(fileName).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            if (!resource.exists() || !resource.isReadable()) {
                throw new AppRTException("File not found: " + fileName, HttpStatus.NOT_FOUND);
            }
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .body(resource);
        } catch (MalformedURLException e) {
            throw new AppRTException("Error fetching file from database.", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
