import React, { useState } from "react";
import { useBulkImportQualificationsMutation } from "../../services/AdminAPIService";
import { toast } from "react-toastify";

const BulkImportModal = ({ isOpen, onClose, onSuccess }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const [importResults, setImportResults] = useState(null);

  const [bulkImportQualifications, { isLoading }] = useBulkImportQualificationsMutation();

  if (!isOpen) return null;

  const handleFileSelect = (file) => {
    if (file && file.name.endsWith('.xlsx')) {
      setSelectedFile(file);
      setImportResults(null);
    } else {
      toast.error("Please select a valid .xlsx file");
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInputChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleImport = async () => {
    if (!selectedFile) {
      toast.error("Please select a file first");
      return;
    }

    try {
      const result = await bulkImportQualifications(selectedFile).unwrap();
      setImportResults(result);
      
      if (result.errorCount === 0) {
        toast.success(result.message);
        onSuccess();
      } else {
        toast.warning(`Import completed with ${result.errorCount} errors. Check details below.`);
      }
    } catch (error) {
      toast.error(error?.data?.message || "Import failed");
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setImportResults(null);
    onClose();
  };

  const downloadTemplate = () => {
    // Create a sample Excel template
    const headers = [
      "Category", "Code", "Qualification Name", "RPL Low", "RPL High", 
      "ENROLLMENT", "Offshore Low", "Notes", "Processing Time", "Demand"
    ];
    
    const csvContent = headers.join(",") + "\n" + 
      "Education,CHC30121,Certificate III in Early Childhood Education and Care,1650,1800,2450,0,Sample notes,6-8 weeks,High";
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'qualification_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div className="fixed inset-0 bg-black opacity-50" onClick={handleClose}></div>
      
      {/* Modal content */}
      <div className="bg-white rounded-lg shadow-xl z-10 p-6 w-full max-w-2xl relative max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Bulk Import Qualifications</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {!importResults ? (
          <>
            {/* Instructions */}
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Instructions:</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Upload an Excel (.xlsx) file with qualification data</li>
                <li>• Required columns: Category, Code, Qualification Name, RPL Low, RPL High, ENROLLMENT, Offshore Low, Notes, Processing Time, Demand</li>
                <li>• If a qualification with the same code exists, it will be updated</li>
                <li>• New qualifications will be created for codes that don't exist</li>
              </ul>
              <button
                onClick={downloadTemplate}
                className="mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Download Sample Template
              </button>
            </div>

            {/* File Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive 
                  ? 'border-[#6E39CB] bg-purple-50' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                type="file"
                accept=".xlsx"
                onChange={handleFileInputChange}
                className="hidden"
                id="file-upload"
              />
              
              {selectedFile ? (
                <div className="space-y-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-green-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                  <p className="text-xs text-gray-500">File selected successfully</p>
                  <button
                    onClick={() => setSelectedFile(null)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove file
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <p className="text-sm text-gray-600">
                    Drag and drop your Excel file here, or{" "}
                    <label htmlFor="file-upload" className="text-[#6E39CB] hover:text-[#5E2CB8] cursor-pointer font-medium">
                      browse
                    </label>
                  </p>
                  <p className="text-xs text-gray-500">Only .xlsx files are supported</p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={handleClose}
                className="border border-gray-300 text-gray-700 px-5 py-2.5 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleImport}
                disabled={!selectedFile || isLoading}
                className="bg-[#6E39CB] text-white px-5 py-2.5 rounded-lg hover:bg-[#5E2CB8] transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Importing...
                  </>
                ) : (
                  "Import Qualifications"
                )}
              </button>
            </div>
          </>
        ) : (
          /* Import Results */
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-3">Import Results</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{importResults.totalProcessed}</div>
                  <div className="text-gray-600">Total Processed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{importResults.successCount}</div>
                  <div className="text-gray-600">Successful</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{importResults.updateCount}</div>
                  <div className="text-gray-600">Updated</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{importResults.createCount}</div>
                  <div className="text-gray-600">Created</div>
                </div>
              </div>
              {importResults.errorCount > 0 && (
                <div className="mt-4 text-center">
                  <div className="text-2xl font-bold text-red-600">{importResults.errorCount}</div>
                  <div className="text-gray-600">Errors</div>
                </div>
              )}
            </div>

            {importResults.errors && importResults.errors.length > 0 && (
              <div className="p-4 bg-red-50 rounded-lg">
                <h4 className="font-semibold text-red-900 mb-2">Errors:</h4>
                <div className="max-h-40 overflow-y-auto">
                  {importResults.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-800 mb-1">
                      {error}
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3">
              <button
                onClick={handleClose}
                className="bg-[#6E39CB] text-white px-5 py-2.5 rounded-lg hover:bg-[#5E2CB8] transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BulkImportModal;
