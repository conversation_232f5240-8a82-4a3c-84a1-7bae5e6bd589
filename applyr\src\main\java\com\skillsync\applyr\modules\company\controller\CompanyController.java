package com.skillsync.applyr.modules.company.controller;


import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.company.models.*;
import com.skillsync.applyr.modules.company.models.QualificationUpdateDTO;
import com.skillsync.applyr.modules.company.models.BulkImportResultDTO;
import com.skillsync.applyr.modules.company.services.CompanyServices;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.parameters.P;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/company")
@PreAuthorize("hasRole('ADMIN')")
public class CompanyController {

    private final CompanyServices companyServices;

    public CompanyController(CompanyServices companyServices) {
        this.companyServices = companyServices;
    }




    @PostMapping("/qualification/add")
    public ResponseEntity<SuccessResponse> addQualification(@RequestBody QualificationRequestDTO qualification) {
        return ResponseEntity.ok(companyServices.addQualification(qualification));
    }

    @PutMapping("/qualification/edit")
    public ResponseEntity<SuccessResponse> updateQualification(@RequestBody QualificationUpdateDTO qualificationUpdateDTO) {
        return ResponseEntity.ok(companyServices.updateQualification(qualificationUpdateDTO));
    }

    @DeleteMapping("/qualification/{qualificationId}")
    public ResponseEntity<SuccessResponse> deleteQualification(@PathVariable String qualificationId) {
        return ResponseEntity.ok(companyServices.deleteQualification(qualificationId));
    }

    @PostMapping("/qualification/bulk-import")
    public ResponseEntity<BulkImportResultDTO> bulkImportQualifications(@RequestParam("file") MultipartFile file) {
        return ResponseEntity.ok(companyServices.bulkImportQualifications(file));
    }

    @PostMapping("/qualification/{qualificationId}/rto/{rtoCode}")
    public ResponseEntity<SuccessResponse> addRTOToQualification(
            @PathVariable String qualificationId,
            @PathVariable String rtoCode) {
        return ResponseEntity.ok(companyServices.addRTOToQualification(qualificationId, rtoCode));
    }

    @DeleteMapping("/qualification/{qualificationId}/rto/{rtoCode}")
    public ResponseEntity<SuccessResponse> removeRTOFromQualification(
            @PathVariable String qualificationId,
            @PathVariable String rtoCode) {
        return ResponseEntity.ok(companyServices.removeRTOFromQualification(qualificationId, rtoCode));
    }

    @PostMapping("/admin/add")
    public ResponseEntity<SuccessResponse> registerAdmin(@RequestBody AdminRegisterDTO adminInfo) {
        return ResponseEntity.ok(companyServices.registerAdmin(adminInfo));
    }

    @PostMapping("/sales/add")
    public ResponseEntity<SuccessResponse> registerSales(@RequestBody AdminRegisterDTO salesInfo) {
        return ResponseEntity.ok(companyServices.registerSalesAgent(salesInfo));
    }

    @PostMapping("/operations/add")
    public ResponseEntity<SuccessResponse> registerOperationsAgent(@RequestBody AdminRegisterDTO info) {
        return ResponseEntity.ok(companyServices.registerOperations(info));
    }

    @GetMapping("/employee/all")
    public ResponseEntity<List<ProfileDTO>> getAllEmployees() {
        return ResponseEntity.ok(companyServices.getAllEmployees());
    }

    @DeleteMapping("/employee/{username}")
    public ResponseEntity<SuccessResponse> deleteEmployee(@PathVariable String username) {
        return ResponseEntity.ok(companyServices.deleteEmployee(username));
    }

    @GetMapping("/employee/agents/all")
    public ResponseEntity<List<ProfileDTO>> getAllAgents() {
        return ResponseEntity.ok(companyServices.getAllAgents());
    }

    @GetMapping("/applications")
    public ResponseEntity<List<ApplicationResponseDTO>> getApplications() {
        return ResponseEntity.ok(companyServices.getAllApplication());
    }

    @PutMapping("/target/{username}/{amount}")
    public ResponseEntity<SuccessResponse> updateTarget(@PathVariable String username, @PathVariable double amount) {
        return ResponseEntity.ok(null);
    }

    @GetMapping("/dashboard/{startDate}/{endDate}")
    public ResponseEntity<DashboardDTO> getDashboard(
            @PathVariable LocalDateTime startDate,
            @PathVariable LocalDateTime endDate,
            @RequestParam(required = false) Long targetId,
            @RequestParam(required = false) List<Long> targetIds) {
        if (targetIds != null && !targetIds.isEmpty()) {
            return ResponseEntity.ok(companyServices.getAdminDashboardByMultipleTargetIds(targetIds));
        }
        if (targetId != null) {
            return ResponseEntity.ok(companyServices.getAdminDashboardByTargetId(targetId));
        }
        return ResponseEntity.ok(companyServices.getAdminDashboard(startDate, endDate));
    }

    @GetMapping("/dashboard/this-month")
    public ResponseEntity<DashboardDTO> getDashboardThisMonth() {
        return ResponseEntity.ok(companyServices.getAdminDashboardThisMonth());
    }

    @GetMapping("/dashboard/this-year")
    public ResponseEntity<DashboardDTO> getDashboardThisYear() {
        return ResponseEntity.ok(companyServices.getAdminDashboardThisYear());
    }



    @PostMapping("/xero/import/kpi1")
    public ResponseEntity<SuccessResponse> uploadXeroInvoice(@RequestParam("file") MultipartFile file) {
        return ResponseEntity.ok(companyServices.uploadXeroInvoiceData(file));
    }

    @GetMapping("/xero/kpi1")
    public ResponseEntity<List<KPI1XeroDTO>> getAllKPI1XeroData() {
        return ResponseEntity.ok(companyServices.getAllXeroKPI1Data());
    }

    @PostMapping("/xero/import/kpi2")
    public ResponseEntity<SuccessResponse> uploadXeroInBank(@RequestParam("file") MultipartFile file) {
        return ResponseEntity.ok(companyServices.uploadxeroInBankData(file));
    }

    @GetMapping("xero/kpi2")
    public ResponseEntity<List<KPI2XeroDTO>> getAllKPI2XeroData() {
        return ResponseEntity.ok(companyServices.getAllXeroKPI2Data());
    }

}
