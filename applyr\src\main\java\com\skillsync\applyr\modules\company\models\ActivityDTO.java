package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.core.models.entities.Activity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
public class ActivityDTO {
    private String content;

    private String createdAt;
    private String updatedAt;
    private String createdBy;
    private String updatedBy;

    public ActivityDTO(Activity activity) {
        this.content = activity.getDescription();
        this.createdBy = activity.getCreatedBy();
        this.createdAt = activity.getCreatedDate().toString();
        this.updatedBy = activity.getLastModifiedBy();
        this.updatedAt = activity.getLastModifiedDate().toString();

    }
}


