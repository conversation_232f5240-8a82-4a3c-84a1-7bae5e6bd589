import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faTimes, 
  faSpinner, 
  faExclamationTriangle,
  faFileAlt,
  faDownload
} from '@fortawesome/free-solid-svg-icons';

const AIAnalysisModal = ({
  isOpen,
  onClose,
  isLoading,
  error,
  reportData
}) => {
  if (!isOpen) return null;

  const handleExportPDF = () => {
    // Create a temporary div to hold the report content
    const element = document.createElement('div');
    element.innerHTML = reportData;
    
    const opt = {
      margin: [10, 10],
      filename: `Performance_Analysis_${new Date().toISOString().split('T')[0]}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 2, useCORS: true },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    };

  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[90vh] flex flex-col">
        {/* Header - Fixed height */}
        <div className="flex-none p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">AI Performance Analysis Report</h2>
              <p className="text-sm text-gray-500 mt-1">
                Executive summary, KPI analysis, leaderboard insights, recommendations, and risk assessment
              </p>
            </div>
            <div className="flex items-center gap-4">
              {reportData && !isLoading && !error && (
                <button
                  onClick={handleExportPDF}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  <FontAwesomeIcon icon={faDownload} className="mr-2" />
                  Export PDF
                </button>
              )}
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <FontAwesomeIcon icon={faTimes} className="h-6 w-6 text-gray-500" />
              </button>
            </div>
          </div>
        </div>

        {/* Content - Scrollable */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faFileAlt} className="text-[#6E39CB] mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Analysis Report</h3>
          </div>

          {isLoading && (
            <div className="flex flex-col items-center justify-center py-12">
              <FontAwesomeIcon icon={faSpinner} className="text-[#6E39CB] text-4xl animate-spin mb-4" />
              <p className="text-gray-600 text-lg mb-2">Generating comprehensive analysis report...</p>
              <p className="text-gray-500">Creating insights, recommendations, and strategic guidance</p>
            </div>
          )}

          {error && (
            <div className="flex flex-col items-center justify-center py-12">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-4xl mb-4" />
              <p className="text-red-500 text-lg mb-2">Error generating report</p>
              <p className="text-gray-500 mb-4">{error}</p>
            </div>
          )}

          {!isLoading && !error && reportData && (
            <div
              className="prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: reportData }}
            />
          )}

          {!isLoading && !error && !reportData && (
            <div className="flex items-center justify-center py-12 text-gray-500">
              <p>No report data available</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIAnalysisModal;
