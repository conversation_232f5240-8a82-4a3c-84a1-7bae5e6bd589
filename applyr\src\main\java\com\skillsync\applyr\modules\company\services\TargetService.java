package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.models.entities.SalesAgent;
import com.skillsync.applyr.core.models.entities.WeeklyTarget;
import com.skillsync.applyr.core.models.entities.WeeklyTargetCombined;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.company.models.*;
import com.skillsync.applyr.modules.company.repositories.ApplicationRepository;
import com.skillsync.applyr.modules.company.repositories.SalesAgentRepository;
import com.skillsync.applyr.modules.company.repositories.WeeklyTargetRepository;
import com.skillsync.applyr.modules.company.repositories.WeeklyTargetsCombinedRepository;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

import static com.skillsync.applyr.modules.company.services.EmployeeProfileServiceHelper.fromAgentToProfileDTO;

@Service
public class TargetService {

    private final WeeklyTargetsCombinedRepository weeklyTargetsCombinedRepository;
    private final WeeklyTargetRepository weeklyTargetRepository;
    private final SalesAgentRepository salesAgentRepository;
    private final ApplicationRepository applicationRepository;
    private final ApplicationService applicationService;


    public TargetService(WeeklyTargetsCombinedRepository weeklyTargetsCombinedRepository, WeeklyTargetRepository weeklyTargetRepository, SalesAgentRepository salesAgentRepository, ApplicationRepository applicationRepository, ApplicationService applicationService) {
        this.weeklyTargetsCombinedRepository = weeklyTargetsCombinedRepository;
        this.weeklyTargetRepository = weeklyTargetRepository;
        this.salesAgentRepository = salesAgentRepository;

        this.applicationRepository = applicationRepository;
        this.applicationService = applicationService;
    }

    public SuccessResponse createTarget(WeeklyTargetsRequestDTO target) {
        WeeklyTargetCombined weeklyTargetCombined = new WeeklyTargetCombined();
        weeklyTargetCombined.setTitle(target.getTitle());
        weeklyTargetCombined.setStartDate(target.getStartDate().plusDays(1));
        weeklyTargetCombined.setEndDate(target.getEndDate().plusDays(1));
        weeklyTargetCombined = weeklyTargetsCombinedRepository.save(weeklyTargetCombined);
        for (WeeklyTargetDTO dto : target.getTargets()) {
            WeeklyTarget weeklyTarget = new WeeklyTarget(dto);
            weeklyTargetCombined.addWeeklyTarget(weeklyTarget);
        }
        weeklyTargetCombined = weeklyTargetsCombinedRepository.save(weeklyTargetCombined);
        return new SuccessResponse("Successfully created " + weeklyTargetCombined.getTitle());
    }

    public SuccessResponse updateTarget(WeeklyTargetsRequestDTO target) {
        Optional<WeeklyTargetCombined> rawTargetOpt = weeklyTargetsCombinedRepository.findById(target.getId());
        if (rawTargetOpt.isPresent()) {
            WeeklyTargetCombined rawTarget = rawTargetOpt.get();
            rawTarget.setTitle(target.getTitle());
            rawTarget.setStartDate(target.getStartDate());
            rawTarget.setEndDate(target.getEndDate());
            for (WeeklyTargetDTO weeklyTargetDTO : target.getTargets()) {
                var weeklyTarget = rawTarget.getWeeklyTargets().stream()
                        .filter(wt -> wt.getAgentUsername().equals(weeklyTargetDTO.getAgentUsername()))
                        .findFirst()
                        .orElseThrow(() -> new NoSuchElementException("No WeeklyTarget found for username: " + weeklyTargetDTO.getAgentUsername()));
                weeklyTarget.setKpi1(weeklyTargetDTO.getKpi1());
                weeklyTarget.setKpi2(weeklyTargetDTO.getKpi2());
                weeklyTargetRepository.save(weeklyTarget);
            }
            weeklyTargetsCombinedRepository.save(rawTarget);
            return new SuccessResponse("Successfully updated weekly targets of Period " + rawTarget.getTitle());
        }
        throw new AppRTException("Unable to update weekly target as it does not exist", HttpStatus.NOT_FOUND);
    }

    public List<WeeklyTargetResponseDTO> getAllTargets() {
        List<WeeklyTargetCombined> weeklyTargetCombineds = weeklyTargetsCombinedRepository.findAll();
        List<WeeklyTargetResponseDTO> weeklyTargetDTOs = new ArrayList<>();
        for (WeeklyTargetCombined weeklyTargetCombined : weeklyTargetCombineds) {
            weeklyTargetDTOs.add(fromWeeklyTargetCombinedToDTO(weeklyTargetCombined));
        }
        return weeklyTargetDTOs;
    }

    private WeeklyTargetResponseDTO fromWeeklyTargetCombinedToDTO(WeeklyTargetCombined weeklyTargetCombined) {
        WeeklyTargetResponseDTO weeklyTargetResponseDTO = new WeeklyTargetResponseDTO();
        weeklyTargetResponseDTO.setId(weeklyTargetCombined.getId());
        weeklyTargetResponseDTO.setTargets(new ArrayList<>());
        weeklyTargetResponseDTO.setTitle(weeklyTargetCombined.getTitle());
        weeklyTargetResponseDTO.setStartDate(weeklyTargetCombined.getStartDate().toString());
        weeklyTargetResponseDTO.setEndDate(weeklyTargetCombined.getEndDate().toString());

        for (WeeklyTarget weeklyTarget : weeklyTargetCombined.getWeeklyTargets()) {
            AgentTargetResponseDTO agentTargetResponseDTO = new AgentTargetResponseDTO();
            Optional<SalesAgent> salesAgent = salesAgentRepository.getSalesAgentByUserUsername(weeklyTarget.getAgentUsername());
            ProfileDTO profileDTO = new ProfileDTO();
            if (salesAgent.isPresent()) {
                profileDTO = fromAgentToProfileDTO(salesAgent.get());
            } else {
                profileDTO.setFullName("System Error for this Agent");
            }
            agentTargetResponseDTO.setProfile(profileDTO);

            agentTargetResponseDTO.setKpi1Target(weeklyTarget.getKpi1());
            agentTargetResponseDTO.setKpi2Target(weeklyTarget.getKpi2());

            List<Application> applications = applicationRepository
                    .findAllByAgentUsernameAndCreatedDateAfterAndCreatedDateBefore(
                            weeklyTarget.getAgentUsername(),
                            weeklyTargetCombined.getStartDate(),
                            weeklyTargetCombined.getEndDate()
                                    .plusDays(1).toLocalDate().atStartOfDay());

            List<Application> applicationsKPI2 = applicationRepository
                    .findAllByAgentUsernameAndLastModifiedDateAfterAndLastModifiedDateBefore(
                            weeklyTarget.getAgentUsername(),
                            weeklyTargetCombined.getStartDate(),
                            weeklyTargetCombined.getEndDate()
                                    .plusDays(1).toLocalDate().atStartOfDay());

            double kpi1Actual = 0;
            double kpi2Actual = 0;
            for (Application application : applications) {
                kpi1Actual += application.getPrice();
            }

            for (Application application : applicationsKPI2) {
                kpi2Actual += application.getPaidAmount();
            }

            agentTargetResponseDTO.setKpi1Actual(kpi1Actual);
            agentTargetResponseDTO.setKpi2Actual(kpi2Actual);
            agentTargetResponseDTO.setApplications(applicationService.fromApplicationsToApplicationDTOs(applications));

            weeklyTargetResponseDTO.getTargets().add(agentTargetResponseDTO);
        }
        return weeklyTargetResponseDTO;
    }
}
