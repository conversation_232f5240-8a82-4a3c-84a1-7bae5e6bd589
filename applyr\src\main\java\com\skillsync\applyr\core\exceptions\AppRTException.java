package com.skillsync.applyr.core.exceptions;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public class AppRTException extends RuntimeException {

    private final String message;
    private final HttpStatus httpStatus;

    public AppRTException(String message, HttpStatus httpStatus){
        this.message = message;
        this.httpStatus = httpStatus;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public HttpStatus getStatus() {
        return this.httpStatus;
    }
}
