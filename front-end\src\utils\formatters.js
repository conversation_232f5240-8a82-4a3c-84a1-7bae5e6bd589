/**
 * Format a number as currency
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (default: AUD)
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = "AUD") => {
  return new Intl.NumberFormat("en-AU", {
    style: "currency",
    currency: currency,
  }).format(amount);
};

/**
 * Format a date string to a readable format
 * @param {string} dateString - The date string to format
 * @returns {string} Formatted date string
 */
export const formatDate = (dateString) => {
  if (!dateString) return "";

  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-AU", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(date);
};

/**
 * Format a JavaScript Date object to LocalDateTime format for Spring Boot
 * @param {Date} date - The date object to format
 * @returns {string} Formatted datetime string (YYYY-MM-DDTHH:mm:ss)
 */
export const toLocalDateTime = (date) => {
  if (!date) return "";

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
};

/**
 * Get current date in LocalDateTime format for Spring Boot
 * @returns {string} Current datetime in YYYY-MM-DDTHH:mm:ss format
 */
export const getCurrentLocalDateTime = () => {
  return toLocalDateTime(new Date());
};
