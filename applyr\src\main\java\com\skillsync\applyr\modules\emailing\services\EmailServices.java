package com.skillsync.applyr.modules.emailing.services;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class EmailServices {


    private final JavaMailSender mailSender;

    // Inject the sender email from application.properties
    @Value("${spring.mail.username}")
    private String senderEmail;

    @Async
    public void sendEmail(String subject, List<String> recipients, List<String> cc, String body) {
        try {
            MimeMessage message = mailSender.createMimeMessage();

            // Set multipart to true and encoding to UTF-8
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(senderEmail); // Use the injected sender email
            helper.setSubject(subject);
            helper.setText(body, true); // Set to true for HTML content

            helper.setTo(recipients.toArray(new String[0]));

            if (cc != null && !cc.isEmpty()) {
                helper.setCc(cc.toArray(new String[0]));
            }

            mailSender.send(message);
            System.out.println("Email sent successfully to " + recipients);
        } catch (MessagingException e) {
            System.out.println("Failed to send email");
        } catch (Exception e) {
            System.out.println("Unexpected error while sending " + e.getMessage());
        }
    }
}
