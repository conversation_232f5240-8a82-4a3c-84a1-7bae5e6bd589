import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faMoneyBillWave, faUpload, faSpinner } from "@fortawesome/free-solid-svg-icons";

const KPI2InvoicingTable = ({
  filteredData,
  setImportTab,
  setIsModalOpen,
  isLoading,
}) => {
  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-[#3A3541]">
          <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2 text-[#6E39CB]" />
          KPI2 Money in Bank Data
        </h2>
        <button
          onClick={() => {
            setImportTab("KPI2");
            setIsModalOpen(true);
          }}
          className="flex items-center gap-2 px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-opacity-90 transition-colors"
        >
          <FontAwesomeIcon icon={faUpload} />
          Import Data
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-[#DBDCDE] overflow-hidden">
        <div className="overflow-x-auto" style={{ maxWidth: '100%', overflowX: 'auto' }}>
          <table className="w-full divide-y divide-[#DBDCDE] table-fixed">
            <thead className="bg-[#F9FAFB]">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[100px]">
                  Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[120px]">
                  Agent
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[120px]">
                  Reference
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[150px]">
                  Contact
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[180px]">
                  Description
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[80px]">
                  Debit
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[80px]">
                  Credit
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[80px]">
                  Net
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[120px]">
                  Source
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-[#DBDCDE]">
              {isLoading ? (
                <tr>
                  <td colSpan="9" className="px-3 py-8 text-center text-[#89868D]">
                    <div className="flex flex-col items-center">
                      <FontAwesomeIcon icon={faSpinner} className="text-3xl mb-2 text-[#6E39CB] animate-spin" />
                      <p className="text-[#3A3541] font-medium">Loading payment data...</p>
                    </div>
                  </td>
                </tr>
              ) : filteredData.length > 0 ? (
                filteredData.map((item, index) => (
                  <tr key={index} className="hover:bg-[#F9FAFB] transition-colors">
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.date}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541] truncate" title={item.agentCommission}>
                      {item.agentCommission}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.reference ? (
                        <a
                          href={`/admin/application/profile/${item.applicationId || 'MJ99OSE'}`}
                          className="text-[#6E39CB] hover:underline"
                        >
                          {item.reference}
                        </a>
                      ) : (
                        <span className="text-[#89868D]">-</span>
                      )}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541] truncate" title={item.contact}>
                      {item.contact}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541] truncate" title={item.description}>
                      {item.description}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      ${parseFloat(item.debit).toLocaleString()}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      ${parseFloat(item.credit).toLocaleString()}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      ${parseFloat(item.net).toLocaleString()}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541] truncate" title={item.source}>
                      {item.source}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="9" className="px-3 py-8 text-center text-[#89868D]">
                    <div className="flex flex-col items-center">
                      <FontAwesomeIcon icon={faMoneyBillWave} className="text-3xl mb-2 text-[#DBDCDE]" />
                      <p className="text-[#3A3541] font-medium">No payment data found</p>
                      <p className="text-sm mt-1">Import data or adjust your filters</p>
                      <button
                        onClick={() => {
                          setImportTab("KPI2");
                          setIsModalOpen(true);
                        }}
                        className="mt-4 flex items-center gap-2 px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-opacity-90 transition-colors"
                      >
                        <FontAwesomeIcon icon={faUpload} />
                        Import Data
                      </button>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
};

export default KPI2InvoicingTable;
