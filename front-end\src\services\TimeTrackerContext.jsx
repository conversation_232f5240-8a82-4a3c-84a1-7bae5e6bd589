import React, { createContext, useEffect, useState } from "react";
import { getToken } from "./LocalStorageService";
import { useUpdateTimeStampMutation } from "./CompanyAPIService";
const TimeTrackerContext = createContext();

export const TimeTrackerProvider = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(!!getToken());
  const [firstAccess, setFirstAccess] = useState(null);
  const [lastAccess, setLastAccess] = useState(null);

  const [updateTimeTracker] = useUpdateTimeStampMutation();

  useEffect(() => {
    if (!firstAccess) {
      const firstTime = new Date().toLocaleTimeString();
      setFirstAccess(firstTime);
    }

    const interval = setInterval(async () => {
      const token = getToken();
      setIsLoggedIn(!!token);
      if (token) {
        try {
          const response = await updateTimeTracker().unwrap();
          console.log("Time updated:", response);
        } catch (err) {
          console.error("Error updating time:", err);
        }
      }
    }, 150000);

    const handleBeforeUnload = () => {
      const lastTime = new Date().toLocaleTimeString();
      setLastAccess(lastTime);
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      clearInterval(interval);
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [firstAccess, updateTimeTracker]);

  return (
    <TimeTrackerContext.Provider
      value={{ firstAccess, lastAccess, isLoggedIn }}
    >
      {children}
    </TimeTrackerContext.Provider>
  );
};

export default TimeTrackerContext;
