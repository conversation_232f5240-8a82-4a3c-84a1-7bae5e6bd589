import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faEye,
  faTrash,
  faExternalLinkAlt,
  faMapMarkerAlt,
  faIdCard,
  faBuilding,
  faUserFriends,
  faCheckCircle
} from "@fortawesome/free-solid-svg-icons";

const RTOCard = ({ rto, onView, onDelete, onExternalLink }) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-all duration-300 hover:border-blue-300 w-full">
      <div className="p-5">
        {/* RTO Code and External Link */}
        <div className="flex justify-between items-center mb-3">
          <div className="flex items-center">
            <div className="bg-blue-100 p-2 rounded-full mr-3">
              <FontAwesomeIcon icon={faIdCard} className="text-blue-600" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500">RTO Code</div>
              <div className="font-bold text-gray-800">{rto.code}</div>
            </div>
          </div>
          <button
            onClick={onExternalLink}
            className="text-gray-400 hover:text-blue-600 transition-colors duration-200 p-2"
            title="View on training.gov.au"
          >
            <FontAwesomeIcon icon={faExternalLinkAlt} />
          </button>
        </div>

        {/* RTO Name */}
        <h3 className="text-lg font-bold text-gray-800 mb-3 line-clamp-2">
          {rto.legalName}
        </h3>

        {/* RTO Details */}
        <div className="space-y-2 mb-4">
          {rto.businessName && (
            <div className="flex items-center text-sm text-gray-600">
              <FontAwesomeIcon icon={faBuilding} className="mr-2 text-blue-500 w-4" />
              <span className="line-clamp-1">{rto.businessName}</span>
            </div>
          )}

          {rto.address && (
            <div className="flex items-start text-sm text-gray-600">
              <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2 mt-1 text-blue-500 w-4" />
              <span className="line-clamp-2">{rto.address}</span>
            </div>
          )}

          {rto.rtoType && (
            <div className="flex items-center text-sm text-gray-600">
              <FontAwesomeIcon icon={faCheckCircle} className="mr-2 text-blue-500 w-4" />
              <span>{rto.rtoType}</span>
            </div>
          )}
        </div>

        {/* Info Bar */}
        <div className="flex items-center justify-between bg-gray-50 p-3 rounded-lg mb-4">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faUserFriends} className="text-blue-500 mr-2" />
            <span className="text-sm font-medium">{rto.contacts?.length || 0} Contacts</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <button
            onClick={onView}
            className="bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200 flex-1 justify-center mr-2"
          >
            <FontAwesomeIcon icon={faEye} className="mr-2" />
            View Details
          </button>

          {onDelete && (
            <button
              onClick={onDelete}
              className="bg-red-50 hover:bg-red-100 text-red-600 px-4 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faTrash} className="mr-2" />
              Remove
            </button>
          )}

          {!onDelete && (
            <button
              onClick={onExternalLink}
              className="bg-gray-50 hover:bg-gray-100 text-gray-600 px-4 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faExternalLinkAlt} className="mr-2" />
              TGA
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default RTOCard;
