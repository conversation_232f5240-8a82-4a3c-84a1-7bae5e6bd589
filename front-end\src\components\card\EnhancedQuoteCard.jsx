
import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faEdit,
  faPlus,
  faFileInvoice,
  faFileContract,
  faPhone,
  faEnvelope,
  faCopy,
  faEllipsisVertical,
  faCheck,
  faChevronDown,
  faChevronUp
} from "@fortawesome/free-solid-svg-icons";
import CreateInvoiceQuoteModal from "../modal/CreateInvoiceQuoteModal";
import StatusChangeModal from "../modal/StatusChangeModal";
import EditReferenceModal from "../modal/EditReferenceModal";
import { useChangeQuoteStatusMutation, useChangeInvoiceStatusMutation } from "../../services/CompanyAPIService";

const EnhancedQuoteCard = ({
  onQuoteUpdated,
  agentName,
  clientName,
  clientEmail,
  clientPhone,
  candidateName,
  phoneNumber,
  email,
  otherInformation,
  quoteRequestDetail,
  price,
  quoteNumber,
  invoiceNumber,
  quoteStatus = "SENT",
  invoiceStatus = "SENT",
  applicationId,
  createdAt,
}) => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isQuoteStatusModalOpen, setIsQuoteStatusModalOpen] = useState(false);
  const [isInvoiceStatusModalOpen, setIsInvoiceStatusModalOpen] = useState(false);
  const [isEditQuoteModalOpen, setIsEditQuoteModalOpen] = useState(false);
  const [isEditInvoiceModalOpen, setIsEditInvoiceModalOpen] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [copiedField, setCopiedField] = useState(null);

  // Use the actual status values from props
  const [localQuoteStatus, setLocalQuoteStatus] = useState(quoteStatus);
  const [localInvoiceStatus, setLocalInvoiceStatus] = useState(invoiceStatus);

  // Use the mutation hooks directly
  const [changeQuoteStatus] = useChangeQuoteStatusMutation();
  const [changeInvoiceStatus] = useChangeInvoiceStatusMutation();

  // Handle open/close of the modals
  const handleOpenCreateModal = () => setIsCreateModalOpen(true);
  const handleCloseCreateModal = () => setIsCreateModalOpen(false);

  const handleOpenQuoteStatusModal = () => setIsQuoteStatusModalOpen(true);
  const handleCloseQuoteStatusModal = () => setIsQuoteStatusModalOpen(false);

  const handleOpenInvoiceStatusModal = () => setIsInvoiceStatusModalOpen(true);
  const handleCloseInvoiceStatusModal = () => setIsInvoiceStatusModalOpen(false);

  // Edit reference modals
  const handleOpenEditQuoteModal = () => setIsEditQuoteModalOpen(true);
  const handleCloseEditQuoteModal = () => setIsEditQuoteModalOpen(false);

  const handleOpenEditInvoiceModal = () => setIsEditInvoiceModalOpen(true);
  const handleCloseEditInvoiceModal = () => setIsEditInvoiceModalOpen(false);

  // Toggle dropdown menu
  const toggleDropdown = () => setShowDropdown(!showDropdown);

  // Format price to ensure consistent display
  const formattedPrice = typeof price === 'number'
    ? price.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })
    : typeof price === 'string' && price.startsWith('$') ? price : `$${price}`;


  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Copy to clipboard function
  const copyToClipboard = (text, field) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status, type) => {
    const baseColors = {
      quote: {
        PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
        SENT: "bg-green-100 text-green-800 border-green-200",
        DRAFT: "bg-gray-100 text-gray-800 border-gray-200"
      },
      invoice: {
        PENDING: "bg-blue-100 text-blue-800 border-blue-200",
        SENT: "bg-purple-100 text-purple-800 border-purple-200",
        DRAFT: "bg-gray-100 text-gray-800 border-gray-200"
      }
    };

    return baseColors[type]?.[status] || "bg-gray-100 text-gray-800 border-gray-200";
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
      {/* Card header */}
      <div className="p-5 border-b border-gray-100 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex-1">
          <div className="flex flex-col md:flex-row md:items-center gap-4">
            <div className="flex items-center">
              <div>
                <span className="text-xs font-medium text-gray-500 uppercase">Source</span>
                <div className="flex items-center">
                  <h3 className="text-lg font-semibold text-gray-900 mr-2">{clientName}</h3>
                  <button
                    onClick={() => copyToClipboard(clientName, 'client')}
                    className="text-gray-400 hover:text-gray-600"
                    title="Copy client name"
                  >
                    {copiedField === 'client' ? (
                      <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                    ) : (
                      <FontAwesomeIcon icon={faCopy} size="sm" />
                    )}
                  </button>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <div className="h-4 w-0.5 bg-gray-200 mx-3 hidden md:block"></div>
              <div>
                <span className="text-xs font-medium text-gray-500 uppercase">Candidate</span>
                <div className="flex items-center">
                  <div className="text-sm font-medium text-gray-700 mr-2">{candidateName}</div>
                  <button
                    onClick={() => copyToClipboard(candidateName, 'candidate')}
                    className="text-gray-400 hover:text-gray-600"
                    title="Copy candidate name"
                  >
                    {copiedField === 'candidate' ? (
                      <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                    ) : (
                      <FontAwesomeIcon icon={faCopy} size="sm" />
                    )}
                  </button>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <div className="h-4 w-0.5 bg-gray-200 mx-3 hidden md:block"></div>
              <div>
                <span className="text-xs font-medium text-gray-500 uppercase">Agent</span>
                <div className="flex items-center">
                  <div className="text-sm font-medium text-gray-700 mr-2">{agentName}</div>
                  <button
                    onClick={() => copyToClipboard(agentName, 'agent')}
                    className="text-gray-400 hover:text-gray-600"
                    title="Copy agent name"
                  >
                    {copiedField === 'agent' ? (
                      <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                    ) : (
                      <FontAwesomeIcon icon={faCopy} size="sm" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Date */}
          <div className="text-sm text-gray-500 hidden md:block">
            {formatDate(createdAt)}
          </div>

          {/* Status badges */}
          <div className="flex gap-2">
            {quoteNumber && (
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusBadgeColor(localQuoteStatus, 'quote')}`}>
                Quote: {localQuoteStatus}
              </span>
            )}
            {invoiceNumber && (
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusBadgeColor(localInvoiceStatus, 'invoice')}`}>
                Invoice: {localInvoiceStatus}
              </span>
            )}
          </div>

          {/* Price */}
          <div className="flex items-center">
            <div className="font-bold text-lg text-gray-800 mr-2">{formattedPrice}</div>
            <button
              onClick={() => copyToClipboard(formattedPrice, 'price')}
              className="text-gray-400 hover:text-gray-600"
              title="Copy price"
            >
              {copiedField === 'price' ? (
                <FontAwesomeIcon icon={faCheck} className="text-green-500" />
              ) : (
                <FontAwesomeIcon icon={faCopy} />
              )}
            </button>
          </div>

          {/* Expand/Collapse button */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 mr-2"
            title={isExpanded ? "Collapse" : "Expand"}
          >
            <FontAwesomeIcon
              icon={isExpanded ? faChevronUp : faChevronDown}
              className="text-gray-500"
            />
          </button>

          {/* Actions dropdown */}
          <div className="relative">
            <button
              onClick={toggleDropdown}
              className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
            >
              <FontAwesomeIcon icon={faEllipsisVertical} className="text-gray-500" />
            </button>

            {showDropdown && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-100">
                <div className="py-1">
                  {!quoteNumber ? (
                    <button
                      onClick={() => {
                        handleOpenCreateModal();
                        setShowDropdown(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <FontAwesomeIcon icon={faPlus} className="mr-2" />
                      Raise Quote and Invoice
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={() => {
                          handleOpenQuoteStatusModal();
                          setShowDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FontAwesomeIcon icon={faFileContract} className="mr-2" />
                        Change Quote Status
                      </button>
                      <button
                        onClick={() => {
                          handleOpenInvoiceStatusModal();
                          setShowDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FontAwesomeIcon icon={faFileInvoice} className="mr-2" />
                        Change Invoice Status
                      </button>
                      <button
                        onClick={() => {
                          handleOpenEditQuoteModal();
                          setShowDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FontAwesomeIcon icon={faEdit} className="mr-2" />
                        Edit Quote Reference
                      </button>
                      <button
                        onClick={() => {
                          handleOpenEditInvoiceModal();
                          setShowDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FontAwesomeIcon icon={faEdit} className="mr-2" />
                        Edit Invoice Reference
                      </button>
                    </>
                  )}
                  <hr className="my-1 border-gray-100" />
                  <button
                    onClick={() => {
                      copyToClipboard(JSON.stringify({
                        clientName,
                        clientEmail,
                        clientPhone,
                        candidateName,
                        agentName,
                        phoneNumber,
                        email,
                        quoteRequestDetail,
                        price,
                        quoteNumber,
                        invoiceNumber,
                        quoteStatus,
                        invoiceStatus,
                        applicationId
                      }, null, 2), 'all');
                      setShowDropdown(false);
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <FontAwesomeIcon icon={faCopy} className="mr-2" />
                    Copy All Information
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Card content - only show when expanded */}
      {isExpanded && (
        <div className="p-5">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column - Contact information and Quote Request Details */}
          <div>
            <div className="flex items-center mb-3">
              <h4 className="text-sm font-semibold text-gray-700">Contact Information</h4>
              <div className="ml-auto flex space-x-2">
                {phoneNumber && (
                  <a href={`tel:${phoneNumber}`} className="text-gray-500 hover:text-blue-600 p-1 bg-gray-100 rounded-full">
                    <FontAwesomeIcon icon={faPhone} />
                  </a>
                )}
                {email && (
                  <a href={`mailto:${email}`} className="text-gray-500 hover:text-blue-600 p-1 bg-gray-100 rounded-full">
                    <FontAwesomeIcon icon={faEnvelope} />
                  </a>
                )}
              </div>
            </div>

            <div className="bg-white p-3 rounded-md border border-gray-200 mb-4">
              <div className="grid grid-cols-2 gap-x-3 gap-y-2 text-sm">

                <h6 className="font-semibold text-gray-700 col-span-2 mb-1 pb-1 border-b border-gray-100">Source</h6>
                <div className="flex items-center justify-between col-span-2">
                  <span className="font-medium text-gray-600">Phone:</span>
                  <div className="flex items-center">
                    <span className="text-gray-800 mr-2">{clientPhone || "N/A"}</span>
                    {clientPhone && (
                      <button
                        onClick={() => copyToClipboard(clientPhone, 'clientPhone')}
                        className="text-gray-400 hover:text-gray-600"
                        title="Copy client phone number"
                      >
                        {copiedField === 'clientPhone' ? (
                          <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                        ) : (
                          <FontAwesomeIcon icon={faCopy} />
                        )}
                      </button>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between col-span-2">
                  <span className="font-medium text-gray-600">Email:</span>
                  <div className="flex items-center">
                    <span className="text-gray-800 truncate mr-2">{clientEmail || "N/A"}</span>
                    {clientEmail && (
                      <button
                        onClick={() => copyToClipboard(clientEmail, 'clientEmail')}
                        className="text-gray-400 hover:text-gray-600"
                        title="Copy client email"
                      >
                        {copiedField === 'clientEmail' ? (
                          <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                        ) : (
                          <FontAwesomeIcon icon={faCopy} />
                        )}
                      </button>
                    )}
                  </div>
                </div>

                <h6 className="font-semibold text-gray-700 col-span-2 mt-3 mb-1 pb-1 border-b border-gray-100">Candidate</h6>
                <div className="flex items-center justify-between col-span-2">
                  <span className="font-medium text-gray-600">Phone:</span>
                  <div className="flex items-center">
                    <span className="text-gray-800 mr-2">{phoneNumber}</span>
                    <button
                      onClick={() => copyToClipboard(phoneNumber, 'phone')}
                      className="text-gray-400 hover:text-gray-600"
                      title="Copy phone number"
                    >
                      {copiedField === 'phone' ? (
                        <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                      ) : (
                        <FontAwesomeIcon icon={faCopy} />
                      )}
                    </button>
                  </div>
                </div>

                <div className="flex items-center justify-between col-span-2">
                  <span className="font-medium text-gray-600">Email:</span>
                  <div className="flex items-center">
                    <span className="text-gray-800 truncate mr-2">{email}</span>
                    <button
                      onClick={() => copyToClipboard(email, 'email')}
                      className="text-gray-400 hover:text-gray-600"
                      title="Copy email"
                    >
                      {copiedField === 'email' ? (
                        <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                      ) : (
                        <FontAwesomeIcon icon={faCopy} />
                      )}
                    </button>
                  </div>
                </div>

                

                {otherInformation && (
                  <div className="col-span-2 mt-3">
                    <span className="font-medium text-gray-600 block mb-2">Other Info:</span>
                    <div className="flex items-start">
                      <span className="text-gray-800 flex-1 mr-2 whitespace-pre-wrap break-words">{otherInformation}</span>
                      <button
                        onClick={() => copyToClipboard(otherInformation, 'otherInfo')}
                        className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                        title="Copy other information"
                      >
                        {copiedField === 'otherInfo' ? (
                          <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                        ) : (
                          <FontAwesomeIcon icon={faCopy} />
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Quote Request Details */}
            <div>
              <div className="flex items-center mb-2">
                <h4 className="text-sm font-semibold text-gray-700">Quote Request Details</h4>
                <button
                  onClick={() => copyToClipboard(quoteRequestDetail, 'quoteDetails')}
                  className="ml-auto text-gray-400 hover:text-gray-600"
                  title="Copy quote request details"
                >
                  {copiedField === 'quoteDetails' ? (
                    <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                  ) : (
                    <FontAwesomeIcon icon={faCopy} />
                  )}
                </button>
              </div>
              <div className="bg-white p-3 rounded-md border border-gray-200">
                <p className="text-gray-800 text-sm">{quoteRequestDetail}</p>
              </div>
            </div>
          </div>

          {/* Right column - Quote and Invoice information */}
          <div>
            {!quoteNumber ? (
              <div className="flex flex-col justify-center items-center h-full bg-white p-6 rounded-md border border-dashed border-gray-300">
                <button
                  className="inline-flex items-center bg-blue-600 text-white py-3 px-6 rounded-md shadow hover:bg-blue-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 text-base font-medium"
                  onClick={handleOpenCreateModal}
                >
                  <FontAwesomeIcon icon={faPlus} className="mr-2" />
                  Raise Quote and Invoice
                </button>
                <p className="text-gray-500 text-sm mt-3">No quote or invoice has been created yet</p>
              </div>
            ) : (
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-3">Quote & Invoice Information</h4>
                <div className="grid grid-cols-1 gap-4">
                  {/* Quote Card - black and white */}
                  <div className="border border-gray-200 bg-white rounded-lg p-4 shadow-sm">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center">
                        <div className="bg-gray-100 p-2 rounded-full mr-3">
                          <FontAwesomeIcon icon={faFileContract} className="text-gray-600" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-700 text-xs uppercase">Quote Reference</p>
                          <div className="flex items-center">
                            <p className="text-xl font-bold text-gray-800 mr-2">{quoteNumber}</p>
                            <button
                              onClick={() => copyToClipboard(quoteNumber, 'quoteNumber')}
                              className="text-gray-400 hover:text-gray-600"
                              title="Copy quote reference"
                            >
                              {copiedField === 'quoteNumber' ? (
                                <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                              ) : (
                                <FontAwesomeIcon icon={faCopy} />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                      <button
                        className="text-gray-600 hover:bg-gray-100 p-2 rounded-full transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-gray-400"
                        aria-label="Edit Quote"
                        title="Edit Quote"
                        onClick={handleOpenEditQuoteModal}
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </button>
                    </div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Status</label>
                    <div className="flex items-center">
                      <div className={`flex-1 font-medium text-sm px-2 py-1 rounded ${getStatusBadgeColor(localQuoteStatus, 'quote')}`}>
                        {localQuoteStatus}
                      </div>
                      <button
                        onClick={handleOpenQuoteStatusModal}
                        className="ml-2 text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-1 rounded"
                      >
                        Change
                      </button>
                    </div>
                  </div>

                  {/* Invoice Card - black and white */}
                  <div className="border border-gray-200 bg-white rounded-lg p-4 shadow-sm">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center">
                        <div className="bg-gray-100 p-2 rounded-full mr-3">
                          <FontAwesomeIcon icon={faFileInvoice} className="text-gray-600" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-700 text-xs uppercase">Invoice Reference</p>
                          <div className="flex items-center">
                            <p className="text-xl font-bold text-gray-800 mr-2">{invoiceNumber}</p>
                            <button
                              onClick={() => copyToClipboard(invoiceNumber, 'invoiceNumber')}
                              className="text-gray-400 hover:text-gray-600"
                              title="Copy invoice reference"
                            >
                              {copiedField === 'invoiceNumber' ? (
                                <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                              ) : (
                                <FontAwesomeIcon icon={faCopy} />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                      <button
                        className="text-gray-600 hover:bg-gray-100 p-2 rounded-full transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-gray-400"
                        aria-label="Edit Invoice"
                        title="Edit Invoice"
                        onClick={handleOpenEditInvoiceModal}
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </button>
                    </div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Status</label>
                    <div className="flex items-center">
                      <div className={`flex-1 font-medium text-sm px-2 py-1 rounded ${getStatusBadgeColor(localInvoiceStatus, 'invoice')}`}>
                        {localInvoiceStatus}
                      </div>
                      <button
                        onClick={handleOpenInvoiceStatusModal}
                        className="ml-2 text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-1 rounded"
                      >
                        Change
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      )}

      {/* Modals */}
      <CreateInvoiceQuoteModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        applicationId={applicationId}
        onSuccess={() => {
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />

      <StatusChangeModal
        isOpen={isQuoteStatusModalOpen}
        onClose={handleCloseQuoteStatusModal}
        type="quote"
        applicationId={applicationId}
        refNumber={quoteNumber}
        currentStatus={localQuoteStatus}
        onSuccess={() => {
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />

      <StatusChangeModal
        isOpen={isInvoiceStatusModalOpen}
        onClose={handleCloseInvoiceStatusModal}
        type="invoice"
        applicationId={applicationId}
        refNumber={invoiceNumber}
        currentStatus={localInvoiceStatus}
        onSuccess={() => {
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />

      <EditReferenceModal
        isOpen={isEditQuoteModalOpen}
        onClose={handleCloseEditQuoteModal}
        type="quote"
        applicationId={applicationId}
        currentRefNumber={quoteNumber}
        onSuccess={() => {
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />

      <EditReferenceModal
        isOpen={isEditInvoiceModalOpen}
        onClose={handleCloseEditInvoiceModal}
        type="invoice"
        applicationId={applicationId}
        currentRefNumber={invoiceNumber}
        onSuccess={() => {
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />
    </div>
  );
};

export default EnhancedQuoteCard;