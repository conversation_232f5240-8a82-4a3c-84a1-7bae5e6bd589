package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.Company;
import com.skillsync.applyr.core.models.entities.SalesAgent;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface SalesAgentRepository extends JpaRepository<SalesAgent, Long> {
    Optional<SalesAgent> getSalesAgentByUserUsername(String username);

    Optional<SalesAgent> getSalesAgentByFullNameContaining(String word);
}
