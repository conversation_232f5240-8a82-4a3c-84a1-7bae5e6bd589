import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFileInvoiceDollar,
  faFileContract,
  faFolderOpen,
  faFileExcel,
  faCalendarAlt,
  faSearch,
  faPhone,
  faEnvelope,
  faUser,
  faCopy,
  faCheck,
  faCertificate
} from '@fortawesome/free-solid-svg-icons';
import { useGetAllEmployeeQuery } from '../../../services/AdminAPIService';

const OperationsDashboard = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [copiedField, setCopiedField] = useState(null);

  // Fetch employee data
  const { data: employees, isLoading, error } = useGetAllEmployeeQuery();

  // Copy to clipboard function
  const copyToClipboard = (text, field) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    });
  };

  // Filter employees based on search term and exclude admins
  const filteredEmployees = employees
    ? employees.filter(
        (employee) =>
          // Exclude ADMIN role
          employee.role !== 'ADMIN' &&
          (
            employee.fullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            employee.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            employee.role?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (employee.phone && employee.phone.toLowerCase().includes(searchTerm.toLowerCase()))
          )
      )
    : [];

  return (
    <div className="w-full">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Operations Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome to the operations dashboard
          </p>
        </div>

        <div className="mt-4 md:mt-0">
          <div className="inline-flex items-center bg-white border border-gray-200 rounded-lg px-3 py-2">
            <FontAwesomeIcon icon={faCalendarAlt} className="text-gray-500 mr-2" />
            <span className="text-sm font-medium text-gray-600">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </div>
        </div>
      </div>



      {/* Quick Access */}
      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Access</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <a
            href="/operations/quoterequests"
            className="flex flex-col items-center justify-center p-4 bg-[#F4F5F9] rounded-lg hover:bg-[#EAECF2] transition-colors"
          >
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-8 w-8 text-[#6E39CB] mb-3" />
            <span className="text-sm font-medium text-gray-700">Quote & Invoice Requests</span>
          </a>

          <a
            href="/operations/draftedquotes"
            className="flex flex-col items-center justify-center p-4 bg-[#F4F5F9] rounded-lg hover:bg-[#EAECF2] transition-colors"
          >
            <FontAwesomeIcon icon={faFileContract} className="h-8 w-8 text-[#3B82F6] mb-3" />
            <span className="text-sm font-medium text-gray-700">Drafted Quotes & Invoices</span>
          </a>

          <a
            href="/operations/applications"
            className="flex flex-col items-center justify-center p-4 bg-[#F4F5F9] rounded-lg hover:bg-[#EAECF2] transition-colors"
          >
            <FontAwesomeIcon icon={faFolderOpen} className="h-8 w-8 text-[#10B981] mb-3" />
            <span className="text-sm font-medium text-gray-700">Applications</span>
          </a>

          <a
            href="/operations/filestatus"
            className="flex flex-col items-center justify-center p-4 bg-[#F4F5F9] rounded-lg hover:bg-[#EAECF2] transition-colors"
          >
            <FontAwesomeIcon icon={faFileExcel} className="h-8 w-8 text-[#F59E0B] mb-3" />
            <span className="text-sm font-medium text-gray-700">File Status</span>
          </a>

          <a
            href="/operations/qualifications"
            className="flex flex-col items-center justify-center p-4 bg-[#F4F5F9] rounded-lg hover:bg-[#EAECF2] transition-colors"
          >
            <FontAwesomeIcon icon={faCertificate} className="h-8 w-8 text-[#4F46E5] mb-3" />
            <span className="text-sm font-medium text-gray-700">Qualifications</span>
          </a>
        </div>
      </div>

      {/* Employee Information */}
      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Employee Information</h2>
            <p className="text-xs text-gray-500">Contact information for operations and sales staff</p>
          </div>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search employees..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#6E39CB]"></div>
          </div>
        ) : error ? (
          <div className="text-center p-4 text-red-500">
            Error loading employee data. Please try again later.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEmployees.length === 0 ? (
                  <tr>
                    <td colSpan="5" className="px-6 py-4 text-center text-sm text-gray-500">
                      No operations or sales staff found matching your search criteria.
                    </td>
                  </tr>
                ) : (
                  filteredEmployees.map((employee) => (
                    <tr key={employee.username} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-[#F4F5F9] flex items-center justify-center">
                            <FontAwesomeIcon icon={faUser} className="text-[#6E39CB]" />
                          </div>
                          <div className="ml-4">
                            <div className="flex items-center">
                              <div className="text-sm font-medium text-gray-900 mr-2">{employee.fullName}</div>
                              <button
                                onClick={() => copyToClipboard(employee.fullName, `name-${employee.username}`)}
                                className="text-gray-400 hover:text-gray-600"
                                title="Copy name"
                              >
                                {copiedField === `name-${employee.username}` ? (
                                  <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                                ) : (
                                  <FontAwesomeIcon icon={faCopy} size="xs" />
                                )}
                              </button>
                            </div>
                            <div className="text-xs text-gray-500">{employee.username}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm text-gray-900 mr-2">{employee.email}</div>
                          <button
                            onClick={() => copyToClipboard(employee.email, `email-${employee.username}`)}
                            className="text-gray-400 hover:text-gray-600"
                            title="Copy email"
                          >
                            {copiedField === `email-${employee.username}` ? (
                              <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                            ) : (
                              <FontAwesomeIcon icon={faCopy} size="xs" />
                            )}
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm text-gray-900 mr-2">{employee.phone || 'N/A'}</div>
                          {employee.phone && (
                            <button
                              onClick={() => copyToClipboard(employee.phone, `phone-${employee.username}`)}
                              className="text-gray-400 hover:text-gray-600"
                              title="Copy phone"
                            >
                              {copiedField === `phone-${employee.username}` ? (
                                <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                              ) : (
                                <FontAwesomeIcon icon={faCopy} size="xs" />
                              )}
                            </button>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${employee.role === 'ADMIN' ? 'bg-purple-100 text-purple-800' : employee.role === 'SALES' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>
                          {employee.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex space-x-3">
                          {employee.phone && (
                            <a href={`tel:${employee.phone}`} className="text-blue-600 hover:text-blue-800">
                              <FontAwesomeIcon icon={faPhone} />
                            </a>
                          )}
                          <a href={`mailto:${employee.email}`} className="text-blue-600 hover:text-blue-800">
                            <FontAwesomeIcon icon={faEnvelope} />
                          </a>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default OperationsDashboard;
