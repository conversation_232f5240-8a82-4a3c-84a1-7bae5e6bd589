package com.skillsync.applyr.core.models.entities;

import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WeeklyTargetCombined {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String title;
    private LocalDateTime startDate;
    private LocalDateTime endDate;

    // One-to-Many relationship with WeeklyTarget
    @OneToMany(mappedBy = "weeklyTargetsCombined", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<WeeklyTarget> weeklyTargets = new ArrayList<>();

    public void addWeeklyTarget(WeeklyTarget target) {
        weeklyTargets.add(target);
        target.setWeeklyTargetsCombined(this);
    }

    public void removeWeeklyTarget(WeeklyTarget target) {
        weeklyTargets.remove(target);
        target.setWeeklyTargetsCombined(null);
    }
}
