package com.skillsync.applyr.modules.sales.models;


import com.skillsync.applyr.core.models.entities.TroubleAnswer;
import com.skillsync.applyr.core.models.enums.TroubleStatus;
import com.skillsync.applyr.modules.company.models.ProfileDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TroublesResponseDTO {
    private long troubleId;
    private String questions;
    private ProfileDTO createdBy;
    private ProfileDTO assignedTo;
    private LocalDateTime createdDate;
    private LocalDateTime dueDate;
    private TroubleStatus status;

    private List<TroubleAnswerDTO> answers = new ArrayList<>();

    public void addAnswer(TroubleAnswerDTO answer) {
        answers.add(answer);
    }
}
