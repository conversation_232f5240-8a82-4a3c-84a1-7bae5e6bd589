package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.Tracker;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface TrackerRepository extends JpaRepository<Tracker, Long> {
    Optional<Tracker> findByUsernameAndCreatedDateAfter(String username, LocalDateTime createdDateAfter);

    List<Tracker> findAllByUsernameAndCreatedDateAfter(String username, LocalDateTime createdDateAfter);

    List<Tracker> findAllByUsernameAndCreatedDateAfterAndCreatedDateBefore(String username, LocalDateTime localDateTime, LocalDateTime localDateTime1);
}
