package com.skillsync.applyr.modules.authentication.controller;


import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.authentication.models.AuthRequestDTO;
import com.skillsync.applyr.modules.authentication.models.AuthResponseDTO;
import com.skillsync.applyr.modules.authentication.services.AuthServices;
import com.skillsync.applyr.modules.company.services.CompanyServices;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/auth")
public class AuthController {

    private final AuthServices authServices;
    private final CompanyServices companyServices;

    public AuthController(AuthServices authServices, CompanyServices companyServices) {
        this.authServices = authServices;
        this.companyServices = companyServices;
    }

    @PostMapping("/login")
    public ResponseEntity<AuthResponseDTO> login(@RequestBody AuthRequestDTO authRequest) {
        return ResponseEntity.ok(new AuthResponseDTO(authServices.authenticate(authRequest), authRequest.getUsername()));
    }

    @GetMapping("/setup")
    public ResponseEntity<SuccessResponse> setup() {
        return ResponseEntity.ok(companyServices.setup());
    }

    @GetMapping("/setup/migration")
    public ResponseEntity<SuccessResponse> migrate() {
        return ResponseEntity.ok(companyServices.migrate());
    }






}