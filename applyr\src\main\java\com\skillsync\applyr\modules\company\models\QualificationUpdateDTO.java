package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class QualificationUpdateDTO {
    private String qualificationId;
    private String qualificationName;
    private double rplPrice;
    private double rtoPriceHigh;
    private double enrollmentPrice;
    private double offshorePrice;
    private String notes;
    private String type;

    private String processingTime;
    private String demand;
    private List<String> checklist;
}
