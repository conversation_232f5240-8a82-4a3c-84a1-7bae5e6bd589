import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faComments } from "@fortawesome/free-solid-svg-icons"; // Importing comment icon
import { ToastContainer, toast } from "react-toastify"; // For toast notifications
import "react-toastify/dist/ReactToastify.css";

// FileStatusCard Component
const FileStatusCard = ({
  clientName,
  agentName,
  invoiceNumber,
  certificationName,
  status,
  lodgementDate,
  softCopyReleaseDate,
  hardCopyReleaseDate,
}) => {
  // State to manage the comment section visibility and new comment
  const [isCommentDrawerOpen, setIsCommentDrawerOpen] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [comments, setComments] = useState([]);

  // Function to handle status change
  const handleStatusChange = (e) => {
    console.log("Status changed to:", e.target.value);
  };

  // Function to handle adding a comment
  const handleAddComment = () => {
    if (newComment.trim()) {
      setComments([...comments, newComment]);
      toast.success("Comment added successfully!");
      setNewComment(""); // Clear comment input
    } else {
      toast.error("Please write a comment before submitting.");
    }
  };

  // Toggle the comment drawer visibility
  const toggleCommentDrawer = () => {
    setIsCommentDrawerOpen(!isCommentDrawerOpen);
  };

  return (
    <div className="relative flex border p-6 rounded-lg shadow-lg">
      {/* Left Column */}
      <div className="w-1/3 pr-4">
        <h3 className="font-medium text-lg">
          Client Name: <span className="font-normal">{clientName}</span>
        </h3>
        <p className="font-medium">
          Agent Name: <span className="font-normal">{agentName}</span>
        </p>
        <p className="font-medium">
          Invoice Number: <span className="font-normal">{invoiceNumber}</span>
        </p>
        <p className="font-medium mt-4">
          Date of Lodgement with RTO:{" "}
          <span className="font-normal">{lodgementDate}</span>
        </p>
        <p className="font-medium mt-2">
          Date of Soft Copy Release:{" "}
          <span className="font-normal">{softCopyReleaseDate}</span>
        </p>
      </div>

      <div className="border-l-2 border-gray-300 mx-4"></div>

      {/* Middle Column */}
      <div className="w-1/3 pl-4">
        <h3 className="font-semibold text-lg">Certification Name:</h3>
        <p>{certificationName}</p>
        <p className="font-medium mt-4">
          Date of Hard Copy Released:{" "}
          <span className="font-normal">{hardCopyReleaseDate}</span>
        </p>
      </div>

      <div className="border-l-2 border-gray-300 mx-4"></div>

      {/* Right Column */}
      <div className="w-1/3 pl-4 flex flex-col justify-between">
        {/* Status Dropdown and Comment Button */}
        <div className="flex flex-col items-end w-full">
          <select
            value={status}
            onChange={handleStatusChange}
            className="border p-2 rounded w-40 mb-4"
          >
            <option value="Pending Evidence">Pending Evidence</option>
            <option value="Lodged & Processing">Lodged & Processing</option>
            <option value="Require Additional Information">
              Require Additional Information
            </option>
            <option value="Soft Copy Received - Pending Payment">
              Soft Copy Received - Pending Payment
            </option>
            <option value="Soft Copy Released - Full Payment Received">
              Soft Copy Released - Full Payment Received
            </option>
            <option value="Hard Copy Received">Hard Copy Received</option>
            <option value="Hard Copy Mailed & Closed">
              Hard Copy Mailed & Closed
            </option>
          </select>

          {/* Comment Button */}
          <button
            onClick={toggleCommentDrawer}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg mt-2"
          >
            <FontAwesomeIcon icon={faComments} size="sm" /> Comments
          </button>
        </div>
      </div>

      {/* Comment Drawer */}
      {isCommentDrawerOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-30 z-40"
          onClick={toggleCommentDrawer}
        />
      )}
      <div
        className={`fixed top-0 right-0 h-full w-1/3 bg-white p-4 shadow-lg transform transition-transform duration-300 z-50 ${
          isCommentDrawerOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Comments</h3>
          <button
            onClick={toggleCommentDrawer}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        <div className="mt-4">
          <div className="space-y-2">
            {comments.length > 0 ? (
              comments.map((comment, index) => (
                <div
                  key={index}
                  className="p-3 border rounded-md bg-gray-50 text-sm"
                >
                  <p className="text-gray-700">{comment}</p>
                </div>
              ))
            ) : (
              <p className="text-gray-500">No comments yet.</p>
            )}
          </div>

          <div className="mt-4">
            <textarea
              className="w-full p-2 border border-gray-300 rounded-md"
              rows="3"
              placeholder="Write a comment..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
            />
            <button
              onClick={handleAddComment}
              className="bg-blue-500 text-white px-4 py-2 rounded-md mt-2"
            >
              Add Comment
            </button>
          </div>
        </div>
      </div>

      <ToastContainer />
    </div>
  );
};

export default FileStatusCard;