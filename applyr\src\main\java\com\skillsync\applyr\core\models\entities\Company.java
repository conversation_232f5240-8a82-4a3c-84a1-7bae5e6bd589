package com.skillsync.applyr.core.models.entities;


import com.skillsync.applyr.modules.company.models.AdminRegisterDTO;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Entity
@Table(name = "company")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class Company extends Auditable<String> {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private String fullName;

    @Column(nullable = false)
    private String gender;

    @Column(nullable = false, unique = true)
    private String emailAddress;

    @Column()
    private String phoneNumber;

    @Column()
    private String address;

    @Column()
    private boolean active;

    @Column()
    private boolean isSystemAdmin;

    @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(referencedColumnName = "id", nullable = false)
    private User user;

    public Company(AdminRegisterDTO adminInfo) {
        this.fullName = adminInfo.getFullName();
        this.gender = adminInfo.getGender();
        this.emailAddress = adminInfo.getEmail();
        this.phoneNumber = adminInfo.getPhone();
        this.address = adminInfo.getAddress();
    }
}
