package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.XeroInBank;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface XeroInBankRepository extends JpaRepository<XeroInBank, Long> {
    Optional<XeroInBank> findByApplicationId(String applicationId);

    Optional<XeroInBank> findByReference(String reference);
}
