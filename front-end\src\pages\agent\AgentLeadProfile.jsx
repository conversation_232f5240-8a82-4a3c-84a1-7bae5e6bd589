import React, { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faClock } from "@fortawesome/free-solid-svg-icons";
import { useParams, useNavigate } from "react-router-dom";
import ReactSelect from "react-select";
import {
  useGetLeadByPhoneQuery,
  useAddLeadCommentMutation,
  useUpdateLeadCommentMutation,
  useDeleteLeadCommentMutation,
  useUpdateLeadStatusMutation,
  useGetLeadsQuery,
  useCreateApplicationMutation,
  useChangeLeadOwnerMutation,
} from "../../services/CompanyAPIService";
import { useGetAllAgentsQuery } from "../../services/AdminAPIService";
import { useGetQualificationsQuery } from "../../services/SalesAPIService";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PointElement,
  LineElement,
} from "chart.js";
import { Bar, Line } from "react-chartjs-2";
import MultiStepCreateApplicationModal from "../../components/modal/MultiStepCreateApplicationModal";
import { showErrorToast } from "../../utils/toastUtils";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  PointElement,
  LineElement
);

const ChangeOwnerModal = ({ isOpen, onClose, onSave, agents }) => {
  const [selectedAgent, setSelectedAgent] = useState(null);

  if (!isOpen) return null;

  const handleSave = () => {
    if (selectedAgent) {
      onSave(selectedAgent);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50">
      <div className="bg-white w-full max-w-lg p-6 rounded-lg shadow-lg">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Change Lead Owner</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Agent
          </label>
          <ReactSelect
            options={agents}
            getOptionLabel={(option) =>
              `${option.fullName} (${option.username})`
            }
            getOptionValue={(option) => option.id}
            value={selectedAgent}
            onChange={setSelectedAgent}
            isSearchable
            placeholder="Select an agent..."
            className="react-select-container"
            classNamePrefix="react-select"
            styles={{
              control: (base) => ({
                ...base,
                borderColor: '#E5E7EB',
                borderRadius: '0.5rem',
                minHeight: '42px',
                boxShadow: 'none',
                '&:hover': {
                  borderColor: '#6E39CB'
                },
                '&:focus': {
                  borderColor: '#6E39CB',
                  boxShadow: '0 0 0 1px #6E39CB'
                }
              }),
              option: (base, state) => ({
                ...base,
                backgroundColor: state.isSelected ? '#6E39CB' : state.isFocused ? '#F4F5F9' : 'white',
                color: state.isSelected ? 'white' : '#111827',
                '&:active': {
                  backgroundColor: '#6E39CB',
                  color: 'white'
                }
              })
            }}
          />
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
            disabled={!selectedAgent}
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

// Helper function to get status class based on status value
const getStatusClass = (status) => {
  switch (status?.toUpperCase()) {
    case "HOT":
      return "bg-red-100 text-red-800";
    case "WARM":
      return "bg-orange-100 text-orange-800";
    case "COLD":
      return "bg-blue-100 text-blue-800";
    case "FRESH":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const AgentLeadProfile = () => {
  const { phoneNumber } = useParams();
  const navigate = useNavigate();
  const [leadNumber, setLeadNumber] = useState("");
  const handleOpenModal = (phoneNumber) => {
    setLeadNumber(phoneNumber);
    setIsModalOpen(true);
  };
  const { data: lead, error, isLoading } = useGetLeadByPhoneQuery(phoneNumber);
  const [addLeadComment, { isLoading: isAddingComment }] =
    useAddLeadCommentMutation();
  const [updateLeadComment] = useUpdateLeadCommentMutation();
  const [deleteLeadComment] = useDeleteLeadCommentMutation();
  const [leadStatus, setLeadStatus] = useState(lead?.status || "FRESH");
  const [applications, setApplications] = useState([]);
  const [reminders, setReminders] = useState([]);
  const [activities, setActivities] = useState([]);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState("");
  const [updateLeadStatus, { isLoading: isUpdatingStatus }] =
    useUpdateLeadStatusMutation();

  // State for editing comments
  const [editingComment, setEditingComment] = useState(null);
  const [editCommentText, setEditCommentText] = useState("");

  const { data: qualificationsData = [] } = useGetQualificationsQuery();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const { data: agentLeads = [] } = useGetLeadsQuery();

  // NEW: Get all agents (for the change owner modal)
  const { data: allAgents = [] } = useGetAllAgentsQuery();
  const [changeLeadOwnerMutation] = useChangeLeadOwnerMutation();

  // Tabs and search state
  const [activeTab, setActiveTab] = useState("applications");
  const [appFilter, setAppFilter] = useState("");
  const [appSortOrder, setAppSortOrder] = useState("asc");
  const [activitySearchTerm, setActivitySearchTerm] = useState("");
  const [commentSearchTerm, setCommentSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState("week");
  const [customDateFrom, setCustomDateFrom] = useState("");
  const [customDateTo, setCustomDateTo] = useState("");
  const [createApplication] = useCreateApplicationMutation();

  // Modal state for adding application
  const [showAddAppModal, setShowAddAppModal] = useState(false);
  const [newApp, setNewApp] = useState({
    fullName: "",
    applicationId: "",
    price: "",
    qualificationName: "",
    applicationDate: "",
    status: "Pending",
    notes: "",
  });

  // NEW: Local state for assigned agents (used in the Lead Owner tab)
  const [assignedAgents, setAssignedAgents] = useState([]);
  // NEW: State to show/hide the change owner modal
  const [showChangeOwnerModal, setShowChangeOwnerModal] = useState(false);

  useEffect(() => {
    if (lead) {
      setApplications(lead.applications || []);
      setActivities(lead.activities || []);
      setLeadStatus(lead.status || "FRESH");
      setComments(lead.comments || []);
      setAssignedAgents(lead.assignedAgents || []);
    }
  }, [lead]);

  // Modal state for adding a reminder
  const [showAddReminderModal, setShowAddReminderModal] = useState(false);
  const [newReminder, setNewReminder] = useState({
    title: "",
    message: "",
    email: "",
    reminderTime: "",
  });

  if (isLoading)
    return <div className="p-6 text-gray-600">Loading lead details...</div>;
  if (error)
    return <div className="p-6 text-red-600">Error fetching lead details.</div>;

  // Filter and sort the applications
  const filteredApplications = applications
    .filter((app) => {
      if (!appFilter) return true;
      const lowerFilter = appFilter.toLowerCase();
      return (
        app.fullName.toLowerCase().includes(lowerFilter) ||
        app.applicationId.toLowerCase().includes(lowerFilter) ||
        app.status.toLowerCase().includes(lowerFilter) ||
        (Array.isArray(app.qualificationName) &&
          app.qualificationName.join(" ").toLowerCase().includes(lowerFilter))
      );
    })

    .sort((a, b) => {
      if (appSortOrder === "asc") {
        return a.fullName.localeCompare(b.fullName);
      } else {
        return b.fullName.localeCompare(a.fullName);
      }
    });
  // ─── Handler for Changing Lead Owner ─────────────────────────
  // ─── Handler for Changing Lead Owner ─────────────────────────
  const handleChangeOwner = async (newAgent) => {
    try {
      await changeLeadOwnerMutation({
        leadPhone: lead.phone,
        newOwner: newAgent.username,
      }).unwrap();

      setAssignedAgents([newAgent]);

      // Show success message
      alert.success(`Lead owner changed to ${newAgent.username}`);
    } catch (error) {
      console.error("Failed to change lead owner:", error);
    }
    // Close the modal
    setShowChangeOwnerModal(false);
  };

  const handleSortChange = () => {
    setAppSortOrder((prevOrder) => (prevOrder === "asc" ? "desc" : "asc"));
  };

  // Filter for Activity and Comments tabs
  const filteredActivities = activities.filter((activity) =>
    activity.content.toLowerCase().includes(activitySearchTerm.toLowerCase())
  );
  const filteredComments = comments.filter((comment) =>
    comment.content.toLowerCase().includes(commentSearchTerm.toLowerCase())
  );

  // Handlers for adding an application
  const handleAddAppChange = (e) => {
    const { name, value } = e.target;
    setNewApp((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddComment = async () => {
    if (!newComment.trim()) {
      toast.error("Comment cannot be empty.");
      return;
    }

    // Store the comment text before clearing it
    const commentText = newComment;

    // Clear the comment field immediately
    setNewComment("");

    try {
      const newCommentData = {
        content: commentText,
        createdAt: new Date().toISOString(),
        createdBy: "You", // Adjust based on authentication
      };

      // Optimistically update the UI
      setComments((prevComments) => [...prevComments, newCommentData]);

      await addLeadComment({
        leadPhone: lead.phone,
        comment: commentText,
      }).unwrap();

      toast.success("Comment added successfully!");

      // Refetch the data from the server to ensure consistency
      refetch();
    } catch (error) {
      console.error("Failed to add comment:", error);
      toast.error("Failed to add comment.");
    }
  };

  const handleEditComment = (comment) => {
    setEditingComment(comment.id);
    setEditCommentText(comment.content || comment.comment);
  };

  const handleUpdateComment = async () => {
    if (editCommentText.trim()) {
      try {
        await updateLeadComment({
          leadPhone: lead.phone,
          commentId: editingComment,
          content: editCommentText,
        }).unwrap();
        toast.success("Comment updated successfully!");
        setEditingComment(null);
        setEditCommentText("");
        // Refetch the data from the server to ensure consistency
        refetch();
      } catch (error) {
        console.error("Failed to update comment:", error);
        toast.error("Failed to update comment.");
      }
    }
  };

  const handleDeleteComment = async (commentId) => {
    if (window.confirm("Are you sure you want to delete this comment?")) {
      try {
        await deleteLeadComment({
          leadPhone: lead.phone,
          commentId: commentId,
        }).unwrap();
        toast.success("Comment deleted successfully!");
        // Refetch the data from the server to ensure consistency
        refetch();
      } catch (error) {
        console.error("Failed to delete comment:", error);
        toast.error("Failed to delete comment.");
      }
    }
  };

  const cancelEdit = () => {
    setEditingComment(null);
    setEditCommentText("");
  };

  const handleAddApplication = (e) => {
    e.preventDefault();
    // Convert comma-separated qualification names into an array
    const qualifications = newApp.qualificationName
      .split(",")
      .map((q) => q.trim())
      .filter((q) => q.length > 0);

    const applicationToAdd = {
      ...newApp,
      qualificationName: Array.isArray(newApp.qualificationName)
        ? newApp.qualificationName
        : (newApp.qualificationName || "")
            .split(",")
            .map((q) => q.trim())
            .filter((q) => q.length > 0),
    };

    setApplications([...applications, applicationToAdd]);
    setNewApp({
      fullName: "",
      applicationId: "",
      price: "",
      qualificationName: "",
      applicationDate: "",
      status: "Pending",
      notes: "",
    });
    setShowAddAppModal(false);
  };

  const handleCreateApplication = async (formValues) => {
    try {
      const newApp = await createApplication(formValues).unwrap();
      setApplications((prev) => [newApp, ...prev]);
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error("Error creating application:", error);
      showErrorToast(error, "Unable to create application. Please try again.");
    }
  };

  // Handlers for adding a reminder
  const handleAddReminderChange = (e) => {
    const { name, value } = e.target;
    setNewReminder((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddReminder = (e) => {
    e.preventDefault();
    // For design purposes, we'll simply add the reminder to our list.
    setReminders([...reminders, newReminder]);
    setNewReminder({
      title: "",
      message: "",
      email: "",
      reminderTime: "",
    });
    setShowAddReminderModal(false);
  };

  const handleStatusChange = async (e) => {
    const newStatus = e.target.value;
    setLeadStatus(newStatus); // Optimistic update
    try {
      await updateLeadStatus({
        leadPhone: lead.phone,
        status: newStatus,
      }).unwrap();
      toast.success(`Lead status updated to ${newStatus}!`);
    } catch (error) {
      console.error("Failed to update lead status:", error);
      toast.error("There was an error updating the lead status.");
    }
  };

  // ─── ANALYTICS COMPUTATIONS ─────────────────────────
  // 1. Certificate Distribution
  const certificateCounts = {};
  lead.applications.forEach((app) => {
    app.qualificationName.forEach((qualification) => {
      const certName = qualification.qualificationName;
      certificateCounts[certName] = (certificateCounts[certName] || 0) + 1;
    });
  });

  const certificatesChartData = {
    labels: Object.keys(certificateCounts),
    datasets: [
      {
        label: "Certificate Count",
        data: Object.values(certificateCounts),
        backgroundColor: "#3b82f6",
      },
    ],
  };

  // 2. Application Status Summary
  const statusCounts = {};
  lead.applications.forEach((app) => {
    const status = app.applicationStatus;
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });

  const statusesChartData = {
    labels: Object.keys(statusCounts),
    datasets: [
      {
        label: "Application Status Count",
        data: Object.values(statusCounts),
        backgroundColor: "#f59e0b",
      },
    ],
  };

  const handleDateRangeChange = (e) => {
    setDateRange(e.target.value);
    // Add custom filtering if required.
  };

  // Function to handle navigation to application profile
  const handleApplicationNavigation = (applicationId) => {
    // Check if user is admin or agent based on current route
    const currentPath = window.location.pathname;
    const isAdminRoute = currentPath.includes('/admin/');

    if (isAdminRoute) {
      navigate(`/admin/application/profile/${applicationId}`);
    } else {
      navigate(`/agent/application/profile/${applicationId}`);
    }
  };

  return (
    <div className="py-6 space-y-6">
      {/* HEADER / LEAD OVERVIEW */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-50 overflow-hidden">
        {/* Header with back button */}
        <div className="border-b border-gray-100 px-6 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <a href="/admin/leads" className="text-gray-500 hover:text-gray-700 mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </a>
            <h1 className="text-xl font-semibold text-gray-900">Lead Profile</h1>
          </div>
          <div>
            <select
              value={leadStatus}
              onChange={handleStatusChange}
              className={`rounded-full px-3 py-1 text-xs font-medium focus:outline-none focus:ring-2 focus:ring-[#6E39CB] ${getStatusClass(leadStatus)}`}
              disabled={isUpdatingStatus}
            >
              <option value="FRESH">FRESH</option>
              <option value="HOT">HOT</option>
              <option value="COLD">COLD</option>
              <option value="WARM">WARM</option>
            </select>
          </div>
        </div>

        {/* Lead Info */}
        <div className="p-6">
          <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-6">
            {/* Left side: Lead Info */}
            <div className="flex-1">
              <div className="flex items-center mb-6">
                <div className="bg-[#F4F5F9] rounded-full h-12 w-12 flex items-center justify-center mr-3">
                  <span className="text-[#6E39CB] font-semibold text-lg">{lead.leadName.charAt(0).toUpperCase()}</span>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">{lead.leadName}</h2>
                  <p className="text-sm text-gray-500">{lead.companyName || "Individual Lead"}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-[#F4F5F9] rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="bg-[#6E39CB] bg-opacity-10 p-2 rounded-full mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500">Phone</p>
                      <p className="text-sm font-medium text-gray-900">{lead.phone}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-[#F4F5F9] rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="bg-[#6E39CB] bg-opacity-10 p-2 rounded-full mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500">Email</p>
                      <a href={`mailto:${lead.email}`} className="text-sm font-medium text-[#6E39CB] hover:underline">
                        {lead.email}
                      </a>
                    </div>
                  </div>
                </div>

                <div className="bg-[#F4F5F9] rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="bg-[#6E39CB] bg-opacity-10 p-2 rounded-full mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500">Company</p>
                      <p className="text-sm font-medium text-gray-900">{lead.companyName || "N/A"}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-[#F4F5F9] rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="bg-[#6E39CB] bg-opacity-10 p-2 rounded-full mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500">Address</p>
                      <p className="text-sm font-medium text-gray-900">{lead.address || "N/A"}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side: Stats & Actions */}
            <div className="md:w-64 flex flex-col items-center md:items-end">
              <div className="bg-white rounded-lg border border-gray-100 p-4 w-full mb-4 text-center">
                <p className="text-sm text-gray-500 mb-1">Total Applications</p>
                <p className="text-2xl font-semibold text-[#6E39CB]">{applications.length}</p>
              </div>

              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors w-full justify-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Create Application
              </button>
            </div>
          </div>
        </div>
      </div>

      <MultiStepCreateApplicationModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreate={handleCreateApplication}
        qualifications={qualificationsData}
        preSelectedLead={lead}
      />

      {/* TABS NAVIGATION */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-50 overflow-hidden">
        <div className="border-b border-gray-100 px-6 py-4">
          <div className="flex flex-wrap -mb-px space-x-4">
            <button
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                activeTab === "applications"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap`}
              onClick={() => setActiveTab("applications")}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Applications
            </button>
            <button
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                activeTab === "activity"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap`}
              onClick={() => setActiveTab("activity")}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Activity
            </button>
            <button
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                activeTab === "comments"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap`}
              onClick={() => setActiveTab("comments")}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
              Comments
            </button>
            <button
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                activeTab === "analysis"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap`}
              onClick={() => setActiveTab("analysis")}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Analysis
            </button>
            <button
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                activeTab === "owner"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap`}
              onClick={() => setActiveTab("owner")}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Lead Owner
            </button>
          </div>
        </div>

        {/* TAB CONTENTS */}
        <div className="p-6">
          {/* ─── APPLICATIONS TAB ───────────────────────── */}
          {activeTab === "applications" && (
            <div>
              {/* Filter & Sort */}
              <div className="flex flex-col md:flex-row items-center justify-between mb-4 gap-2">
                <div className="relative w-full md:w-1/2">
                  <input
                    type="text"
                    placeholder="Search by Name, Application ID, Status or Qualifications"
                    onChange={(e) => setAppFilter(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  />
                  <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <button
                  onClick={handleSortChange}
                  className="bg-[#6E39CB] text-white px-4 py-2 text-sm rounded-lg hover:bg-[#5E2CB8] transition-colors flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                  </svg>
                  Sort by Name ({appSortOrder === "asc" ? "A-Z" : "Z-A"})
                </button>
              </div>

              {/* Applications Table */}
              <div className="overflow-x-auto">
                <table className="min-w-full text-sm text-left border-separate border-spacing-0">
                  <thead>
                    <tr className="bg-[#F4F5F9]">
                      <th className="py-3 px-4 font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">Name</th>
                      <th className="py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Application ID</th>
                      <th className="py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Price</th>
                      <th className="py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Qualifications</th>
                      <th className="py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="py-3 px-4 font-medium text-gray-500 uppercase tracking-wider rounded-tr-lg">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredApplications.length === 0 ? (
                      <tr>
                        <td
                          colSpan={7}
                          className="py-8 px-4 text-center text-gray-500 bg-white border-b border-gray-100"
                        >
                          <div className="flex flex-col items-center justify-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <p className="text-gray-500">No applications found.</p>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      filteredApplications.map((app, idx) => {
                        const totalPrice = (app.qualificationName || []).reduce(
                          (sum, q) => sum + (q.price || 0),
                          0
                        );

                        return (
                          <tr
                            key={idx}
                            onClick={() => handleApplicationNavigation(app.applicationId)}
                            className="bg-white border-b border-gray-100 hover:bg-[#F4F5F9] transition-colors cursor-pointer"
                          >
                            <td className="py-3 px-4 font-medium text-gray-900">
                              {app.fullName}
                            </td>
                            <td className="py-3 px-4 text-gray-700">{app.applicationId}</td>
                            <td className="py-3 px-4 text-gray-700">
                              <span className="font-medium text-[#6E39CB]">${totalPrice.toFixed(2)}</span>
                            </td>
                            <td className="py-3 px-4 text-gray-700">
                              {(app.qualificationName || []).map((q, i) => (
                                <span key={i} className="inline-flex items-center px-2.5 py-0.5 rounded-full bg-[#F4F5F9] text-xs font-medium text-[#6E39CB] mr-1 mb-1">
                                  {q.qualificationName}
                                </span>
                              ))}
                            </td>
                            <td className="py-3 px-4 text-gray-700">
                              {new Date(app.createdAt).toLocaleString("en-US", {
                                year: "numeric",
                                month: "short",
                                day: "2-digit",
                              })}
                            </td>
                            <td className="py-3 px-4">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${app.applicationStatus === "Completed" ? "bg-green-100 text-green-800" : app.applicationStatus === "Pending" ? "bg-yellow-100 text-yellow-800" : "bg-gray-100 text-gray-800"}`}>
                                {app.applicationStatus}
                              </span>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* ─── ACTIVITY TAB ───────────────────────── */}
          {activeTab === "activity" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-lg font-semibold text-gray-900">Activity Log</h1>
              </div>

              <div className="relative w-full mb-6">
                <input
                  type="text"
                  placeholder="Search activities..."
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  value={activitySearchTerm}
                  onChange={(e) => setActivitySearchTerm(e.target.value)}
                />
                <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>

              {lead.activities.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 bg-[#F4F5F9] rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-gray-500">No recent activities.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {lead.activities.map((activity, index) => (
                    <div
                      key={index}
                      className="p-4 rounded-lg border border-gray-100 bg-white hover:bg-[#F4F5F9] transition-colors"
                    >
                      <div className="flex items-start">
                        <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{activity.content}</p>
                          <div className="flex items-center mt-2">
                            <p className="text-xs text-gray-500">
                              {new Date(activity.createdAt).toLocaleString("en-US", {
                                year: "numeric",
                                month: "short",
                                day: "2-digit",
                                hour: "2-digit",
                                minute: "2-digit",
                                hour12: true,
                              })}
                            </p>
                            <span className="mx-2 text-gray-300">•</span>
                            <p className="text-xs text-gray-500">By: {activity.createdBy}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* ─── COMMENTS TAB ───────────────────────── */}
          {activeTab === "comments" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-lg font-semibold text-gray-900">Comments</h1>
                <span className="bg-[#F4F5F9] text-[#6E39CB] text-xs font-medium px-2.5 py-1 rounded-full">
                  {comments ? comments.length : 0} comments
                </span>
              </div>

              <div className="relative w-full mb-6">
                <input
                  type="text"
                  placeholder="Search comments..."
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  value={commentSearchTerm}
                  onChange={(e) => setCommentSearchTerm(e.target.value)}
                />
                <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>

              <div className="mb-6">
                <h2 className="text-base font-semibold text-gray-900 mb-3">Add Comment</h2>
                <div className="space-y-3">
                  <textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="border border-gray-200 rounded-lg p-3 w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                    placeholder="Type your comment here..."
                    rows="3"
                    disabled={isAddingComment}
                  />
                  <div className="flex justify-end">
                    <button
                      onClick={handleAddComment}
                      disabled={isAddingComment}
                      className="px-4 py-2 rounded-lg flex items-center bg-[#6E39CB] text-white hover:bg-[#5E2CB8] transition-colors"
                    >
                      {isAddingComment ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Submitting...
                        </>
                      ) : (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                          </svg>
                          Submit Comment
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {filteredComments.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 bg-[#F4F5F9] rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                  <p className="text-gray-500">No comments yet.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredComments.map((comment, index) => (
                    <div key={comment.id || index} className="border border-gray-100 rounded-lg p-4 bg-white hover:bg-[#F4F5F9] transition-colors">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center">
                          <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-2">
                            <span className="text-[#6E39CB] font-medium">{(comment.createdBy || "Unknown").charAt(0).toUpperCase()}</span>
                          </div>
                          <div className="font-medium text-gray-900">{comment.createdBy || "Unknown"}</div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="text-xs text-gray-500">
                            {new Date(comment.createdAt).toLocaleString()}
                          </div>
                          {comment.id && (
                            <div className="flex items-center space-x-1">
                              <button
                                onClick={() => handleEditComment(comment)}
                                className="p-1 text-gray-400 hover:text-[#6E39CB] transition-colors"
                                title="Edit comment"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>
                              <button
                                onClick={() => handleDeleteComment(comment.id)}
                                className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                                title="Delete comment"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                      {editingComment === comment.id ? (
                        <div className="space-y-3 pl-10">
                          <textarea
                            value={editCommentText}
                            onChange={(e) => setEditCommentText(e.target.value)}
                            className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] text-sm"
                            rows="3"
                          />
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={cancelEdit}
                              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                            >
                              Cancel
                            </button>
                            <button
                              onClick={handleUpdateComment}
                              disabled={!editCommentText.trim()}
                              className="px-3 py-1 bg-[#6E39CB] text-white text-sm rounded hover:bg-[#5E2CB8] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                              Save
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="mt-1 text-gray-700 pl-10">{comment.content}</div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* ─── ANALYSIS TAB ───────────────────────── */}
          {activeTab === "analysis" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-lg font-semibold text-gray-900">Analytics Overview</h1>
                <div className="bg-[#F4F5F9] rounded-lg px-4 py-2">
                  <span className="text-sm font-medium text-gray-700">Total Applications: </span>
                  <span className="text-sm font-semibold text-[#6E39CB]">{lead.applications.length}</span>
                </div>
              </div>

              <div className="mb-8">
                <h3 className="text-base font-semibold text-gray-900 mb-4">Certificates Distribution</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white rounded-lg border border-gray-100 p-4 overflow-hidden">
                    <table className="min-w-full text-sm divide-y divide-gray-200">
                      <thead>
                        <tr>
                          <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Certificate</th>
                          <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {Object.keys(certificateCounts).length === 0 ? (
                          <tr>
                            <td colSpan="2" className="py-4 px-4 text-center text-gray-500">No certificates data available</td>
                          </tr>
                        ) : (
                          Object.keys(certificateCounts).map((cert, index) => (
                            <tr key={index} className="hover:bg-[#F4F5F9]">
                              <td className="py-3 px-4 text-gray-900">{cert}</td>
                              <td className="py-3 px-4">
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full bg-[#F4F5F9] text-xs font-medium text-[#6E39CB]">
                                  {certificateCounts[cert]}
                                </span>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>

                  <div className="bg-white rounded-lg border border-gray-100 p-4 h-64">
                    <Bar
                      data={{
                        labels: Object.keys(certificateCounts),
                        datasets: [
                          {
                            label: "Certificate Count",
                            data: Object.values(certificateCounts),
                            backgroundColor: "#6E39CB",
                            borderRadius: 6,
                          },
                        ],
                      }}
                      options={{
                        responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                      x: { display: true },
                      y: { display: true, beginAtZero: true },
                    },
                  }}
                />
                  </div>
                </div>
              </div>

              <div className="mb-8">
                <h3 className="text-base font-semibold text-gray-900 mb-4">Application Status Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white rounded-lg border border-gray-100 p-4 overflow-hidden">
                    <table className="min-w-full text-sm divide-y divide-gray-200">
                      <thead>
                        <tr>
                          <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {Object.entries(statusCounts).length === 0 ? (
                          <tr>
                            <td colSpan="2" className="py-4 px-4 text-center text-gray-500">No status data available</td>
                          </tr>
                        ) : (
                          Object.entries(statusCounts).map(([status, count], idx) => (
                            <tr key={idx} className="hover:bg-[#F4F5F9]">
                              <td className="py-3 px-4 text-gray-900">{status}</td>
                              <td className="py-3 px-4">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status === "Completed" ? "bg-green-100 text-green-800" : status === "Pending" ? "bg-yellow-100 text-yellow-800" : "bg-[#F4F5F9] text-[#6E39CB]"}`}>
                                  {count}
                                </span>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>

                  <div className="bg-white rounded-lg border border-gray-100 p-4 h-64">
                    <Bar
                      data={statusesChartData}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: {
                          x: { display: true },
                          y: { display: true, beginAtZero: true },
                        },
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* ─── LEAD OWNER TAB ───────────────────────── */}
          {activeTab === "owner" && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-lg font-semibold text-gray-900">Lead Owner</h1>
              </div>

              {assignedAgents && assignedAgents.length > 0 ? (
                <div className="space-y-6">
                  {assignedAgents.map((agent, index) => (
                    <div key={index} className="bg-white rounded-lg border border-gray-100 p-6">
                      <div className="flex items-center mb-6">
                        <div className="bg-[#F4F5F9] rounded-full h-12 w-12 flex items-center justify-center mr-3">
                          <span className="text-[#6E39CB] font-semibold text-lg">{agent.fullName.charAt(0).toUpperCase()}</span>
                        </div>
                        <div>
                          <h2 className="text-lg font-semibold text-gray-900">{agent.fullName}</h2>
                          <p className="text-sm text-gray-500">{agent.username}</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div className="bg-[#F4F5F9] rounded-lg p-4">
                          <div className="flex items-center">
                            <div className="bg-[#6E39CB] bg-opacity-10 p-2 rounded-full mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                              </svg>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-500">Email</p>
                              <a href={`mailto:${agent.username}`} className="text-sm font-medium text-[#6E39CB] hover:underline">
                                {agent.username}
                              </a>
                            </div>
                          </div>
                        </div>
                      </div>

                      <button
                        onClick={() => setShowChangeOwnerModal(true)}
                        className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Change Owner
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 bg-[#F4F5F9] rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <p className="text-gray-500 mb-4">No assigned agents.</p>
                  <button
                    onClick={() => setShowChangeOwnerModal(true)}
                    className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Assign Owner
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* ─── CHANGE OWNER MODAL ───────────────────────── */}
      {showChangeOwnerModal && (
        <ChangeOwnerModal
          isOpen={showChangeOwnerModal}
          onClose={() => setShowChangeOwnerModal(false)}
          onSave={handleChangeOwner}
          agents={allAgents}
        />
      )}

      {/* ─── ADD APPLICATION MODAL ───────────────────────── */}
      {showAddAppModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white w-full max-w-md p-6 rounded shadow-lg relative">
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
              onClick={() => setShowAddAppModal(false)}
            >
              <span className="sr-only">Close</span>
              &times;
            </button>
            <h2 className="text-lg font-semibold mb-4">Add Application</h2>
            <form onSubmit={handleAddApplication} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Full Name
                </label>
                <input
                  type="text"
                  name="fullName"
                  value={newApp.fullName}
                  onChange={handleAddAppChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Application ID
                </label>
                <input
                  type="text"
                  name="applicationId"
                  value={newApp.applicationId}
                  onChange={handleAddAppChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Price
                </label>
                <input
                  type="text"
                  name="price"
                  value={newApp.price}
                  onChange={handleAddAppChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Qualifications (comma separated)
                </label>
                <input
                  type="text"
                  name="qualificationName"
                  value={newApp.qualificationName}
                  onChange={handleAddAppChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Application Date
                </label>
                <input
                  type="date"
                  name="applicationDate"
                  value={newApp.applicationDate}
                  onChange={handleAddAppChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  name="status"
                  value={newApp.status}
                  onChange={handleAddAppChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                >
                  <option value="Pending">Pending</option>
                  <option value="Approved">Approved</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Notes
                </label>
                <textarea
                  name="notes"
                  value={newApp.notes}
                  onChange={handleAddAppChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                ></textarea>
              </div>
              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  className="text-sm px-4 py-2 rounded border border-gray-300 hover:bg-gray-50"
                  onClick={() => setShowAddAppModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="text-sm px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700"
                >
                  Add Application
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* ─── ADD REMINDER MODAL ───────────────────────── */}
      {showAddReminderModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white w-full max-w-md p-6 rounded shadow-lg relative">
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
              onClick={() => setShowAddReminderModal(false)}
            >
              <span className="sr-only">Close</span>
              &times;
            </button>
            <h2 className="text-lg font-semibold mb-4">Add Reminder</h2>
            <form onSubmit={handleAddReminder} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Title
                </label>
                <input
                  type="text"
                  name="title"
                  value={newReminder.title}
                  onChange={handleAddReminderChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Message
                </label>
                <textarea
                  name="message"
                  value={newReminder.message}
                  onChange={handleAddReminderChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                ></textarea>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Send To (Email)
                </label>
                <input
                  type="email"
                  name="email"
                  value={newReminder.email}
                  onChange={handleAddReminderChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Reminder Time
                </label>
                <input
                  type="datetime-local"
                  name="reminderTime"
                  value={newReminder.reminderTime}
                  onChange={handleAddReminderChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md p-2 text-sm"
                />
              </div>
              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  className="text-sm px-4 py-2 rounded border border-gray-300 hover:bg-gray-50"
                  onClick={() => setShowAddReminderModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="text-sm px-4 py-2 rounded bg-green-600 text-white hover:bg-green-700"
                >
                  Add Reminder
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* ─── CHANGE OWNER MODAL ───────────────────────── */}
      {showChangeOwnerModal && (
        <ChangeOwnerModal
          isOpen={showChangeOwnerModal}
          onClose={() => setShowChangeOwnerModal(false)}
          onSave={handleChangeOwner}
          agents={allAgents}
        />
      )}
    </div>
  );
};

export default AgentLeadProfile;
