import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes, faSpinner, faCalendarAlt } from "@fortawesome/free-solid-svg-icons";
import {
  useUpdateDocumentReceivedDateMutation,
  useUpdateSoftCopyReceivedDateMutation,
  useUpdateSoftCopyReleasedDateMutation,
  useUpdateHardCopyReceivedDateMutation,
  useUpdateHardCopyMailedDateMutation,
  useUpdateLodgedToRTOMutation
} from "../../../../../services/CompanyAPIService";

/**
 * DateUpdateModal component for updating dates related to file status
 * @param {boolean} isOpen - Whether the modal is open
 * @param {function} onClose - Function to close the modal
 * @param {object} fileStatus - File status data
 * @param {string} selectedStatus - The newly selected status
 * @param {function} showToast - Function to show toast notifications
 * @param {function} refetch - Function to refetch data
 */
const DateUpdateModal = ({ isOpen, onClose, fileStatus, selectedStatus, showToast, refetch }) => {
  // State for form data
  const [formData, setFormData] = useState({
    documentReceivedDate: "",
    lodgedDate: "",
    softCopyReceivedDate: "",
    softCopyReleasedDate: "",
    hardCopyReceivedDate: "",
    hardCopyMailedDate: ""
  });

  // API mutations
  const [updateDocumentReceivedDate, { isLoading: isUpdatingDocumentDate }] = useUpdateDocumentReceivedDateMutation();
  const [updateLodgedToRTO, { isLoading: isUpdatingLodgedStatus }] = useUpdateLodgedToRTOMutation();
  const [updateSoftCopyReceivedDate, { isLoading: isUpdatingSoftCopyReceivedDate }] = useUpdateSoftCopyReceivedDateMutation();
  const [updateSoftCopyReleasedDate, { isLoading: isUpdatingSoftCopyReleasedDate }] = useUpdateSoftCopyReleasedDateMutation();
  const [updateHardCopyReceivedDate, { isLoading: isUpdatingHardCopyReceivedDate }] = useUpdateHardCopyReceivedDateMutation();
  const [updateHardCopyMailedDate, { isLoading: isUpdatingHardCopyMailedDate }] = useUpdateHardCopyMailedDateMutation();

  // Loading state
  const isLoading =
    isUpdatingDocumentDate ||
    isUpdatingLodgedStatus ||
    isUpdatingSoftCopyReceivedDate ||
    isUpdatingSoftCopyReleasedDate ||
    isUpdatingHardCopyReceivedDate ||
    isUpdatingHardCopyMailedDate;

  // Initialize form with existing data
  useEffect(() => {
    if (fileStatus) {
      const today = new Date().toISOString().split('T')[0];

      setFormData({
        documentReceivedDate: fileStatus.documentReceivedDate ? new Date(fileStatus.documentReceivedDate).toISOString().split('T')[0] : today,
        lodgedDate: fileStatus.lodgedDate ? new Date(fileStatus.lodgedDate).toISOString().split('T')[0] : today,
        softCopyReceivedDate: fileStatus.softCopyReceivedDate ? new Date(fileStatus.softCopyReceivedDate).toISOString().split('T')[0] : today,
        softCopyReleasedDate: fileStatus.softCopyReleasedDate ? new Date(fileStatus.softCopyReleasedDate).toISOString().split('T')[0] : today,
        hardCopyReceivedDate: fileStatus.hardCopyReceivedDate ? new Date(fileStatus.hardCopyReceivedDate).toISOString().split('T')[0] : today,
        hardCopyMailedDate: fileStatus.hardCopyMailedDate ? new Date(fileStatus.hardCopyMailedDate).toISOString().split('T')[0] : today
      });
    }
  }, [fileStatus, isOpen]);

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      let updatePromises = [];

      // Format date to include time (required by backend)
      const formatDateForBackend = (dateString) => {
        if (!dateString) return null;
        return `${dateString}T00:00:00`; // Add time component for LocalDateTime parsing
      };

      // Add appropriate update promises based on selected status
      if (selectedStatus === "DOCUMENTS_RECEIVED" && formData.documentReceivedDate) {
        updatePromises.push(
          updateDocumentReceivedDate({
            applicationId: fileStatus.application?.applicationId,
            qualificationId: fileStatus.qualificationCode,
            documentReceivedDate: formatDateForBackend(formData.documentReceivedDate)
          }).unwrap()
        );
      }

      if (selectedStatus === "LODGED_AND_PROCESSING" && formData.lodgedDate) {
        updatePromises.push(
          updateLodgedToRTO({
            applicationId: fileStatus.application?.applicationId,
            qualificationId: fileStatus.qualificationCode,
            lodgedToRTO: true,
            lodgedDate: formatDateForBackend(formData.lodgedDate)
          }).unwrap()
        );
      }

      if (selectedStatus === "SOFT_COPY_RECEIVED" && formData.softCopyReceivedDate) {
        updatePromises.push(
          updateSoftCopyReceivedDate({
            applicationId: fileStatus.application?.applicationId,
            qualificationId: fileStatus.qualificationCode,
            softCopyReceivedDate: formatDateForBackend(formData.softCopyReceivedDate)
          }).unwrap()
        );
      }

      if (selectedStatus === "SOFT_COPY_RELEASED" && formData.softCopyReleasedDate) {
        updatePromises.push(
          updateSoftCopyReleasedDate({
            applicationId: fileStatus.application?.applicationId,
            qualificationId: fileStatus.qualificationCode,
            softCopyReleasedDate: formatDateForBackend(formData.softCopyReleasedDate)
          }).unwrap()
        );
      }

      if (selectedStatus === "HARD_COPY_RECEIVED" && formData.hardCopyReceivedDate) {
        updatePromises.push(
          updateHardCopyReceivedDate({
            applicationId: fileStatus.application?.applicationId,
            qualificationId: fileStatus.qualificationCode,
            hardCopyReceivedDate: formatDateForBackend(formData.hardCopyReceivedDate)
          }).unwrap()
        );
      }

      if (selectedStatus === "HARD_COPY_MAILED_AND_CLOSED" && formData.hardCopyMailedDate) {
        updatePromises.push(
          updateHardCopyMailedDate({
            applicationId: fileStatus.application?.applicationId,
            qualificationId: fileStatus.qualificationCode,
            hardCopyMailedDate: formatDateForBackend(formData.hardCopyMailedDate)
          }).unwrap()
        );
      }

      // Execute all update promises
      await Promise.all(updatePromises);

      showToast("Dates updated successfully");
      refetch();
      onClose();
    } catch (error) {
      showToast("Failed to update dates", "error");
      console.error("Error updating dates:", error);
    }
  };

  // Get modal title based on selected status
  const getModalTitle = () => {
    switch (selectedStatus) {
      case "DOCUMENTS_RECEIVED":
        return "Update Document Received Date";
      case "LODGED_AND_PROCESSING":
        return "Update Lodged Date";
      case "SOFT_COPY_RECEIVED":
        return "Update Soft Copy Received Date";
      case "SOFT_COPY_RELEASED":
        return "Update Soft Copy Released Date";
      case "HARD_COPY_RECEIVED":
        return "Update Hard Copy Received Date";
      case "HARD_COPY_MAILED_AND_CLOSED":
        return "Update Hard Copy Mailed Date";
      default:
        return "Update Dates";
    }
  };

  // Render appropriate form fields based on selected status
  const renderFormFields = () => {
    switch (selectedStatus) {
      case "DOCUMENTS_RECEIVED":
        return (
          <div className="mb-4">
            <label htmlFor="documentReceivedDate" className="block text-sm font-medium text-gray-700 mb-1">
              Document Received Date
            </label>
            <input
              type="date"
              id="documentReceivedDate"
              name="documentReceivedDate"
              value={formData.documentReceivedDate}
              onChange={handleChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
            />
          </div>
        );
      case "LODGED_AND_PROCESSING":
        return (
          <div className="mb-4">
            <label htmlFor="lodgedDate" className="block text-sm font-medium text-gray-700 mb-1">
              Lodged Date
            </label>
            <input
              type="date"
              id="lodgedDate"
              name="lodgedDate"
              value={formData.lodgedDate}
              onChange={handleChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
            />
          </div>
        );
      case "SOFT_COPY_RECEIVED":
        return (
          <div className="mb-4">
            <label htmlFor="softCopyReceivedDate" className="block text-sm font-medium text-gray-700 mb-1">
              Soft Copy Received Date
            </label>
            <input
              type="date"
              id="softCopyReceivedDate"
              name="softCopyReceivedDate"
              value={formData.softCopyReceivedDate}
              onChange={handleChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
            />
          </div>
        );
      case "SOFT_COPY_RELEASED":
        return (
          <div className="mb-4">
            <label htmlFor="softCopyReleasedDate" className="block text-sm font-medium text-gray-700 mb-1">
              Soft Copy Released Date
            </label>
            <input
              type="date"
              id="softCopyReleasedDate"
              name="softCopyReleasedDate"
              value={formData.softCopyReleasedDate}
              onChange={handleChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
            />
          </div>
        );
      case "HARD_COPY_RECEIVED":
        return (
          <div className="mb-4">
            <label htmlFor="hardCopyReceivedDate" className="block text-sm font-medium text-gray-700 mb-1">
              Hard Copy Received Date
            </label>
            <input
              type="date"
              id="hardCopyReceivedDate"
              name="hardCopyReceivedDate"
              value={formData.hardCopyReceivedDate}
              onChange={handleChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
            />
          </div>
        );
      case "HARD_COPY_MAILED_AND_CLOSED":
        return (
          <div className="mb-4">
            <label htmlFor="hardCopyMailedDate" className="block text-sm font-medium text-gray-700 mb-1">
              Hard Copy Mailed Date
            </label>
            <input
              type="date"
              id="hardCopyMailedDate"
              name="hardCopyMailedDate"
              value={formData.hardCopyMailedDate}
              onChange={handleChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
            />
          </div>
        );
      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Modal Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            {getModalTitle()}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        {/* Modal Body */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            <div className="flex items-center mb-4 text-gray-600">
              <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
              <p className="text-sm">
                Please update the date for the selected status. This will help track the progress of the file.
              </p>
            </div>

            {renderFormFields()}
          </div>

          {/* Modal Footer */}
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-[#5E2CB8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
            >
              {isLoading ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
                  Updating...
                </>
              ) : (
                "Update Date"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DateUpdateModal;
