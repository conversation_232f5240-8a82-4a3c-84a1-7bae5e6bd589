package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.*;
import com.skillsync.applyr.core.models.enums.LeadsStatus;
import com.skillsync.applyr.core.models.enums.PaidStatus;
import com.skillsync.applyr.core.models.enums.Status;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.core.utills.UserUtils;
import com.skillsync.applyr.modules.company.models.*;
import com.skillsync.applyr.modules.company.repositories.*;
import com.skillsync.applyr.modules.sales.models.ApplicationDTO;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.skillsync.applyr.modules.company.services.EmployeeProfileServiceHelper.fromAgentToProfileDTO;
import static com.skillsync.applyr.modules.company.services.EmployeeProfileServiceHelper.fromCompanyToProfileDTO;

@Service
public class ApplicationService {

    private final ApplicationRepository applicationRepository;
    private final QualificationRepository qualificationRepository;
    private final SoldQualificationsRepository soldQualificationsRepository;
    private final LeadsService leadsService;
    private final LeadsRepository leadsRepository;
    private final CompanyRepository companyRepository;
    private final SalesAgentRepository salesAgentRepository;
    private final ApplicationCommentsRepository applicationCommentsRepository;

    public ApplicationService(ApplicationRepository applicationRepository,
                              QualificationRepository qualificationRepository,
                              SoldQualificationsRepository soldQualificationsRepository, LeadsService leadsService, LeadsRepository leadsRepository, CompanyRepository companyRepository, SalesAgentRepository salesAgentRepository, ApplicationCommentsRepository applicationCommentsRepository) {
        this.applicationRepository = applicationRepository;
        this.qualificationRepository = qualificationRepository;
        this.soldQualificationsRepository = soldQualificationsRepository;
        this.leadsService = leadsService;
        this.leadsRepository = leadsRepository;
        this.companyRepository = companyRepository;
        this.salesAgentRepository = salesAgentRepository;
        this.applicationCommentsRepository = applicationCommentsRepository;
    }

    public ApplicationResponseDTO addApplications(ApplicationRequestDTO applicationDTO) {
        String username = UserUtils.getUsernameFromToken();

        // If there is no lead phone, assume applicant phone becomes the lead phone.
        if (applicationDTO.getLeadPhone() == null || applicationDTO.getLeadPhone().isEmpty()) {
            applicationDTO.setLeadPhone(applicationDTO.getApplicantPhone());
            LeadDTO leadDTO = new LeadDTO();
            leadDTO.setEmail(applicationDTO.getApplicantEmail());
            leadDTO.setPhone(applicationDTO.getApplicantPhone());
            leadDTO.setAddress(applicationDTO.getApplicantAddress());
            leadDTO.setCompanyName("N/A"); // TODO:: individual
            leadDTO.setStatus(LeadsStatus.HOT);
            leadDTO.setLeadName(applicationDTO.getApplicantName());
            leadDTO.setAssignedAgents(List.of(new SalesAgentDTO("Agent", UserUtils.getUsernameFromToken())));

            try {
                leadsService.uploadSingleLead(leadDTO);
            } catch (AppRTException e) {
                throw e;
            }
        }


        Application raw = new Application(applicationDTO);
        raw.setAgentUsername(username);
        Application saved = applicationRepository.save(raw);

        for (SoldQualificationDTO qDTO : applicationDTO.getSoldQualifications()) {
            var qualification = qualificationRepository.findByQualificationId(qDTO.getQualificationId());
            SoldQualifications soldQualifications = new SoldQualifications(qualification, qDTO.getPrice(), saved);
            saved.getSoldQualificationsList().add(soldQualifications);
        }
        saved.generateTotalPrice();
        if (applicationDTO.getCreatedOn() != null) {
            saved.setCreatedDate(applicationDTO.getCreatedOn());
        }
        saved = applicationRepository.save(saved);

        List<ApplicationResponseDTO> dtos = fromApplicationsToApplicationDTOs(List.of(saved));
        return dtos.get(0);
    }

    public List<ApplicationResponseDTO> getAllApplicationOfAgent() {
        String username = UserUtils.getUsernameFromToken();
        List<Application> applications = applicationRepository.findAllByAgentUsername(username);
        return fromApplicationsToApplicationDTOs(applications);
    }

    public List<ApplicationResponseDTO> getAllApplication() {
        List<Application> applications = applicationRepository.findAll();
        return fromApplicationsToApplicationDTOs(applications);
    }

    public SuccessResponse updateApplicationStatus(String applicationId, Status newStatus) {
        Application application = applicationRepository.findByApplicationId(applicationId)
                .orElseThrow(() -> new AppRTException("Unable to find application", HttpStatus.INTERNAL_SERVER_ERROR));
        application.setStatus(newStatus);
        applicationRepository.save(application);
        return new SuccessResponse("Application updated successfully");
    }

    public SuccessResponse updateApplicantInformation(String applicationId, com.skillsync.applyr.modules.company.models.ApplicantUpdateDTO updateDTO) {
        Optional<Application> application = applicationRepository.findByApplicationId(applicationId);
        if (application.isPresent()) {
            application.get().setApplicantName(updateDTO.getApplicantName());
            application.get().setApplicantEmail(updateDTO.getApplicantEmail());
            application.get().setApplicantPhone(updateDTO.getApplicantPhone());
            application.get().setApplicantAddress(updateDTO.getApplicantAddress());
            applicationRepository.save(application.get());
            return new SuccessResponse("Successfully updated Applicant Information");
        }
        throw new AppRTException("Failed to update applicant information", HttpStatus.NOT_FOUND);
    }

    public SuccessResponse removeSoldQualification(String applicationId, String qualificationId) {
        Optional<SoldQualifications> soldQualifications = soldQualificationsRepository.findByApplicationApplicationIdAndQualificationQualificationId(applicationId, qualificationId);
        soldQualifications.ifPresent(soldQualificationsRepository::delete);
        Optional<Application> application = applicationRepository.findByApplicationId(applicationId);
        if (application.isPresent()) {
            application.get().generateTotalPrice();
            applicationRepository.save(application.get());
        }
        return new SuccessResponse("Successfully removed qualification from application");
    }

    public SuccessResponse addSoldQualification(String applicationId, SoldQualificationDTO soldQualificationDTO) {
        Optional<Application> application = applicationRepository.findByApplicationId(applicationId);
        if (application.isPresent()) {
            SoldQualifications soldQualifications = new SoldQualifications();
            soldQualifications.setApplication(application.get());
            soldQualifications.setSoldPrice(soldQualificationDTO.getPrice());
            var qualification = qualificationRepository.findByQualificationId(soldQualificationDTO.getQualificationId());
            soldQualifications.setQualification(qualification);
            soldQualifications = soldQualificationsRepository.save(soldQualifications);
            application.get().getSoldQualificationsList().add(soldQualifications);
            application.get().generateTotalPrice();
            applicationRepository.save(application.get());
            return new SuccessResponse("Added Qualification to Application");
        }
        throw new AppRTException("Unable to find application", HttpStatus.NOT_FOUND);
    }

    public SuccessResponse updateApplicationPaymentStatus(String applicationId, PaidStatus newStatus) {
        Application application = applicationRepository.findByApplicationId(applicationId)
                .orElseThrow(() -> new AppRTException("Unable to find application", HttpStatus.INTERNAL_SERVER_ERROR));
        application.setPaidStatus(newStatus);
        if (newStatus == PaidStatus.FULLY_PAID) {
            application.setPaidAmount(application.getPrice());
        }
        applicationRepository.save(application);
        return new SuccessResponse("Application updated successfully");
    }

    public SuccessResponse updateApplicationCreatedDate(String applicationId, LocalDateTime newCreatedDate) {
        Application application = applicationRepository.findByApplicationId(applicationId)
                .orElseThrow(() -> new AppRTException("Unable to find application", HttpStatus.INTERNAL_SERVER_ERROR));
        application.setCreatedDate(newCreatedDate);
        applicationRepository.save(application);
        return new SuccessResponse("Application created date updated successfully");
    }

    public SuccessResponse updateApplicationPaymentAmount(String applicationId, double paidAmount) {
        Optional<Application> application = applicationRepository.findByApplicationId(applicationId);
        if (application.isPresent()) {
            application.get().setPaidStatus(PaidStatus.PARTIALLY_PAID);
            application.get().setPaidAmount(paidAmount);
            applicationRepository.save(application.get());
            return new SuccessResponse("Application updated successfully");
        }
        throw new AppRTException("Unable to update application payment amount", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ApplicationResponseDTO getSingleApplications(String applicationId) {
        Optional<Application> application = applicationRepository.findByApplicationId(applicationId);
        if (application.isPresent()) {
            return fromApplicationsToApplicationDTOs(List.of(application.get())).get(0);
        }
        throw new AppRTException("Application does not exist in our server", HttpStatus.NOT_FOUND);
    }

    public SuccessResponse deleteApplication(String applicationId) {
        try {
            Optional<Application> applicationOptional = applicationRepository.findByApplicationId(applicationId);
            if (applicationOptional.isEmpty()) {
                throw new AppRTException("Application not found with ID: " + applicationId, HttpStatus.NOT_FOUND);
            }

            Application application = applicationOptional.get();

            // Delete the application (this will cascade delete sold qualifications due to orphanRemoval = true)
            applicationRepository.delete(application);
            return new SuccessResponse("Application deleted successfully");
        } catch (AppRTException e) {
            throw e;
        } catch (Exception e) {
            throw new AppRTException("Unable to delete application: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public List<ApplicationResponseDTO> fromApplicationsToApplicationDTOs(List<Application> applications) {
        List<ApplicationResponseDTO> applicationResponseDTOs = new ArrayList<>();
        for (Application application : applications) {

            ApplicationResponseDTO applicationResponseDTO = new ApplicationResponseDTO();
            applicationResponseDTO.setApplicationId(application.getApplicationId());
            applicationResponseDTO.setApplicantName(application.getApplicantName());
            applicationResponseDTO.setApplicantEmail(application.getApplicantEmail());
            applicationResponseDTO.setApplicantPhone(application.getApplicantPhone());
            applicationResponseDTO.setApplicantAddress(application.getApplicantAddress());

            applicationResponseDTO.setTotalPrice(application.getPrice());

            Optional<Lead> lead = leadsRepository.getLeadByPhone(application.getLeadPhone());

            LeadResponseDTO leadResponseDTO = new LeadResponseDTO();

            if (lead.isPresent()) {
                leadResponseDTO.setCompanyName(lead.get().getCompanyName());
                leadResponseDTO.setLeadName(lead.get().getLeadName());
                leadResponseDTO.setAddress(lead.get().getAddress());
                leadResponseDTO.setEmail(lead.get().getEmail());
                leadResponseDTO.setPhone(lead.get().getPhone());
            }

            applicationResponseDTO.setLead(leadResponseDTO);


            List<SoldQualificationDTO> soldQualifications = new ArrayList<>();
            for (SoldQualifications raw : application.getSoldQualificationsList()) {
                SoldQualificationDTO soldQualificationDTO = new SoldQualificationDTO();
                soldQualificationDTO.setQualificationId(raw.getQualification().getQualificationId());
                soldQualificationDTO.setQualificationName(raw.getQualification().getQualificationName());
                soldQualificationDTO.setPrice(raw.getSoldPrice());


                soldQualifications.add(soldQualificationDTO);
            }

            applicationResponseDTO.setSoldQualifications(soldQualifications);
            applicationResponseDTO.setStatus(application.getStatus());

            // Add comments
            List<ApplicationCommentDTO> commentDTOs = new ArrayList<>();
            for (ApplicationComments comment : application.getComments()) {
                commentDTOs.add(new ApplicationCommentDTO(comment));
            }
            applicationResponseDTO.setComments(commentDTOs);

            applicationResponseDTO.setQuoteRefNumber(application.getQuoteRefNumber());
            applicationResponseDTO.setInvoiceRefNumber(application.getInvoiceRefNumber());

            ProfileDTO profile = new ProfileDTO();
            Optional<SalesAgent> agent = salesAgentRepository.getSalesAgentByUserUsername(application.getCreatedBy());
            if (agent.isPresent()) {
                profile = fromAgentToProfileDTO(agent.get());
            } else {
                Optional<Company> company = companyRepository.getCompanyByUserUsername(application.getCreatedBy());
                if (company.isPresent()) {
                    profile = fromCompanyToProfileDTO(company.get());
                } else {
                    profile = new ProfileDTO();
                    profile.setFullName("SYSTEM ADMIN");
                    profile.setUsername("system");
                    profile.setPhone("N/A");
                    profile.setEmail("N/A");
                    profile.setAddress("N/A");

                }

            }
            applicationResponseDTO.setCreatedBy(profile);
            applicationResponseDTO.setCreatedAt(application.getCreatedDate().toString());
            applicationResponseDTO.setPaidStatus(application.getPaidStatus());
            applicationResponseDTO.setPaidAmount(application.getPaidAmount());
            applicationResponseDTOs.add(applicationResponseDTO);
        }

        return applicationResponseDTOs;
    }

    // Application Comments Methods
    public ApplicationCommentDTO addApplicationComment(ApplicationCommentRequestDTO commentRequestDTO) {
        Optional<Application> application = applicationRepository.findByApplicationId(commentRequestDTO.getApplicationId());
        if (application.isPresent()) {
            ApplicationComments comment = new ApplicationComments();
            comment.setApplication(application.get());
            comment.setContent(commentRequestDTO.getComment());

            application.get().addComment(comment);
            applicationRepository.save(application.get());

            return new ApplicationCommentDTO(comment);
        }
        throw new AppRTException("Unable to add application comment", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public List<ApplicationCommentDTO> getApplicationComments(String applicationId) {
        List<ApplicationComments> comments = applicationCommentsRepository.findByApplicationApplicationIdOrderByCreatedDateDesc(applicationId);
        List<ApplicationCommentDTO> commentDTOs = new ArrayList<>();
        for (ApplicationComments comment : comments) {
            commentDTOs.add(new ApplicationCommentDTO(comment));
        }
        return commentDTOs;
    }

    public ApplicationCommentDTO updateApplicationComment(Long commentId, String applicationId, String newContent) {
        Optional<ApplicationComments> comment = applicationCommentsRepository.findByIdAndApplicationApplicationId(commentId, applicationId);
        if (comment.isPresent()) {
            comment.get().setContent(newContent);
            ApplicationComments updatedComment = applicationCommentsRepository.save(comment.get());
            return new ApplicationCommentDTO(updatedComment);
        }
        throw new AppRTException("Unable to update application comment", HttpStatus.NOT_FOUND);
    }

    public SuccessResponse deleteApplicationComment(Long commentId, String applicationId) {
        Optional<ApplicationComments> comment = applicationCommentsRepository.findByIdAndApplicationApplicationId(commentId, applicationId);
        if (comment.isPresent()) {
            applicationCommentsRepository.delete(comment.get());
            return new SuccessResponse("Comment deleted successfully");
        }
        throw new AppRTException("Unable to delete application comment", HttpStatus.NOT_FOUND);
    }
}
