package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class AgentDashboardDTO {
    private double weeklyTarget;
    private double weeklyRevenue;
    private int freshLeads;
    private int hotLeads;

    private int weekOneApplicationCount;
    private int weekTwoApplicationCount;
    private int weekThreeApplicationCount;
    private int weekFourApplicationCount;


}
