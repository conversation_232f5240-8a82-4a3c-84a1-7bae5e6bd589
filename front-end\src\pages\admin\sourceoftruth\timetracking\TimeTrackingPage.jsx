import React, { useContext, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faClock,
  faUserClock,
  faCalendarAlt,
  faSearch,
  faFilter,
  faChartLine,
  faEye,
  faArrowUp,
  faArrowDown,
  faSignInAlt,
  faSignOutAlt,
  faHourglass,
  faSpinner,
  faExclamationTriangle
} from "@fortawesome/free-solid-svg-icons";
import { useGetTodayTimeTrackerQuery, useGetTrackersByMonthYearQuery } from "../../../../services/CompanyAPIService";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Bar, Line } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const TimeTrackingPage = () => {
  // State for filtering and sorting
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRole, setSelectedRole] = useState("All");
  const [sortConfig, setSortConfig] = useState({ key: 'totalSeconds', direction: 'desc' });
  const [dateFilter, setDateFilter] = useState(new Date());
  const [viewMode, setViewMode] = useState("table"); // "table" or "card"
  const [showModal, setShowModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Fetch time tracking data
  const { data: timeTrackingData = [], isLoading, isError, error } = useGetTodayTimeTrackerQuery();

  // Filter data based on search query and role
  const filteredData = timeTrackingData.filter(tracker => {
    const matchesSearch =
      tracker.profile?.fullName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tracker.profile?.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tracker.profile?.email?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesRole =
      selectedRole === "All" ||
      tracker.profile?.role === selectedRole;

    return matchesSearch && matchesRole;
  });

  // Sort data based on sort configuration
  const sortedData = [...filteredData].sort((a, b) => {
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === 'asc' ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });

  // Handle sort request
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Format time function
  const formatTime = (seconds) => {
    if (!seconds) return "0h 0m";
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    return `${hrs}h ${mins}m`;
  };

  // Calculate stats
  const calculateStats = () => {
    if (!timeTrackingData || timeTrackingData.length === 0) {
      return {
        totalUsers: 0,
        activeUsers: 0,
        totalHours: 0,
        averageHours: 0
      };
    }

    const activeUsers = timeTrackingData.filter(tracker => tracker.totalSeconds > 0);
    const totalSeconds = timeTrackingData.reduce((total, tracker) => total + (tracker.totalSeconds || 0), 0);
    const totalHours = totalSeconds / 3600;
    const averageHours = activeUsers.length > 0 ? totalHours / activeUsers.length : 0;

    return {
      totalUsers: timeTrackingData.length,
      activeUsers: activeUsers.length,
      totalHours,
      averageHours
    };
  };

  const stats = calculateStats();

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Time Tracking</h1>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 mb-6 flex justify-center items-center">
          <div className="text-center">
            <FontAwesomeIcon icon={faSpinner} spin className="h-8 w-8 text-[#6E39CB] mb-4" />
            <p className="text-gray-600">Loading time tracking data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Time Tracking</h1>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="text-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="h-8 w-8 text-red-500 mb-4" />
            <p className="text-red-500 font-medium">Error loading time tracking data</p>
            <p className="text-gray-600 mt-2">{error?.message || "Please try again later"}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Time Tracking</h1>
        <div className="flex items-center space-x-2">
          <div className="bg-[#F4F5F9] px-4 py-2 rounded-md text-sm font-medium flex items-center">
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-[#6E39CB]" />
            <span>{dateFilter.toLocaleDateString()}</span>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">Total Users</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faUserClock} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">Active Users</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faUserClock} className="h-5 w-5 text-green-600" />
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.activeUsers}</p>
          <p className="text-xs text-gray-500 mt-1">
            {stats.totalUsers > 0
              ? Math.round((stats.activeUsers / stats.totalUsers) * 100)
              : 0}% of total users
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">Total Hours</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faClock} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.totalHours.toFixed(1)}h</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">Average Hours</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faClock} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.averageHours.toFixed(1)}h</p>
          <p className="text-xs text-gray-500 mt-1">Per active user</p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6 border border-gray-100">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex flex-col sm:flex-row gap-3 flex-grow">
            {/* Search */}
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Search by name or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
              <FontAwesomeIcon
                icon={faSearch}
                className="absolute left-3 top-3 text-gray-400"
              />
            </div>

            {/* Role Filter */}
            <div className="min-w-[150px]">
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              >
                <option value="All">All Roles</option>
                <option value="ROLE_ADMIN">Admin</option>
                <option value="ROLE_SALES">Sales</option>
                <option value="ROLE_OPERATIONS">Operations</option>
              </select>
            </div>

            {/* Date Picker */}
            <div className="min-w-[150px]">
              <DatePicker
                selected={dateFilter}
                onChange={(date) => setDateFilter(date)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                dateFormat="MMMM d, yyyy"
              />
            </div>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode("table")}
              className={`px-3 py-1.5 rounded-md ${
                viewMode === "table"
                  ? "bg-[#6E39CB] text-white"
                  : "bg-gray-100 text-gray-600"
              }`}
            >
              Table View
            </button>
            <button
              onClick={() => setViewMode("card")}
              className={`px-3 py-1.5 rounded-md ${
                viewMode === "card"
                  ? "bg-[#6E39CB] text-white"
                  : "bg-gray-100 text-gray-600"
              }`}
            >
              Card View
            </button>
          </div>
        </div>
      </div>

      {/* Time Tracking Table */}
      {viewMode === "table" ? (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    User
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => requestSort('firstLogin')}
                  >
                    First Login
                    {sortConfig.key === 'firstLogin' && (
                      <span className="ml-1">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => requestSort('lastTime')}
                  >
                    Last Activity
                    {sortConfig.key === 'lastTime' && (
                      <span className="ml-1">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => requestSort('totalSeconds')}
                  >
                    Total Time
                    {sortConfig.key === 'totalSeconds' && (
                      <span className="ml-1">
                        {sortConfig.direction === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedData.length > 0 ? (
                  sortedData.map((tracker, index) => {
                    const isActive = tracker.totalSeconds > 0;
                    const firstLogin = tracker.firstLogin ? new Date(tracker.firstLogin).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'N/A';
                    const lastActivity = tracker.lastTime ? new Date(tracker.lastTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'N/A';

                    return (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 bg-[#F4F5F9] rounded-full flex items-center justify-center">
                              <span className="text-[#6E39CB] font-medium">
                                {tracker.profile?.fullName?.charAt(0) || "U"}
                              </span>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {tracker.profile?.fullName || "Unknown User"}
                              </div>
                              <div className="text-sm text-gray-500">
                                {tracker.profile?.email || ""}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {firstLogin}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {lastActivity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {formatTime(tracker.totalSeconds)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              isActive
                                ? "bg-green-100 text-green-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {isActive ? "Active" : "Inactive"}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => {
                              setSelectedUser(tracker);
                              setShowModal(true);
                            }}
                            className="text-[#6E39CB] hover:text-[#5E2CB8]"
                          >
                            View Details
                          </button>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                      No time tracking data found matching your filters.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {sortedData.length > 0 ? (
            sortedData.map((tracker, index) => {
              const isActive = tracker.totalSeconds > 0;
              const firstLogin = tracker.firstLogin ? new Date(tracker.firstLogin).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'N/A';
              const lastActivity = tracker.lastTime ? new Date(tracker.lastTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'N/A';

              return (
                <div key={index} className="bg-white rounded-lg shadow-sm p-5 border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <div className="h-10 w-10 bg-[#F4F5F9] rounded-full flex items-center justify-center">
                        <span className="text-[#6E39CB] font-medium">
                          {tracker.profile?.fullName?.charAt(0) || "U"}
                        </span>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          {tracker.profile?.fullName || "Unknown User"}
                        </p>
                        <p className="text-xs text-gray-500">
                          {tracker.profile?.email || ""}
                        </p>
                      </div>
                    </div>
                    <span
                      className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        isActive
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {isActive ? "Active" : "Inactive"}
                    </span>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between">
                      <span className="text-xs text-gray-500">First Login:</span>
                      <span className="text-xs font-medium">{firstLogin}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-gray-500">Last Activity:</span>
                      <span className="text-xs font-medium">{lastActivity}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-gray-500">Total Time:</span>
                      <span className="text-xs font-medium">{formatTime(tracker.totalSeconds)}</span>
                    </div>
                  </div>

                  <button
                    onClick={() => {
                      setSelectedUser(tracker);
                      setShowModal(true);
                    }}
                    className="w-full py-2 bg-[#F4F5F9] text-[#6E39CB] rounded-md hover:bg-[#EAECF4] transition-colors text-sm font-medium"
                  >
                    View Details
                  </button>
                </div>
              );
            })
          ) : (
            <div className="col-span-full bg-white rounded-lg shadow-sm p-6 text-center text-gray-500">
              No time tracking data found matching your filters.
            </div>
          )}
        </div>
      )}

      {/* Modal for user details - placeholder for now */}
      {showModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4">
            <div className="flex items-center justify-between p-5 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800">
                Time Tracking Details
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                &times;
              </button>
            </div>
            <div className="p-5">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 bg-[#F4F5F9] rounded-full flex items-center justify-center">
                  <span className="text-[#6E39CB] font-medium text-lg">
                    {selectedUser.profile?.fullName?.charAt(0) || "U"}
                  </span>
                </div>
                <div className="ml-4">
                  <p className="text-lg font-medium text-gray-900">
                    {selectedUser.profile?.fullName || "Unknown User"}
                  </p>
                  <p className="text-sm text-gray-500">
                    {selectedUser.profile?.email || ""}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div className="bg-[#F4F5F9] rounded-lg p-4">
                  <p className="text-sm font-medium text-gray-700 mb-1">First Login</p>
                  <p className="text-lg font-bold text-gray-900">
                    {selectedUser.firstLogin
                      ? new Date(selectedUser.firstLogin).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                      : 'N/A'}
                  </p>
                </div>

                <div className="bg-[#F4F5F9] rounded-lg p-4">
                  <p className="text-sm font-medium text-gray-700 mb-1">Last Activity</p>
                  <p className="text-lg font-bold text-gray-900">
                    {selectedUser.lastTime
                      ? new Date(selectedUser.lastTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                      : 'N/A'}
                  </p>
                </div>

                <div className="bg-[#F4F5F9] rounded-lg p-4 md:col-span-2">
                  <p className="text-sm font-medium text-gray-700 mb-1">Total Time</p>
                  <p className="text-lg font-bold text-gray-900">
                    {formatTime(selectedUser.totalSeconds)}
                  </p>
                </div>
              </div>

              <p className="text-sm text-gray-500 mb-4">
                This is a placeholder for more detailed time tracking information.
                In a future update, this modal will include historical data and charts.
              </p>

              <div className="flex justify-end">
                <button
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-opacity-90 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimeTrackingPage;
