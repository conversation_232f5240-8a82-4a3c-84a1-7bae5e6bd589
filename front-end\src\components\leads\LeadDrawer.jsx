import React, { useState, memo } from "react";
import {
  FaTimes,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaUser,
  FaBuilding,
  FaComments,
  FaFileAlt,
  FaPlus,
  FaCalendarAlt,
  FaClock,
  FaUserTie,
  FaHistory,
  FaDollarSign,
  FaEdit,
  FaTrash,
  FaSave,
  FaTimes as FaCancel
} from "react-icons/fa";

const LeadDrawer = ({
  isOpen,
  onClose,
  lead,
  selectedLead, // Keep for backward compatibility
  drawerTab,
  setDrawerTab,
  applicantSearch,
  setApplicantSearch,
  newComment,
  setNewComment,
  onAddComment,
  onEditComment,
  onDeleteComment,
  filteredApplications,
  onProfileRedirect,
  isAddingComment,
  onOpenCreateApplicationModal
}) => {
  // Use lead prop if available, fallback to selectedLead for backward compatibility
  const currentLead = lead || selectedLead;

  // Use drawerTab if provided, otherwise use local state with comments as default
  const [localActiveTab, setLocalActiveTab] = useState("comments");
  const activeTab = drawerTab || localActiveTab;
  const setActiveTab = setDrawerTab || setLocalActiveTab;

  // State for editing comments
  const [editingComment, setEditingComment] = useState(null);
  const [editCommentText, setEditCommentText] = useState("");

  if (!isOpen || !currentLead) return null;

  const handleAddComment = async () => {
    if (newComment && newComment.trim()) {
      if (onAddComment) {
        await onAddComment(currentLead.phone, newComment);
        if (setNewComment) {
          setNewComment("");
        }
      }
    }
  };

  const handleEditComment = (comment) => {
    setEditingComment(comment.id);
    setEditCommentText(comment.content || comment.comment);
  };

  const handleUpdateComment = async () => {
    if (editCommentText.trim() && onEditComment) {
      await onEditComment(currentLead.phone, editingComment, editCommentText);
      setEditingComment(null);
      setEditCommentText("");
    }
  };

  const handleDeleteComment = async (commentId) => {
    if (window.confirm("Are you sure you want to delete this comment?") && onDeleteComment) {
      await onDeleteComment(currentLead.phone, commentId);
    }
  };

  const cancelEdit = () => {
    setEditingComment(null);
    setEditCommentText("");
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper function to get status color (matching app color scheme)
  const getStatusColor = (status) => {
    switch (status) {
      case "HOT": return "bg-red-100 text-red-800 border-red-200";
      case "WARM": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "COLD": return "bg-blue-100 text-blue-800 border-blue-200";
      case "FRESH": return "bg-green-100 text-green-800 border-green-200";
      case "CLOSED": return "bg-gray-100 text-gray-800 border-gray-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Helper function to get application status color (matching app color scheme)
  const getApplicationStatusColor = (status) => {
    switch (status) {
      case "QUOTE_RAISED": return "bg-blue-100 text-blue-800 border-blue-200";
      case "INVOICE_RAISED": return "bg-purple-100 text-purple-800 border-purple-200";
      case "ENROLLED": return "bg-green-100 text-green-800 border-green-200";
      case "FALLOUT": return "bg-red-100 text-red-800 border-red-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-30 z-40"
        onClick={onClose}
      />

      {/* Drawer */}
      <div className="fixed right-0 top-0 h-full w-[480px] bg-white shadow-2xl z-50 transform transition-transform duration-300 border-l border-gray-100">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-white">
          <div className="flex items-center space-x-3">
            {currentLead.companyName && currentLead.companyName !== "N/A" ? (
              <FaBuilding className="text-[#6E39CB] text-lg" />
            ) : (
              <FaUser className="text-[#6E39CB] text-lg" />
            )}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {currentLead.companyName && currentLead.companyName !== "N/A"
                  ? currentLead.companyName
                  : currentLead.leadName || currentLead.name}
              </h3>
              <p className="text-sm text-gray-500">
                {currentLead.companyName && currentLead.companyName !== "N/A"
                  ? `Contact: ${currentLead.leadName || currentLead.name}`
                  : "Individual Lead"}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg"
          >
            <FaTimes className="h-4 w-4" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 bg-white">
          <button
            onClick={() => setActiveTab("comments")}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-all ${
              activeTab === "comments"
                ? "text-gray-900 border-b-2 border-[#6E39CB] bg-white"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            <FaComments className="inline mr-2 text-xs" />
            Comments ({currentLead.comments?.length || 0})
          </button>
          <button
            onClick={() => setActiveTab("applications")}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-all ${
              activeTab === "applications"
                ? "text-gray-900 border-b-2 border-[#6E39CB] bg-white"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            <FaFileAlt className="inline mr-2 text-xs" />
            Applications ({currentLead.applications?.length || 0})
          </button>
          <button
            onClick={() => setActiveTab("details")}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-all ${
              activeTab === "details"
                ? "text-gray-900 border-b-2 border-[#6E39CB] bg-white"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            <FaUser className="inline mr-2 text-xs" />
            Details
          </button>
          <button
            onClick={() => setActiveTab("activities")}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-all ${
              activeTab === "activities"
                ? "text-gray-900 border-b-2 border-[#6E39CB] bg-white"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            <FaHistory className="inline mr-2 text-xs" />
            Activities ({currentLead.activities?.length || 0})
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto" style={{ height: 'calc(100vh - 180px)' }}>

          {/* Comments Tab */}
          {activeTab === "comments" && (
            <div className="p-6 space-y-6">
              {/* Add Comment Section */}
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h4 className="text-sm font-medium text-gray-900 flex items-center">
                  <FaComments className="mr-2 text-gray-500" />
                  Add New Comment
                </h4>
                <textarea
                  value={newComment || ""}
                  onChange={(e) => setNewComment && setNewComment(e.target.value)}
                  placeholder="Write your comment here..."
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] text-sm bg-white"
                  rows="3"
                />
                <button
                  onClick={handleAddComment}
                  disabled={!newComment || !newComment.trim() || isAddingComment}
                  className="w-full bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                >
                  {isAddingComment ? "Adding..." : "Add Comment"}
                </button>
              </div>

              {/* Comments List */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-foreground flex items-center">
                  <FaHistory className="mr-2 text-muted-foreground" />
                  Comment History ({currentLead.comments?.length || 0})
                </h4>
                {currentLead.comments && currentLead.comments.length > 0 ? (
                  <div className="space-y-3">
                    {[...currentLead.comments]
                      .sort((a, b) => new Date(b.createdAt || b.commentedAt) - new Date(a.createdAt || a.commentedAt))
                      .map((comment, index) => (
                      <div key={comment.id || index} className="bg-card border border-border rounded-lg p-4 hover:shadow-sm transition-shadow">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <FaUserTie className="text-muted-foreground text-xs" />
                            <span className="text-sm font-medium text-foreground">
                              {comment.createdBy || comment.commentedBy || "Unknown User"}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                              <FaClock className="text-xs" />
                              <span>{formatDate(comment.createdAt || comment.commentedAt)}</span>
                            </div>
                            {comment.id && (onEditComment || onDeleteComment) && (
                              <div className="flex items-center space-x-1 ml-2">
                                {onEditComment && (
                                  <button
                                    onClick={() => handleEditComment(comment)}
                                    className="p-1 text-gray-400 hover:text-[#6E39CB] transition-colors"
                                    title="Edit comment"
                                  >
                                    <FaEdit className="text-xs" />
                                  </button>
                                )}
                                {onDeleteComment && (
                                  <button
                                    onClick={() => handleDeleteComment(comment.id)}
                                    className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                                    title="Delete comment"
                                  >
                                    <FaTrash className="text-xs" />
                                  </button>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                        {editingComment === comment.id ? (
                          <div className="space-y-3">
                            <textarea
                              value={editCommentText}
                              onChange={(e) => setEditCommentText(e.target.value)}
                              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] text-sm"
                              rows="3"
                            />
                            <div className="flex justify-end space-x-2">
                              <button
                                onClick={cancelEdit}
                                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                              >
                                <FaCancel className="inline mr-1" />
                                Cancel
                              </button>
                              <button
                                onClick={handleUpdateComment}
                                disabled={!editCommentText.trim()}
                                className="px-3 py-1 bg-[#6E39CB] text-white text-sm rounded hover:bg-[#5E2CB8] disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                              >
                                <FaSave className="inline mr-1" />
                                Save
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="text-sm text-foreground whitespace-pre-wrap leading-relaxed">
                            {comment.content || comment.comment}
                          </div>
                        )}
                        {comment.updatedAt && comment.updatedAt !== comment.createdAt && (
                          <div className="mt-2 text-xs text-muted-foreground italic">
                            Last updated: {formatDate(comment.updatedAt)}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    <FaComments className="mx-auto text-3xl mb-3 text-muted" />
                    <p className="text-sm">No comments yet</p>
                    <p className="text-xs text-muted-foreground mt-1">Be the first to add a comment</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Applications Tab */}
          {activeTab === "applications" && (
            <div className="p-6 space-y-6">
              {/* Create Application Button */}
              {onOpenCreateApplicationModal && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <button
                    onClick={() => onOpenCreateApplicationModal(currentLead.phone)}
                    className="w-full flex items-center justify-center space-x-2 bg-[#6E39CB] text-white px-4 py-3 rounded-lg hover:bg-[#5E2CB8] transition-colors"
                  >
                    <FaPlus className="h-4 w-4" />
                    <span className="font-medium">Create New Application</span>
                  </button>
                </div>
              )}

              {/* Applications List */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-foreground flex items-center">
                  <FaFileAlt className="mr-2 text-muted-foreground" />
                  Applications ({currentLead.applications?.length || 0})
                </h4>
                {currentLead.applications && currentLead.applications.length > 0 ? (
                  <div className="space-y-4">
                    {currentLead.applications.map((app, index) => (
                      <div
                        key={index}
                        onClick={() => onProfileRedirect && onProfileRedirect(app.applicationId)}
                        className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all cursor-pointer hover:bg-gray-50 hover:border-[#6E39CB]"
                      >
                        {/* Application Header */}
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h5 className="font-medium text-foreground text-sm">
                              {app.fullName}
                            </h5>
                            <p className="text-xs text-muted-foreground mt-1">
                              ID: {app.applicationId}
                            </p>
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getApplicationStatusColor(app.applicationStatus)}`}>
                            {app.applicationStatus?.replace('_', ' ')}
                          </span>
                        </div>

                        {/* Qualifications */}
                        <div className="space-y-2 mb-3">
                          <h6 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Qualifications</h6>
                          {app.qualificationName && app.qualificationName.map((qual, qualIndex) => (
                            <div key={qualIndex} className="bg-muted rounded p-3">
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <p className="text-sm font-medium text-foreground">{qual.qualificationName}</p>
                                  <p className="text-xs text-muted-foreground mt-1">Code: {qual.qualificationId}</p>
                                </div>
                                <div className="text-right ml-3">
                                  <div className="flex items-center text-sm font-medium text-foreground">
                                    <FaDollarSign className="text-xs mr-1" />
                                    {qual.price?.toLocaleString()}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Application Footer */}
                        <div className="flex items-center justify-between pt-3 border-t border-border">
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <FaCalendarAlt className="text-xs" />
                            <span>Created: {formatDate(app.createdAt)}</span>
                          </div>
                          <div className="text-xs text-[#6E39CB] font-medium">
                            View Details →
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    <FaFileAlt className="mx-auto text-3xl mb-3 text-muted" />
                    <p className="text-sm">No applications yet</p>
                    <p className="text-xs text-muted-foreground mt-1">Create the first application for this lead</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Details Tab */}
          {activeTab === "details" && (
            <div className="p-6 space-y-6">
              {/* Lead Overview */}
              <div className="bg-muted rounded-lg p-4 space-y-4">
                <h4 className="text-sm font-medium text-foreground flex items-center">
                  <FaUser className="mr-2 text-muted-foreground" />
                  Lead Overview
                </h4>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Status</label>
                    <span className={`inline-block px-3 py-1 text-xs font-medium rounded-full border mt-1 ${getStatusColor(currentLead.status)}`}>
                      {currentLead.status}
                    </span>
                  </div>
                  <div>
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Applications</label>
                    <p className="text-sm font-medium text-foreground mt-1">{currentLead.applicationCount || 0}</p>
                  </div>
                </div>

                <div>
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Created Date</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <FaCalendarAlt className="text-muted-foreground text-xs" />
                    <p className="text-sm text-foreground">{formatDate(currentLead.createdDate || currentLead.createdAt)}</p>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-card border border-border rounded-lg p-4 space-y-4">
                <h4 className="text-sm font-medium text-foreground flex items-center">
                  <FaPhone className="mr-2 text-muted-foreground" />
                  Contact Information
                </h4>

                <div className="space-y-3">
                  {currentLead.companyName && currentLead.companyName !== "N/A" && (
                    <div>
                      <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Company Name</label>
                      <div className="flex items-center space-x-2 mt-1">
                        <FaBuilding className="text-muted-foreground text-xs" />
                        <p className="text-sm text-foreground">{currentLead.companyName}</p>
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                      {currentLead.companyName && currentLead.companyName !== "N/A" ? "Contact Person" : "Lead Name"}
                    </label>
                    <div className="flex items-center space-x-2 mt-1">
                      <FaUser className="text-muted-foreground text-xs" />
                      <p className="text-sm text-foreground">{currentLead.leadName || currentLead.name}</p>
                    </div>
                  </div>

                  <div>
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Phone</label>
                    <div className="flex items-center space-x-2 mt-1">
                      <FaPhone className="text-muted-foreground text-xs" />
                      <p className="text-sm text-foreground">{currentLead.phone}</p>
                    </div>
                  </div>

                  {currentLead.email && (
                    <div>
                      <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Email</label>
                      <div className="flex items-center space-x-2 mt-1">
                        <FaEnvelope className="text-muted-foreground text-xs" />
                        <p className="text-sm text-foreground">{currentLead.email}</p>
                      </div>
                    </div>
                  )}

                  {currentLead.address && currentLead.address.trim() && (
                    <div>
                      <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Address</label>
                      <div className="flex items-center space-x-2 mt-1">
                        <FaMapMarkerAlt className="text-muted-foreground text-xs" />
                        <p className="text-sm text-foreground">{currentLead.address}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Assigned Agents */}
              {currentLead.assignedAgents && currentLead.assignedAgents.length > 0 && (
                <div className="bg-card border border-border rounded-lg p-4 space-y-4">
                  <h4 className="text-sm font-medium text-foreground flex items-center">
                    <FaUserTie className="mr-2 text-muted-foreground" />
                    Assigned Agents ({currentLead.assignedAgents.length})
                  </h4>
                  <div className="space-y-2">
                    {currentLead.assignedAgents.map((agent, index) => (
                      <div key={index} className="flex items-center space-x-3 p-2 bg-muted rounded">
                        <FaUserTie className="text-muted-foreground text-xs" />
                        <div>
                          <p className="text-sm font-medium text-foreground">{agent.fullName}</p>
                          <p className="text-xs text-muted-foreground">{agent.username}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Activities Tab */}
          {activeTab === "activities" && (
            <div className="p-6 space-y-6">
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-foreground flex items-center">
                  <FaHistory className="mr-2 text-muted-foreground" />
                  Activity Timeline ({currentLead.activities?.length || 0})
                </h4>
                {currentLead.activities && currentLead.activities.length > 0 ? (
                  <div className="space-y-3">
                    {[...currentLead.activities]
                      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                      .map((activity, index) => (
                      <div key={index} className="bg-card border border-border rounded-lg p-4 hover:shadow-sm transition-shadow">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <FaHistory className="text-muted-foreground text-xs" />
                            <span className="text-sm font-medium text-foreground">
                              {activity.createdBy || "System"}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <FaClock className="text-xs" />
                            <span>{formatDate(activity.createdAt)}</span>
                          </div>
                        </div>
                        <div className="text-sm text-foreground whitespace-pre-wrap leading-relaxed">
                          {activity.content}
                        </div>
                        {activity.updatedAt && activity.updatedAt !== activity.createdAt && (
                          <div className="mt-2 text-xs text-muted-foreground italic">
                            Last updated: {formatDate(activity.updatedAt)}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    <FaHistory className="mx-auto text-3xl mb-3 text-muted" />
                    <p className="text-sm">No activities yet</p>
                    <p className="text-xs text-muted-foreground mt-1">Activities will appear here as they occur</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default memo(LeadDrawer);
