import { useState, useMemo, useCallback } from 'react';
import { filterLeads, filterLeadsByTab, sortLeads, calculateLeadStats, getUniqueAgentNames } from '../utils/leadUtils';

export const useLeadFilters = (leadsData = []) => {
  // Filter states
  const [leadSearch, setLeadSearch] = useState("");
  const [selectedAgentFilter, setSelectedAgentFilter] = useState("");
  const [leadTypeFilter, setLeadTypeFilter] = useState("all");
  const [leadTab, setLeadTab] = useState("All");
  
  // Sorting states
  const [sortField, setSortField] = useState(null);
  const [sortDirection, setSortDirection] = useState('asc');

  // Memoized filtered and sorted data
  const filteredLeadsForStats = useMemo(() => {
    return filterLeads(leadsData, {
      leadSearch,
      selectedAgentFilter,
      leadTypeFilter
    });
  }, [leadsData, leadSearch, selectedAgentFilter, leadTypeFilter]);

  const filteredLeads = useMemo(() => {
    const tabFiltered = filterLeadsByTab(filteredLeadsForStats, leadTab);
    return tabFiltered;
  }, [filteredLeadsForStats, leadTab]);

  const sortedLeads = useMemo(() => {
    return sortLeads(filteredLeads, sortField, sortDirection);
  }, [filteredLeads, sortField, sortDirection]);

  // Memoized statistics
  const stats = useMemo(() => {
    return calculateLeadStats(filteredLeadsForStats);
  }, [filteredLeadsForStats]);

  // Memoized agent names
  const agentNames = useMemo(() => {
    return getUniqueAgentNames(leadsData);
  }, [leadsData]);

  // Filter text for display
  const agentFilterText = selectedAgentFilter ? selectedAgentFilter : "All";
  const leadTypeFilterText = leadTypeFilter === "all" ? "" : ` - ${leadTypeFilter}`;
  const cardTitleSuffix = selectedAgentFilter || leadTypeFilter !== "all" ? `(${agentFilterText}${leadTypeFilterText})` : "";

  // Sorting handlers
  const handleSort = useCallback((field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField, sortDirection]);

  const handleLeadTabChange = useCallback((tab) => setLeadTab(tab), []);

  return {
    // States
    leadSearch,
    setLeadSearch,
    selectedAgentFilter,
    setSelectedAgentFilter,
    leadTypeFilter,
    setLeadTypeFilter,
    leadTab,
    setLeadTab,
    sortField,
    setSortField,
    sortDirection,
    setSortDirection,
    
    // Computed data
    filteredLeadsForStats,
    filteredLeads,
    sortedLeads,
    stats,
    agentNames,
    cardTitleSuffix,
    
    // Handlers
    handleSort,
    handleLeadTabChange
  };
};
