// src/pages/applicant/ApplicantTracker.jsx

import React, { useState, useEffect } from "react";
import logo from "../../assets/logo.png"; // Replace with actual path
import DocumentModal from "../../components/modal/DocumentModal";
import DocumentButton from "../../components/modal/DocumentButton";
import QRCode from "react-qr-code";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCopy } from "@fortawesome/free-solid-svg-icons";

// Mock Data
const mockUserData = {
  applicationId: "APP654321",
  emailAddress: "<EMAIL>",
  name: "<PERSON>",
  address: "456 Elm Street, Melbourne, VIC",
  usiNumber: "USI67890",
  dateOfBirth: "1992-05-15",
  gender: "Female",
  phoneNumber: "************",
  status: "In Progress",
  createdAt: "2024-10-10",
};

const mockDocumentSections = [
  {
    title: "Identification Documents",
    documents: [
      {
        name: "Passport",
        uploadedDate: "2024-10-15",
        fileLink:
          "https://www.buyglobaldocument.com/wp-content/uploads/2024/10/Australia-Passport.jpg",
      },
      {
        name: "Driver's License",
        uploadedDate: "2024-09-20",
        fileLink:
          "https://clevergrid924.weebly.com/uploads/1/2/5/3/*********/*********.jpg",
      },
    ],
  },
  {
    title: "Academic Documents",
    documents: [
      {
        name: "Certificate III in Engineering",
        uploadedDate: "2024-08-10",
        fileLink:
          "https://image.isu.pub/211123093303-ab88169dd7924dfab83b5f62ccfe6a1a/jpg/page_1.jpg",
      },
    ],
  },
];

const mockFeedbacks = [
  {
    date: "2024-11-01",
    message: "Your documents have been verified.",
  },
  {
    date: "2024-11-05",
    message: "Quote has been raised for your application.",
  },
];

const mockActivities = [
  { date: "2024-10-10", change: "Application Submitted" },
  { date: "2024-10-15", change: "Documents Verified" },
  { date: "2024-11-01", change: "Quote Raised" },
];

const ApplicantTracker = () => {
  const [userData, setUserData] = useState(mockUserData);
  const [activeTab, setActiveTab] = useState("Status");
  const [feedbacks, setFeedbacks] = useState(mockFeedbacks);
  const [activities, setActivities] = useState(mockActivities);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [uploadLink, setUploadLink] = useState(
    `https://apply.skillsync.com.au/upload?applicationId=${mockUserData.applicationId}`
  );

  // Handle Open Modal
  const handleOpenModal = (document) => {
    setSelectedDocument(document);
    setIsModalOpen(true);
  };

  // Handle Copy Tracker Link
  const handleCopyTracker = () => {
    navigator.clipboard.writeText(uploadLink).then(
      () => {
        alert("Tracker link copied to clipboard!");
      },
      (err) => {
        console.error("Could not copy text: ", err);
      }
    );
  };

  // Status Steps
  const statusSteps = [
    "Document Pending",
    "Quote Raised",
    "Invoice Raised",
    "In Progress",
    "Soft Copy Ready",
    "Soft Copy Sent",
    "Hard Copy Ready",
    "Hard Copy Sent",
  ];

  // Determine current step index
  const currentStepIndex = statusSteps.indexOf(userData.status);

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header Section */}
      <br /><br />
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-800">Application Tracker</h1>
        <img src={logo} alt="Company Logo" className="w-24 h-auto" />
      </div>

      {/* Applicant Information */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <div className="flex flex-col md:flex-row items-center md:items-start">
        <QRCode value={uploadLink} size={128}  className="mr-6 mb-4 md:mb-0"/>
          <div>
            <h2 className="text-2xl font-semibold text-gray-800">{userData.name}</h2>
            <p className="text-gray-600">{userData.emailAddress}</p>
            <p className="text-gray-600">{userData.phoneNumber}</p>
            <p className="text-gray-600">USI Number: {userData.usiNumber}</p>
            <p className="text-gray-600">Address: {userData.address}</p>
          </div>
        </div>
      </div>

      {/* Status Tracker */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Application Status</h3>
        <div className="flex overflow-x-auto">
          {statusSteps.map((step, index) => (
            <div key={index} className="flex flex-col items-center flex-1">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 ${
                  index <= currentStepIndex
                    ? "bg-blue-500 text-white"
                    : "bg-gray-300 text-gray-600"
                }`}
              >
                {index + 1}
              </div>
              <p
                className={`text-sm text-center ${
                  index <= currentStepIndex ? "text-blue-600" : "text-gray-600"
                }`}
              >
                {step}
              </p>
              {index < statusSteps.length - 1 && (
                <div
                  className={`flex-1 w-1 bg-${
                    index < currentStepIndex ? "blue-500" : "gray-300"
                  }`}
                ></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Tabs Section */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        {/* Tabs Navigation */}
        <div className="flex border-b mb-4">
          {["Status", "Documents", "Feedbacks", "Activity"].map((tab) => (
            <button
              key={tab}
              className={`py-2 px-4 -mb-px border-b-2 font-medium text-sm ${
                activeTab === tab
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-600 hover:text-blue-600 hover:border-blue-500"
              }`}
              onClick={() => setActiveTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Tabs Content */}
        <div>
          {activeTab === "Status" && (
            <div>
              <p className="text-lg text-gray-700">
                Current Status:{" "}
                <span className="font-semibold text-blue-600">{userData.status}</span>
              </p>
            </div>
          )}

          {activeTab === "Documents" && (
            <div>
              {mockDocumentSections.map((section, idx) => (
                <div key={idx} className="mb-6">
                  <h3 className="text-xl font-semibold mb-4 text-gray-800">
                    {section.title}
                  </h3>
                  <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-4">
                    {section.documents.map((doc, idx) => (
                      <DocumentButton
                        key={idx}
                        title={doc.name}
                        date={doc.uploadedDate}
                        handleOpenModal={() => handleOpenModal(doc)}
                        fileLink={doc.fileLink}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === "Feedbacks" && (
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-800">
                Feedback from Admin
              </h3>
              {feedbacks.length > 0 ? (
                <ul className="space-y-2">
                  {feedbacks.map((feedback, idx) => (
                    <li
                      key={idx}
                      className="p-4 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <p className="text-xs text-gray-500">{feedback.date}</p>
                      <p className="text-sm text-gray-800 mt-1">{feedback.message}</p>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500">No feedback available at this moment.</p>
              )}
            </div>
          )}

          {activeTab === "Activity" && (
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-800">Activity Log</h3>
              {activities.length > 0 ? (
                <ul className="space-y-2">
                  {activities.map((activity, index) => (
                    <li
                      key={index}
                      className="p-4 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <p className="text-xs text-gray-500">{activity.date}</p>
                      <p className="text-sm text-gray-800 mt-1">{activity.change}</p>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500">No recent activities.</p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* QR Code and Tracker Link */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8 flex flex-col md:flex-row items-center justify-between">
        {/* QR Code */}
        

        {/* Tracker Link */}
        <div className="text-center md:text-left">
          <p className="text-gray-700 mb-2">Upload Additional Documents:</p>
          <a
            href={uploadLink}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 underline break-all"
          >
            {uploadLink}
          </a>
        </div>
      </div>

      {/* Document Modal */}
      <DocumentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        document={selectedDocument}
      />
    </div>
  );
};

export default ApplicantTracker;
