import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft } from "@fortawesome/free-solid-svg-icons";

const ApplicationHeader = ({ 
  application, 
  navigate, 
  selectedStatus, 
  selectedPaymentStatus, 
  canEditStatus, 
  canEditPayments, 
  handleStatusChange, 
  handlePaymentStatusChange 
}) => {
  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => navigate(-1)}
            className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Go back"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {application.applicationId}
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              Application for {application.applicantName}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
            <select
              value={selectedPaymentStatus}
              onChange={handlePaymentStatusChange}
              disabled={!canEditPayments}
              className={`w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] ${!canEditPayments ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            >
              <option value="PENDING">PENDING</option>
              <option value="PARTIALLY_PAID">PARTIALLY PAID</option>
              <option value="FULLY_PAID">FULLY PAID</option>
              <option value="INVOICE_EXPIRED">INVOICE EXPIRED</option>
            </select>
            {!canEditPayments && (
              <p className="text-xs text-gray-500 mt-1">View only - Contact admin to modify payment status</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Application Status</label>
            <select
              value={selectedStatus}
              onChange={handleStatusChange}
              disabled={!canEditStatus}
              className={`w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] ${!canEditStatus ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            >
              <option value="DOCUMENT_PENDING">DOCUMENT PENDING</option>
              <option value="QUOTE_RAISED">QUOTE RAISED</option>
              <option value="INVOICE_RAISED">INVOICE RAISED</option>
              <option value="IN_PROGRESS">IN PROGRESS</option>
              <option value="SOFT_COPY_READY">SOFT COPY READY</option>
              <option value="SOFT_COPY_SENT">SOFT COPY SENT</option>
              <option value="HARD_COPY_READY">HARD COPY READY</option>
              <option value="HARD_COPY_SENT">HARD COPY SENT</option>
              <option value="FALLOUT">FALLOUT</option>
            </select>
            {!canEditStatus && (
              <p className="text-xs text-gray-500 mt-1">View only - Contact admin to modify application status</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationHeader;
