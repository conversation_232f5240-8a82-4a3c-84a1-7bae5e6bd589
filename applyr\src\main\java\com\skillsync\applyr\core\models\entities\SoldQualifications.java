package com.skillsync.applyr.core.models.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "sold_qualifications")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SoldQualifications {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "qualification_id")
    private Qualification qualification;

    private double soldPrice;

    @ManyToOne
    @JoinColumn(name = "application_id")
    private Application application;

    public SoldQualifications(Qualification qualification, double price, Application saved) {
        this.qualification = qualification;
        this.soldPrice = price;
        this.application = saved;
    }
}
