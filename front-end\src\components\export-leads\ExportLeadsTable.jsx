import React, { useState, useRef, useCallback } from "react";

const ExportLeadsTable = ({
  leads,
  selectedLeads,
  onSelectAll,
  onSelectLead,
  isAllSelected,
  isIndeterminate,
  sortField,
  sortDirection,
  onSort,
  isLoading,
  error
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartIndex, setDragStartIndex] = useState(null);
  const [dragAction, setDragAction] = useState(null); // 'select' or 'unselect'
  const tableRef = useRef(null);

  const getSortIcon = (field) => {
    if (sortField !== field) {
      return (
        <svg className="ml-1 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
        </svg>
      );
    }
    return sortDirection === 'asc' ? (
      <svg className="ml-1 w-4 h-4 text-[#6E39CB]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
      </svg>
    ) : (
      <svg className="ml-1 w-4 h-4 text-[#6E39CB]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    );
  };

  // Row click handler for selection
  const handleRowClick = useCallback((phone, e) => {
    // Don't trigger row selection if clicking on checkbox or other interactive elements
    if (e.target.type === 'checkbox' || e.target.closest('input[type="checkbox"]')) {
      return;
    }

    // Toggle selection
    const isSelected = selectedLeads.has(phone);
    onSelectLead(phone, !isSelected);
  }, [selectedLeads, onSelectLead]);

  // Drag selection handlers
  const handleMouseDown = useCallback((index, e) => {
    if (e.target.type === 'checkbox') return; // Don't start drag on checkbox

    // Determine the drag action based on the current selection state of the clicked item
    const phone = leads[index]?.phone;
    const isCurrentlySelected = selectedLeads.has(phone);
    const action = isCurrentlySelected ? 'unselect' : 'select';

    setIsDragging(true);
    setDragStartIndex(index);
    setDragAction(action);

    // Apply the action to the starting item
    onSelectLead(phone, !isCurrentlySelected);

    e.preventDefault();
  }, [leads, selectedLeads, onSelectLead]);

  const handleMouseEnter = useCallback((index) => {
    if (!isDragging || dragStartIndex === null || dragAction === null) return;

    const startIdx = Math.min(dragStartIndex, index);
    const endIdx = Math.max(dragStartIndex, index);

    // Apply the drag action (select or unselect) to all items in the range
    const shouldSelect = dragAction === 'select';

    for (let i = startIdx; i <= endIdx; i++) {
      if (i < leads.length) {
        onSelectLead(leads[i].phone, shouldSelect);
      }
    }
  }, [isDragging, dragStartIndex, dragAction, leads, onSelectLead]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragStartIndex(null);
    setDragAction(null);
  }, []);

  // Add global mouse up listener
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mouseup', handleMouseUp);
      return () => document.removeEventListener('mouseup', handleMouseUp);
    }
  }, [isDragging, handleMouseUp]);

  const getStatusBadgeColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'fresh':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'warm':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'hot':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'cold':
        return 'bg-gray-50 text-gray-700 border-gray-200';
      case 'closed':
        return 'bg-green-50 text-green-700 border-green-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-AU', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl border border-gray-100">
        <div className="p-12 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-[#6E39CB] border-t-transparent mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading leads...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl border border-gray-100">
        <div className="p-8">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-gray-600">
              Error loading leads: {error?.data?.message || error?.message || 'Unknown error'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-100">
      <div className="overflow-x-auto">
        <table ref={tableRef} className="min-w-full">
          <thead>
            <tr className="border-b border-gray-100">
              <th className="px-6 py-4 text-left">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={(input) => {
                    if (input) input.indeterminate = isIndeterminate;
                  }}
                  onChange={(e) => onSelectAll(e.target.checked)}
                  className="h-4 w-4 text-[#6E39CB] focus:ring-[#6E39CB] border-gray-300 rounded"
                />
              </th>
              <th
                className="px-6 py-4 text-left text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => onSort('companyName')}
              >
                <div className="flex items-center">
                  Company
                  {getSortIcon('companyName')}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => onSort('leadName')}
              >
                <div className="flex items-center">
                  Lead Name
                  {getSortIcon('leadName')}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => onSort('phone')}
              >
                <div className="flex items-center">
                  Phone
                  {getSortIcon('phone')}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => onSort('email')}
              >
                <div className="flex items-center">
                  Email
                  {getSortIcon('email')}
                </div>
              </th>
              <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">
                Status
              </th>
              <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">
                Apps
              </th>
              <th
                className="px-6 py-4 text-left text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => onSort('createdDate')}
              >
                <div className="flex items-center">
                  Created
                  {getSortIcon('createdDate')}
                </div>
              </th>
              <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">
                Assigned Agent
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-50">
            {leads.length === 0 ? (
              <tr>
                <td colSpan="9" className="px-6 py-16 text-center">
                  <div className="text-gray-500">
                    <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p className="text-lg font-medium text-gray-900 mb-1">No leads found</p>
                    <p className="text-gray-500">Try adjusting your filters to see more results.</p>
                  </div>
                </td>
              </tr>
            ) : (
              leads.map((lead, index) => (
                <tr
                  key={lead.phone}
                  className={`hover:bg-gray-50 transition-all duration-200 cursor-pointer ${
                    selectedLeads.has(lead.phone)
                      ? 'bg-blue-50 border-l-4 border-[#6E39CB] shadow-sm'
                      : 'hover:shadow-sm'
                  } ${isDragging ? 'select-none' : ''}`}
                  onClick={(e) => handleRowClick(lead.phone, e)}
                  onMouseDown={(e) => handleMouseDown(index, e)}
                  onMouseEnter={() => handleMouseEnter(index)}
                >
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedLeads.has(lead.phone)}
                      onChange={(e) => onSelectLead(lead.phone, e.target.checked)}
                      className="h-4 w-4 text-[#6E39CB] focus:ring-[#6E39CB] border-gray-300 rounded"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900 truncate max-w-[150px]">
                      {lead.companyName && lead.companyName !== 'N/A' ? lead.companyName : '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 truncate max-w-[150px]">
                      {lead.leadName?.trim() || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 font-mono">
                      {lead.phone || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 truncate max-w-[200px]">
                      {lead.email || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-3 py-1 text-xs font-medium rounded-full border ${getStatusBadgeColor(lead.status)}`}>
                      {lead.status || 'Unknown'}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {lead.applicationCount || 0}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {formatDate(lead.createdDate)}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 truncate max-w-[150px]">
                      {lead.assignedAgents?.length > 0
                        ? lead.assignedAgents[0].fullName
                        : '-'}
                      {lead.assignedAgents?.length > 1 && (
                        <span className="text-gray-500 ml-1">
                          +{lead.assignedAgents.length - 1}
                        </span>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Table Footer with Summary */}
      {leads.length > 0 && (
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-100 rounded-b-xl">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing <span className="font-medium text-gray-900">{leads.length}</span> lead{leads.length !== 1 ? 's' : ''} on this page
              {selectedLeads.size > 0 && (
                <span className="ml-2">
                  • <span className="font-medium text-[#6E39CB]">{selectedLeads.size} total selected</span>
                  {(() => {
                    const currentPagePhones = new Set(leads.map(lead => lead.phone));
                    const selectedOnCurrentPage = [...selectedLeads].filter(phone => currentPagePhones.has(phone)).length;
                    const selectedOnOtherPages = selectedLeads.size - selectedOnCurrentPage;

                    if (selectedOnOtherPages > 0) {
                      return (
                        <span className="text-blue-600">
                          {" "}({selectedOnCurrentPage} on this page, {selectedOnOtherPages} on other pages)
                        </span>
                      );
                    }
                    return null;
                  })()}
                </span>
              )}
            </div>
            <div className="text-sm text-gray-500">
              {isDragging ? (
                <span className="italic">
                  {dragAction === 'select' ? 'Drag to select multiple leads' : 'Drag to unselect multiple leads'}
                </span>
              ) : (
                <span>💡 Click rows to select • Drag to select/unselect multiple</span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExportLeadsTable;
