import React, { useState, useCallback, useMemo, useEffect } from "react";
import {
  useGetLeadsPaginatedQuery,
  useExportLeadsMutation,
  useExportLeadsEnhancedMutation,
} from "../../../services/CompanyAPIService";
import { useGetAllAgentsQuery } from "../../../services/AdminAPIService";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Components
import ExportLeadsHeader from "../../../components/export-leads/ExportLeadsHeader";
import ExportLeadsTable from "../../../components/export-leads/ExportLeadsTable";
import ExportLeadsFilters from "../../../components/export-leads/ExportLeadsFilters";
import ExportLeadsPagination from "../../../components/export-leads/ExportLeadsPagination";
import ExportOptionsModal from "../../../components/export-leads/ExportOptionsModal";

// Utils
import { getUniqueAgentNames } from "../../../utils/leadUtils";

const ExportLeadsPage = () => {
  // =======================
  // Local State
  // =======================
  const [selectedLeads, setSelectedLeads] = useState(new Set());
  const [leadSearch, setLeadSearch] = useState("");
  const [selectedAgentFilter, setSelectedAgentFilter] = useState("");
  const [leadStatusFilter, setLeadStatusFilter] = useState("all");
  const [dateFilterType, setDateFilterType] = useState("all");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [specificDate, setSpecificDate] = useState("");
  const [applicationFilter, setApplicationFilter] = useState("all");
  const [sortField, setSortField] = useState("createdDate");
  const [sortDirection, setSortDirection] = useState('desc');
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(100);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);

  // =======================
  // Build Filter Criteria
  // =======================
  const filterCriteria = useMemo(() => {
    const criteria = {
      page: currentPage,
      size: pageSize,
      sortField,
      sortDirection,
      search: leadSearch || undefined,
      agentFilter: selectedAgentFilter || undefined,
      statusFilter: leadStatusFilter !== 'all' ? leadStatusFilter : undefined,
      applicationFilter: applicationFilter !== 'all' ? applicationFilter : undefined,
      dateFilterType: dateFilterType !== 'all' ? dateFilterType : undefined,
    };

    // Add date fields based on filter type
    if (dateFilterType === 'custom') {
      if (startDate) criteria.startDate = new Date(startDate).toISOString();
      if (endDate) criteria.endDate = new Date(endDate).toISOString();
    } else if (dateFilterType === 'specificDate' && specificDate) {
      criteria.specificDate = new Date(specificDate).toISOString();
    }

    return criteria;
  }, [
    currentPage, pageSize, sortField, sortDirection, leadSearch,
    selectedAgentFilter, leadStatusFilter, applicationFilter,
    dateFilterType, startDate, endDate, specificDate
  ]);

  // =======================
  // API Hooks
  // =======================
  const {
    data: paginatedData,
    isLoading: isLeadsLoading,
    isFetching: isLeadsFetching,
    error: leadsError,
  } = useGetLeadsPaginatedQuery(filterCriteria);

  const {
    data: agentsData = [],
    isLoading: isAgentsLoading,
  } = useGetAllAgentsQuery();

  const [exportLeads, { isLoading: isExporting }] = useExportLeadsMutation();
  const [exportLeadsEnhanced, { isLoading: isExportingEnhanced }] = useExportLeadsEnhancedMutation();

  // =======================
  // Computed Values
  // =======================
  const leads = paginatedData?.leads || [];
  const totalElements = paginatedData?.totalElements || 0;
  const totalPages = paginatedData?.totalPages || 0;
  const hasNext = paginatedData?.hasNext || false;
  const hasPrevious = paginatedData?.hasPrevious || false;

  const agentNames = useMemo(() => {
    return getUniqueAgentNames(agentsData);
  }, [agentsData]);

  // Total selections across all pages
  const totalSelectedCount = selectedLeads.size;

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(0);
  }, [leadSearch, selectedAgentFilter, leadStatusFilter, dateFilterType, startDate, endDate, specificDate, applicationFilter]);

  // Don't reset selection when page changes - we want to persist across pages
  // useEffect(() => {
  //   setSelectedLeads(new Set());
  // }, [currentPage]);

  // =======================
  // Handlers
  // =======================
  const handleSort = useCallback((field) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField]);

  const handlePageChange = useCallback((newPage) => {
    setCurrentPage(newPage);
  }, []);

  const handlePageSizeChange = useCallback((newSize) => {
    setPageSize(newSize);
    setCurrentPage(0); // Reset to first page
  }, []);

  // =======================
  // Selection Handlers
  // =======================
  const handleSelectAll = useCallback((checked) => {
    if (checked) {
      const currentPagePhones = new Set(leads.map(lead => lead.phone));
      setSelectedLeads(prev => new Set([...prev, ...currentPagePhones]));
    } else {
      const currentPagePhones = new Set(leads.map(lead => lead.phone));
      setSelectedLeads(prev => new Set([...prev].filter(phone => !currentPagePhones.has(phone))));
    }
  }, [leads]);

  const handleSelectLead = useCallback((phone, checked) => {
    setSelectedLeads(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(phone);
      } else {
        newSet.delete(phone);
      }
      return newSet;
    });
  }, []);

  const handleClearSelections = useCallback(() => {
    setSelectedLeads(new Set());
  }, []);

  // =======================
  // Export Handlers
  // =======================
  const handleExportClick = useCallback(() => {
    setIsExportModalOpen(true);
  }, []);

  const handleExportWithOptions = useCallback(async (exportOptions) => {
    try {
      const exportRequest = {
        ...filterCriteria,
        page: 0,
        size: 1000000, // Large number but within Java int range
        format: exportOptions.format,
        selectedColumns: exportOptions.selectedColumns,
      };

      // If specific leads are selected, export only those
      if (selectedLeads.size > 0) {
        exportRequest.selectedLeads = Array.from(selectedLeads);
      }

      const result = await exportLeadsEnhanced(exportRequest).unwrap();

      // Create download link
      const url = window.URL.createObjectURL(result);
      const link = document.createElement('a');
      link.href = url;

      const now = new Date();
      const dateStr = now.toLocaleDateString('en-AU', {
        year: 'numeric',
        month: 'short',
        day: '2-digit'
      }).replace(/\s/g, '-');

      const extension = exportOptions.format.toLowerCase() === 'excel' ? 'xlsx' : 'csv';
      link.download = `leads-export-${dateStr}.${extension}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setIsExportModalOpen(false);

      toast.success(
        totalSelectedCount > 0
          ? `Successfully exported ${totalSelectedCount} selected leads to ${exportOptions.format}`
          : `Successfully exported all filtered leads (${totalElements} total) to ${exportOptions.format}`
      );
    } catch (error) {
      console.error('Export failed:', error);
      toast.error(error?.data?.message || 'Failed to export leads');
    }
  }, [exportLeadsEnhanced, filterCriteria, selectedLeads, totalElements, totalSelectedCount]);

  // =======================
  // Selection State
  // =======================
  const currentPagePhones = new Set(leads.map(lead => lead.phone));
  const selectedOnCurrentPage = [...selectedLeads].filter(phone => currentPagePhones.has(phone));
  const isAllCurrentPageSelected = leads.length > 0 && selectedOnCurrentPage.length === leads.length;
  const isIndeterminate = selectedOnCurrentPage.length > 0 && selectedOnCurrentPage.length < leads.length;

  return (
    <div className="w-full py-6">
      <ToastContainer />

      {/* Page Header */}
      <ExportLeadsHeader
        selectedCount={totalSelectedCount}
        totalCount={totalElements}
        filteredCount={totalElements}
        onExport={handleExportClick}
        isExporting={isExportingEnhanced}
        onClearSelections={handleClearSelections}
      />

      {/* Filters */}
      <ExportLeadsFilters
        leadSearch={leadSearch}
        setLeadSearch={setLeadSearch}
        selectedAgentFilter={selectedAgentFilter}
        setSelectedAgentFilter={setSelectedAgentFilter}
        leadStatusFilter={leadStatusFilter}
        setLeadStatusFilter={setLeadStatusFilter}
        dateFilterType={dateFilterType}
        setDateFilterType={setDateFilterType}
        startDate={startDate}
        setStartDate={setStartDate}
        endDate={endDate}
        setEndDate={setEndDate}
        specificDate={specificDate}
        setSpecificDate={setSpecificDate}
        applicationFilter={applicationFilter}
        setApplicationFilter={setApplicationFilter}
        agentNames={agentNames}
      />

      {/* Export Table */}
      <ExportLeadsTable
        leads={leads}
        selectedLeads={selectedLeads}
        onSelectAll={handleSelectAll}
        onSelectLead={handleSelectLead}
        isAllSelected={isAllCurrentPageSelected}
        isIndeterminate={isIndeterminate}
        sortField={sortField}
        sortDirection={sortDirection}
        onSort={handleSort}
        isLoading={isLeadsLoading || isLeadsFetching}
        error={leadsError}
      />

      {/* Pagination */}
      <ExportLeadsPagination
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalElements={totalElements}
        hasNext={hasNext}
        hasPrevious={hasPrevious}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        isLoading={isLeadsLoading || isLeadsFetching}
      />

      {/* Export Options Modal */}
      <ExportOptionsModal
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        onExport={handleExportWithOptions}
        isExporting={isExportingEnhanced}
        selectedCount={totalSelectedCount}
        totalCount={totalElements}
      />

      {/* Floating Selection Counter */}
      {totalSelectedCount > 0 && (
        <div className="fixed bottom-6 right-6 z-50">
          <div className="bg-[#6E39CB] text-white px-4 py-2 rounded-full shadow-lg flex items-center space-x-2 animate-in slide-in-from-bottom-2 duration-300">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="font-medium text-sm">
              {totalSelectedCount} selected
            </span>
            <button
              onClick={handleClearSelections}
              className="ml-2 text-white/80 hover:text-white transition-colors"
              title="Clear selections"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExportLeadsPage;
