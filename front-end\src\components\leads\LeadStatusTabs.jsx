import React from "react";

const LeadStatusTabs = ({
  leadTab,
  onTabChange,
  totalLeads,
  leadsWithApplications,
  statusCounts
}) => {
  const tabs = [
    { label: "All Leads", value: "All" },
    { label: "Hot Leads", value: "HOT", color: "text-red-600" },
    { label: "Warm Leads", value: "WARM", color: "text-yellow-600" },
    { label: "Cold/Fresh Leads", value: "COLD_FRESH", color: "text-blue-600" },
    { label: "Leads with Applications", value: "Leads with Applications", color: "text-indigo-600" },
    { label: "Closed", value: "CLOSED", color: "text-gray-600" },
  ];

  const getTabCount = (value) => {
    switch (value) {
      case "All":
        return totalLeads;
      case "Leads with Applications":
        return leadsWithApplications;
      case "COLD_FRESH":
        return (statusCounts["COLD"] || 0) + (statusCounts["FRESH"] || 0);
      case "CLOSED":
        return statusCounts["CLOSED"] || 0;
      default:
        return statusCounts[value] || 0;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-50 mb-6">
      <div className="border-b border-gray-100 px-6 py-4">
        <div className="flex flex-wrap -mb-px">
          {tabs.map(({ label, value, color }) => (
            <button
              key={value}
              onClick={() => onTabChange(value)}
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                leadTab === value
                  ? `border-[#6E39CB] ${color || "text-[#6E39CB]"}`
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap mr-4`}
            >
              {label}
              <span
                className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                  leadTab === value ? "bg-[#F4F5F9]" : "bg-gray-100"
                }`}
              >
                {getTabCount(value)}
              </span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LeadStatusTabs;
