import { useState, useCallback } from 'react';

export const useLeadModals = () => {
  // Bulk upload modal states
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [uploadFile, setUploadFile] = useState(null);
  const [selectedSalesReps, setSelectedSalesReps] = useState([]);
  const [salesRepSearch, setSalesRepSearch] = useState("");
  const [isSalesRepDropdownOpen, setIsSalesRepDropdownOpen] = useState(false);

  // Single lead modal states
  const [isSingleLeadModalOpen, setIsSingleLeadModalOpen] = useState(false);

  // Edit lead modal states
  const [isEditLeadModalOpen, setIsEditLeadModalOpen] = useState(false);
  const [leadToEdit, setLeadToEdit] = useState(null);

  // Delete confirmation states
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [leadToDelete, setLeadToDelete] = useState(null);

  // Drawer states
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedLead, setSelectedLead] = useState(null);
  const [drawerTab, setDrawerTab] = useState("details");
  const [applicantSearch, setApplicantSearch] = useState("");
  const [newComment, setNewComment] = useState("");

  // Full screen state
  const [isFullScreen, setIsFullScreen] = useState(false);

  // Bulk upload modal handlers
  const openModal = () => {
    setIsModalOpen(true);
    setUploadFile(null);
    setSelectedSalesReps([]);
    setSalesRepSearch("");
    setIsSalesRepDropdownOpen(false);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setUploadFile(null);
    setSelectedSalesReps([]);
    setSalesRepSearch("");
    setIsSalesRepDropdownOpen(false);
  };

  // Single lead modal handlers
  const openSingleLeadModal = () => {
    setIsSingleLeadModalOpen(true);
  };

  const closeSingleLeadModal = () => {
    setIsSingleLeadModalOpen(false);
  };

  // Edit lead modal handlers
  const handleOpenEditModal = (lead) => {
    setLeadToEdit(lead);
    setIsEditLeadModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditLeadModalOpen(false);
    setLeadToEdit(null);
  };

  // Delete confirmation handlers
  const handleOpenDeleteDialog = (lead) => {
    setLeadToDelete(lead);
    setIsDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setLeadToDelete(null);
  };

  // Drawer handlers
  const openDrawer = useCallback((lead) => {
    setSelectedLead(lead);
    setIsDrawerOpen(true);
    setDrawerTab("comments"); // Set default tab to comments
    setApplicantSearch("");
    setNewComment("");
  }, []);

  const closeDrawer = useCallback(() => {
    setIsDrawerOpen(false);
    setSelectedLead(null);
  }, []);

  const setSelectedLeadData = useCallback((lead) => {
    setSelectedLead(lead);
  }, []);

  // File upload handlers
  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) setUploadFile(file);
  };

  const handleFileDrop = (e) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files && files[0]) setUploadFile(files[0]);
  };

  const handleDragOver = (e) => e.preventDefault();
  const handleDragEnter = (e) => e.preventDefault();

  // Sales rep selection handlers
  const toggleSalesRepSelection = (username) => {
    if (selectedSalesReps.includes(username)) {
      setSelectedSalesReps(selectedSalesReps.filter(r => r !== username));
    } else {
      setSelectedSalesReps([...selectedSalesReps, username]);
    }
  };

  const handleSalesRepSearchChange = (e) => {
    setSalesRepSearch(e.target.value);
  };

  return {
    // States
    isModalOpen,
    uploadFile,
    selectedSalesReps,
    salesRepSearch,
    isSalesRepDropdownOpen,
    isSingleLeadModalOpen,
    isEditLeadModalOpen,
    leadToEdit,
    isDeleteDialogOpen,
    leadToDelete,
    isDrawerOpen,
    selectedLead,
    drawerTab,
    applicantSearch,
    newComment,
    isFullScreen,

    // Setters
    setUploadFile,
    setSelectedSalesReps,
    setSalesRepSearch,
    setIsSalesRepDropdownOpen,
    setDrawerTab,
    setApplicantSearch,
    setNewComment,
    setIsFullScreen,
    setSelectedLeadData,

    // Handlers
    openModal,
    closeModal,
    openSingleLeadModal,
    closeSingleLeadModal,
    handleOpenEditModal,
    handleCloseEditModal,
    handleOpenDeleteDialog,
    handleCloseDeleteDialog,
    openDrawer,
    closeDrawer,
    handleFileSelect,
    handleFileDrop,
    handleDragOver,
    handleDragEnter,
    toggleSalesRepSelection,
    handleSalesRepSearchChange
  };
};
