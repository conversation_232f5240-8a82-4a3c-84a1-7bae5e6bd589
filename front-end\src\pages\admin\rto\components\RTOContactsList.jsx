import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUser,
  faPhone,
  faEnvelope,
  faBriefcase,
  faEdit,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";
import { useDeleteRTOContactMutation } from "../../../../services/CompanyAPIService";
import EditContactModal from "./EditContactModal";
import ConfirmationModal from "../../../../components/modal/ConfirmationModal";

const RTOContactsList = ({ contacts, rtoCode, onContactUpdated }) => {
  const [selectedContact, setSelectedContact] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const [deleteContact, { isLoading: isDeleting }] = useDeleteRTOContactMutation();

  // Handle edit contact
  const handleEditClick = (contact) => {
    setSelectedContact(contact);
    setIsEditModalOpen(true);
  };

  // Handle delete contact
  const handleDeleteClick = (contact) => {
    setSelectedContact(contact);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteContact(selectedContact.id).unwrap();
      setIsDeleteModalOpen(false);
      if (onContactUpdated) onContactUpdated();
    } catch (error) {
      console.error("Failed to delete contact:", error);
      alert("Failed to delete contact. Please try again.");
    }
  };

  if (!contacts || contacts.length === 0) {
    return (
      <div className="bg-gray-50 rounded-lg p-8 text-center">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <FontAwesomeIcon icon={faUser} className="text-blue-500 text-xl" />
          </div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">No Contacts Found</h3>
          <p className="text-gray-600 max-w-md">This RTO doesn't have any contacts yet. Add a contact to get started.</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="overflow-x-auto rounded-lg border border-gray-200">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Phone
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Email
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {contacts.map((contact) => (
              <tr key={contact.id} className="hover:bg-gray-50 transition-colors duration-150">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <FontAwesomeIcon icon={faUser} className="text-blue-500" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {contact.firstName} {contact.lastName}
                      </div>
                      {contact.jobTitle && (
                        <div className="text-sm text-gray-500 flex items-center">
                          <FontAwesomeIcon icon={faBriefcase} className="mr-1 text-gray-400" size="xs" />
                          {contact.jobTitle}
                        </div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                    {contact.role || "N/A"}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-700 font-medium">
                    {contact.contactType || "General"}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {contact.phone ? (
                    <div className="flex items-center">
                      <div className="bg-blue-50 p-1 rounded-full mr-2">
                        <FontAwesomeIcon icon={faPhone} className="text-blue-500" size="sm" />
                      </div>
                      <a href={`tel:${contact.phone}`} className="text-blue-600 hover:text-blue-800 font-medium">
                        {contact.phone}
                      </a>
                    </div>
                  ) : (
                    <span className="text-gray-500">N/A</span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {contact.email ? (
                    <div className="flex items-center">
                      <div className="bg-blue-50 p-1 rounded-full mr-2">
                        <FontAwesomeIcon icon={faEnvelope} className="text-blue-500" size="sm" />
                      </div>
                      <a href={`mailto:${contact.email}`} className="text-blue-600 hover:text-blue-800 font-medium truncate max-w-xs">
                        {contact.email}
                      </a>
                    </div>
                  ) : (
                    <span className="text-gray-500">N/A</span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => handleEditClick(contact)}
                      className="bg-blue-50 hover:bg-blue-100 text-blue-600 p-2 rounded-full transition-colors duration-200"
                      title="Edit Contact"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                    <button
                      onClick={() => handleDeleteClick(contact)}
                      className="bg-red-50 hover:bg-red-100 text-red-600 p-2 rounded-full transition-colors duration-200"
                      title="Delete Contact"
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Edit Contact Modal */}
      {selectedContact && (
        <EditContactModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          contact={selectedContact}
          onSuccess={() => {
            setIsEditModalOpen(false);
            if (onContactUpdated) onContactUpdated();
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Contact"
        message={`Are you sure you want to delete ${selectedContact?.firstName} ${selectedContact?.lastName}? This action cannot be undone.`}
        confirmText="Delete"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
        isLoading={isDeleting}
      />
    </div>
  );
};

export default RTOContactsList;
