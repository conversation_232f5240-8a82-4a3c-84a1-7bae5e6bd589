import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowLeft,
  faPlus,
  faTrash,
  faEdit,
} from "@fortawesome/free-solid-svg-icons";
import { jwtDecode } from "jwt-decode";
import { getToken } from "../../../services/LocalStorageService";

// Import new components
import ApplicationHeader from "./components/ApplicationHeader";
import ApplicationTabs from "./components/ApplicationTabs";
import PaymentInfo from "./components/PaymentInfo";
import ApplicationComments from "./components/ApplicationComments";

import {
  useGetSingleApplicationQuery,
  useChangeApplicationStatusMutation,
  useUpdateApplicationQuoteMutation,
  useUpdateApplicationInvoiceMutation,
  useUpdateApplicationPaymentStatusMutation,
  useUpdateApplicationPaidAmountMutation,
  useUpdateApplicationCreatedDateMutation,
  useUpdateApplicantInfoMutation,
  useAddQualificationMutation,
  useRemoveQualificationMutation,
  useAddApplicationCommentMutation,
  useGetApplicationCommentsQuery,
  useUpdateApplicationCommentMutation,
  useDeleteApplicationCommentMutation,
} from "../../../services/CompanyAPIService";

import { useGetQualificationsQuery } from "../../../services/SalesAPIService";
import MultiStepCreateApplicationModal from "../../../components/modal/MultiStepCreateApplicationModal";

// -------------------- Reusable Modal Component --------------------
const Modal = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white w-full max-w-md p-6 rounded-lg shadow-xl relative">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <button
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
            onClick={onClose}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

// -------------------- Modal Contents --------------------

// Update Applicant Info Modal
const UpdateApplicantModal = ({
  isOpen,
  onClose,
  initialData,
  onSave,
}) => {
  const [name, setName] = useState(initialData.name || "");
  const [email, setEmail] = useState(initialData.email || "");
  const [phone, setPhone] = useState(initialData.phone || "");
  const [address, setAddress] = useState(initialData.address || "");

  // Keep fields in sync if initialData changes
  useEffect(() => {
    setName(initialData.name || "");
    setEmail(initialData.email || "");
    setPhone(initialData.phone || "");
    setAddress(initialData.address || "");
  }, [initialData]);

  const handleSave = () => {
    onSave({ name, email, phone, address });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Update Applicant Info">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            placeholder="Enter applicant name"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            placeholder="Enter email address"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
          <input
            type="text"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            placeholder="Enter phone number"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
          <textarea
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            rows="3"
            placeholder="Enter full address"
          ></textarea>
        </div>
      </div>
      <div className="flex justify-end mt-6 space-x-3">
        <button
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
        >
          Save Changes
        </button>
      </div>
    </Modal>
  );
};

// Add Qualification Modal
const AddQualificationModal = ({
  isOpen,
  onClose,
  qualifications,
  onAdd,
}) => {
  const [selectedQualificationId, setSelectedQualificationId] = useState("");
  const [price, setPrice] = useState("");

  const handleSelectChange = (e) => {
    const id = e.target.value;
    setSelectedQualificationId(id);
    const selected = qualifications.find((q) => q.qualificationId === id);
    setPrice(selected ? selected.rplPrice : "");
  };

  const handleAddQualification = () => {
    if (!selectedQualificationId || !price) return;
    const selected = qualifications.find((q) => q.qualificationId === selectedQualificationId);
    if (selected) {
      onAdd({
        qualificationId: selectedQualificationId,
        qualificationName: selected.qualificationName,
        price: parseFloat(price),
      });
      // Reset state after adding
      setSelectedQualificationId("");
      setPrice("");
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={() => { onClose(); setSelectedQualificationId(""); setPrice(""); }} title="Add Qualification">
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Select Qualification
          </label>
          <select
            value={selectedQualificationId}
            onChange={handleSelectChange}
            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
          >
            <option value="">Select a qualification</option>
            {qualifications.map((q) => (
              <option key={q.qualificationId} value={q.qualificationId}>
                {q.qualificationName}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500">$</span>
            </div>
            <input
              type="number"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              className="pl-8 pr-4 py-2 w-full border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              placeholder="0.00"
            />
          </div>
        </div>
      </div>
      <div className="flex justify-end mt-6 space-x-3">
        <button
          onClick={() => { onClose(); setSelectedQualificationId(""); setPrice(""); }}
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleAddQualification}
          className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
        >
          Add Qualification
        </button>
      </div>
    </Modal>
  );
};

// Remove Qualification Modal
const RemoveQualificationModal = ({
  isOpen,
  onClose,
  qualification,
  onRemove,
}) => {
  if (!qualification) return null;
  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Remove Qualification">
      <div className="mb-6 text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8 text-red-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </div>
        <p className="text-gray-700">
          Are you sure you want to remove{" "}
          <span className="font-semibold text-gray-900">{qualification.qualificationName}</span>?
        </p>
        <p className="text-sm text-gray-500 mt-2">
          This action cannot be undone.
        </p>
      </div>
      <div className="flex justify-end space-x-3">
        <button
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={onRemove}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Remove
        </button>
      </div>
    </Modal>
  );
};

// Partial Payment Modal
const PaymentModal = ({ isOpen, onClose, totalPrice, onSubmit }) => {
  const [amount, setAmount] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = () => {
    if (!amount) {
      setError("Please enter a payment amount");
      return;
    }

    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      setError("Please enter a valid payment amount");
      return;
    }

    if (numAmount > totalPrice) {
      setError("Payment amount cannot exceed the total price");
      return;
    }

    onSubmit(amount);
    setAmount("");
    setError("");
  };

  const handleClose = () => {
    onClose();
    setAmount("");
    setError("");
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Process Partial Payment">
      <div className="mb-6">
        <div className="bg-[#F4F5F9] rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center">
            <p className="text-sm font-medium text-gray-600">Total Price</p>
            <p className="text-lg font-semibold text-[#6E39CB]">
              ${totalPrice || 0}
            </p>
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Payment Amount
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500">$</span>
            </div>
            <input
              type="number"
              value={amount}
              onChange={(e) => {
                setAmount(e.target.value);
                setError("");
              }}
              placeholder="0.00"
              className={`pl-8 pr-4 py-2 w-full border ${error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-200 focus:ring-[#6E39CB] focus:border-[#6E39CB]'} rounded-lg focus:outline-none focus:ring-2`}
            />
          </div>
          {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>

        <p className="text-sm text-gray-500 mb-4">
          Enter the amount received as partial payment. This will update the payment status to "PARTIALLY PAID".
        </p>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          onClick={handleClose}
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleSubmit}
          className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
        >
          Process Payment
        </button>
      </div>
    </Modal>
  );
};

// Modal Component for Updating Created Date
const UpdateCreatedDateModal = ({ isOpen, onClose, currentCreatedDate, onSubmit }) => {
  const [dateValue, setDateValue] = useState("");
  const [timeValue, setTimeValue] = useState("");

  useEffect(() => {
    if (isOpen && currentCreatedDate) {
      const date = new Date(currentCreatedDate);
      const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD
      const timeStr = date.toTimeString().split(' ')[0].slice(0, 5); // HH:MM
      setDateValue(dateStr);
      setTimeValue(timeStr);
    }
  }, [isOpen, currentCreatedDate]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (dateValue && timeValue) {
      const combinedDateTime = `${dateValue}T${timeValue}:00`;
      onSubmit(combinedDateTime);
      setDateValue("");
      setTimeValue("");
    }
  };

  const handleClose = () => {
    setDateValue("");
    setTimeValue("");
    onClose();
  };

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Update Created Date">
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-gray-600">
            Update the created date and time for this application.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date
            </label>
            <input
              type="date"
              value={dateValue}
              onChange={(e) => setDateValue(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Time
            </label>
            <input
              type="time"
              value={timeValue}
              onChange={(e) => setTimeValue(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              required
            />
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-[#5E2CB8] transition-colors"
            >
              Update Date
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

// Reference Modal (Quote / Invoice)
const ReferenceModal = ({ isOpen, onClose, modalType, onSubmit }) => {
  const [refNumber, setRefNumber] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = () => {
    if (!refNumber.trim()) {
      setError("Please enter a reference number");
      return;
    }

    onSubmit(refNumber);
    setRefNumber("");
    setError("");
  };

  const handleClose = () => {
    onClose();
    setRefNumber("");
    setError("");
  };

  const isQuote = modalType === "quote";
  const title = isQuote ? "Enter Quote Reference Number" : "Enter Invoice Reference Number";
  const icon = isQuote ? (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  ) : (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
    </svg>
  );

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title={title}>
      <div className="mb-6">
        <div className="flex items-center mb-4">
          <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
            {icon}
          </div>
          <p className="text-gray-600">
            {isQuote
              ? "Enter the quote reference number to update the application status to 'QUOTE RAISED'."
              : "Enter the invoice reference number to update the application status to 'INVOICE RAISED'."}
          </p>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {isQuote ? "Quote Reference" : "Invoice Reference"}
          </label>
          <input
            type="text"
            value={refNumber}
            onChange={(e) => {
              setRefNumber(e.target.value);
              setError("");
            }}
            placeholder={isQuote ? "e.g. Q-12345" : "e.g. INV-12345"}
            className={`px-3 py-2 w-full border ${error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-200 focus:ring-[#6E39CB] focus:border-[#6E39CB]'} rounded-lg focus:outline-none focus:ring-2`}
          />
          {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          onClick={handleClose}
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleSubmit}
          className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
        >
          Save Reference
        </button>
      </div>
    </Modal>
  );
};

// Define keyframe animation for toast
const fadeInDownKeyframes = `
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translate3d(0, -20px, 0);
    }
    to {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
  }
`;

// Toast Notification Component
const Toast = ({ message, type, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 5000);
    return () => clearTimeout(timer);
  }, [onClose]);

  const bgColor = type === "success" ? "bg-green-50 border-green-200" :
                 type === "error" ? "bg-red-50 border-red-200" :
                 "bg-blue-50 border-blue-200";

  const textColor = type === "success" ? "text-green-800" :
                   type === "error" ? "text-red-800" :
                   "text-blue-800";

  const icon = type === "success" ? (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ) : type === "error" ? (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ) : (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );

  // Add animation styles
  const animationStyles = {
    animation: 'fadeInDown 0.5s ease-out forwards',
  };

  // Add keyframes to document
  useEffect(() => {
    // Create style element
    const styleEl = document.createElement('style');
    styleEl.appendChild(document.createTextNode(fadeInDownKeyframes));
    document.head.appendChild(styleEl);

    // Clean up
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  return (
    <div
      className={`fixed top-4 right-4 flex items-center p-4 mb-4 rounded-lg border ${bgColor} z-50 shadow-md max-w-md`}
      role="alert"
      style={animationStyles}
    >
      <div className="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 rounded-lg">
        {icon}
      </div>
      <div className={`ml-3 text-sm font-medium ${textColor}`}>{message}</div>
      <button
        type="button"
        className={`ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex h-8 w-8 ${textColor} hover:bg-gray-100`}
        onClick={onClose}
        aria-label="Close"
      >
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path>
        </svg>
      </button>
    </div>
  );
};

// -------------------- Main Component --------------------
const ApplicationProfile = () => {
  const { applicationId } = useParams();
  const navigate = useNavigate();

  // API Data fetching
  const { data: qualificationsData = [] } = useGetQualificationsQuery();
  const {
    data: application,
    isLoading,
    error,
    refetch,
  } = useGetSingleApplicationQuery(applicationId);

  // API mutations
  const [changeApplicationStatus] = useChangeApplicationStatusMutation();
  const [updateApplicationQuote] = useUpdateApplicationQuoteMutation();
  const [updateApplicationInvoice] = useUpdateApplicationInvoiceMutation();
  const [updateApplicationPaymentStatus] = useUpdateApplicationPaymentStatusMutation();
  const [updateApplicationPaidAmount] = useUpdateApplicationPaidAmountMutation();
  const [updateApplicationCreatedDate] = useUpdateApplicationCreatedDateMutation();
  const [updateApplicantInfo] = useUpdateApplicantInfoMutation();
  const [addQualification] = useAddQualificationMutation();
  const [removeQualification] = useRemoveQualificationMutation();
  const [addApplicationComment] = useAddApplicationCommentMutation();
  const [updateApplicationComment] = useUpdateApplicationCommentMutation();
  const [deleteApplicationComment] = useDeleteApplicationCommentMutation();
  const { data: comments = [], refetch: refetchComments } = useGetApplicationCommentsQuery(applicationId);

  // Local UI states
  const [activeTab, setActiveTab] = useState("qualifications");
  const [selectedStatus, setSelectedStatus] = useState("DOCUMENT_PENDING");
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState("PENDING");

  // Toast notification state
  const [toast, setToast] = useState({ visible: false, message: "", type: "success" });
  const showToast = (message, type = "success") => {
    setToast({ visible: true, message, type });
  };
  const hideToast = () => {
    setToast({ ...toast, visible: false });
  };

  // Modal states
  const [showUpdateInfoModal, setShowUpdateInfoModal] = useState(false);
  const [showAddQualModal, setShowAddQualModal] = useState(false);
  const [showUpdateCreatedDateModal, setShowUpdateCreatedDateModal] = useState(false);
  const [showRemoveQualModal, setShowRemoveQualModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showRefModal, setShowRefModal] = useState(false);
  const [modalType, setModalType] = useState(""); // "quote" or "invoice"

  // Additional state for removal
  const [qualificationToRemove, setQualificationToRemove] = useState(null);

  // Comment-related states
  const [newComment, setNewComment] = useState("");
  const [editingComment, setEditingComment] = useState(null);
  const [editCommentText, setEditCommentText] = useState("");

  // State for qualification view and sorting
  const [qualificationView, setQualificationView] = useState("card"); // "card" or "table"
  const [sortBy, setSortBy] = useState("name"); // "name", "price-high", "price-low", "id"

  // Role-based permissions
  const [userRole, setUserRole] = useState("");
  const [canEditStatus, setCanEditStatus] = useState(true);
  const [canEditPayments, setCanEditPayments] = useState(true);

  // Determine user role and permissions
  useEffect(() => {
    const token = getToken();
    if (token) {
      try {
        const decodedToken = jwtDecode(token);
        const roles = decodedToken.roles || [];
        if (roles.includes("ROLE_SALES")) {
          setUserRole("SALES");
          setCanEditStatus(false);
          setCanEditPayments(false);
        } else if (roles.includes("ROLE_ADMIN")) {
          setUserRole("ADMIN");
          setCanEditStatus(true);
          setCanEditPayments(true);
        } else if (roles.includes("ROLE_OPERATIONS")) {
          setUserRole("OPERATIONS");
          setCanEditStatus(true);
          setCanEditPayments(true);
        }
      } catch (error) {
        console.error("Error decoding token:", error);
      }
    }
  }, []);

  // Pre-fill values on data load
  useEffect(() => {
    if (application) {
      setSelectedStatus(application.status);
      setSelectedPaymentStatus(application.paidStatus || "PENDING");
    }
  }, [application]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="w-16 h-16 border-4 border-[#6E39CB] border-t-transparent rounded-full animate-spin mb-4"></div>
        <p className="text-lg font-medium text-gray-700">Loading application details...</p>
        <p className="text-sm text-gray-500 mt-2">This may take a moment</p>
      </div>
    );
  }

  if (error || !application) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-red-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 text-center mb-2">Error Loading Application</h2>
          <p className="text-gray-600 text-center mb-6">We couldn't load the application details. Please try again.</p>
          <div className="flex justify-center">
            <button
              onClick={() => navigate(-1)}
              className="mr-3 px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Go Back
            </button>
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Derived values
  const duePayment = (application.totalPrice || 0) - (application.paidAmount || 0);
  const soldQualifications = application.soldQualifications || [];

  // Sort qualifications based on selected sort option
  const sortedQualifications = [...soldQualifications].sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.qualificationName.localeCompare(b.qualificationName);
      case "price-high":
        return b.price - a.price;
      case "price-low":
        return a.price - b.price;
      case "id":
        return a.qualificationId.localeCompare(b.qualificationId);
      default:
        return 0;
    }
  });

  // ---------- Handlers ----------
  const handleStatusChange = async (e) => {
    const newStatus = e.target.value;
    // If the new status requires a reference modal
    if (newStatus === "QUOTE_RAISED" || newStatus === "INVOICE_RAISED") {
      setModalType(newStatus === "QUOTE_RAISED" ? "quote" : "invoice");
      setShowRefModal(true);
      return;
    }
    try {
      await changeApplicationStatus({
        applicationId: application.applicationId,
        status: newStatus,
      }).unwrap();
      setSelectedStatus(newStatus);
      refetch();
      showToast(`Application status updated to ${newStatus.replace("_", " ").toLowerCase()}.`);
    } catch (err) {
      console.error("Error updating status:", err);
      showToast("Unable to update application status.", "error");
    }
  };

  const handlePaymentStatusChange = async (e) => {
    const newPaymentStatus = e.target.value;
    if (newPaymentStatus === "PARTIALLY_PAID") {
      setShowPaymentModal(true);
      return;
    }
    try {
      await updateApplicationPaymentStatus({
        applicationId: application.applicationId,
        status: newPaymentStatus,
      }).unwrap();
      setSelectedPaymentStatus(newPaymentStatus);
      refetch();
      showToast(`Payment status updated to ${newPaymentStatus.replace("_", " ").toLowerCase()}.`);
    } catch (err) {
      console.error("Error updating payment status:", err);
      showToast("Unable to update payment status.", "error");
    }
  };

  const handlePartialPaymentSubmit = async (paymentAmount) => {
    try {
      await updateApplicationPaidAmount({
        applicationId: application.applicationId,
        paid: paymentAmount,
      }).unwrap();
      await updateApplicationPaymentStatus({
        applicationId: application.applicationId,
        status: "PARTIALLY_PAID",
      }).unwrap();
      setShowPaymentModal(false);
      refetch();
      showToast(`Payment of $${paymentAmount} processed successfully.`);
    } catch (err) {
      console.error("Error processing partial payment:", err);
      showToast("Unable to process partial payment.", "error");
    }
  };

  const handleReferenceSubmit = async (referenceNumber) => {
    if (!referenceNumber) return;
    try {
      if (modalType === "quote") {
        await updateApplicationQuote({
          applicationId: application.applicationId,
          quote: referenceNumber,
        }).unwrap();
        // Update local state to show changes immediately
        application.quote = referenceNumber;
        application.quoteRefNum = referenceNumber;
        showToast(`Quote reference number ${referenceNumber} saved successfully.`);
      } else if (modalType === "invoice") {
        await updateApplicationInvoice({
          applicationId: application.applicationId,
          invoice: referenceNumber,
        }).unwrap();
        // Update local state to show changes immediately
        application.invoice = referenceNumber;
        application.invoiceRefNum = referenceNumber;
        showToast(`Invoice reference number ${referenceNumber} saved successfully.`);
      }
      setShowRefModal(false);
      refetch();
    } catch (err) {
      console.error("Error updating reference number:", err);
      showToast("Unable to update reference number.", "error");
    }
  };

  const handleApplicantUpdate = async (updatedInfo) => {
    try {
      await updateApplicantInfo({
        applicationId: application.applicationId,
        applicantName: updatedInfo.name,
        applicantEmail: updatedInfo.email,
        applicantPhone: updatedInfo.phone,
        applicantAddress: updatedInfo.address,
      }).unwrap();
      setShowUpdateInfoModal(false);
      refetch();
      showToast("Applicant information updated successfully.");
    } catch (err) {
      console.error("Error updating applicant info:", err);
      showToast("Unable to update applicant information.", "error");
    }
  };

  const handleCreatedDateUpdate = async (newCreatedDate) => {
    try {
      await updateApplicationCreatedDate({
        applicationId: application.applicationId,
        createdDate: newCreatedDate,
      }).unwrap();
      setShowUpdateCreatedDateModal(false);
      refetch();
      showToast("Application created date updated successfully.");
    } catch (err) {
      console.error("Error updating created date:", err);
      showToast("Unable to update created date.", "error");
    }
  };

  const handleAddQualification = async (qualification) => {
    try {
      await addQualification({
        applicationId: application.applicationId,
        qualificationId: qualification.qualificationId,
        qualificationName: qualification.qualificationName,
        price: qualification.price,
      }).unwrap();
      setShowAddQualModal(false);
      refetch();
      showToast(`Qualification ${qualification.qualificationName} added successfully.`);
    } catch (err) {
      console.error("Error adding qualification:", err);
      showToast("Unable to add qualification.", "error");
    }
  };

  const handleRemoveQualificationConfirm = async () => {
    if (!qualificationToRemove) return;
    try {
      await removeQualification({
        applicationId: application.applicationId,
        qualificationId: qualificationToRemove.qualificationId,
      }).unwrap();
      const qualName = qualificationToRemove.qualificationName;
      setShowRemoveQualModal(false);
      setQualificationToRemove(null);
      refetch();
      showToast(`Qualification ${qualName} removed successfully.`);
    } catch (err) {
      console.error("Error removing qualification:", err);
      showToast("Unable to remove qualification.", "error");
    }
  };

  // Comment handlers
  const handleAddComment = async () => {
    if (!newComment.trim()) return;
    try {
      await addApplicationComment({
        applicationId: application.applicationId,
        comment: newComment,
      }).unwrap();
      setNewComment("");
      refetchComments();
      showToast("Comment added successfully.");
    } catch (err) {
      console.error("Error adding comment:", err);
      showToast("Unable to add comment.", "error");
    }
  };

  const handleEditComment = (comment) => {
    setEditingComment(comment.id);
    setEditCommentText(comment.content);
  };

  const handleUpdateComment = async () => {
    if (!editCommentText.trim()) return;
    try {
      await updateApplicationComment({
        applicationId: application.applicationId,
        commentId: editingComment,
        content: editCommentText,
      }).unwrap();
      setEditingComment(null);
      setEditCommentText("");
      refetchComments();
      showToast("Comment updated successfully.");
    } catch (err) {
      console.error("Error updating comment:", err);
      showToast("Unable to update comment.", "error");
    }
  };

  const handleDeleteComment = async (commentId) => {
    if (!window.confirm("Are you sure you want to delete this comment?")) return;
    try {
      await deleteApplicationComment({
        applicationId: application.applicationId,
        commentId,
      }).unwrap();
      refetchComments();
      showToast("Comment deleted successfully.");
    } catch (err) {
      console.error("Error deleting comment:", err);
      showToast("Unable to delete comment.", "error");
    }
  };

  const cancelEdit = () => {
    setEditingComment(null);
    setEditCommentText("");
  };

  // -------------------- Render Layout --------------------
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Toast Notification */}
      {toast.visible && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={hideToast}
        />
      )}
      {/* Header */}
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate(-1)}
          className="mr-4 p-2 rounded-full text-gray-500 hover:bg-gray-100 transition-colors"
        >
          <FontAwesomeIcon icon={faArrowLeft} />
        </button>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Application Details</h1>
          <p className="text-sm text-gray-500">View and manage application information</p>
        </div>
      </div>

      {/* Applicant Info & Summary */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <div className="flex flex-col md:flex-row justify-between">
          {/* Applicant Info Card */}
          <div className="space-y-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {application.applicantName || "N/A"}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Phone */}
                <div className="flex items-start">
                  <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Phone</p>
                    <p className="text-sm font-medium">{application.applicantPhone || "N/A"}</p>
                  </div>
                </div>
                {/* Email */}
                <div className="flex items-start">
                  <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Email</p>
                    <p className="text-sm font-medium">{application.applicantEmail || "N/A"}</p>
                  </div>
                </div>
                {/* Address */}
                <div className="flex items-start md:col-span-2">
                  <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Address</p>
                    <p className="text-sm font-medium">{application.applicantAddress || "N/A"}</p>
                  </div>
                </div>
                {/* Created Date */}
                <div className="flex items-start">
                  <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a1 1 0 012 0v4m0 0V3a1 1 0 012 0v4m0 0h4m-4 0H8m0 0v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2H10a2 2 0 00-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Created On</p>
                    <p className="text-sm font-medium">
                      {application.createdAt
                        ? new Date(application.createdAt).toLocaleDateString("en-GB", {
                            day: "numeric",
                            month: "short",
                            year: "numeric",
                          })
                        : "N/A"}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => setShowUpdateInfoModal(true)}
                className="inline-flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors shadow-sm"
              >
                <FontAwesomeIcon icon={faEdit} className="h-4 w-4 mr-2" />
                Update Applicant Info
              </button>
              <button
                onClick={() => setShowUpdateCreatedDateModal(true)}
                className="inline-flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors shadow-sm"
              >
                <FontAwesomeIcon icon={faEdit} className="h-4 w-4 mr-2" />
                Update Created Date
              </button>
            </div>
          </div>

          {/* Summary & Actions */}
          <div className="mt-6 md:mt-0 md:ml-6 md:w-1/3">
            <div className="bg-[#F4F5F9] rounded-lg p-4 mb-4">
              <div className="flex justify-between items-center mb-3">
                <p className="text-sm font-medium text-gray-600">Total Price</p>
                <p className="text-lg font-semibold text-[#6E39CB]">
                  ${application.totalPrice || 0}
                </p>
              </div>
              <div className="flex justify-between items-center mb-3">
                <p className="text-sm font-medium text-gray-600">Paid Amount</p>
                <p className="text-lg font-semibold text-green-600">
                  ${application.paidAmount || 0}
                </p>
              </div>
              <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                <p className="text-sm font-medium text-gray-600">Due Payment</p>
                <p className="text-lg font-semibold text-red-600">
                  ${duePayment.toFixed(2)}
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                <select
                  value={selectedPaymentStatus}
                  onChange={handlePaymentStatusChange}
                  disabled={!canEditPayments}
                  className={`w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] ${!canEditPayments ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                >
                  <option value="PENDING">PENDING</option>
                  <option value="PARTIALLY_PAID">PARTIALLY PAID</option>
                  <option value="FULLY_PAID">FULLY PAID</option>
                  <option value="INVOICE_EXPIRED">INVOICE EXPIRED</option>
                </select>
                {!canEditPayments && (
                  <p className="text-xs text-gray-500 mt-1">View only - Contact admin to modify payment status</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Application Status</label>
                <select
                  value={selectedStatus}
                  onChange={handleStatusChange}
                  disabled={!canEditStatus}
                  className={`w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] ${!canEditStatus ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                >
                  <option value="DOCUMENT_PENDING">DOCUMENT PENDING</option>
                  <option value="QUOTE_RAISED">QUOTE RAISED</option>
                  <option value="INVOICE_RAISED">INVOICE RAISED</option>
                  <option value="IN_PROGRESS">IN PROGRESS</option>
                  <option value="SOFT_COPY_READY">SOFT COPY READY</option>
                  <option value="SOFT_COPY_SENT">SOFT COPY SENT</option>
                  <option value="HARD_COPY_READY">HARD COPY READY</option>
                  <option value="HARD_COPY_SENT">HARD COPY SENT</option>
                  <option value="FALLOUT">FALLOUT</option>
                </select>
                {!canEditStatus && (
                  <p className="text-xs text-gray-500 mt-1">View only - Contact admin to modify application status</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <ApplicationTabs
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        comments={comments}
      />

      {/* Tab Content */}
      {activeTab === "qualifications" && (
        <div
          className="p-6"
          role="tabpanel"
          id="qualifications-panel"
          aria-labelledby="qualifications-tab"
        >
          <div className="flex flex-wrap items-center justify-between mb-6">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-1">Sold Qualifications</h2>
              <p className="text-sm text-gray-500">
                Manage qualifications associated with this application
              </p>
            </div>
            <button
              onClick={() => setShowAddQualModal(true)}
              className="inline-flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors shadow-sm"
            >
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Add Qualification
            </button>
          </div>

          {/* Qualification Progress */}
          <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <div className="bg-[#F4F5F9] p-2 rounded-full mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h5 class="text-md font-medium text-gray-900">Qualification Summary</h5>
              </div>
              <div className="flex items-center space-x-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {soldQualifications.length} Qualification{soldQualifications.length !== 1 ? 's' : ''}
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  ${soldQualifications.reduce((total, qual) => total + qual.price, 0).toFixed(2)} Total
                </span>
              </div>
            </div>



            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-[#F4F5F9] rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-sm font-medium text-gray-700">Total Value</p>
                </div>
                <p className="text-2xl font-bold text-[#6E39CB]">
                  ${soldQualifications.reduce((total, qual) => total + qual.price, 0).toFixed(2)}
                </p>
                <p className="text-xs text-gray-500 mt-1">Combined qualification price</p>
              </div>

              <div className="bg-[#F4F5F9] rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                  </svg>
                  <p className="text-sm font-medium text-gray-700">Average Price</p>
                </div>
                <p className="text-2xl font-bold text-[#6E39CB]">
                  ${soldQualifications.length > 0
                    ? (soldQualifications.reduce((total, qual) => total + qual.price, 0) / soldQualifications.length).toFixed(2)
                    : '0.00'}
                </p>
                <p className="text-xs text-gray-500 mt-1">Per qualification</p>
              </div>

              <div className="bg-[#F4F5F9] rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  <p className="text-sm font-medium text-gray-700">Qualification Count</p>
                </div>
                <p className="text-2xl font-bold text-[#6E39CB]">
                  {soldQualifications.length}
                </p>
                <p className="text-xs text-gray-500 mt-1">Total qualifications</p>
              </div>
            </div>
          </div>

          {soldQualifications.length === 0 ? (
            <div className="bg-white rounded-lg border border-gray-100 p-8 text-center">
              <div className="flex flex-col items-center justify-center">
                <div className="bg-[#F4F5F9] p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Qualifications Found</h3>
                <p className="text-gray-500 mb-4">This application doesn't have any qualifications yet.</p>
                <button
                  onClick={() => setShowAddQualModal(true)}
                  className="inline-flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors shadow-sm"
                >
                  <FontAwesomeIcon icon={faPlus} className="mr-2" />
                  Add First Qualification
                </button>
              </div>
            </div>
          ) : (
            <div>
              {/* Filter and Sort Controls */}
              <div className="flex flex-wrap items-center justify-between mb-4 bg-white rounded-lg border border-gray-100 shadow-sm p-4">
                <div className="flex items-center space-x-2 mb-2 sm:mb-0">
                  <span className="text-sm font-medium text-gray-700">View:</span>
                  <button
                    className={`inline-flex items-center px-3 py-1.5 ${qualificationView === "card" ? "bg-[#6E39CB] text-white" : "bg-gray-100 text-gray-700 hover:bg-gray-200"} rounded-md transition-colors`}
                    onClick={() => setQualificationView("card")}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                    </svg>
                    Cards
                  </button>
                  <button
                    className={`inline-flex items-center px-3 py-1.5 ${qualificationView === "table" ? "bg-[#6E39CB] text-white" : "bg-gray-100 text-gray-700 hover:bg-gray-200"} rounded-md transition-colors`}
                    onClick={() => setQualificationView("table")}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    Table
                  </button>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-700">Sort by:</span>
                  <select
                    className="text-sm border border-gray-200 rounded-md px-2 py-1.5 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                  >
                    <option value="name">Name</option>
                    <option value="price-high">Price (High to Low)</option>
                    <option value="price-low">Price (Low to High)</option>
                    <option value="id">Certificate ID</option>
                  </select>
                </div>
              </div>

              {/* Qualification Cards or Table */}
              {qualificationView === "card" ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {sortedQualifications.map((q, index) => (
                  <div key={index} className="bg-white rounded-lg border border-gray-100 shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div className="border-b border-gray-100 bg-[#F4F5F9] px-6 py-4 flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="bg-white rounded-full h-10 w-10 flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                          <span className="text-[#6E39CB] text-lg font-medium">
                            {q.qualificationName ? q.qualificationName.charAt(0).toUpperCase() : "?"}
                          </span>
                        </div>
                        <div>
                          <div className="flex items-center">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                              {q.qualificationId}
                            </span>

                          </div>
                        </div>
                      </div>
                      <div>
                        <span className="text-lg font-bold text-[#6E39CB]">${q.price.toFixed(2)}</span>
                      </div>
                    </div>

                    <div className="p-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-3">{q.qualificationName}</h3>

                      <div className="mb-4">
                        <p className="text-xs text-gray-500 mb-1">Certificate ID</p>
                        <p className="text-sm font-medium">{q.qualificationId}</p>
                      </div>

                      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Price</p>
                          <p className="text-lg font-semibold text-[#6E39CB]">${q.price.toFixed(2)}</p>
                        </div>
                        <button
                          onClick={() => {
                            setQualificationToRemove(q);
                            setShowRemoveQualModal(true);
                          }}
                          className="flex items-center justify-center text-red-600 hover:text-red-800 px-3 py-1.5 border border-red-200 rounded-lg hover:bg-red-50 transition-colors"
                          title="Remove Qualification"
                        >
                          <FontAwesomeIcon icon={faTrash} className="mr-2" />
                          Remove
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-gray-100 shadow-sm overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-[#F4F5F9]">
                        <tr>
                          <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Qualification Name
                          </th>
                          <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Certificate ID
                          </th>
                          <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Price
                          </th>
                          <th className="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Action
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {sortedQualifications.map((q, index) => (
                          <tr key={index} className="hover:bg-[#F4F5F9] transition-colors">
                            <td className="px-6 py-4">
                              <div className="flex items-center">
                                <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                                  <span className="text-[#6E39CB] font-medium">
                                    {q.qualificationName ? q.qualificationName.charAt(0).toUpperCase() : "?"}
                                  </span>
                                </div>
                                <span className="font-medium text-gray-900">{q.qualificationName}</span>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {q.qualificationId}
                              </span>
                            </td>
                            <td className="px-6 py-4 text-right">
                              <span className="font-medium text-[#6E39CB]">${q.price.toFixed(2)}</span>
                            </td>
                            <td className="px-6 py-4 text-center">
                              <button
                                onClick={() => {
                                  setQualificationToRemove(q);
                                  setShowRemoveQualModal(true);
                                }}
                                className="inline-flex items-center text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-50"
                                title="Remove Qualification"
                              >
                                <FontAwesomeIcon icon={faTrash} />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Add More Button */}
              <div className="mt-6 text-center">
                <button
                  onClick={() => setShowAddQualModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-[#6E39CB] text-[#6E39CB] rounded-lg hover:bg-[#F4F5F9] transition-colors"
                >
                  <FontAwesomeIcon icon={faPlus} className="mr-2" />
                  Add Another Qualification
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === "paymentInfo" && (
        <PaymentInfo
          application={application}
          selectedPaymentStatus={selectedPaymentStatus}
          duePayment={duePayment}
          canEditStatus={canEditStatus}
          setModalType={setModalType}
          setShowRefModal={setShowRefModal}
        />
      )}

      {activeTab === "leadInfo" && (
        <div
          className="p-6"
          role="tabpanel"
          id="leadInfo-panel"
          aria-labelledby="leadInfo-tab"
        >
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-1">Lead Information</h2>
            <p className="text-sm text-gray-500">
              Details about the lead associated with this application
            </p>
          </div>
          {application.lead ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Lead Profile Card */}
              <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 lg:col-span-1">
                <div className="flex flex-col items-center text-center mb-6">
                  <div className="bg-[#F4F5F9] rounded-full h-24 w-24 flex items-center justify-center mb-4">
                    <span className="text-[#6E39CB] text-3xl font-medium">
                      {application.lead.leadName ? application.lead.leadName.charAt(0).toUpperCase() : "?"}
                    </span>
                  </div>
                  <h3 className="text-xl font-medium text-gray-900">{application.lead.leadName}</h3>
                  <p className="text-sm text-gray-500 mt-1">{application.lead.companyName !== "N/A" ? application.lead.companyName : "Individual Lead"}</p>

                  <div className="mt-4 w-full">
                    <div className="flex items-center justify-center space-x-3">
                      <a
                        href={`mailto:${application.lead.email}`}
                        className="bg-[#F4F5F9] p-2 rounded-full hover:bg-[#6E39CB] hover:text-white transition-colors"
                        title="Send Email"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </a>
                      <a
                        href={`tel:${application.lead.phone}`}
                        className="bg-[#F4F5F9] p-2 rounded-full hover:bg-[#6E39CB] hover:text-white transition-colors"
                        title="Call"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                      </a>
                      <a
                        href={`https://maps.google.com/?q=${encodeURIComponent(application.lead.address)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-[#F4F5F9] p-2 rounded-full hover:bg-[#6E39CB] hover:text-white transition-colors"
                        title="View on Map"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>

                <div className="border-t border-gray-100 pt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Lead Type</h4>
                  <div className="bg-[#F4F5F9] rounded-lg p-3 text-center">
                    <span className="text-sm font-medium">
                      {application.lead.companyName !== "N/A" ? "Corporate" : "Individual"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Lead Details Card */}
              <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 lg:col-span-2">
                <h5 class="text-md font-medium text-gray-900 mb-4 pb-2 border-b border-gray-100">Contact Information</h5>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Contact Details */}
                  <div>
                    <div className="space-y-4">
                      <div className="flex items-start">
                        <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Phone</p>
                          <p className="text-sm font-medium">{application.lead.phone}</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Email</p>
                          <p className="text-sm font-medium">{application.lead.email}</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Address</p>
                          <p className="text-sm font-medium">{application.lead.address}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Lead Metrics */}
                  <div>
                    <div className="grid grid-cols-1 gap-4">
                      <div className="bg-[#F4F5F9] rounded-lg p-4">
                        <p className="text-xs text-gray-500 mb-1">Lead Status</p>
                        <div className="flex items-center">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                            Converted
                          </span>
                          <p className="text-sm font-medium">Application Created</p>
                        </div>
                      </div>



                      <div className="bg-[#F4F5F9] rounded-lg p-4">
                        <p className="text-xs text-gray-500 mb-1">Lead Source</p>
                        <p className="text-sm font-medium">
                          {application.lead.companyName !== "N/A" ? "Corporate Partner" : "Direct Inquiry / Sales Rep"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-8 text-center">
              <div className="flex flex-col items-center justify-center">
                <div className="bg-[#F4F5F9] p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Lead Information</h3>
                <p className="text-gray-500 mb-4">This application is not associated with any lead.</p>
                <button
                  onClick={() => navigate(`/admin/leads`)}
                  className="inline-flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors shadow-sm"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Go to Leads
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === "createdBy" && (
        <div
          className="p-6"
          role="tabpanel"
          id="createdBy-panel"
          aria-labelledby="createdBy-tab"
        >
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-1">Created By</h2>
            <p className="text-sm text-gray-500">
              Information about the user who created this application
            </p>
          </div>
          {application.createdBy ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* User Profile Card */}
              <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 lg:col-span-1">
                <div className="flex flex-col items-center text-center">
                  <div className="bg-[#F4F5F9] rounded-full h-24 w-24 flex items-center justify-center mb-4">
                    <span className="text-[#6E39CB] text-3xl font-medium">
                      {application.createdBy.fullName ? application.createdBy.fullName.charAt(0).toUpperCase() : "?"}
                    </span>
                  </div>
                  <h3 className="text-xl font-medium text-gray-900">{application.createdBy.fullName}</h3>
                  <p className="text-sm text-gray-500 mt-1">@{application.createdBy.username}</p>

                  <div className="mt-4 mb-6">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      application.createdBy.role === "ADMIN" ? "bg-purple-100 text-purple-800" :
                      application.createdBy.role === "MANAGER" ? "bg-blue-100 text-blue-800" :
                      application.createdBy.role === "CONSULTANT" ? "bg-green-100 text-green-800" :
                      "bg-gray-100 text-gray-800"
                    }`}>
                      {application.createdBy.role}
                    </span>
                  </div>

                  <div className="w-full">
                    <div className="flex items-center justify-center space-x-3">
                      <a
                        href={`mailto:${application.createdBy.email}`}
                        className="bg-[#F4F5F9] p-2 rounded-full hover:bg-[#6E39CB] hover:text-white transition-colors"
                        title="Send Email"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </a>
                      <a
                        href={`tel:${application.createdBy.phone}`}
                        className="bg-[#F4F5F9] p-2 rounded-full hover:bg-[#6E39CB] hover:text-white transition-colors"
                        title="Call"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* User Details Card */}
              <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 lg:col-span-2">
                <h5 class="text-md font-medium text-gray-900 mb-4 pb-2 border-b border-gray-100">Staff Information</h5>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Contact Details */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Contact Information</h4>
                    <div className="space-y-4">
                      <div className="flex items-start">
                        <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Email</p>
                          <p className="text-sm font-medium">{application.createdBy.email}</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Phone</p>
                          <p className="text-sm font-medium">{application.createdBy.phone}</p>
                        </div>
                      </div>
                      <div className="flex items-start">
                        <div className="bg-[#F4F5F9] p-2 rounded-md mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Address</p>
                          <p className="text-sm font-medium">{application.createdBy.address}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Staff Details */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Staff Details</h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div className="bg-[#F4F5F9] rounded-lg p-4">
                        <p className="text-xs text-gray-500 mb-1">Role</p>
                        <div className="flex items-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            application.createdBy.role === "ADMIN" ? "bg-purple-100 text-purple-800" :
                            application.createdBy.role === "MANAGER" ? "bg-blue-100 text-blue-800" :
                            application.createdBy.role === "CONSULTANT" ? "bg-green-100 text-green-800" :
                            "bg-gray-100 text-gray-800"
                          } mr-2`}>
                            {application.createdBy.role}
                          </span>
                          <p className="text-sm font-medium">
                            {application.createdBy.role === "ADMIN" ? "Administrator" :
                             application.createdBy.role === "SALES" ? "Sales Agent" :
                             application.createdBy.role === "OPERATIONS" ? "Operations Staff" : "Staff"}
                          </p>
                        </div>
                      </div>

                      <div className="bg-[#F4F5F9] rounded-lg p-4">
                        <p className="text-xs text-gray-500 mb-1">Gender</p>
                        <p className="text-sm font-medium">{application.createdBy.gender}</p>
                      </div>

                      <div className="bg-[#F4F5F9] rounded-lg p-4">
                        <p className="text-xs text-gray-500 mb-1">Weekly Target</p>
                        <p className="text-sm font-medium">
                          {application.createdBy.weeklyTarget > 0 ?
                            `${application.createdBy.weeklyTarget} applications` :
                            "No target set"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t border-gray-100">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Application Creation</h4>
                  <div className="bg-[#F4F5F9] rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="bg-blue-100 p-2 rounded-full mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Created On</p>
                          <p className="text-sm font-medium">
                            {new Date(application.createdAt).toLocaleDateString()} at {new Date(application.createdAt).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => setShowUpdateCreatedDateModal(true)}
                        className="text-gray-400 hover:text-gray-600 p-1"
                        title="Edit Created Date"
                      >
                        <FontAwesomeIcon icon={faEdit} size="sm" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-8 text-center">
              <div className="flex flex-col items-center justify-center">
                <div className="bg-[#F4F5F9] p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Creator Information</h3>
                <p className="text-gray-500 mb-4">Information about who created this application is not available.</p>
                <button
                  onClick={() => navigate(`/admin/employees`)}
                  className="inline-flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors shadow-sm"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  View Employees
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === "comments" && (
        <ApplicationComments
          comments={comments}
          newComment={newComment}
          setNewComment={setNewComment}
          editingComment={editingComment}
          editCommentText={editCommentText}
          setEditCommentText={setEditCommentText}
          handleAddComment={handleAddComment}
          handleEditComment={handleEditComment}
          handleUpdateComment={handleUpdateComment}
          handleDeleteComment={handleDeleteComment}
          cancelEdit={cancelEdit}
        />
      )}

      {/* -------------------- Modals -------------------- */}
      {/* Update Applicant Info Modal */}
      <UpdateApplicantModal
        isOpen={showUpdateInfoModal}
        onClose={() => setShowUpdateInfoModal(false)}
        initialData={{
          name: application.applicantName,
          email: application.applicantEmail,
          phone: application.applicantPhone,
          address: application.applicantAddress,
        }}
        onSave={handleApplicantUpdate}
      />

      {/* Add Qualification Modal */}
      <AddQualificationModal
        isOpen={showAddQualModal}
        onClose={() => setShowAddQualModal(false)}
        qualifications={qualificationsData}
        onAdd={handleAddQualification}
      />

      {/* Remove Qualification Modal */}
      <RemoveQualificationModal
        isOpen={showRemoveQualModal}
        onClose={() => setShowRemoveQualModal(false)}
        qualification={qualificationToRemove}
        onRemove={handleRemoveQualificationConfirm}
      />

      {/* Partial Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        totalPrice={application.totalPrice}
        onSubmit={handlePartialPaymentSubmit}
      />

      {/* Reference Modal for Quote/Invoice */}
      <ReferenceModal
        isOpen={showRefModal}
        onClose={() => setShowRefModal(false)}
        modalType={modalType}
        onSubmit={handleReferenceSubmit}
      />

      {/* Update Created Date Modal */}
      <UpdateCreatedDateModal
        isOpen={showUpdateCreatedDateModal}
        onClose={() => setShowUpdateCreatedDateModal(false)}
        currentCreatedDate={application.createdAt}
        onSubmit={handleCreatedDateUpdate}
      />
    </div>
  );
};

export default ApplicationProfile;
