import { jwtDecode } from "jwt-decode";
import { getToken } from "../services/LocalStorageService";

/**
 * Get the current user information from JWT token
 * @returns {Object|null} User object with username and roles, or null if no valid token
 */
export const getCurrentUserFromToken = () => {
  try {
    const token = getToken();
    if (!token) {
      return null;
    }

    const decodedToken = jwtDecode(token);
    
    return {
      username: decodedToken.sub, // JWT standard claim for subject (username)
      roles: decodedToken.roles || [],
      fullName: decodedToken.fullName || decodedToken.sub, // Fallback to username if fullName not available
      email: decodedToken.email || null,
      exp: decodedToken.exp, // Expiration time
      iat: decodedToken.iat  // Issued at time
    };
  } catch (error) {
    console.error("Error decoding JWT token:", error);
    return null;
  }
};

/**
 * Check if the current user has a specific role
 * @param {string} role - Role to check for
 * @returns {boolean} True if user has the role, false otherwise
 */
export const hasRole = (role) => {
  const user = getCurrentUserFromToken();
  return user ? user.roles.includes(role) : false;
};

/**
 * Check if the current user is an agent/sales person
 * @returns {boolean} True if user is an agent, false otherwise
 */
export const isAgent = () => {
  return hasRole("ROLE_SALES");
};

/**
 * Check if the current user is an admin
 * @returns {boolean} True if user is an admin, false otherwise
 */
export const isAdmin = () => {
  return hasRole("ROLE_ADMIN");
};

/**
 * Check if the JWT token is expired
 * @returns {boolean} True if token is expired, false otherwise
 */
export const isTokenExpired = () => {
  const user = getCurrentUserFromToken();
  if (!user || !user.exp) {
    return true;
  }
  
  const currentTime = Date.now() / 1000; // Convert to seconds
  return user.exp < currentTime;
};

/**
 * Get username from JWT token
 * @returns {string|null} Username or null if no valid token
 */
export const getUsernameFromToken = () => {
  const user = getCurrentUserFromToken();
  return user ? user.username : null;
};
