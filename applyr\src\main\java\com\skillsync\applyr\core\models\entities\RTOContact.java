package com.skillsync.applyr.core.models.entities;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "rto_contact")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RTOContact {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    private String contactType;

    private String firstName;
    private String lastName;

    private String jobTitle;
    private String role;

    private String phone;
    private String email;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "rto_id")
    private RTO rto;
}
