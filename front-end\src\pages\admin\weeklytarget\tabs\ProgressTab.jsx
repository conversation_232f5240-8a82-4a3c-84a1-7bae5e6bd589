import React, { useState } from "react";
import { Line, Bar } from "react-chartjs-2";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faChartLine,
  faChartBar,
  faCalendarAlt,
  faInfoCircle,
  faArrowUp,
  faArrowDown,
  faExchangeAlt,
  faUsers,
} from "@fortawesome/free-solid-svg-icons";

const ProgressTab = ({
  targetData,
  lineChartData,
  lineOptions,
  barData,
  barOptions,
  formattedStart,
  formattedEnd,
}) => {
  const [activeView, setActiveView] = useState('both');

  // Calculate overall progress percentages
  const kpi1Progress = targetData.targets.reduce((sum, agent) => sum + agent.kpi1Actual, 0) /
                      targetData.targets.reduce((sum, agent) => sum + agent.kpi1Target, 0) * 100 || 0;

  const kpi2Progress = targetData.targets.reduce((sum, agent) => sum + agent.kpi2Actual, 0) /
                      targetData.targets.reduce((sum, agent) => sum + agent.kpi2Target, 0) * 100 || 0;

  // Create KPI1 progress chart data
  const createKPI1ChartData = () => {
    // Sort agents by KPI1 performance
    const sortedAgents = [...targetData.targets].sort((a, b) => {
      return b.kpi1Actual - a.kpi1Actual; // Descending order by KPI1 Actual
    });

    return {
      labels: sortedAgents.map(agent => agent.profile?.fullName || agent.profile?.username || "Agent"),
      datasets: [
        {
          label: 'KPI1 Actual ($)',
          data: sortedAgents.map(agent => agent.kpi1Actual),
          backgroundColor: 'rgba(75, 192, 150, 0.8)', // Green for KPI1
          borderColor: 'rgba(75, 192, 150, 1)',
          borderWidth: 1,
          barPercentage: 0.8,
          categoryPercentage: 0.9,
        },
        {
          label: 'Remaining ($)',
          data: sortedAgents.map(agent => Math.max(0, agent.kpi1Target - agent.kpi1Actual)),
          backgroundColor: 'rgba(220, 220, 220, 0.8)', // Light gray for remaining
          borderColor: 'rgba(220, 220, 220, 1)',
          borderWidth: 1,
          barPercentage: 0.8,
          categoryPercentage: 0.9,
        },
      ],
    };
  };

  // Create KPI2 progress chart data
  const createKPI2ChartData = () => {
    // Sort agents by KPI2 performance
    const sortedAgents = [...targetData.targets].sort((a, b) => {
      return b.kpi2Actual - a.kpi2Actual; // Descending order by KPI2 Actual
    });

    return {
      labels: sortedAgents.map(agent => agent.profile?.fullName || agent.profile?.username || "Agent"),
      datasets: [
        {
          label: 'KPI2 Actual ($)',
          data: sortedAgents.map(agent => agent.kpi2Actual),
          backgroundColor: 'rgba(153, 102, 255, 0.8)', // Purple for KPI2
          borderColor: 'rgba(153, 102, 255, 1)',
          borderWidth: 1,
          barPercentage: 0.8,
          categoryPercentage: 0.9,
        },
        {
          label: 'Remaining ($)',
          data: sortedAgents.map(agent => Math.max(0, agent.kpi2Target - agent.kpi2Actual)),
          backgroundColor: 'rgba(220, 220, 220, 0.8)', // Light gray for remaining
          borderColor: 'rgba(220, 220, 220, 1)',
          borderWidth: 1,
          barPercentage: 0.8,
          categoryPercentage: 0.9,
        },
      ],
    };
  };

  // Create options for horizontal bar charts
  const horizontalBarOptions = {
    indexAxis: 'y', // Horizontal bars
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: true,
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          callback: (value) => `$${value.toLocaleString()}`,
          maxRotation: 0,
        },
      },
      y: {
        stacked: true,
        grid: {
          display: false,
        },
      },
    },
    plugins: {
      tooltip: {
        callbacks: {
          label: (context) => {
            let label = context.dataset.label || '';
            if (label) label += ': ';
            if (context.parsed.x !== null) {
              label += `$${context.parsed.x.toLocaleString()}`;
            }
            return label;
          },
          afterBody: (tooltipItems) => {
            const item = tooltipItems[0];
            const index = item.dataIndex;
            const agent = targetData.targets.find(a =>
              (a.profile?.fullName || a.profile?.username) === item.label
            );

            if (!agent) return '';

            if (item.dataset.label.includes('KPI1')) {
              const percentage = agent.kpi1Target > 0
                ? Math.round((agent.kpi1Actual / agent.kpi1Target) * 100)
                : 0;
              return [`Target: $${agent.kpi1Target.toLocaleString()}`, `Progress: ${percentage}%`];
            } else if (item.dataset.label.includes('KPI2')) {
              const percentage = agent.kpi2Target > 0
                ? Math.round((agent.kpi2Actual / agent.kpi2Target) * 100)
                : 0;
              return [`Target: $${agent.kpi2Target.toLocaleString()}`, `Progress: ${percentage}%`];
            }
            return '';
          },
        },
      },
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 15,
          boxWidth: 10,
        },
      },
    },
  };

  // Generate the chart data
  const kpi1ChartData = createKPI1ChartData();
  const kpi2ChartData = createKPI2ChartData();

  // Get days passed and total days
  const startDate = new Date(targetData.startDate);
  const endDate = new Date(targetData.endDate);
  const currentDate = new Date();
  const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
  const daysPassed = Math.min(Math.ceil((currentDate - startDate) / (1000 * 60 * 60 * 24)), totalDays);
  const timeProgress = (daysPassed / totalDays) * 100;

  // Determine if we're ahead or behind schedule
  const kpi1Status = kpi1Progress >= timeProgress ? 'ahead' : 'behind';
  const kpi2Status = kpi2Progress >= timeProgress ? 'ahead' : 'behind';

  return (
    <div className="space-y-8">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="bg-[#F4F5F9] p-2 rounded-lg mr-3">
                <FontAwesomeIcon icon={faCalendarAlt} className="h-5 w-5 text-[#6E39CB]" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">Time Progress</h3>
            </div>
            <div className="text-sm font-medium text-gray-500">
              Day {daysPassed} of {totalDays}
            </div>
          </div>
          <div className="mb-2 flex justify-between text-sm font-medium">
            <span>Progress</span>
            <span>{Math.round(timeProgress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
            <div
              className="bg-gray-500 h-2.5 rounded-full"
              style={{ width: `${timeProgress}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600">
            <FontAwesomeIcon icon={faInfoCircle} className="mr-2 text-gray-400" />
            {formattedStart} - {formattedEnd}
          </p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="bg-[#F4F5F9] p-2 rounded-lg mr-3">
                <FontAwesomeIcon icon={faChartLine} className="h-5 w-5 text-[#6E39CB]" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">KPI 1 Progress</h3>
            </div>
            <div className={`text-sm font-medium ${kpi1Status === 'ahead' ? 'text-green-600' : 'text-red-600'}`}>
              <FontAwesomeIcon icon={kpi1Status === 'ahead' ? faArrowUp : faArrowDown} className="mr-1" />
              {kpi1Status === 'ahead' ? 'Ahead' : 'Behind'}
            </div>
          </div>
          <div className="mb-2 flex justify-between text-sm font-medium">
            <span>Progress</span>
            <span>{Math.round(kpi1Progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
            <div
              className={`h-2.5 rounded-full ${kpi1Status === 'ahead' ? 'bg-green-500' : 'bg-red-500'}`}
              style={{ width: `${Math.min(kpi1Progress, 100)}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600">
            <FontAwesomeIcon icon={faExchangeAlt} className="mr-2 text-gray-400" />
            ${targetData.targets.reduce((sum, agent) => sum + agent.kpi1Actual, 0).toLocaleString()} of ${targetData.targets.reduce((sum, agent) => sum + agent.kpi1Target, 0).toLocaleString()}
          </p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="bg-[#F4F5F9] p-2 rounded-lg mr-3">
                <FontAwesomeIcon icon={faChartLine} className="h-5 w-5 text-[#6E39CB]" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">KPI 2 Progress</h3>
            </div>
            <div className={`text-sm font-medium ${kpi2Status === 'ahead' ? 'text-green-600' : 'text-red-600'}`}>
              <FontAwesomeIcon icon={kpi2Status === 'ahead' ? faArrowUp : faArrowDown} className="mr-1" />
              {kpi2Status === 'ahead' ? 'Ahead' : 'Behind'}
            </div>
          </div>
          <div className="mb-2 flex justify-between text-sm font-medium">
            <span>Progress</span>
            <span>{Math.round(kpi2Progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
            <div
              className={`h-2.5 rounded-full ${kpi2Status === 'ahead' ? 'bg-green-500' : 'bg-red-500'}`}
              style={{ width: `${Math.min(kpi2Progress, 100)}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600">
            <FontAwesomeIcon icon={faExchangeAlt} className="mr-2 text-gray-400" />
            ${targetData.targets.reduce((sum, agent) => sum + agent.kpi2Actual, 0).toLocaleString()} of ${targetData.targets.reduce((sum, agent) => sum + agent.kpi2Target, 0).toLocaleString()}
          </p>
        </div>
      </div>

      {/* Chart View Selector */}
      <div className="flex justify-center mb-4">
        <div className="inline-flex rounded-md shadow-sm" role="group">
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium rounded-l-lg border ${activeView === 'line' ? 'bg-[#6E39CB] text-white border-[#6E39CB]' : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-100'}`}
            onClick={() => setActiveView('line')}
          >
            <FontAwesomeIcon icon={faChartLine} className="mr-2" />
            Progress Over Time
          </button>
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium border-t border-b ${activeView === 'both' ? 'bg-[#6E39CB] text-white border-[#6E39CB]' : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-100'}`}
            onClick={() => setActiveView('both')}
          >
            <FontAwesomeIcon icon={faExchangeAlt} className="mr-2" />
            Both Charts
          </button>
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium rounded-r-lg border ${activeView === 'bar' ? 'bg-[#6E39CB] text-white border-[#6E39CB]' : 'bg-white text-gray-700 border-gray-200 hover:bg-gray-100'}`}
            onClick={() => setActiveView('bar')}
          >
            <FontAwesomeIcon icon={faChartBar} className="mr-2" />
            Agent Comparison
          </button>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 gap-6">
        {(activeView === 'line' || activeView === 'both') && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="bg-[#F4F5F9] p-2 rounded-lg mr-3">
                  <FontAwesomeIcon icon={faChartLine} className="h-5 w-5 text-[#6E39CB]" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Progress Over Time</h3>
              </div>
              <div className="text-sm text-gray-500">
                <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
                {formattedStart} - {formattedEnd}
              </div>
            </div>
            <div className="h-96">
              <Line data={lineChartData} options={lineOptions} />
            </div>
            <p className="text-sm text-gray-500 mt-4 text-center">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
              This chart shows actual vs. ideal progress over the target period
            </p>
          </div>
        )}

        {(activeView === 'bar' || activeView === 'both') && (
          <div className="space-y-6">
            {/* KPI1 Progress Chart */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="bg-[#F4F5F9] p-2 rounded-lg mr-3">
                    <FontAwesomeIcon icon={faChartBar} className="h-5 w-5 text-[#6E39CB]" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">KPI1 Progress</h3>
                </div>
              </div>
              <div className="h-72">
                <Bar data={kpi1ChartData} options={horizontalBarOptions} />
              </div>
            </div>

            {/* KPI2 Progress Chart */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="bg-[#F4F5F9] p-2 rounded-lg mr-3">
                    <FontAwesomeIcon icon={faChartBar} className="h-5 w-5 text-[#6E39CB]" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">KPI2 Progress</h3>
                </div>
              </div>
              <div className="h-72">
                <Bar data={kpi2ChartData} options={horizontalBarOptions} />
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">About These Charts</h4>
              <p className="text-sm text-gray-600">
                <FontAwesomeIcon icon={faInfoCircle} className="mr-2 text-gray-400" />
                These charts show the progress of each agent toward their KPI targets. The colored bars represent
                actual performance, while the gray bars show the remaining amount needed to reach the target.
                Agents are sorted by performance (highest to lowest) for each KPI.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgressTab;
