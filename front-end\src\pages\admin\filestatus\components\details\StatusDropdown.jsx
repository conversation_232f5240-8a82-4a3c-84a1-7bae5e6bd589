import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheck, faSpinner } from "@fortawesome/free-solid-svg-icons";
import DateUpdateModal from "./DateUpdateModal";

/**
 * StatusDropdown component for updating file status
 * @param {string} currentStatus - Current status value
 * @param {function} onStatusUpdate - Function to call when status is updated
 * @param {boolean} isLoading - Loading state
 * @param {object} fileStatus - File status data
 * @param {function} showToast - Function to show toast notifications
 * @param {function} refetch - Function to refetch data
 */
const StatusDropdown = ({ currentStatus, onStatusUpdate, isLoading = false, fileStatus, showToast, refetch }) => {
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);
  const [isDateModalOpen, setIsDateModalOpen] = useState(false);

  // Status options based on FileStatus enum
  const statusOptions = [
    "DOCUMENTS_PENDING",
    "DOCUMENTS_RECEIVED",
    "LODGED_AND_PROCESSING",
    "SOFT_COPY_RECEIVED",
    "SOFT_COPY_RELEASED",
    "HARD_COPY_RECEIVED",
    "HARD_COPY_MAILED_AND_CLOSED",
    "PENDING_EVIDENCE",
    "CANCELLED",
    "STATUS_UNAVAILABLE",
    "HOLD",
  ];

  // Format status for display
  const formatStatus = (status) => {
    if (!status) return "";
    return status.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, (c) => c.toUpperCase());
  };

  // Handle status change
  const handleStatusChange = (e) => {
    setSelectedStatus(e.target.value);
  };

  // Check if status requires date update
  const requiresDateUpdate = (status) => {
    return [
      "DOCUMENTS_RECEIVED",
      "LODGED_AND_PROCESSING",
      "SOFT_COPY_RECEIVED",
      "SOFT_COPY_RELEASED",
      "HARD_COPY_RECEIVED",
      "HARD_COPY_MAILED_AND_CLOSED"
    ].includes(status);
  };

  // Handle status update
  const handleStatusUpdate = async () => {
    if (selectedStatus === currentStatus) return;

    // Check if we need to show the date modal
    if (requiresDateUpdate(selectedStatus)) {
      setIsDateModalOpen(true);
      return;
    }

    try {
      await onStatusUpdate(selectedStatus);
    } catch (error) {
      console.error("Error updating status:", error);
    }
  };

  // Handle status update after date is set
  const handleStatusUpdateAfterDate = async () => {
    try {
      await onStatusUpdate(selectedStatus);
    } catch (error) {
      console.error("Error updating status:", error);
    }
  };

  // Get status color based on status
  const getStatusColor = (status) => {
    if (!status) return "bg-gray-100 text-gray-800";

    if (status.includes("PENDING") || status === "DOCUMENTS_PENDING") {
      return "bg-yellow-100 text-yellow-800";
    } else if (status.includes("RECEIVED") || status === "DOCUMENTS_RECEIVED") {
      return "bg-blue-100 text-blue-800";
    } else if (status.includes("PROCESSING") || status === "LODGED_AND_PROCESSING") {
      return "bg-indigo-100 text-indigo-800";
    } else if (status.includes("RELEASED") || status === "SOFT_COPY_RELEASED") {
      return "bg-green-100 text-green-800";
    } else if (status.includes("MAILED") || status === "HARD_COPY_MAILED_AND_CLOSED") {
      return "bg-purple-100 text-purple-800";
    } else if (status === "CANCELLED") {
      return "bg-red-100 text-red-800";
    } else if (status === "HOLD") {
      return "bg-orange-100 text-orange-800";
    } else {
      return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4 mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div className="mb-4 md:mb-0">
          <h3 className="text-lg font-semibold text-gray-900">Current Status</h3>
          <div className="mt-2">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(currentStatus)}`}>
              {formatStatus(currentStatus)}
            </span>
          </div>
        </div>

        <div className="flex items-end">
          <div className="mr-2">
            <label htmlFor="statusSelect" className="block text-sm font-medium text-gray-500 mb-1">
              Update Status
            </label>
            <select
              id="statusSelect"
              value={selectedStatus}
              onChange={handleStatusChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              disabled={isLoading}
            >
              {statusOptions.map((status) => (
                <option key={status} value={status}>
                  {formatStatus(status)}
                </option>
              ))}
            </select>
          </div>

          <button
            onClick={handleStatusUpdate}
            disabled={isLoading || selectedStatus === currentStatus}
            className={`px-4 py-2 rounded-md ${
              selectedStatus === currentStatus
                ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                : "bg-[#6E39CB] text-white hover:bg-[#5E2CB8]"
            } transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]`}
          >
            {isLoading ? (
              <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
            ) : (
              <FontAwesomeIcon icon={faCheck} className="mr-2" />
            )}
            Update
          </button>
        </div>
      </div>

      {/* Date Update Modal */}
      <DateUpdateModal
        isOpen={isDateModalOpen}
        onClose={() => {
          setIsDateModalOpen(false);
          // Reset selected status if modal is closed without updating
          setSelectedStatus(currentStatus);
        }}
        fileStatus={fileStatus}
        selectedStatus={selectedStatus}
        showToast={showToast}
        refetch={() => {
          refetch();
          handleStatusUpdateAfterDate();
        }}
      />
    </div>
  );
};

export default StatusDropdown;
