import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFileInvoice,
  faMoneyBillWave,
  faUpload,
  faTimes,
  faFileExcel,
  faSpinner,
  faExclamationTriangle,
  faCheckCircle,
  faDownload,
  faHistory,
  faInfoCircle,
  faCalendarAlt,
  faUser,
  faFilter,
  faSearch,
  faSort,
  faSortUp,
  faSortDown,
  faPlus,
  faEye,
  faEdit,
  faTrash,
  faCircle
} from "@fortawesome/free-solid-svg-icons";
import { toast } from "react-toastify";
import {
  useUploadXeroInvoiceMutation,
  useGetAllXeroInvoicesQuery,
  useUploadXeroInBankMutation,
  useGetAllXeroInBankQuery,
  useCreateXeroInvoiceMutation,
  useCreateXeroInBankMutation
} from "../../../../services/SourceOfTruthApiService";
import { format, parse } from "date-fns";

// Import components
import XeroHeader from "./components/XeroHeader";
import XeroFilterBar from "./components/XeroFilterBar";
import KPI1Table from "./components/KPI1Table";
import KPI2Table from "./components/KPI2Table";
import XeroImportModal from "./components/XeroImportModal";
import XeroSingleEntryModal from "./components/XeroSingleEntryModal";

const XeroImportPage = () => {
  // Tab state
  const [selectedTab, setSelectedTab] = useState("KPI1");

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [paymentStatusFilter, setPaymentStatusFilter] = useState("");
  const [dateRangeFilter, setDateRangeFilter] = useState("all");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  // Modal states
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isSingleEntryModalOpen, setIsSingleEntryModalOpen] = useState(false);

  // File upload state
  const [uploadFile, setUploadFile] = useState(null);

  // Single entry form states
  const [singleEntryForm, setSingleEntryForm] = useState({
    applicationId: "",
    invoiceNumber: "",
    contact: "",
    invoiceDate: "",
    dueDate: "",
    reference: "",
    amount: "",
    description: "",
    status: "Approved"
  });

  // API hooks
  const [uploadXeroInvoice, { isLoading: isUploadingKPI1 }] = useUploadXeroInvoiceMutation();
  const { data: kpi1Data, refetch: refetchKPI1, isLoading: isLoadingKPI1, error: kpi1Error } = useGetAllXeroInvoicesQuery();
  const [uploadXeroInBank, { isLoading: isUploadingKPI2 }] = useUploadXeroInBankMutation();
  const { data: kpi2Data, refetch: refetchKPI2, isLoading: isLoadingKPI2, error: kpi2Error } = useGetAllXeroInBankQuery();
  const [createXeroInvoice] = useCreateXeroInvoiceMutation();
  const [createXeroInBank] = useCreateXeroInBankMutation();

  // Log API states for debugging
  if (kpi1Error || kpi2Error) {
    console.log("🌐 API Errors:");
    if (kpi1Error) console.log("🌐 kpi1Error:", kpi1Error);
    if (kpi2Error) console.log("🌐 kpi2Error:", kpi2Error);
  }
  // State for normalized KPI1 data
  const [normalizedKpi1Data, setNormalizedKpi1Data] = useState([]);
  // State for normalized KPI2 data
  const [normalizedKpi2Data, setNormalizedKpi2Data] = useState([]);
  // State for agent filter
  const [agentFilter, setAgentFilter] = useState("");

  // Normalize KPI1 data for table display
  useEffect(() => {
    if (kpi1Data && Array.isArray(kpi1Data)) {
      console.log("🔍 Processing KPI1 data - length:", kpi1Data.length);

      const parseAndFormatDate = (dateStr) => {
        if (!dateStr) return "";
        let parsedDate = null;

        // First try direct Date parsing (should work for ISO-like formats)
        try {
          parsedDate = new Date(dateStr);
          if (parsedDate && !isNaN(parsedDate.getTime())) {
            return format(parsedDate, "dd MMM yyyy");
          }
        } catch (e) {
          // Continue to next parsing method
        }

        // Try 'yyyy-MM-dd\'T\'HH:mm' (e.g., 2025-05-01T00:00)
        if (!parsedDate || isNaN(parsedDate.getTime())) {
          try {
            parsedDate = parse(dateStr, "yyyy-MM-dd'T'HH:mm", new Date());
            if (parsedDate && !isNaN(parsedDate.getTime())) {
              return format(parsedDate, "dd MMM yyyy");
            }
          } catch (e) {
            // Continue to next parsing method
          }
        }

        // If not ISO, try M/d/yyyy (e.g., 5/6/2025)
        if (!parsedDate || isNaN(parsedDate.getTime())) {
          try {
            parsedDate = parse(dateStr, "M/d/yyyy", new Date());
            if (parsedDate && !isNaN(parsedDate.getTime())) {
              return format(parsedDate, "dd MMM yyyy");
            }
          } catch (e) {
            // Continue to fallback
          }
        }

        return dateStr; // Return original if all parsing fails
      };

      const normalized = kpi1Data.map((item, index) => {
        let rawDate = item.invoiceDate ?? item.invoice_date ?? "";
        let formattedDate = parseAndFormatDate(rawDate);
        let rawExpiryDate = item.invoiceExpiryDate ?? item.dueDate ?? item.invoice_expiry_date ?? "";
        let formattedExpiryDate = parseAndFormatDate(rawExpiryDate);

        return {
          invoiceNumber: item.invoiceNumber ?? item.invoice_number ?? "",
          contact: item.contactName ?? item.contact ?? "",
          invoiceDate: formattedDate,
          invoiceExpiryDate: formattedExpiryDate,
          reference: item.quoteNumber ?? item.reference ?? "",
          gross: item.gross ?? item.amount ?? 0,
          balance: item.balance ?? 0,
          status: item.status ?? "",
          source: item.source ?? "",
          invoiceStatus: item.invoiceSentStatus ?? item.invoiceStatus ?? item.invoice_sent_status ?? "",
          agent: item.agent?.fullName ?? item.agentFullName ?? "",
          _rawInvoiceDate: rawDate // for sorting
        };
      });

      // Sort by _rawInvoiceDate descending (most recent first)
      normalized.sort((a, b) => {
        const dateA = a._rawInvoiceDate ? new Date(a._rawInvoiceDate) : new Date(0);
        const dateB = b._rawInvoiceDate ? new Date(b._rawInvoiceDate) : new Date(0);
        return dateB - dateA;
      });

      console.log("✅ KPI1 data normalized successfully - count:", normalized.length);
      setNormalizedKpi1Data(normalized);
    } else {
      setNormalizedKpi1Data([]);
    }
  }, [kpi1Data]);

  // Normalize KPI2 data for table display
  useEffect(() => {
    if (kpi2Data && Array.isArray(kpi2Data)) {
      const parseAndFormatDate = (dateStr) => {
        if (!dateStr) return "";
        let parsedDate = null;
        try {
          parsedDate = parse(dateStr, "yyyy-MM-dd'T'HH:mm", new Date());
        } catch {}
        if (!parsedDate || isNaN(parsedDate.getTime())) {
          parsedDate = Date.parse(dateStr) ? new Date(dateStr) : null;
        }
        if (!parsedDate || isNaN(parsedDate.getTime())) {
          try {
            parsedDate = parse(dateStr, "M/d/yyyy", new Date());
          } catch {
            parsedDate = null;
          }
        }
        if (parsedDate && !isNaN(parsedDate.getTime())) {
          return format(parsedDate, "dd MMM yyyy");
        }
        return dateStr;
      };
      const normalized = kpi2Data.map(item => {
        let rawDate = item.insertDate ?? item.dateInserted ?? item.date ?? "";
        let formattedDate = parseAndFormatDate(rawDate);
        return {
          dateInserted: formattedDate,
          agent: item.agent?.fullName || item.agentUsername || "",
          reference: item.invoiceNumber || item.reference || "",
          contact: item.contactName || item.contact || "",
          description: item.description || "",
          debitAmount: item.debitAmount ?? item.debit ?? 0,
          creditAmount: item.creditAmount ?? item.credit ?? 0,
          netAmount: item.netAmount ?? item.net ?? 0,
          source: item.source || "",
        };
      });
      setNormalizedKpi2Data(normalized);
    } else {
      setNormalizedKpi2Data([]);
    }
  }, [kpi2Data]);

  // Filter and sort data
  useEffect(() => {
    // Initialize dates if they're empty
    if (dateRangeFilter === "custom" && (!startDate || !endDate)) {
      const today = new Date();
      const oneWeekAgo = new Date(today);
      oneWeekAgo.setDate(today.getDate() - 7);

      setStartDate(oneWeekAgo.toISOString().split('T')[0]);
      setEndDate(today.toISOString().split('T')[0]);
    }
  }, [dateRangeFilter, startDate, endDate]);

  // Handle tab change
  const handleTabChange = (tab) => {
    setSelectedTab(tab);
  };

  // Handle file upload for bulk import
  const handleFileUpload = async () => {
    if (!uploadFile) {
      toast.error("Please select a file to upload");
      return;
    }

    try {
      if (selectedTab === "KPI1") {
        await uploadXeroInvoice(uploadFile).unwrap();
        toast.success("KPI1 data imported successfully");
        refetchKPI1();
      } else {
        await uploadXeroInBank(uploadFile).unwrap();
        toast.success("KPI2 data imported successfully");
        refetchKPI2();
      }

      setUploadFile(null);
      setIsImportModalOpen(false);
    } catch (error) {
      console.error("Error uploading file:", error);
      toast.error("Failed to upload file. Please try again.");
    }
  };

  // Handle single entry submission
  const handleSingleEntrySubmit = async () => {
    // Validate form
    if (!singleEntryForm.applicationId ||
        (selectedTab === "KPI1" && (!singleEntryForm.invoiceNumber || !singleEntryForm.contact)) ||
        (selectedTab === "KPI2" && (!singleEntryForm.reference || !singleEntryForm.description))) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      if (selectedTab === "KPI1") {
        // Prepare data for KPI1 (invoice)
        const invoiceData = {
          invoiceNumber: singleEntryForm.invoiceNumber,
          quoteNumber: singleEntryForm.reference || "",
          contactName: singleEntryForm.contact,
          gross: parseFloat(singleEntryForm.amount) || 0,
          balance: parseFloat(singleEntryForm.amount) || 0,
          status: singleEntryForm.status,
          applicationId: singleEntryForm.applicationId,
          agentUsername: singleEntryForm.agent || "",
          invoiceDate: singleEntryForm.invoiceDate ? new Date(singleEntryForm.invoiceDate).toISOString() : new Date().toISOString(),
          invoiceExpiryDate: singleEntryForm.dueDate ? new Date(singleEntryForm.dueDate).toISOString() : new Date().toISOString(),
          source: "Manual Entry",
          invoiceSentStatus: "NOT_SENT"
        };

        await createXeroInvoice(invoiceData).unwrap();
      } else {
        // Prepare data for KPI2 (in bank)
        const inBankData = {
          agentUsername: singleEntryForm.agent || "",
          invoiceNumber: singleEntryForm.invoiceNumber,
          contactName: singleEntryForm.contact,
          applicationId: singleEntryForm.applicationId,
          insertDate: singleEntryForm.invoiceDate ? new Date(singleEntryForm.invoiceDate).toISOString() : new Date().toISOString(),
          description: singleEntryForm.description || "",
          debitAmount: 0,
          creditAmount: parseFloat(singleEntryForm.amount) || 0,
          netAmount: parseFloat(singleEntryForm.amount) || 0,
          source: "Manual Entry"
        };

        await createXeroInBank(inBankData).unwrap();
      }

      toast.success(`${selectedTab} entry added successfully`);
      setIsSingleEntryModalOpen(false);

      // Reset form
      setSingleEntryForm({
        applicationId: "",
        invoiceNumber: "",
        contact: "",
        invoiceDate: "",
        dueDate: "",
        reference: "",
        amount: "",
        description: "",
        status: "Approved",
        agent: ""
      });

      // Refresh data
      if (selectedTab === "KPI1") {
        refetchKPI1();
      } else {
        refetchKPI2();
      }
    } catch (error) {
      console.error("Error adding entry:", error);
      toast.error("Failed to add entry. Please try again.");
    }
  };

  // For agent dropdown, always use all unique agents from normalizedKpi1Data for KPI1
  const uniqueAgents = Array.from(new Set((normalizedKpi1Data || []).map(item => item.agent).filter(Boolean)));

  // For agent dropdown, use all unique agents from normalizedKpi2Data for KPI2
  const uniqueKpi2Agents = Array.from(new Set((normalizedKpi2Data || []).map(item => item.agent).filter(Boolean)));

  // Agent filter for KPI2
  const [kpi2AgentFilter, setKpi2AgentFilter] = useState("");

  // Filter data based on search query and filters
  const getFilteredData = () => {
    let filteredData = selectedTab === "KPI1" ? (normalizedKpi1Data || []) : (normalizedKpi2Data || []);
    const initialCount = filteredData.length;

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase().trim();
      filteredData = filteredData.filter(item => {
        if (selectedTab === "KPI1") {
          // Check for invoice number match
          const invoiceMatch = item.invoiceNumber ?
            item.invoiceNumber.toString().toLowerCase().includes(query) : false;

          // Check for contact name match
          const contactMatch = item.contact ?
            item.contact.toString().toLowerCase().includes(query) : false;

          // Check for agent match
          const agentMatch = item.agent ?
            item.agent.toString().toLowerCase().includes(query) : false;

          return invoiceMatch || contactMatch || agentMatch;
        } else {
          // KPI2: search reference, contact, description, agent
          const referenceMatch = item.reference && item.reference.toString().toLowerCase().includes(query);
          const contactMatch = item.contact && item.contact.toString().toLowerCase().includes(query);
          const descriptionMatch = item.description && item.description.toString().toLowerCase().includes(query);
          const agentMatch = item.agent && item.agent.toLowerCase().includes(query);
          return referenceMatch || contactMatch || descriptionMatch || agentMatch;
        }
      });
    }

    // Apply status filter
    if (statusFilter) {
      filteredData = filteredData.filter(item => {
        if (selectedTab === "KPI1") {
          return item.status && item.status.toLowerCase() === statusFilter.toLowerCase();
        } else {
          return item.status && item.status.toLowerCase() === statusFilter.toLowerCase();
        }
      });
    }

    // Apply agent filter
    if ((selectedTab === "KPI1" && agentFilter) || (selectedTab === "KPI2" && kpi2AgentFilter)) {
      filteredData = filteredData.filter(item => {
        const agentVal = selectedTab === "KPI1" ? agentFilter : kpi2AgentFilter;
        return item.agent && item.agent.toLowerCase() === agentVal.toLowerCase();
      });
    }

    // Apply payment status filter (KPI1 only)
    if (selectedTab === "KPI1" && paymentStatusFilter) {
      filteredData = filteredData.filter(item =>
        item.invoiceStatus && item.invoiceStatus.toLowerCase() === paymentStatusFilter.toLowerCase()
      );
    }

    // Apply date filter
    if (dateRangeFilter !== "all") {
      const today = new Date();
      let filterStartDate, filterEndDate;

      switch (dateRangeFilter) {
        case "thisWeek":
          filterStartDate = new Date(today);
          filterStartDate.setDate(today.getDate() - today.getDay());
          filterEndDate = new Date(today);
          filterEndDate.setDate(filterStartDate.getDate() + 6);
          break;
        case "lastWeek":
          filterStartDate = new Date(today);
          filterStartDate.setDate(today.getDate() - today.getDay() - 7);
          filterEndDate = new Date(today);
          filterEndDate.setDate(filterStartDate.getDate() + 6);
          break;
        case "thisMonth":
          filterStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
          filterEndDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
          break;
        case "custom":
          if (startDate && endDate) {
            filterStartDate = new Date(startDate);
            filterEndDate = new Date(endDate);
            filterEndDate.setHours(23, 59, 59, 999);
          }
          break;
        default:
          break;
      }

      if (filterStartDate && filterEndDate) {
        filteredData = filteredData.filter(item => {
          let itemDate;
          if (selectedTab === "KPI1") {
            if (item._rawInvoiceDate) {
              itemDate = new Date(item._rawInvoiceDate);
            } else if (item.invoiceDate) {
              try {
                const parts = item.invoiceDate.split(' ');
                if (parts.length === 3) {
                  const day = parseInt(parts[0]);
                  const month = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].indexOf(parts[1]);
                  const year = parseInt(parts[2]);
                  if (!isNaN(day) && month !== -1 && !isNaN(year)) {
                    itemDate = new Date(year, month, day);
                  }
                }
              } catch (e) {
                console.error("Error parsing date:", e);
              }
            }
          } else {
            // For KPI2, parse dd MMM yyyy
            if (item.dateInserted) {
              const parts = item.dateInserted.split(' ');
              if (parts.length === 3) {
                const day = parseInt(parts[0]);
                const month = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].indexOf(parts[1]);
                const year = parseInt(parts[2]);
                if (!isNaN(day) && month !== -1 && !isNaN(year)) {
                  itemDate = new Date(year, month, day);
                }
              }
              if (!itemDate || isNaN(itemDate.getTime())) {
                itemDate = new Date(item.dateInserted);
              }
            }
          }
          if (!itemDate || isNaN(itemDate.getTime())) {
            return false;
          }
          return itemDate >= filterStartDate && itemDate <= filterEndDate;
        });
      }
    }

    // Log filtering summary if data was filtered out
    if (initialCount > 0 && filteredData.length === 0) {
      console.log("⚠️ All data filtered out:", {
        initialCount,
        filters: { searchQuery, statusFilter, agentFilter, paymentStatusFilter, dateRangeFilter }
      });
    }

    return filteredData;
  };
  // Download sample template
  const downloadSampleTemplate = () => {
    // This would need to be implemented based on your requirements
    toast.info("Sample template download functionality would be implemented here");
  };

  // Get filtered data
  const filteredData = getFilteredData();

  // Debug info for data display
  if (filteredData.length === 0 && (normalizedKpi1Data.length > 0 || normalizedKpi2Data.length > 0)) {
    console.log("⚠️ No data showing - check filters:", {
      selectedTab,
      normalizedKpi1Count: normalizedKpi1Data.length,
      normalizedKpi2Count: normalizedKpi2Data.length,
      dateRangeFilter,
      statusFilter,
      agentFilter,
      paymentStatusFilter
    });
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header with tabs */}
      <XeroHeader
        selectedTab={selectedTab}
        handleTabChange={handleTabChange}
      />

      {/* Filter bar */}
      <XeroFilterBar
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        paymentStatusFilter={paymentStatusFilter}
        setPaymentStatusFilter={setPaymentStatusFilter}
        dateRangeFilter={dateRangeFilter}
        setDateRangeFilter={setDateRangeFilter}
        startDate={startDate}
        setStartDate={setStartDate}
        endDate={endDate}
        setEndDate={setEndDate}
        selectedTab={selectedTab}
        agentFilter={agentFilter}
        setAgentFilter={setAgentFilter}
        uniqueAgents={uniqueAgents}
      />

      {/* Action buttons */}
      <div className="flex flex-wrap gap-4 mb-6">
        <button
          onClick={() => setIsSingleEntryModalOpen(true)}
          className="flex items-center gap-2 px-4 py-2 bg-white border border-[#6E39CB] text-[#6E39CB] rounded-md hover:bg-[#F4F5F9] transition-colors"
        >
          <FontAwesomeIcon icon={selectedTab === "KPI1" ? faFileInvoice : faMoneyBillWave} />
          <span>Add Single Entry</span>
        </button>

        <button
          onClick={() => setIsImportModalOpen(true)}
          className="flex items-center gap-2 px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-opacity-90 transition-colors"
        >
          <FontAwesomeIcon icon={faUpload} />
          <span>Bulk Import via Excel</span>
        </button>

        <button
          onClick={downloadSampleTemplate}
          className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          <FontAwesomeIcon icon={faDownload} />
          <span>Download Template</span>
        </button>
      </div>

      {/* Data tables */}
      {selectedTab === "KPI1" ? (
        <KPI1Table
          data={filteredData}
          isLoading={isLoadingKPI1}
          onImport={() => setIsImportModalOpen(true)}
        />
      ) : (
        <KPI2Table
          data={filteredData}
          isLoading={isLoadingKPI2}
          onImport={() => setIsImportModalOpen(true)}
          uniqueAgents={uniqueKpi2Agents}
          agentFilter={kpi2AgentFilter}
          setAgentFilter={setKpi2AgentFilter}
        />
      )}

      {/* Import Modal */}
      <XeroImportModal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        uploadFile={uploadFile}
        setUploadFile={setUploadFile}
        handleUpload={handleFileUpload}
        selectedTab={selectedTab}
        isUploading={isUploadingKPI1 || isUploadingKPI2}
      />

      {/* Single Entry Modal */}
      <XeroSingleEntryModal
        isOpen={isSingleEntryModalOpen}
        onClose={() => setIsSingleEntryModalOpen(false)}
        formData={singleEntryForm}
        setFormData={setSingleEntryForm}
        handleSubmit={handleSingleEntrySubmit}
        selectedTab={selectedTab}
      />
    </div>
  );
};

export default XeroImportPage;
