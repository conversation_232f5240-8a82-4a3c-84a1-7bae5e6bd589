import React, { useState } from "react";
import { FaTimes, FaDownload, FaFileExcel, FaFileCsv } from "react-icons/fa";

const ExportOptionsModal = ({ 
  isOpen, 
  onClose, 
  onExport, 
  isExporting,
  selectedCount,
  totalCount 
}) => {
  const [format, setFormat] = useState("CSV");
  const [selectedColumns, setSelectedColumns] = useState([
    "companyName",
    "leadName", 
    "phone",
    "email",
    "address",
    "status",
    "applicationCount",
    "createdDate",
    "assignedAgents"
  ]);

  const availableColumns = [
    { key: "companyName", label: "Company Name" },
    { key: "leadName", label: "Lead Name" },
    { key: "phone", label: "Phone" },
    { key: "email", label: "Email" },
    { key: "address", label: "Address" },
    { key: "status", label: "Status" },
    { key: "applicationCount", label: "Application Count" },
    { key: "createdDate", label: "Created Date" },
    { key: "assignedAgents", label: "Assigned Agents" }
  ];

  const handleColumnToggle = (columnKey) => {
    setSelectedColumns(prev => 
      prev.includes(columnKey)
        ? prev.filter(col => col !== columnKey)
        : [...prev, columnKey]
    );
  };

  const handleSelectAll = () => {
    setSelectedColumns(availableColumns.map(col => col.key));
  };

  const handleDeselectAll = () => {
    setSelectedColumns([]);
  };

  const handleExport = () => {
    if (selectedColumns.length === 0) {
      alert("Please select at least one column to export.");
      return;
    }
    
    onExport({
      format: format.toUpperCase(),
      selectedColumns
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Export Options</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Export Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-600">
              {selectedCount > 0
                ? `Exporting ${selectedCount} selected leads`
                : `Exporting all ${totalCount} leads`}
            </p>
          </div>

          {/* Format Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Export Format
            </label>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => setFormat("CSV")}
                className={`p-4 border-2 rounded-lg flex items-center justify-center space-x-2 transition-all ${
                  format === "CSV"
                    ? "border-[#6E39CB] bg-[#6E39CB]/5 text-[#6E39CB]"
                    : "border-gray-200 hover:border-gray-300 text-gray-600"
                }`}
              >
                <FaFileCsv className="w-5 h-5" />
                <span className="font-medium">CSV</span>
              </button>
              <button
                onClick={() => setFormat("EXCEL")}
                className={`p-4 border-2 rounded-lg flex items-center justify-center space-x-2 transition-all ${
                  format === "EXCEL"
                    ? "border-[#6E39CB] bg-[#6E39CB]/5 text-[#6E39CB]"
                    : "border-gray-200 hover:border-gray-300 text-gray-600"
                }`}
              >
                <FaFileExcel className="w-5 h-5" />
                <span className="font-medium">Excel</span>
              </button>
            </div>
          </div>

          {/* Column Selection */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Select Columns to Export
              </label>
              <div className="space-x-2">
                <button
                  onClick={handleSelectAll}
                  className="text-xs text-[#6E39CB] hover:text-[#5A2FA3] font-medium"
                >
                  Select All
                </button>
                <span className="text-gray-300">|</span>
                <button
                  onClick={handleDeselectAll}
                  className="text-xs text-gray-500 hover:text-gray-700 font-medium"
                >
                  Deselect All
                </button>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-3 max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-4">
              {availableColumns.map((column) => (
                <label
                  key={column.key}
                  className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-2 rounded"
                >
                  <input
                    type="checkbox"
                    checked={selectedColumns.includes(column.key)}
                    onChange={() => handleColumnToggle(column.key)}
                    className="w-4 h-4 text-[#6E39CB] border-gray-300 rounded focus:ring-[#6E39CB] focus:ring-2"
                  />
                  <span className="text-sm text-gray-700">{column.label}</span>
                </label>
              ))}
            </div>
            
            <p className="text-xs text-gray-500 mt-2">
              {selectedColumns.length} of {availableColumns.length} columns selected
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting || selectedColumns.length === 0}
            className={`px-6 py-2 text-sm font-medium rounded-lg transition-all flex items-center space-x-2 ${
              isExporting || selectedColumns.length === 0
                ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                : "bg-[#6E39CB] text-white hover:bg-[#5A2FA3] shadow-sm hover:shadow-md"
            }`}
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                <span>Exporting...</span>
              </>
            ) : (
              <>
                <FaDownload className="w-4 h-4" />
                <span>Export {format}</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportOptionsModal;
