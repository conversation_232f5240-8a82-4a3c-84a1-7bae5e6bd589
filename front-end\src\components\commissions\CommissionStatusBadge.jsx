import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faClock,
  faCheckCircle,
  faFileInvoiceDollar,
} from "@fortawesome/free-solid-svg-icons";

const CommissionStatusBadge = ({ status }) => {
  let bgColor, textColor, icon, label;

  switch (status) {
    case "PENDING_CHECKED":
      bgColor = "bg-yellow-100";
      textColor = "text-yellow-800";
      icon = faClock;
      label = "Pending";
      break;
    case "CHECKED_AND_PAID":
      bgColor = "bg-green-100";
      textColor = "text-green-800";
      icon = faFileInvoiceDollar;
      label = "Paid";
      break;
    case "RECONCILED_AND_COMPLETED":
      bgColor = "bg-blue-100";
      textColor = "text-blue-800";
      icon = faCheckCircle;
      label = "Completed";
      break;
    default:
      bgColor = "bg-gray-100";
      textColor = "text-gray-800";
      icon = faClock;
      label = "Unknown";
  }

  return (
    <div className={`${bgColor} ${textColor} px-3 py-1 rounded-full text-sm font-semibold inline-flex items-center`}>
      <FontAwesomeIcon icon={icon} className="mr-1" />
      {label}
    </div>
  );
};

export default CommissionStatusBadge;
