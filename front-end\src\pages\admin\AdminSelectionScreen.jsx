import React from "react";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faChartLine,
  faUserShield,
} from "@fortawesome/free-solid-svg-icons";
import logo from "../../assets/logo.png";
import routes from "../../routes";
import { removeToken } from "../../services/LocalStorageService";

/**
 * A reusable card component for selection options.
 * @param {object} props - The component props.
 * @param {import('@fortawesome/fontawesome-svg-core').IconDefinition} props.icon - The FontAwesome icon to display.
 * @param {string} props.title - The title of the selection card.
 * @param {string} props.description - The description text for the card.
 * @param {() => void} props.onClick - The function to execute when the card is clicked.
 * @param {string} props.hoverBorderColorClass - Tailwind CSS class for the border color on hover.
 * @param {string} props.focusRingColorClass - Tailwind CSS class for the focus ring color.
 * @param {string} props.iconColorClass - Tailwind CSS class for the icon color.
 * @param {string} props.iconBgHoverColorClass - Tailwind CSS class for the icon's background color on group hover.
 */
const SelectionCard = ({
  icon,
  title,
  description,
  onClick,
  hoverBorderColorClass,
  focusRingColorClass,
  iconColorClass,
  iconBgHoverColorClass,
}) => {
  return (
    <button
      onClick={onClick}
      className={`group bg-white border-2 border-transparent ${hoverBorderColorClass} rounded-xl p-7 flex flex-col items-center shadow-md hover:shadow-lg transition-all focus:outline-none focus:ring-2 ${focusRingColorClass} cursor-pointer min-h-[180px] w-full`}
    >
      <div
        className={`mb-4 flex items-center justify-center w-14 h-14 rounded-full bg-[#f4f5f9] ${iconBgHoverColorClass} transition-colors`}
      >
        <FontAwesomeIcon icon={icon} className={`${iconColorClass} text-3xl`} />
      </div>
      <div className="font-bold text-xl text-gray-900 mb-1">{title}</div>
      <div className="text-gray-500 text-center text-base">{description}</div>
    </button>
  );
};

/**
 * A component that displays a selection screen for admin users after login.
 * Allows admins to choose between different management sections.
 */
const AdminSelectionScreen = () => {
  const navigate = useNavigate();

  const handleLogout = () => {
    removeToken("token");
    navigate("/");
  };

  const selectionOptions = [
    {
      id: 'adminPanel',
      title: 'Admin Panel',
      description: 'Manage employees, leads, applications, and more.',
      icon: faUserShield,
      action: () => navigate(routes.adminDashboard),
      colors: {
        hoverBorder: 'hover:border-[#6E39CB]',
        focusRing: 'focus:ring-[#6E39CB]',
        icon: 'text-[#6E39CB]',
        iconBgHover: 'group-hover:bg-[#6E39CB]/10',
      },
    },
    {
      id: 'sourceOfTruth',
      title: 'Source of Truth',
      description: 'Access financial dashboards, Xero imports, targets, and time tracking.',
      icon: faChartLine,
      action: () => navigate(routes.sourceOfTruthDashboard),
      colors: {
        hoverBorder: 'hover:border-[#00C2B7]',
        focusRing: 'focus:ring-[#00C2B7]',
        icon: 'text-[#00C2B7]',
        iconBgHover: 'group-hover:bg-[#00C2B7]/10',
      },
    },
  ];

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-[#f4e9ff] via-[#f4f5f9] to-[#e9e6f7] p-4">
      {/* Top Bar: Logo and Logout */}
      <div className="w-full max-w-3xl flex justify-between items-center mb-8">
        <img src={logo} alt="Logo" className="h-10 w-auto" />
        <button
          onClick={handleLogout}
          className="bg-white border border-red-200 text-red-500 font-bold px-6 py-2 rounded-xl shadow hover:bg-red-500 hover:text-white transition-colors text-base"
        >
          Log out
        </button>
      </div>

      <div className="w-full max-w-3xl bg-white/80 rounded-2xl shadow-xl p-8 md:p-12 flex flex-col items-center">
        <h1 className="text-3xl md:text-4xl font-extrabold text-gray-900 mb-2 text-center">
          What do you want to manage?
        </h1>
        <p className="text-gray-600 text-lg mb-8 text-center max-w-2xl">
          Select a section to access powerful admin features and insights.
        </p>

        {/* Card Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full mb-8">
          {selectionOptions.map((option) => (
            <SelectionCard
              key={option.id}
              icon={option.icon}
              title={option.title}
              description={option.description}
              onClick={option.action}
              hoverBorderColorClass={option.colors.hoverBorder}
              focusRingColorClass={option.colors.focusRing}
              iconColorClass={option.colors.icon}
              iconBgHoverColorClass={option.colors.iconBgHover}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdminSelectionScreen;
