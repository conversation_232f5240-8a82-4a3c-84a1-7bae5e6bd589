import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPhone,
  faEnvelope,
  faCopy,
  faCheck,
  faEllipsisVertical,
  faClipboard,
  faFileInvoiceDollar,
  faFileContract,
  faEdit
} from "@fortawesome/free-solid-svg-icons";
import EditReferenceModal from "../modal/EditReferenceModal";
import { useChangeQuoteStatusMutation, useChangeInvoiceStatusMutation } from "../../services/CompanyAPIService";

const ApplicationCard = ({
  applicationId,
  clientName,
  clientEmail,
  clientPhone,
  candidateName,
  agentName,
  phoneNumber,
  email,
  quoteNumber,
  invoiceNumber,
  quoteStatus,
  invoiceStatus,
  status, // Application status
  applicationStatus, // Alternative name for application status
  price,
  createdAt,
  quoteRequestDetail,
  otherInformation,
  onQuoteUpdated,
  onEditInvoiceRef,
  onEditQuoteRef,
  onChangeStatus,
  onChangeQuoteStatus,
  onChangeInvoiceStatus
}) => {
  // Use applicationStatus if status is not provided
  const currentStatus = status || applicationStatus;
  const [copiedField, setCopiedField] = useState(null);
  const [showDropdown, setShowDropdown] = useState(false);

  // Modal states
  const [isEditQuoteModalOpen, setIsEditQuoteModalOpen] = useState(false);
  const [isEditInvoiceModalOpen, setIsEditInvoiceModalOpen] = useState(false);

  // Use the mutation hooks
  const [changeQuoteStatus] = useChangeQuoteStatusMutation();
  const [changeInvoiceStatus] = useChangeInvoiceStatusMutation();

  // Toggle dropdown menu
  const toggleDropdown = () => setShowDropdown(!showDropdown);

  // Modal handlers
  const handleOpenEditQuoteModal = () => setIsEditQuoteModalOpen(true);
  const handleCloseEditQuoteModal = () => setIsEditQuoteModalOpen(false);

  const handleOpenEditInvoiceModal = () => setIsEditInvoiceModalOpen(true);
  const handleCloseEditInvoiceModal = () => setIsEditInvoiceModalOpen(false);

  // Copy to clipboard function
  const copyToClipboard = (text, field) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    });
  };

  // Format date for display
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status badge color
  const getStatusBadgeColor = (status, type) => {
    const baseColors = {
      quote: {
        PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
        SENT: "bg-green-100 text-green-800 border-green-200",
        DRAFT: "bg-gray-100 text-gray-800 border-gray-200",
        DRAFTED: "bg-gray-100 text-gray-800 border-gray-200",
        ACCEPTED: "bg-green-100 text-green-800 border-green-200"
      },
      invoice: {
        PENDING: "bg-blue-100 text-blue-800 border-blue-200",
        SENT: "bg-purple-100 text-purple-800 border-purple-200",
        DRAFT: "bg-gray-100 text-gray-800 border-gray-200",
        DRAFTED: "bg-gray-100 text-gray-800 border-gray-200",
        ACCEPTED: "bg-green-100 text-green-800 border-green-200"
      },
      application: {
        DOCUMENT_PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
        QUOTE_RAISED: "bg-blue-100 text-blue-800 border-blue-200",
        INVOICE_RAISED: "bg-purple-100 text-purple-800 border-purple-200",
        IN_PROGRESS: "bg-indigo-100 text-indigo-800 border-indigo-200",
        SOFT_COPY_READY: "bg-orange-100 text-orange-800 border-orange-200",
        HARD_COPY_READY: "bg-orange-100 text-orange-800 border-orange-200",
        SOFT_COPY_SENT: "bg-green-100 text-green-800 border-green-200",
        HARD_COPY_SENT: "bg-green-100 text-green-800 border-green-200",
        CLOSED: "bg-gray-100 text-gray-800 border-gray-200",
        FALLOUT: "bg-red-100 text-red-800 border-red-200"
      }
    };

    return baseColors[type]?.[status] || "bg-gray-100 text-gray-800 border-gray-200";
  };

  // Format price to ensure consistent display
  const formattedPrice = typeof price === 'number'
    ? price.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })
    : price?.startsWith('$') ? price : `$${price || 0}`;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
      {/* Card header */}
      <div className="p-5 border-b border-gray-100 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex-1">
          <div className="flex flex-col md:flex-row md:items-center gap-4">
            <div className="flex items-center">
              <div>
                <span className="text-xs font-medium text-gray-500 uppercase">Source</span>
                <div className="flex items-center">
                  <h3 className="text-lg font-semibold text-gray-900 mr-2">{clientName}</h3>
                  <button
                    onClick={() => copyToClipboard(clientName, 'client')}
                    className="text-gray-400 hover:text-gray-600"
                    title="Copy client name"
                  >
                    {copiedField === 'client' ? (
                      <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                    ) : (
                      <FontAwesomeIcon icon={faCopy} size="sm" />
                    )}
                  </button>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <div className="h-4 w-0.5 bg-gray-200 mx-3 hidden md:block"></div>
              <div>
                <span className="text-xs font-medium text-gray-500 uppercase">Candidate</span>
                <div className="flex items-center">
                  <div className="text-sm font-medium text-gray-700 mr-2">{candidateName}</div>
                  <button
                    onClick={() => copyToClipboard(candidateName, 'candidate')}
                    className="text-gray-400 hover:text-gray-600"
                    title="Copy candidate name"
                  >
                    {copiedField === 'candidate' ? (
                      <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                    ) : (
                      <FontAwesomeIcon icon={faCopy} size="sm" />
                    )}
                  </button>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <div className="h-4 w-0.5 bg-gray-200 mx-3 hidden md:block"></div>
              <div>
                <span className="text-xs font-medium text-gray-500 uppercase">Agent</span>
                <div className="flex items-center">
                  <div className="text-sm font-medium text-gray-700 mr-2">{agentName}</div>
                  <button
                    onClick={() => copyToClipboard(agentName, 'agent')}
                    className="text-gray-400 hover:text-gray-600"
                    title="Copy agent name"
                  >
                    {copiedField === 'agent' ? (
                      <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                    ) : (
                      <FontAwesomeIcon icon={faCopy} size="sm" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* Date */}
          <div className="text-sm text-gray-500 hidden md:block">
            {formatDate(createdAt)}
          </div>

          {/* Status badges and dropdowns */}
          <div className="flex gap-2 items-center">
            {/* Application Status with Dropdown */}
            <div className="flex items-center">
              {onChangeStatus && typeof onChangeStatus === 'function' ? (
                <select
                  value={currentStatus || ""}
                  onChange={(e) => {
                    onChangeStatus({
                      applicationId,
                      status: e.target.value
                    }, e.target.value);
                  }}
                  className="px-2 py-1 text-xs border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white"
                >
                  <option value="DOCUMENT_PENDING">DOCUMENT PENDING</option>
                  <option value="QUOTE_RAISED">QUOTE RAISED</option>
                  <option value="INVOICE_RAISED">INVOICE RAISED</option>
                  <option value="IN_PROGRESS">IN PROGRESS</option>
                  <option value="SOFT_COPY_READY">SOFT COPY READY</option>
                  <option value="SOFT_COPY_SENT">SOFT COPY SENT</option>
                  <option value="HARD_COPY_READY">HARD COPY READY</option>
                  <option value="HARD_COPY_SENT">HARD COPY SENT</option>
                  <option value="CLOSED">CLOSED</option>
                  <option value="FALLOUT">FALLOUT</option>
                </select>
              ) : (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusBadgeColor(currentStatus, 'application')}`}>
                  App: {currentStatus ? currentStatus.replace(/_/g, " ") : "N/A"}
                </span>
              )}
            </div>

            {/* Quote Status Badge */}
            {quoteNumber && (
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusBadgeColor(quoteStatus, 'quote')}`}>
                Quote: {quoteStatus}
              </span>
            )}

            {/* Invoice Status Badge */}
            {invoiceNumber && (
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusBadgeColor(invoiceStatus, 'invoice')}`}>
                Invoice: {invoiceStatus}
              </span>
            )}
          </div>

          {/* Price */}
          <div className="flex items-center">
            <div className="font-bold text-lg text-gray-800 mr-2">{formattedPrice}</div>
            <button
              onClick={() => copyToClipboard(formattedPrice, 'price')}
              className="text-gray-400 hover:text-gray-600"
              title="Copy price"
            >
              {copiedField === 'price' ? (
                <FontAwesomeIcon icon={faCheck} className="text-green-500" />
              ) : (
                <FontAwesomeIcon icon={faCopy} />
              )}
            </button>
          </div>

          {/* Actions dropdown */}
          <div className="relative">
            <button
              onClick={toggleDropdown}
              className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
            >
              <FontAwesomeIcon icon={faEllipsisVertical} className="text-gray-500" />
            </button>

            {showDropdown && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-100">
                <div className="py-1">
                  {/* Edit Quote Reference */}
                  {quoteNumber && onEditQuoteRef && typeof onEditQuoteRef === 'function' && (
                    <button
                      onClick={() => {
                        onEditQuoteRef({
                          applicationId,
                          quoteRefNumber: quoteNumber
                        });
                        setShowDropdown(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <FontAwesomeIcon icon={faEdit} className="mr-2" />
                      Edit Quote Reference
                    </button>
                  )}

                  {/* Edit Invoice Reference */}
                  {invoiceNumber && onEditInvoiceRef && typeof onEditInvoiceRef === 'function' && (
                    <button
                      onClick={() => {
                        onEditInvoiceRef({
                          applicationId,
                          invoiceRefNumber: invoiceNumber
                        });
                        setShowDropdown(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <FontAwesomeIcon icon={faEdit} className="mr-2" />
                      Edit Invoice Reference
                    </button>
                  )}

                  {/* Change Quote Status */}
                  {quoteNumber && onChangeQuoteStatus && typeof onChangeQuoteStatus === 'function' && (
                    <button
                      onClick={() => {
                        onChangeQuoteStatus({
                          applicationId,
                          quoteRefNumber: quoteNumber,
                          quoteStatus
                        });
                        setShowDropdown(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <FontAwesomeIcon icon={faFileContract} className="mr-2" />
                      Change Quote Status
                    </button>
                  )}

                  {/* Change Invoice Status */}
                  {invoiceNumber && onChangeInvoiceStatus && typeof onChangeInvoiceStatus === 'function' && (
                    <button
                      onClick={() => {
                        onChangeInvoiceStatus({
                          applicationId,
                          invoiceRefNumber: invoiceNumber,
                          invoiceStatus
                        });
                        setShowDropdown(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <FontAwesomeIcon icon={faFileInvoiceDollar} className="mr-2" />
                      Change Invoice Status
                    </button>
                  )}


                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Card content */}
      <div className="p-5">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column - Contact information and Quote Request Details */}
          <div>
            <div className="flex items-center mb-3">
              <h4 className="text-sm font-semibold text-gray-700">Contact Information</h4>
              <div className="ml-auto flex space-x-2">
                {phoneNumber && (
                  <a href={`tel:${phoneNumber}`} className="text-gray-500 hover:text-blue-600 p-1 bg-gray-100 rounded-full">
                    <FontAwesomeIcon icon={faPhone} />
                  </a>
                )}
                {email && (
                  <a href={`mailto:${email}`} className="text-gray-500 hover:text-blue-600 p-1 bg-gray-100 rounded-full">
                    <FontAwesomeIcon icon={faEnvelope} />
                  </a>
                )}
              </div>
            </div>

            <div className="bg-white p-3 rounded-md border border-gray-200 mb-4">
              <div className="grid grid-cols-2 gap-x-3 gap-y-2 text-sm">
                <h6 className="font-semibold text-gray-700 col-span-2 mb-1 pb-1 border-b border-gray-100">Candidate</h6>
                <div className="flex items-center justify-between col-span-2">
                  <span className="font-medium text-gray-600">Phone:</span>
                  <div className="flex items-center">
                    <span className="text-gray-800 mr-2">{phoneNumber}</span>
                    <button
                      onClick={() => copyToClipboard(phoneNumber, 'phone')}
                      className="text-gray-400 hover:text-gray-600"
                      title="Copy phone number"
                    >
                      {copiedField === 'phone' ? (
                        <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                      ) : (
                        <FontAwesomeIcon icon={faCopy} />
                      )}
                    </button>
                  </div>
                </div>

                <div className="flex items-center justify-between col-span-2">
                  <span className="font-medium text-gray-600">Email:</span>
                  <div className="flex items-center">
                    <span className="text-gray-800 truncate mr-2">{email}</span>
                    <button
                      onClick={() => copyToClipboard(email, 'email')}
                      className="text-gray-400 hover:text-gray-600"
                      title="Copy email"
                    >
                      {copiedField === 'email' ? (
                        <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                      ) : (
                        <FontAwesomeIcon icon={faCopy} />
                      )}
                    </button>
                  </div>
                </div>

                <h6 className="font-semibold text-gray-700 col-span-2 mt-3 mb-1 pb-1 border-b border-gray-100">Source</h6>
                <div className="flex items-center justify-between col-span-2">
                  <span className="font-medium text-gray-600">Phone:</span>
                  <div className="flex items-center">
                    <span className="text-gray-800 mr-2">{clientPhone || "N/A"}</span>
                    {clientPhone && (
                      <button
                        onClick={() => copyToClipboard(clientPhone, 'clientPhone')}
                        className="text-gray-400 hover:text-gray-600"
                        title="Copy client phone number"
                      >
                        {copiedField === 'clientPhone' ? (
                          <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                        ) : (
                          <FontAwesomeIcon icon={faCopy} />
                        )}
                      </button>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between col-span-2">
                  <span className="font-medium text-gray-600">Email:</span>
                  <div className="flex items-center">
                    <span className="text-gray-800 truncate mr-2">{clientEmail || "N/A"}</span>
                    {clientEmail && (
                      <button
                        onClick={() => copyToClipboard(clientEmail, 'clientEmail')}
                        className="text-gray-400 hover:text-gray-600"
                        title="Copy client email"
                      >
                        {copiedField === 'clientEmail' ? (
                          <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                        ) : (
                          <FontAwesomeIcon icon={faCopy} />
                        )}
                      </button>
                    )}
                  </div>
                </div>

                {otherInformation && (
                  <div className="flex items-center justify-between col-span-2 mt-3">
                    <span className="font-medium text-gray-600">Other Info:</span>
                    <div className="flex items-center">
                      <span className="text-gray-800 truncate mr-2">{otherInformation}</span>
                      <button
                        onClick={() => copyToClipboard(otherInformation, 'otherInfo')}
                        className="text-gray-400 hover:text-gray-600"
                        title="Copy other information"
                      >
                        {copiedField === 'otherInfo' ? (
                          <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                        ) : (
                          <FontAwesomeIcon icon={faCopy} />
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Quote Request Details */}
            {quoteRequestDetail && (
              <div>
                <div className="flex items-center mb-2">
                  <h4 className="text-sm font-semibold text-gray-700">Quote Request Details</h4>
                  <button
                    onClick={() => copyToClipboard(quoteRequestDetail, 'quoteDetails')}
                    className="ml-auto text-gray-400 hover:text-gray-600"
                    title="Copy quote request details"
                  >
                    {copiedField === 'quoteDetails' ? (
                      <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                    ) : (
                      <FontAwesomeIcon icon={faCopy} />
                    )}
                  </button>
                </div>
                <div className="bg-white p-3 rounded-md border border-gray-200">
                  <p className="text-gray-800 text-sm">{quoteRequestDetail}</p>
                </div>
              </div>
            )}
          </div>

          {/* Right column - Quote and Invoice information */}
          <div>
            <h4 className="text-sm font-semibold text-gray-700 mb-3">Quote & Invoice Information</h4>
            <div className="grid grid-cols-1 gap-4">
              {/* Quote Card - black and white */}
              <div className="border border-gray-200 bg-white rounded-lg p-4 shadow-sm">
                <div className="flex justify-between items-center mb-3">
                  <div className="flex items-center">
                    <div className="bg-gray-100 p-2 rounded-full mr-3">
                      <FontAwesomeIcon icon={faFileContract} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-700 text-xs uppercase">Quote Reference</p>
                      <div className="flex items-center">
                        <p className="text-xl font-bold text-gray-800 mr-2">{quoteNumber || "-"}</p>
                        {quoteNumber && (
                          <button
                            onClick={() => copyToClipboard(quoteNumber, 'quoteNumber')}
                            className="text-gray-400 hover:text-gray-600"
                            title="Copy quote reference"
                          >
                            {copiedField === 'quoteNumber' ? (
                              <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                            ) : (
                              <FontAwesomeIcon icon={faCopy} />
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <label className="block text-xs font-medium text-gray-600 mb-1">Status</label>
                <div className="flex items-center">
                  <div className={`flex-1 font-medium text-sm px-2 py-1 rounded ${getStatusBadgeColor(quoteStatus, 'quote')}`}>
                    {quoteStatus || "N/A"}
                  </div>
                </div>

                {/* Action buttons for Quote */}
                {quoteNumber && (
                  <div className="mt-3 flex space-x-2">
                    <button
                      onClick={() => {
                        if (onEditQuoteRef && typeof onEditQuoteRef === 'function') {
                          onEditQuoteRef({
                            applicationId,
                            quoteRefNumber: quoteNumber
                          });
                        } else {
                          handleOpenEditQuoteModal();
                        }
                      }}
                      className="flex-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded flex items-center justify-center"
                    >
                      <FontAwesomeIcon icon={faEdit} className="mr-1" />
                      Edit Reference
                    </button>

                    {onChangeQuoteStatus && typeof onChangeQuoteStatus === 'function' && (
                      <button
                        onClick={() => {
                          onChangeQuoteStatus({
                            applicationId,
                            quoteRefNumber: quoteNumber,
                            quoteStatus
                          });
                        }}
                        className="flex-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded flex items-center justify-center"
                      >
                        <FontAwesomeIcon icon={faFileContract} className="mr-1" />
                        Change Status
                      </button>
                    )}
                  </div>
                )}
              </div>

              {/* Invoice Card - black and white */}
              <div className="border border-gray-200 bg-white rounded-lg p-4 shadow-sm">
                <div className="flex justify-between items-center mb-3">
                  <div className="flex items-center">
                    <div className="bg-gray-100 p-2 rounded-full mr-3">
                      <FontAwesomeIcon icon={faFileInvoiceDollar} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-700 text-xs uppercase">Invoice Reference</p>
                      <div className="flex items-center">
                        <p className="text-xl font-bold text-gray-800 mr-2">{invoiceNumber || "-"}</p>
                        {invoiceNumber && (
                          <button
                            onClick={() => copyToClipboard(invoiceNumber, 'invoiceNumber')}
                            className="text-gray-400 hover:text-gray-600"
                            title="Copy invoice reference"
                          >
                            {copiedField === 'invoiceNumber' ? (
                              <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                            ) : (
                              <FontAwesomeIcon icon={faCopy} />
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <label className="block text-xs font-medium text-gray-600 mb-1">Status</label>
                <div className="flex items-center">
                  <div className={`flex-1 font-medium text-sm px-2 py-1 rounded ${getStatusBadgeColor(invoiceStatus, 'invoice')}`}>
                    {invoiceStatus || "N/A"}
                  </div>
                </div>

                {/* Action buttons for Invoice */}
                {invoiceNumber && (
                  <div className="mt-3 flex space-x-2">
                    <button
                      onClick={() => {
                        if (onEditInvoiceRef && typeof onEditInvoiceRef === 'function') {
                          onEditInvoiceRef({
                            applicationId,
                            invoiceRefNumber: invoiceNumber
                          });
                        } else {
                          handleOpenEditInvoiceModal();
                        }
                      }}
                      className="flex-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded flex items-center justify-center"
                    >
                      <FontAwesomeIcon icon={faEdit} className="mr-1" />
                      Edit Reference
                    </button>

                    {onChangeInvoiceStatus && typeof onChangeInvoiceStatus === 'function' && (
                      <button
                        onClick={() => {
                          onChangeInvoiceStatus({
                            applicationId,
                            invoiceRefNumber: invoiceNumber,
                            invoiceStatus
                          });
                        }}
                        className="flex-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded flex items-center justify-center"
                      >
                        <FontAwesomeIcon icon={faFileInvoiceDollar} className="mr-1" />
                        Change Status
                      </button>
                    )}
                  </div>
                )}
              </div>


            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <EditReferenceModal
        isOpen={isEditQuoteModalOpen}
        onClose={handleCloseEditQuoteModal}
        type="quote"
        applicationId={applicationId}
        currentRefNumber={quoteNumber}
        onSuccess={() => {
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />

      <EditReferenceModal
        isOpen={isEditInvoiceModalOpen}
        onClose={handleCloseEditInvoiceModal}
        type="invoice"
        applicationId={applicationId}
        currentRefNumber={invoiceNumber}
        onSuccess={() => {
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />
    </div>
  );
};

export default ApplicationCard;


