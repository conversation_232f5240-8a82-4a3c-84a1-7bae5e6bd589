import React, { useState, useEffect } from "react";
import ReactSelect from "react-select";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTimes,
  faArrowLeft,
  faArrowRight,
  faUser,
  faUsers,
  faPlus,
  faTrash,
  faCheck,
  faGraduationCap,
  faEnvelope,
  faPhone,
  faMapMarkerAlt
} from "@fortawesome/free-solid-svg-icons";
import { useGetLeadsQuery } from "../../services/CompanyAPIService";
import { getCurrentLocalDateTime } from "../../utils/formatters";

const MultiStepCreateApplicationModal = ({
  isOpen,
  onClose,
  onCreate,
  qualifications,
  preSelectedLead = null, // Optional pre-selected lead from leads page
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedPath, setSelectedPath] = useState(null); // 'existing' or 'individual'
  const [selectedLead, setSelectedLead] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  
  // Form data
  const [formData, setFormData] = useState({
    // Step 1 - Lead Association
    leadPhone: "",

    // Step 2 - Core Applicant Information
    applicantName: "",
    applicantEmail: "",
    applicantPhone: "",
    hardcopyAddress: "",

    // Step 3 - Supplementary Details
    otherInformation: "",
    createdOn: getCurrentLocalDateTime(), // Default to current datetime
  });

  // Qualifications management
  const [soldQualifications, setSoldQualifications] = useState([]);

  const [errors, setErrors] = useState({});

  // Get leads data
  const { data: leads = [] } = useGetLeadsQuery();

  // Filter leads based on search term
  const filteredLeads = leads.filter(lead => 
    lead.leadName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lead.phone.includes(searchTerm) ||
    lead.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    if (isOpen) {
      // Reset form when modal opens
      setCurrentStep(preSelectedLead ? 2 : 1); // Skip step 1 if lead is pre-selected
      setSelectedPath(preSelectedLead ? 'existing' : null);
      setSelectedLead(preSelectedLead);
      setSearchTerm("");
      setFormData({
        leadPhone: preSelectedLead?.phone || "",
        applicantName: "",
        applicantEmail: preSelectedLead?.email || "",
        applicantPhone: preSelectedLead?.phone || "",
        hardcopyAddress: "",
        otherInformation: "",
        createdOn: getCurrentLocalDateTime(),
      });
      setSoldQualifications([]);
      setErrors({});
    }
  }, [isOpen, preSelectedLead]);

  // Handle lead selection
  const handleLeadSelection = (lead) => {
    setSelectedLead(lead);
    setFormData(prev => ({
      ...prev,
      leadPhone: lead.phone,
      applicantEmail: lead.email,
      applicantPhone: lead.phone,
    }));
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Qualification management functions
  const addQualification = () => {
    setSoldQualifications(prev => [...prev, {
      id: Date.now(), // temporary ID for frontend
      qualificationId: "",
      qualificationName: "",
      price: "",
    }]);
  };

  const removeQualification = (id) => {
    setSoldQualifications(prev => prev.filter(q => q.id !== id));
  };

  const updateQualification = (id, field, value) => {
    setSoldQualifications(prev => prev.map(q => {
      if (q.id === id) {
        const updated = { ...q, [field]: value };

        // Auto-fill qualification name and default price when qualification is selected
        if (field === 'qualificationId' && qualifications) {
          const selectedQual = qualifications.find(qual => qual.qualificationId === value);
          if (selectedQual) {
            updated.qualificationName = selectedQual.qualificationName;
            updated.price = selectedQual.rplPrice || "";
          }
        }

        return updated;
      }
      return q;
    }));
  };

  // Validation functions
  const validateStep1 = () => {
    const newErrors = {};
    
    if (!selectedPath) {
      newErrors.path = "Please select a path to continue.";
    }
    
    if (selectedPath === 'existing' && !selectedLead) {
      newErrors.lead = "Please select a lead.";
    }
    
    if (selectedPath === 'individual') {
      if (!formData.applicantName.trim()) {
        newErrors.applicantName = "Applicant name is required.";
      }
      if (!formData.applicantEmail.trim()) {
        newErrors.applicantEmail = "Email is required.";
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.applicantEmail)) {
        newErrors.applicantEmail = "Invalid email format.";
      }
      if (!formData.applicantPhone.trim()) {
        newErrors.applicantPhone = "Phone is required.";
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = () => {
    const newErrors = {};

    if (!formData.applicantName.trim()) {
      newErrors.applicantName = "Applicant name is required.";
    }
    // hardcopyAddress is now optional - no validation required

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep3 = () => {
    const newErrors = {};

    if (soldQualifications.length === 0) {
      newErrors.qualifications = "Please add at least one qualification.";
    } else {
      // Validate each qualification
      const invalidQuals = soldQualifications.some(q =>
        !q.qualificationId || !q.price || parseFloat(q.price) <= 0
      );
      if (invalidQuals) {
        newErrors.qualifications = "Please complete all qualification details with valid prices.";
      }
    }

    if (!formData.createdOn) {
      newErrors.createdOn = "Created date is required.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Navigation functions
  const handleNext = () => {
    let isValid = false;
    
    switch (currentStep) {
      case 1:
        isValid = validateStep1();
        break;
      case 2:
        isValid = validateStep2();
        break;
      default:
        isValid = true;
    }
    
    if (isValid) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = () => {
    if (validateStep3()) {
      // Prepare final data in the format expected by the backend
      const finalData = {
        applicantName: formData.applicantName,
        applicantEmail: formData.applicantEmail,
        applicantPhone: formData.applicantPhone,
        applicantAddress: formData.hardcopyAddress,
        leadPhone: selectedPath === 'existing' ? selectedLead?.phone : null,
        otherInformation: formData.otherInformation,
        createdOn: formData.createdOn,
        soldQualifications: soldQualifications.map(q => ({
          qualificationId: q.qualificationId,
          price: parseFloat(q.price),
        })),
      };

      onCreate(finalData);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 overflow-auto">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Create New Application</h2>
              <p className="text-sm text-gray-600 mt-1">
                {preSelectedLead ? (
                  `Step ${currentStep - 1} of 2: ${
                    currentStep === 2 ? "Applicant Information" : "Qualifications & Details"
                  }`
                ) : (
                  `Step ${currentStep} of 3: ${
                    currentStep === 1 ? "Lead Association" :
                    currentStep === 2 ? "Applicant Information" :
                    "Qualifications & Details"
                  }`
                )}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-md hover:bg-gray-100 transition-colors"
            >
              <FontAwesomeIcon icon={faTimes} size="lg" />
            </button>
          </div>
        </div>

        {/* Content Container */}
        <div className="px-6 py-4 max-h-[calc(90vh-160px)] overflow-y-auto">
          {/* Pre-selected Lead Banner */}
          {preSelectedLead && (
            <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full mr-3">
                  <FontAwesomeIcon icon={faUsers} size="sm" />
                </div>
                <div className="flex-1">
                  <h3 className="text-base font-semibold text-gray-900 mb-1">Creating Application for Selected Lead</h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span className="flex items-center">
                      <FontAwesomeIcon icon={faUser} className="mr-1" />
                      <strong>{preSelectedLead.leadName}</strong>
                    </span>
                    <span className="flex items-center">
                      <FontAwesomeIcon icon={faPhone} className="mr-1" />
                      {preSelectedLead.phone}
                    </span>
                    <span className="flex items-center">
                      <FontAwesomeIcon icon={faEnvelope} className="mr-1" />
                      {preSelectedLead.email}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    preSelectedLead.status === 'HOT' ? 'bg-red-100 text-red-800' :
                    preSelectedLead.status === 'WARM' ? 'bg-orange-100 text-orange-800' :
                    preSelectedLead.status === 'COLD' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {preSelectedLead.status}
                  </span>
                </div>
              </div>
            </div>
          )}
          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              {preSelectedLead ? (
                // Show only 2 steps when lead is pre-selected
                [
                  { step: 2, title: "Applicant Info", icon: faUser, displayStep: 1 },
                  { step: 3, title: "Qualifications", icon: faGraduationCap, displayStep: 2 }
                ].map((item, index) => (
                  <React.Fragment key={item.step}>
                    <div className="flex flex-col items-center">
                      <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all ${
                        item.step <= currentStep
                          ? 'bg-blue-600 border-blue-600 text-white'
                          : 'border-gray-300 text-gray-400'
                      }`}>
                        {item.step < currentStep ? (
                          <FontAwesomeIcon icon={faCheck} size="sm" />
                        ) : (
                          <FontAwesomeIcon icon={item.icon} size="sm" />
                        )}
                      </div>
                      <span className={`mt-2 text-xs font-medium ${
                        item.step <= currentStep ? 'text-blue-600' : 'text-gray-500'
                      }`}>
                        {item.title}
                      </span>
                    </div>
                    {index < 1 && (
                      <div className={`flex-1 h-0.5 mx-3 rounded-full transition-all ${
                        item.step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </React.Fragment>
                ))
              ) : (
                // Show all 3 steps when no lead is pre-selected
                [
                  { step: 1, title: "Lead Selection", icon: faUsers },
                  { step: 2, title: "Applicant Info", icon: faUser },
                  { step: 3, title: "Qualifications", icon: faGraduationCap }
                ].map((item, index) => (
                  <React.Fragment key={item.step}>
                    <div className="flex flex-col items-center">
                      <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all ${
                        item.step <= currentStep
                          ? 'bg-blue-600 border-blue-600 text-white'
                          : 'border-gray-300 text-gray-400'
                      }`}>
                        {item.step < currentStep ? (
                          <FontAwesomeIcon icon={faCheck} size="sm" />
                        ) : (
                          <FontAwesomeIcon icon={item.icon} size="sm" />
                        )}
                      </div>
                      <span className={`mt-2 text-xs font-medium ${
                        item.step <= currentStep ? 'text-blue-600' : 'text-gray-500'
                      }`}>
                        {item.title}
                      </span>
                    </div>
                    {index < 2 && (
                      <div className={`flex-1 h-0.5 mx-3 rounded-full transition-all ${
                        item.step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </React.Fragment>
                ))
              )}
            </div>
          </div>

          {/* Step Content */}
          <div className="min-h-[400px]">
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Choose Your Path</h3>
                  <p className="text-gray-600">How would you like to create this application?</p>
                </div>

                {/* Path Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    className={`border rounded-lg p-6 cursor-pointer transition-all ${
                      selectedPath === 'existing'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedPath('existing')}
                  >
                    <div className="text-center">
                      <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-3 ${
                        selectedPath === 'existing' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600'
                      }`}>
                        <FontAwesomeIcon icon={faUsers} />
                      </div>
                      <h4 className="text-base font-semibold text-gray-900 mb-2">From Existing Lead</h4>
                      <p className="text-sm text-gray-600">Choose from leads already in your system</p>
                      {selectedPath === 'existing' && (
                        <div className="mt-3 flex items-center justify-center text-blue-600">
                          <FontAwesomeIcon icon={faCheck} className="mr-2" />
                          <span className="text-sm font-medium">Selected</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div
                    className={`border rounded-lg p-6 cursor-pointer transition-all ${
                      selectedPath === 'individual'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedPath('individual')}
                  >
                    <div className="text-center">
                      <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full mb-3 ${
                        selectedPath === 'individual' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600'
                      }`}>
                        <FontAwesomeIcon icon={faUser} />
                      </div>
                      <h4 className="text-base font-semibold text-gray-900 mb-2">New Individual</h4>
                      <p className="text-sm text-gray-600">Create an application for someone new</p>
                      {selectedPath === 'individual' && (
                        <div className="mt-3 flex items-center justify-center text-blue-600">
                          <FontAwesomeIcon icon={faCheck} className="mr-2" />
                          <span className="text-sm font-medium">Selected</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {errors.path && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p className="text-red-600 text-sm font-medium">{errors.path}</p>
                  </div>
                )}
              
                {/* Lead Selection */}
                {selectedPath === 'existing' && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                      <FontAwesomeIcon icon={faUsers} className="text-blue-600 mr-2" />
                      Select Lead
                    </h4>

                    <div className="mb-4">
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="Search leads by name, phone, email, or company..."
                          className="w-full border border-gray-300 rounded-lg p-3 pl-10 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                          </svg>
                        </div>
                        {searchTerm && (
                          <button
                            onClick={() => setSearchTerm('')}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>

                    <div className="max-h-80 overflow-y-auto border border-gray-200 rounded-lg">
                      {filteredLeads.length > 0 ? (
                        filteredLeads.map((lead) => (
                          <div
                            key={lead.phone}
                            className={`p-4 border-b border-gray-100 last:border-b-0 cursor-pointer transition-all duration-200 ${
                              selectedLead?.phone === lead.phone
                                ? 'bg-blue-50 border-l-4 border-l-blue-500'
                                : 'hover:bg-gray-50'
                            }`}
                            onClick={() => handleLeadSelection(lead)}
                          >
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="flex items-center mb-2">
                                  <h6 className="font-semibold text-gray-800">{lead.leadName}</h6>
                                  {selectedLead?.phone === lead.phone && (
                                    <FontAwesomeIcon icon={faCheck} className="text-blue-600 ml-2" />
                                  )}
                                </div>
                                <p className="text-sm text-gray-600 mb-1">{lead.companyName}</p>
                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                  <span className="flex items-center">
                                    <FontAwesomeIcon icon={faPhone} className="mr-1" />
                                    {lead.phone}
                                  </span>
                                  <span className="flex items-center">
                                    <FontAwesomeIcon icon={faEnvelope} className="mr-1" />
                                    {lead.email}
                                  </span>
                                </div>
                              </div>
                              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                                lead.status === 'HOT' ? 'bg-red-100 text-red-800' :
                                lead.status === 'WARM' ? 'bg-orange-100 text-orange-800' :
                                lead.status === 'COLD' ? 'bg-blue-100 text-blue-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {lead.status}
                              </span>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="p-8 text-center text-gray-500">
                          <FontAwesomeIcon icon={faUsers} className="text-4xl mb-4 text-gray-300" />
                          <p>
                            {searchTerm ?
                              `No leads match "${searchTerm}". Try a different search term.` :
                              "Start typing to search for leads in your system."
                            }
                          </p>
                        </div>
                      )}
                    </div>

                    {errors.lead && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-3 mt-4">
                        <p className="text-red-600 text-sm font-medium">{errors.lead}</p>
                      </div>
                    )}
                  </div>
                )}
              
                {/* Individual Form */}
                {selectedPath === 'individual' && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                      <FontAwesomeIcon icon={faUser} className="text-blue-600 mr-2" />
                      Applicant Information
                    </h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-gray-700 font-medium mb-2">
                          <FontAwesomeIcon icon={faUser} className="mr-2 text-gray-500" />
                          Applicant Name<span className="text-red-500 ml-1">*</span>
                        </label>
                        <input
                          type="text"
                          name="applicantName"
                          value={formData.applicantName}
                          onChange={handleInputChange}
                          className={`w-full border rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                            errors.applicantName ? "border-red-500 bg-red-50" : "border-gray-300"
                          }`}
                          placeholder="Enter applicant name"
                        />
                        {errors.applicantName && (
                          <p className="text-red-500 text-sm mt-2 flex items-center">
                            <FontAwesomeIcon icon={faTimes} className="mr-1" />
                            {errors.applicantName}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-gray-700 font-medium mb-2">
                          <FontAwesomeIcon icon={faEnvelope} className="mr-2 text-gray-500" />
                          Email<span className="text-red-500 ml-1">*</span>
                        </label>
                        <input
                          type="email"
                          name="applicantEmail"
                          value={formData.applicantEmail}
                          onChange={handleInputChange}
                          className={`w-full border rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                            errors.applicantEmail ? "border-red-500 bg-red-50" : "border-gray-300"
                          }`}
                          placeholder="Enter email address"
                        />
                        {errors.applicantEmail && (
                          <p className="text-red-500 text-sm mt-2 flex items-center">
                            <FontAwesomeIcon icon={faTimes} className="mr-1" />
                            {errors.applicantEmail}
                          </p>
                        )}
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-gray-700 font-medium mb-2">
                          <FontAwesomeIcon icon={faPhone} className="mr-2 text-gray-500" />
                          Phone<span className="text-red-500 ml-1">*</span>
                        </label>
                        <input
                          type="tel"
                          name="applicantPhone"
                          value={formData.applicantPhone}
                          onChange={handleInputChange}
                          className={`w-full border rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                            errors.applicantPhone ? "border-red-500 bg-red-50" : "border-gray-300"
                          }`}
                          placeholder="Enter phone number"
                        />
                        {errors.applicantPhone && (
                          <p className="text-red-500 text-sm mt-2 flex items-center">
                            <FontAwesomeIcon icon={faTimes} className="mr-1" />
                            {errors.applicantPhone}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
            </div>
          )}

            {currentStep === 2 && (
              <div className="space-y-8">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">Core Applicant Information</h3>
                  <p className="text-gray-600">Complete the applicant details and address information</p>
                </div>

                <div className="bg-white border border-gray-200 rounded-xl p-8 shadow-sm">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <label className="block text-gray-700 font-medium mb-3">
                        <FontAwesomeIcon icon={faUser} className="mr-2 text-blue-600" />
                        Applicant Name<span className="text-red-500 ml-1">*</span>
                      </label>
                      <input
                        type="text"
                        name="applicantName"
                        value={formData.applicantName}
                        onChange={handleInputChange}
                        className={`w-full border rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.applicantName ? "border-red-500 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="Enter applicant name"
                      />
                      {errors.applicantName && (
                        <p className="text-red-500 text-sm mt-2 flex items-center">
                          <FontAwesomeIcon icon={faTimes} className="mr-1" />
                          {errors.applicantName}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-700 font-medium mb-3">
                        <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-2 text-blue-600" />
                        Hardcopy Address <span className="text-gray-500 text-sm">(Optional)</span>
                      </label>
                      <textarea
                        name="hardcopyAddress"
                        value={formData.hardcopyAddress}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full border border-gray-300 rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                        placeholder="Enter complete hardcopy address (optional)"
                      />
                    </div>

                    <div>
                      <label className="block text-gray-700 font-medium mb-3">
                        <FontAwesomeIcon icon={faEnvelope} className="mr-2 text-gray-400" />
                        Applicant Email <span className="text-gray-500 text-sm">(Read-Only)</span>
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          value={formData.applicantEmail}
                          readOnly
                          className="w-full border border-gray-300 rounded-lg p-3 bg-gray-50 text-gray-600"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">Pre-filled from previous step</p>
                    </div>

                    <div>
                      <label className="block text-gray-700 font-medium mb-3">
                        <FontAwesomeIcon icon={faPhone} className="mr-2 text-gray-400" />
                        Applicant Phone <span className="text-gray-500 text-sm">(Read-Only)</span>
                      </label>
                      <div className="relative">
                        <input
                          type="tel"
                          value={formData.applicantPhone}
                          readOnly
                          className="w-full border border-gray-300 rounded-lg p-3 bg-gray-50 text-gray-600"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">Pre-filled from previous step</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div className="space-y-8">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">Qualifications & Final Details</h3>
                  <p className="text-gray-600">Add qualifications with their prices and complete the application</p>
                </div>

                {/* Qualifications Section */}
                <div className="bg-white border border-gray-200 rounded-xl p-8 shadow-sm">
                  <div className="flex items-center justify-between mb-6">
                    <h4 className="text-xl font-semibold text-gray-800 flex items-center">
                      <FontAwesomeIcon icon={faGraduationCap} className="mr-3 text-purple-600" />
                      Qualifications
                    </h4>
                    <button
                      type="button"
                      onClick={addQualification}
                      className="flex items-center bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-all duration-200 shadow-md hover:shadow-lg"
                    >
                      <FontAwesomeIcon icon={faPlus} className="mr-2" />
                      Add Qualification
                    </button>
                  </div>

                  {soldQualifications.length === 0 ? (
                    <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                      <FontAwesomeIcon icon={faGraduationCap} className="text-6xl text-gray-300 mb-4" />
                      <p className="text-gray-500 text-lg mb-4">No qualifications added yet</p>
                      <button
                        type="button"
                        onClick={addQualification}
                        className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-all duration-200"
                      >
                        Add Your First Qualification
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {soldQualifications.map((qual, index) => (
                        <div key={qual.id} className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                          <div className="flex items-center justify-between mb-4">
                            <h5 className="text-lg font-medium text-gray-800">
                              Qualification #{index + 1}
                            </h5>
                            <button
                              type="button"
                              onClick={() => removeQualification(qual.id)}
                              className="text-red-500 hover:text-red-700 p-2 rounded-full hover:bg-red-50 transition-all duration-200"
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </button>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="md:col-span-2">
                              <label className="block text-gray-700 font-medium mb-2">
                                Select Qualification<span className="text-red-500 ml-1">*</span>
                              </label>
                              <ReactSelect
                                options={qualifications?.map((q) => ({
                                  label: `${q.qualificationId} - ${q.qualificationName}`,
                                  value: q.qualificationId,
                                  qualificationName: q.qualificationName,
                                  rplPrice: q.rplPrice
                                }))}
                                value={
                                  qual.qualificationId
                                    ? {
                                        label: `${qual.qualificationId} - ${qual.qualificationName}`,
                                        value: qual.qualificationId
                                      }
                                    : null
                                }
                                onChange={(selected) => {
                                  if (selected) {
                                    updateQualification(qual.id, 'qualificationId', selected.value);
                                  } else {
                                    updateQualification(qual.id, 'qualificationId', '');
                                  }
                                }}
                                isSearchable
                                isClearable
                                placeholder="Search or select a qualification..."
                                className="react-select-container"
                                classNamePrefix="react-select"
                                styles={{
                                  control: (provided, state) => ({
                                    ...provided,
                                    borderColor: state.isFocused ? '#6E39CB' : '#d1d5db',
                                    boxShadow: state.isFocused ? '0 0 0 2px rgba(110, 57, 203, 0.2)' : 'none',
                                    '&:hover': {
                                      borderColor: '#6E39CB'
                                    },
                                    padding: '4px',
                                    minHeight: '48px'
                                  }),
                                  option: (provided, state) => ({
                                    ...provided,
                                    backgroundColor: state.isSelected ? '#6E39CB' : state.isFocused ? '#f3f4f6' : 'white',
                                    color: state.isSelected ? 'white' : '#374151',
                                    '&:hover': {
                                      backgroundColor: state.isSelected ? '#6E39CB' : '#f3f4f6'
                                    }
                                  })
                                }}
                              />
                            </div>

                            <div>
                              <label className="block text-gray-700 font-medium mb-2">
                                Price (AUD)<span className="text-red-500 ml-1">*</span>
                              </label>
                              <div className="relative">
                                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">$</span>
                                <input
                                  type="number"
                                  value={qual.price}
                                  onChange={(e) => updateQualification(qual.id, 'price', e.target.value)}
                                  className="w-full border border-gray-300 rounded-lg p-3 pl-8 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                                  placeholder="0.00"
                                  min="0"
                                  step="0.01"
                                />
                              </div>
                            </div>
                          </div>

                          {qual.qualificationName && (
                            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                              <p className="text-blue-800 text-sm">
                                <FontAwesomeIcon icon={faCheck} className="mr-2" />
                                Selected: {qual.qualificationName}
                              </p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  {errors.qualifications && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
                      <p className="text-red-600 text-sm font-medium flex items-center">
                        <FontAwesomeIcon icon={faTimes} className="mr-2" />
                        {errors.qualifications}
                      </p>
                    </div>
                  )}
                </div>

                {/* Additional Details Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Created Date
                    </h4>
                    <input
                      type="datetime-local"
                      name="createdOn"
                      value={formData.createdOn}
                      onChange={handleInputChange}
                      className={`w-full border rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.createdOn ? "border-red-500 bg-red-50" : "border-gray-300"
                      }`}
                    />
                    {errors.createdOn && (
                      <p className="text-red-500 text-sm mt-2 flex items-center">
                        <FontAwesomeIcon icon={faTimes} className="mr-1" />
                        {errors.createdOn}
                      </p>
                    )}
                  </div>

                  <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Other Information
                    </h4>
                    <textarea
                      name="otherInformation"
                      value={formData.otherInformation}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full border border-gray-300 rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none"
                      placeholder="Enter any additional information..."
                    />
                  </div>
                </div>

                {/* Summary Section */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-8">
                  <h4 className="text-xl font-bold text-gray-800 mb-6 flex items-center">
                    <FontAwesomeIcon icon={faCheck} className="mr-3 text-green-600" />
                    Application Summary
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faUser} className="mr-3 text-blue-600 w-4" />
                        <span className="font-medium text-gray-700">Applicant:</span>
                        <span className="ml-2 text-gray-800">{formData.applicantName || "Not entered"}</span>
                      </div>
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faEnvelope} className="mr-3 text-blue-600 w-4" />
                        <span className="font-medium text-gray-700">Email:</span>
                        <span className="ml-2 text-gray-800">{formData.applicantEmail || "Not entered"}</span>
                      </div>
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faPhone} className="mr-3 text-blue-600 w-4" />
                        <span className="font-medium text-gray-700">Phone:</span>
                        <span className="ml-2 text-gray-800">{formData.applicantPhone || "Not entered"}</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faGraduationCap} className="mr-3 text-purple-600 w-4" />
                        <span className="font-medium text-gray-700">Qualifications:</span>
                        <span className="ml-2 text-gray-800">{soldQualifications.length} selected</span>
                      </div>
                      {selectedLead && (
                        <div className="flex items-center">
                          <FontAwesomeIcon icon={faUsers} className="mr-3 text-green-600 w-4" />
                          <span className="font-medium text-gray-700">Associated Lead:</span>
                          <span className="ml-2 text-gray-800">{selectedLead.leadName}</span>
                        </div>
                      )}
                      <div className="flex items-center">
                        <span className="mr-3 text-orange-600 w-4">💰</span>
                        <span className="font-medium text-gray-700">Total Price:</span>
                        <span className="ml-2 text-gray-800 font-bold">
                          ${soldQualifications.reduce((sum, q) => sum + (parseFloat(q.price) || 0), 0).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
        </div>

        </div>

        {/* Navigation Buttons */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <button
              onClick={(preSelectedLead && currentStep === 2) || (!preSelectedLead && currentStep === 1) ? onClose : handlePrevious}
              className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-white transition-colors"
            >
              <FontAwesomeIcon icon={(preSelectedLead && currentStep === 2) || (!preSelectedLead && currentStep === 1) ? faTimes : faArrowLeft} className="mr-2" />
              {(preSelectedLead && currentStep === 2) || (!preSelectedLead && currentStep === 1) ? "Cancel" : "Previous"}
            </button>

            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500">
                {preSelectedLead ? (
                  `Step ${currentStep - 1} of 2`
                ) : (
                  `Step ${currentStep} of 3`
                )}
              </span>
              <button
                onClick={currentStep === 3 ? handleSubmit : handleNext}
                className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
              >
                {currentStep === 3 ? (
                  <>
                    <FontAwesomeIcon icon={faCheck} className="mr-2" />
                    Create Application
                  </>
                ) : (
                  <>
                    Continue
                    <FontAwesomeIcon icon={faArrowRight} className="ml-2" />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiStepCreateApplicationModal;
