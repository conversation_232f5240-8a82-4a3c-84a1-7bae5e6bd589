import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFileInvoice, faUpload, faSpinner } from "@fortawesome/free-solid-svg-icons";

// Helper function to get status color based on status value
const getStatusColor = (status) => {
  if (!status) return 'bg-gray-100 text-gray-600';

  switch (status.toLowerCase()) {
    case 'paid':
      return 'bg-green-100 text-green-800';
    case 'approved':
      return 'bg-blue-100 text-blue-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'expired':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-600';
  }
};

// Helper function to get invoice sent status color
const getInvoiceSentColor = (status) => {
  if (!status) return 'bg-gray-100 text-gray-600';

  switch (status.toUpperCase()) {
    case 'SENT':
      return 'bg-green-100 text-green-800';
    case 'NOT_SENT':
      return 'bg-red-100 text-red-800';
    case 'DRAFTED':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-600';
  }
};

// Helper function to format invoice sent status for display
const formatInvoiceSentStatus = (status) => {
  if (!status) return '-';

  switch (status.toUpperCase()) {
    case 'SENT':
      return 'Sent';
    case 'NOT_SENT':
      return 'Not Sent';
    case 'DRAFTED':
      return 'Drafted';
    default:
      return status;
  }
};

const KPI1InvoicingTable = ({
  filteredData,
  setImportTab,
  setIsModalOpen,
  isLoading,
}) => {
  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-[#3A3541]">
          <FontAwesomeIcon icon={faFileInvoice} className="mr-2 text-[#6E39CB]" />
          KPI1 Invoicing Data
        </h2>
        <button
          onClick={() => {
            setImportTab("KPI1");
            setIsModalOpen(true);
          }}
          className="flex items-center gap-2 px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-opacity-90 transition-colors"
        >
          <FontAwesomeIcon icon={faUpload} />
          Import Data
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-[#DBDCDE] overflow-hidden">
        <div className="overflow-x-auto" style={{ maxWidth: '100%', overflowX: 'auto' }}>
          <table className="w-full divide-y divide-[#DBDCDE] table-fixed">
            <thead className="bg-[#F9FAFB]">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[120px]">
                  Invoice #
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[150px]">
                  Contact
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[100px]">
                  Inv. Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[100px]">
                  Due Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[100px]">
                  Reference
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[80px]">
                  Gross
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[80px]">
                  Balance
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[100px]">
                  Status
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[120px]">
                  Source
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[100px]">
                  Inv. Sent
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider w-[120px]">
                  Agent
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-[#DBDCDE]">
              {isLoading ? (
                <tr>
                  <td colSpan="11" className="px-3 py-8 text-center text-[#89868D]">
                    <div className="flex flex-col items-center">
                      <FontAwesomeIcon icon={faSpinner} className="text-3xl mb-2 text-[#6E39CB] animate-spin" />
                      <p className="text-[#3A3541] font-medium">Loading invoice data...</p>
                    </div>
                  </td>
                </tr>
              ) : filteredData.length > 0 ? (
                filteredData.map((item, index) => (
                  <tr key={index} className="hover:bg-[#F9FAFB] transition-colors">
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541] truncate">
                      {item.invoiceNumber ? (
                        <a
                          href={`/admin/application/profile/${item.applicationId || 'MJ99OSE'}`}
                          className="text-[#6E39CB] hover:underline"
                        >
                          {item.invoiceNumber}
                        </a>
                      ) : (
                        <span className="text-[#89868D]">-</span>
                      )}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541] truncate" title={item.contact}>
                      {item.contact}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.invoiceDate}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.dueDate}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.reference}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      ${parseFloat(item.gross).toLocaleString()}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      ${parseFloat(item.balance).toLocaleString()}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(item.status)}`}>
                        {item.status || '-'}
                      </span>
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541] truncate" title={item.source}>
                      {item.source}
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      <span className={`px-2 py-1 rounded-full text-xs ${getInvoiceSentColor(item.invoiceSent)}`}>
                        {formatInvoiceSentStatus(item.invoiceSent)}
                      </span>
                    </td>
                    <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                      {item.agentCommission || '-'}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="11" className="px-3 py-8 text-center text-[#89868D]">
                    <div className="flex flex-col items-center">
                      <FontAwesomeIcon icon={faFileInvoice} className="text-3xl mb-2 text-[#DBDCDE]" />
                      <p className="text-[#3A3541] font-medium">No invoice data found</p>
                      <p className="text-sm mt-1">Import data or adjust your filters</p>
                      <button
                        onClick={() => {
                          setImportTab("KPI1");
                          setIsModalOpen(true);
                        }}
                        className="mt-4 flex items-center gap-2 px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-opacity-90 transition-colors"
                      >
                        <FontAwesomeIcon icon={faUpload} />
                        Import Data
                      </button>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
};

export default KPI1InvoicingTable;
