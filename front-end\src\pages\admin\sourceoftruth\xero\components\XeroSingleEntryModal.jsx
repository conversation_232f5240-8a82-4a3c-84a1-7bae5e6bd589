import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { 
  faTimes, 
  faSpinner, 
  faFileInvoice, 
  faMoneyBillWave 
} from "@fortawesome/free-solid-svg-icons";

const XeroSingleEntryModal = ({
  isOpen,
  onClose,
  formData,
  setFormData,
  handleSubmit,
  selectedTab,
  isSubmitting
}) => {
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Modal backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-30 z-40"
        onClick={onClose}
      />
      
      {/* Modal content */}
      <div
        className="fixed inset-0 flex items-center justify-center z-50"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl mx-4 sm:mx-0">
          {/* Modal header */}
          <div className="flex items-center justify-between p-5 border-b border-[#DBDCDE]">
            <h3 className="text-lg font-semibold text-[#3A3541] flex items-center">
              <FontAwesomeIcon 
                icon={selectedTab === "KPI1" ? faFileInvoice : faMoneyBillWave} 
                className="mr-2 text-[#6E39CB]" 
              />
              {`Add ${selectedTab === "KPI1" ? "Invoice" : "Payment"} Entry`}
            </h3>
            <button
              onClick={onClose}
              className="text-[#89868D] hover:text-[#3A3541] transition-colors"
            >
              <FontAwesomeIcon icon={faTimes} className="text-xl" />
            </button>
          </div>

          {/* Modal body */}
          <div className="p-6">
            <form onSubmit={(e) => {
              e.preventDefault();
              handleSubmit();
            }}>
              {/* Common fields for both KPI1 and KPI2 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-[#3A3541] mb-1">
                  Application ID <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="applicationId"
                  value={formData.applicationId}
                  onChange={handleInputChange}
                  className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                  placeholder="Enter application ID"
                  required
                />
              </div>

              {/* KPI1 specific fields */}
              {selectedTab === "KPI1" && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Invoice Number <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="invoiceNumber"
                        value={formData.invoiceNumber}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                        placeholder="Enter invoice number"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Contact <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="contact"
                        value={formData.contact}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                        placeholder="Enter contact name"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Invoice Date
                      </label>
                      <input
                        type="date"
                        name="invoiceDate"
                        value={formData.invoiceDate}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Due Date
                      </label>
                      <input
                        type="date"
                        name="dueDate"
                        value={formData.dueDate}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Reference
                      </label>
                      <input
                        type="text"
                        name="reference"
                        value={formData.reference}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                        placeholder="Enter reference"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Amount <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        name="amount"
                        value={formData.amount}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                        placeholder="Enter amount"
                        step="0.01"
                        required
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-[#3A3541] mb-1">
                      Status
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                    >
                      <option value="Approved">Approved</option>
                      <option value="Paid">Paid</option>
                      <option value="Partially Paid">Partially Paid</option>
                      <option value="Pending">Pending</option>
                    </select>
                  </div>
                </>
              )}

              {/* KPI2 specific fields */}
              {selectedTab === "KPI2" && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Date
                      </label>
                      <input
                        type="date"
                        name="date"
                        value={formData.date}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Reference <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="reference"
                        value={formData.reference}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                        placeholder="Enter reference"
                        required
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-[#3A3541] mb-1">
                      Description <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                      placeholder="Enter description"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Debit
                      </label>
                      <input
                        type="number"
                        name="debit"
                        value={formData.debit}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                        placeholder="Enter debit amount"
                        step="0.01"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Credit
                      </label>
                      <input
                        type="number"
                        name="credit"
                        value={formData.credit}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                        placeholder="Enter credit amount"
                        step="0.01"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3A3541] mb-1">
                        Net <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        name="net"
                        value={formData.net}
                        onChange={handleInputChange}
                        className="w-full h-10 rounded-md border border-[#DBDCDE] px-3 text-sm focus:border-[#6E39CB] focus:outline-none"
                        placeholder="Enter net amount"
                        step="0.01"
                        required
                      />
                    </div>
                  </div>
                </>
              )}
            </form>
          </div>

          {/* Modal footer */}
          <div className="flex items-center justify-end p-5 border-t border-[#DBDCDE]">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-[#F4F5F9] text-[#3A3541] rounded-md mr-3 hover:bg-[#DBDCDE] transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className={`px-4 py-2 rounded-md text-white transition-colors flex items-center justify-center min-w-[120px] ${
                !isSubmitting
                  ? "bg-[#6E39CB] hover:bg-opacity-90"
                  : "bg-[#6E39CB] bg-opacity-50 cursor-not-allowed"
              }`}
            >
              {isSubmitting ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                  Saving...
                </>
              ) : (
                "Save Entry"
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default XeroSingleEntryModal;
