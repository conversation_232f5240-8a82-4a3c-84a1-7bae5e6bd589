import React, { useState, useMemo, useEffect } from "react";
import TroubleHeader from "./troubleshoot/TroubleHeader";
import TroubleFilters from "./troubleshoot/TroubleFilters";
import TroubleList from "./troubleshoot/TroubleList";
import DeleteConfirmationModal from "./troubleshoot/DeleteConfirmationModal";
import QuestionEditModal from "../../components/modal/QuestionEditModal.jsx";
import CreateQuestionModal from "../../components/modal/CreateQuestionModal.jsx";
import {
  useGetAllTroubleQuery,
  useAddTroubleMutation,
  useGetAllEmployeesQuery,
  useDeleteTroubleMutation,
  useChangeStatusOfTroubleMutation,
  useAddCommentMutation,
  useEditTroubleMutation,
} from "../../services/SalesAPIService";
import { useGetProfileQuery } from "../../services/CompanyAPIService";

const TroubleShootAgent = () => {
  
  const [selectedTab, setSelectedTab] = useState("all");
  const [dateFilterType, setDateFilterType] = useState("thisMonth");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [searchText, setSearchText] = useState("");
  const [createdByFilter, setCreatedByFilter] = useState("all");
  const [staffFilter, setStaffFilter] = useState("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteTroubleId, setDeleteTroubleId] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editQuestionData, setEditQuestionData] = useState(null);
  const [viewMode, setViewMode] = useState("table");

  
  const { data: troubles, isLoading, error } = useGetAllTroubleQuery();
  const [addTrouble] = useAddTroubleMutation();
  const { data: employees } = useGetAllEmployeesQuery();
  const [deleteTrouble] = useDeleteTroubleMutation();
  const [changeStatusOfTrouble] = useChangeStatusOfTroubleMutation();
  const [addComment] = useAddCommentMutation();
  const [editTrouble] = useEditTroubleMutation();
  const { data: profileData, isLoading: profileLoading } = useGetProfileQuery();

  
  const currentUser = profileData?.username || "";

  
  useEffect(() => {
    if (profileData) {
      console.log("Profile data loaded:", profileData);
      console.log("Current user set to:", currentUser);
    }
  }, [profileData, currentUser]);

  
  const handleCreateQuestion = async (newQuestionData) => {
    try {
      await addTrouble(newQuestionData).unwrap();
      setIsModalOpen(false);
    } catch (err) {
      console.error("Failed to add trouble:", err);
    }
  };

  
  const handleEdit = (trouble) => {
    setEditQuestionData({
      id: trouble.troubleId,
      question: trouble.questions,
      dueDate: trouble.dueDate,
      assignedTo: trouble.assignedTo.username,
      emailAssigned: trouble.emailAssigned || false,
    });
    setIsEditModalOpen(true);
  };

  
  const handleSaveEdit = async (updatedQuestionData) => {
    if (!editQuestionData || !editQuestionData.id) return;
    try {
      await editTrouble({
        id: editQuestionData.id,
        reqDTO: updatedQuestionData,
      }).unwrap();
      setIsEditModalOpen(false);
    } catch (err) {
      console.error("Failed to edit trouble:", err);
    }
  };

  
  const handleDelete = (troubleId) => {
    setDeleteTroubleId(troubleId);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (deleteTroubleId) {
      try {
        await deleteTrouble(deleteTroubleId).unwrap();
        setIsDeleteModalOpen(false);
      } catch (err) {
        console.error("Delete failed:", err);
      }
    }
  };

  const cancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  // Handle status change - wrapper function to format parameters correctly
  const handleStatusChange = async (trouble, newStatus) => {
    try {
      // Convert frontend status to backend format
      const statusMap = {
        'resolved': 'RESOLVED',
        'inProgress': 'IN_PROGRESS',
        'pending': 'PENDING',
        'noSolution': 'NO_SOLUTION'
      };

      const backendStatus = statusMap[newStatus] || newStatus.toUpperCase();

      await changeStatusOfTrouble({
        id: trouble.troubleId,
        status: backendStatus
      }).unwrap();
    } catch (err) {
      console.error("Failed to change status:", err);
    }
  };

  
  const filteredTroubles = useMemo(() => {
    let filtered = troubles || [];

    if (searchText) {
      filtered = filtered.filter((t) =>
        t.questions.toLowerCase().includes(searchText.toLowerCase())
      );
    }
    if (createdByFilter !== "all") {
      filtered = filtered.filter(
        (t) => t.createdBy.username === createdByFilter
      );
    }
    if (staffFilter !== "all") {
      filtered = filtered.filter(
        (t) => t.assignedTo.username === staffFilter
      );
    }
    if (selectedStatus) {
      const statusMap = {
        resolved: "RESOLVED",
        pending: "PENDING",
        inProgress: "IN_PROGRESS",
        noSolution: "NO_SOLUTION",
      };
      filtered = filtered.filter((t) => t.status === statusMap[selectedStatus]);
    }
    const now = new Date();
    if (dateFilterType === "thisWeek") {
      const dayOfWeek = now.getDay();
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - dayOfWeek);
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      filtered = filtered.filter((t) => {
        const createdDate = new Date(t.createdDate);
        return createdDate >= startOfWeek && createdDate <= endOfWeek;
      });
    } else if (dateFilterType === "thisMonth") {
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      filtered = filtered.filter((t) => {
        const createdDate = new Date(t.createdDate);
        return createdDate >= startOfMonth && createdDate <= endOfMonth;
      });
    } else if (dateFilterType === "custom" && startDate && endDate) {
      const customStart = new Date(startDate);
      const customEnd = new Date(endDate);
      filtered = filtered.filter((t) => {
        const createdDate = new Date(t.createdDate);
        return createdDate >= customStart && createdDate <= customEnd;
      });
    }
    if (selectedTab === "assigned") {
      console.log("Current user:", currentUser);
      console.log("Filtering for assigned tickets");
      filtered = filtered.filter((t) => {
        console.log("Ticket assigned to:", t.assignedTo.username);
        return t.assignedTo.username === currentUser;
      });
    }

    // Sort by creation date descending (latest on top)
    filtered.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate));

    return filtered;
  }, [
    troubles,
    searchText,
    createdByFilter,
    staffFilter,
    selectedStatus,
    dateFilterType,
    startDate,
    endDate,
    selectedTab,
    currentUser,
  ]);

  
  let noTroubleMsg = "No troubles found.";
  if (dateFilterType === "thisWeek") {
    noTroubleMsg = "No troubles this week.";
  } else if (dateFilterType === "thisMonth") {
    noTroubleMsg = "No troubles this month.";
  } else if (dateFilterType === "custom") {
    noTroubleMsg = "No troubles for the selected date range.";
  }

  return (
    <div className="bg-[#F4F5F9] p-4 md:p-6 2xl:p-10 min-h-screen">
      <TroubleHeader
        selectedTab={selectedTab}
        setSelectedTab={setSelectedTab}
        viewMode={viewMode}
        setViewMode={setViewMode}
        openModal={() => setIsModalOpen(true)}
      />

      <TroubleFilters
        searchText={searchText}
        setSearchText={setSearchText}
        selectedStatus={selectedStatus}
        setSelectedStatus={setSelectedStatus}
        createdByFilter={createdByFilter}
        setCreatedByFilter={setCreatedByFilter}
        staffFilter={staffFilter}
        setStaffFilter={setStaffFilter}
        dateFilterType={dateFilterType}
        setDateFilterType={setDateFilterType}
        startDate={startDate}
        setStartDate={setStartDate}
        endDate={endDate}
        setEndDate={setEndDate}
        employees={employees}
      />

      <CreateQuestionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onCreate={handleCreateQuestion}
        employees={employees}
      />

      {isLoading || profileLoading ? (
        <div className="flex items-center justify-center h-40 bg-white rounded-lg shadow-sm">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-[#6E39CB]"></div>
        </div>
      ) : error ? (
        <div className="p-6 text-center bg-white rounded-lg shadow-sm">
          <div className="mb-4 text-red-500">
            <i className="fas fa-exclamation-triangle text-3xl"></i>
          </div>
          <p className="text-lg font-medium text-[#3A3541]">Error loading questions.</p>
          <p className="text-sm text-[#89868D] mt-1">Please try again later.</p>
        </div>
      ) : filteredTroubles.length === 0 ? (
        <div className="p-6 text-center bg-white rounded-lg shadow-sm">
          <p className="text-lg font-medium text-[#89868D]">{noTroubleMsg}</p>
        </div>
      ) : (
        <TroubleList
          troubles={filteredTroubles}
          viewMode={viewMode}
          employees={employees}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onStatusChange={handleStatusChange}
          onAddComment={(id, reqDTO) => addComment({ id, reqDTO })}
        />
      )}

      {isDeleteModalOpen && (
        <DeleteConfirmationModal onCancel={cancelDelete} onConfirm={confirmDelete} />
      )}

      <QuestionEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={handleSaveEdit}
        employees={employees}
        initialData={editQuestionData}
      />
    </div>
  );
};

export default TroubleShootAgent;
