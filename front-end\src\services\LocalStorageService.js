// LocalStorageService.js
export const storeToken = (token) => {
    try {
      localStorage.setItem('token', token);
    } catch (error) {
      console.error("Error storing token:", error);
    }
  };

const getToken = () => {
    let token = localStorage.getItem('token')
    return token
}
const removeToken = (value) => {
    localStorage.removeItem(value)

}

// Utility function to handle logout across the application
export const handleLogout = () => {
  removeToken('token');
  // Clear any other user-related data from localStorage if needed
  localStorage.removeItem('userRole');
  localStorage.removeItem('userData');

  // Redirect to login page
  window.location.href = '/';
};

export { getToken, removeToken}