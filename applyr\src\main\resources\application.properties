spring.application.name=applyr
spring.datasource.url=********************************************
spring.datasource.username=postgres
spring.datasource.password=password
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.database=POSTGRESQL
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
server.port=8080



spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB

client.uri=http://localhost:5173/
backend.uri=http://localhost:8080
jwt.secret-key=8zhnFNDvHLcz8DnfGRhsZkhWtulHVx8rt86SjkRnBk8=
jwt.expiration-time=864000000
file.upload-dir=uploads
file.max-size=50MB
spring.web.resources.static-locations=classpath:/static/



spring.mail.host=smtp.hostinger.com
spring.mail.port=465
spring.mail.username=<EMAIL>
spring.mail.password=jimthoe#01A
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.socketFactory.port=465
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.fallback=false
