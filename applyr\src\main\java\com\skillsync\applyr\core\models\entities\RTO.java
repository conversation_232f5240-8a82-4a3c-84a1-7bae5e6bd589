package com.skillsync.applyr.core.models.entities;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "rto")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RTO {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;


    private String code;
    private String legalName;
    private String businessName;
    private String address;
    private String rtoType;

    @OneToMany(mappedBy = "rto", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RTOContact> contacts = new ArrayList<>();
    
    public void addContact(RTOContact contact) {
        contacts.add(contact);
        contact.setRto(this);
    }
    
    public void removeContact(RTOContact contact) {
        contacts.remove(contact);
        contact.setRto(null);
    }
}
