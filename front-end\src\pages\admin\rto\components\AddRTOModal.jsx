import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes, faSpinner, faRobot } from "@fortawesome/free-solid-svg-icons";
import { useFetchAndSaveRTOMutation } from "../../../../services/CompanyAPIService";

const AddRTOModal = ({ isOpen, onClose, onSuccess }) => {
  const [rtoCode, setRtoCode] = useState("");
  const [error, setError] = useState("");
  const [fetchAndSaveRTO, { isLoading }] = useFetchAndSaveRTOMutation();

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate RTO code
    if (!rtoCode.trim()) {
      setError("Please enter an RTO code");
      return;
    }

    // Clear any previous errors
    setError("");

    try {
      await fetchAndSaveRTO(rtoCode.trim()).unwrap();
      setRtoCode("");
      if (onSuccess) onSuccess();
    } catch (err) {
      console.error("Failed to fetch RTO:", err);
      setError(err.data?.message || "Failed to fetch RTO information. Please check the code and try again.");
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white w-full max-w-md rounded-lg shadow-lg overflow-hidden">
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800">Add New RTO</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-6">
            <label htmlFor="rtoCode" className="block text-sm font-medium text-gray-700 mb-2">
              RTO Code
            </label>
            <input
              type="text"
              id="rtoCode"
              value={rtoCode}
              onChange={(e) => setRtoCode(e.target.value)}
              placeholder="e.g. 12345"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
            {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
          </div>

          <div className="bg-gray-50 p-4 rounded-md mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0 mt-0.5">
                <FontAwesomeIcon icon={faRobot} className="text-blue-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-800">Automated Data Retrieval</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Enter the RTO code and we'll automatically fetch all the information from <i>training.gov.au</i>.
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="bg-white text-gray-700 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-3"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
                  Fetching...
                </>
              ) : (
                "Add RTO"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddRTOModal;
