import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash } from "@fortawesome/free-solid-svg-icons";

const DeleteConfirmationModal = ({ onCancel, onConfirm }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
          <FontAwesomeIcon icon={faTrash} className="text-3xl text-red-500" />
        </div>
        <div className="mt-4 text-center">
          <h3 className="mb-2 text-xl font-semibold text-[#3A3541]">
            Delete Confirmation
          </h3>
          <p className="mb-6 text-[#89868D]">
            Are you sure you want to delete this question? This action cannot be undone.
          </p>
          <div className="flex items-center justify-center gap-4">
            <button
              onClick={onCancel}
              className="rounded-md border border-[#DBDCDE] py-2 px-6 text-[#3A3541] hover:bg-[#F4F5F9]"
            >
              Cancel
            </button>
            <button
              onClick={onConfirm}
              className="rounded-md bg-red-500 py-2 px-6 text-white hover:bg-opacity-90"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;
