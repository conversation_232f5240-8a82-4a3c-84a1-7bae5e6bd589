package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.ExternalCommission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ExternalCommissionRepository extends JpaRepository<ExternalCommission, Long> {
    Optional<ExternalCommission> findByApplicationId(String applicationId);
}
