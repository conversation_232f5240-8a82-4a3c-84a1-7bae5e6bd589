package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.core.models.enums.InvoiceSentStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
public class KPI1XeroDTO {
    private ApplicationResponseDTO application;
    private LocalDateTime invoiceDate;
    private LocalDateTime invoiceExpiryDate;
    private String source;
    private String status;
    private InvoiceSentStatus invoiceStatus;
}
