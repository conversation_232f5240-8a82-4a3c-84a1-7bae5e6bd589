import React, { useState, useEffect, useMemo } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUsers,
  faFileInvoiceDollar,
  faMoneyBillWave,
  faCalendarAlt,
  faAward,
  faUser,
  faChartLine,
  faCheckCircle,
  faTrophy,
  faSpinner,
  faExclamationTriangle,
  faMedal,
  faAngleDown,
  faPrint,
  faArrowUp,
  faTimesCircle,
  faRobot,
  faInfoCircle,
} from "@fortawesome/free-solid-svg-icons";

// Import Source of Truth API service for Xero data
import {
  useGetAllXeroInvoicesQuery,
  useGetAllXeroInBankQuery,
} from "../../../../services/SourceOfTruthApiService";
import html2canvas from "html2canvas";

// Import the Google Generative AI library
import { GoogleGenerativeAI } from "@google/generative-ai";

// Import AI Analysis Modal
import AIAnalysisModal from './components/AIAnalysisModal';

// Import Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import { Line } from "react-chartjs-2";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

import {
  useGetCompanyDashboardQuery,
  useGetCompanyDashboardThisMonthQuery,
  useGetCompanyDashboardThisYearQuery
} from "../../../../services/AdminAPIService";
import { useGetAllTargetsQuery } from "../../../../services/CompanyAPIService";

const SourceOfTruthDashboard = () => {
  // 1) Enhanced state for date range and weekly target selection
  const [dateRange, setDateRange] = useState("weeklyTarget");
  const [selectedWeeklyTarget, setSelectedWeeklyTarget] = useState(null);
  const [selectedWeeklyTargets, setSelectedWeeklyTargets] = useState([]);
  const [customRange, setCustomRange] = useState({ from: "", to: "" });
  const [selectedTargetId, setSelectedTargetId] = useState(null);
  const [computedStartDate, setComputedStartDate] = useState(null);
  const [computedEndDate, setComputedEndDate] = useState(null);

  // AI Analysis state
  const [aiInsights, setAiInsights] = useState(null);
  const [isLoadingReport, setIsLoadingReport] = useState(false);
  const [reportError, setReportError] = useState(null);
  const [isAiModalOpen, setIsAiModalOpen] = useState(false);

  // Progress over time chart state
  const [progressChartData, setProgressChartData] = useState(null);
  const [isLoadingProgressData, setIsLoadingProgressData] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState('all'); // 'all' or agent username
  const [availableAgents, setAvailableAgents] = useState([]);

  // Fetch Xero data for progress over time chart
  const { data: xeroInvoicesData, isLoading: isLoadingXeroInvoices } = useGetAllXeroInvoicesQuery();
  const { data: xeroInBankData, isLoading: isLoadingXeroInBank } = useGetAllXeroInBankQuery();

  // 2) Fetch all weekly targets
  const {
    data: weeklyTargets,
    isLoading: targetsLoading,
    isError: targetsError,
  } = useGetAllTargetsQuery();

  // 3) Find the current weekly target (that includes the current datetime)
  useEffect(() => {
    if (weeklyTargets && weeklyTargets.length > 0) {
      const now = new Date();
      const currentTarget = weeklyTargets.find(target => {
        const startDate = new Date(target.startDate);
        const endDate = new Date(target.endDate);
        return now >= startDate && now <= endDate;
      });

      if (currentTarget) {
        setSelectedWeeklyTarget(currentTarget);
        setSelectedTargetId(currentTarget.id);
        setComputedStartDate(currentTarget.startDate);
        setComputedEndDate(currentTarget.endDate);
      } else {
        // If no current target, use the most recent one
        const sortedTargets = [...weeklyTargets].sort(
          (a, b) => new Date(b.endDate) - new Date(a.endDate)
        );
        if (sortedTargets.length > 0) {
          setSelectedWeeklyTarget(sortedTargets[0]);
          setSelectedTargetId(sortedTargets[0].id);
          setComputedStartDate(sortedTargets[0].startDate);
          setComputedEndDate(sortedTargets[0].endDate);
        }
      }
    }
  }, [weeklyTargets]);

  // 4) Fetch dashboard data based on selected target
  const {
    data,
    isLoading,
    isError,
    error
  } = useGetCompanyDashboardQuery({
    startDate: computedStartDate,
    endDate: computedEndDate,
    targetId: selectedTargetId
  }, {
    skip: !selectedTargetId
  });

  // 5) Extract data for display
  const kpi1Target = data?.kpi1Target ?? 0;
  const kpi1ActualInvoice = data?.kpi1Actual ?? 0;
  const kpi2Target = data?.kpi2Target ?? 0;
  const kpi2MoneyBank = data?.kpi2Actual ?? 0;

  // 6) Prepare data for KPI1 leaderboard
  const kpi1Agents = data?.agentInfos?.map((agent) => ({
    name: agent?.profile?.fullName || agent?.profile?.username,
    target: agent?.kpi1Target ?? 0,
    actual: agent?.kpi1Actual ?? 0,
    percentage: agent?.kpi1Target > 0 ? Math.round((agent?.kpi1Actual / agent?.kpi1Target) * 100) : 0,
    status: agent?.kpi1Actual >= agent?.kpi1Target ? "achieved" : "pending",
  })) ?? [];

  // Sort by achievement percentage descending
  const sortedKPI1Agents = useMemo(() => {
    return [...kpi1Agents].sort((a, b) => {
      const aPercentage = a.target > 0 ? (a.actual / a.target) : 0;
      const bPercentage = b.target > 0 ? (b.actual / b.target) : 0;
      return bPercentage - aPercentage;
    });
  }, [kpi1Agents]);

  // 7) Prepare data for KPI2 leaderboard
  const kpi2Agents = data?.agentInfos?.map((agent) => ({
    name: agent?.profile?.fullName || agent?.profile?.username,
    target: agent?.kpi2Target ?? 0,
    actual: agent?.kpi2Actual ?? 0,
    percentage: agent?.kpi2Target > 0 ? Math.round((agent?.kpi2Actual / agent?.kpi2Target) * 100) : 0,
    status: agent?.kpi2Actual >= agent?.kpi2Target ? "achieved" : "pending",
  })) ?? [];

  // Utility to screenshot leaderboard content styled like the provided image
  const takeStyledTableScreenshot = (elementId, title) => {
    const leaderboard = document.getElementById(elementId);
    if (!leaderboard) return;

    // Get table data from DOM
    const table = leaderboard.querySelector('table');
    if (!table) return;

    // Parse table rows into data array
    const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr =>
      Array.from(tr.querySelectorAll('td')).map(td => td.innerText)
    );
    // Parse table headers
    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.innerText);

    // Calculate totals
    let totalTarget = 0, totalActual = 0, metCount = 0;
    rows.forEach(row => {
      const target = Number(row[2]?.replace(/[^\d.-]+/g, "")) || 0;
      const actual = Number(row[3]?.replace(/[^\d.-]+/g, "")) || 0;
      totalTarget += target;
      totalActual += actual;
      if (row[5]?.toLowerCase().includes('achieved')) metCount++;
    });

    // Create a styled table like the image
    const wrapper = document.createElement('div');
    wrapper.style.background = '#fff';
    wrapper.style.padding = '0';
    wrapper.style.display = 'inline-block';
    wrapper.style.fontFamily = 'Arial, sans-serif';
    wrapper.style.margin = '32px';
    wrapper.style.textAlign = 'center';

    // Add a header for the screenshot
    const headerDiv = document.createElement('div');
    headerDiv.style.fontSize = '28px';
    headerDiv.style.fontWeight = 'bold';
    headerDiv.style.color = '#256d4a';
    headerDiv.style.marginBottom = '8px';
    headerDiv.innerText = title;
    wrapper.appendChild(headerDiv);
    const dateDiv = document.createElement('div');
    dateDiv.style.fontSize = '16px';
    dateDiv.style.color = '#888';
    dateDiv.style.marginBottom = '18px';
    dateDiv.innerText = new Date().toLocaleDateString();
    wrapper.appendChild(dateDiv);

    const tableEl = document.createElement('table');
    tableEl.style.borderCollapse = 'collapse';
    tableEl.style.minWidth = '900px';
    tableEl.style.fontFamily = 'Arial, sans-serif';
    tableEl.style.fontSize = '18px';
    tableEl.style.margin = '0 auto';

    // Header row
    const thead = document.createElement('thead');
    const trHead = document.createElement('tr');
    ['Agent Name', 'Target', 'Actual', 'Status'].forEach((h, i) => {
      const th = document.createElement('th');
      th.innerText = h;
      th.style.background = '#256d4a';
      th.style.color = '#fff';
      th.style.fontWeight = '700';
      th.style.fontSize = '20px';
      th.style.border = '2px solid #000';
      th.style.padding = '12px 32px'; // wider columns
      th.style.minWidth = i === 0 ? '220px' : '180px';
      trHead.appendChild(th);
    });
    thead.appendChild(trHead);
    tableEl.appendChild(thead);

    // Data rows
    const tbody = document.createElement('tbody');
    rows.forEach(row => {
      const tr = document.createElement('tr');
      // Agent Name
      const tdName = document.createElement('td');
      console.log(row[1])
      tdName.innerText = row[1].split(' ')[0].substring(1) || '';
      tdName.style.border = '1px solid #000';
      tdName.style.padding = '12px 32px';
      tr.appendChild(tdName);
      // Target
      const tdTarget = document.createElement('td');
      tdTarget.innerText = row[2] || '';
      tdTarget.style.border = '1px solid #000';
      tdTarget.style.padding = '12px 32px';
      tr.appendChild(tdTarget);
      // Actual
      const tdActual = document.createElement('td');
      tdActual.innerText = row[3] || '';
      tdActual.style.border = '1px solid #000';
      tdActual.style.padding = '12px 32px';
      tr.appendChild(tdActual);
      // Status
      const tdStatus = document.createElement('td');
      const met = (row[5]||'').toLowerCase().includes('achieved');
      tdStatus.innerText = met ? 'TARGET MET' : 'TARGET NOT MET';
      tdStatus.style.border = '1px solid #000';
      tdStatus.style.padding = '12px 32px';
      tdStatus.style.background = met ? '#00ff00' : '#ff2222';
      tdStatus.style.color = met ? '#000' : '#fff';
      tdStatus.style.fontWeight = '700';
      tr.appendChild(tdStatus);
      tbody.appendChild(tr);
    });
    // Totals row
    const trTotal = document.createElement('tr');
    const tdTotalLabel = document.createElement('td');
    tdTotalLabel.innerText = 'Total(s)';
    tdTotalLabel.style.border = '1px solid #000';
    tdTotalLabel.style.padding = '12px 32px';
    tdTotalLabel.style.fontWeight = '700';
    trTotal.appendChild(tdTotalLabel);
    const tdTotalTarget = document.createElement('td');
    tdTotalTarget.innerText = totalTarget.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
    tdTotalTarget.style.border = '1px solid #000';
    tdTotalTarget.style.padding = '12px 32px';
    tdTotalTarget.style.fontWeight = '700';
    trTotal.appendChild(tdTotalTarget);
    const tdTotalActual = document.createElement('td');
    tdTotalActual.innerText = totalActual.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
    tdTotalActual.style.border = '1px solid #000';
    tdTotalActual.style.padding = '12px 32px';
    tdTotalActual.style.fontWeight = '700';
    trTotal.appendChild(tdTotalActual);
    const tdTotalStatus = document.createElement('td');
    tdTotalStatus.innerText = (metCount === rows.length && rows.length > 0) ? 'TARGET MET' : 'TARGET NOT MET';
    tdTotalStatus.style.border = '1px solid #000';
    tdTotalStatus.style.padding = '12px 32px';
    tdTotalStatus.style.fontWeight = '700';
    tdTotalStatus.style.background = (metCount === rows.length && rows.length > 0) ? '#00ff00' : '#ff2222';
    tdTotalStatus.style.color = (metCount === rows.length && rows.length > 0) ? '#000' : '#fff';
    trTotal.appendChild(tdTotalStatus);
    tbody.appendChild(trTotal);

    tableEl.appendChild(tbody);
    wrapper.appendChild(tableEl);

    document.body.appendChild(wrapper);
    html2canvas(wrapper, { backgroundColor: '#fff', useCORS: true }).then((canvas) => {
      const link = document.createElement('a');
      link.download = "${title.replace(/\s+/g, '_')}_${new Date().toISOString().slice(0,10)}.png";
      link.href = canvas.toDataURL();
      link.click();
      document.body.removeChild(wrapper);
    });
  };

  // Screenshot handler for KPI1 Leaderboard (styled like image)
  const handleKPI1Screenshot = () => {
    takeStyledTableScreenshot("kpi1-leaderboard-card", "KPI1 Performance Leaderboard");
  };

  // Screenshot handler for KPI2 Leaderboard (styled like image)
  const handleKPI2Screenshot = () => {
    takeStyledTableScreenshot("kpi2-leaderboard-card", "KPI2 Performance Leaderboard");
  };

  // Sort by achievement percentage descending
  const sortedKPI2Agents = useMemo(() => {
    return [...kpi2Agents].sort((a, b) => {
      const aPercentage = a.target > 0 ? (a.actual / a.target) : 0;
      const bPercentage = b.target > 0 ? (b.actual / b.target) : 0;
      return bPercentage - aPercentage;
    });
  }, [kpi2Agents]);

  // Helper function to normalize dates to start of day for proper comparison
  const normalizeDate = (date) => {
    const normalized = new Date(date);
    normalized.setHours(0, 0, 0, 0);
    return normalized;
  };

  // Function to prepare progress over time chart data using actual Xero data
  const prepareProgressOverTimeData = (agentFilter = 'all') => {
    if (!selectedWeeklyTarget || !xeroInvoicesData || !xeroInBankData) return;

    setIsLoadingProgressData(true);

    try {
      // Normalize start and end dates to start of day for proper comparison
      const startDate = normalizeDate(computedStartDate);
      const endDate = normalizeDate(computedEndDate);

      console.log('Preparing progress data for period:', startDate, 'to', endDate);
      console.log('Agent filter:', agentFilter);
      console.log('Current dashboard data:', data);
      console.log('Xero invoices data length:', xeroInvoicesData?.length || 0);
      console.log('Xero in bank data length:', xeroInBankData?.length || 0);

      // Collect available agents from Xero data
      const agentsSet = new Set();

      // Add agents from invoice data
      xeroInvoicesData?.forEach(invoice => {
        if (invoice.agent && invoice.agent.fullName) {
          agentsSet.add(JSON.stringify({
            username: invoice.agent.username,
            fullName: invoice.agent.fullName
          }));
        }
      });

      // Add agents from in bank data
      xeroInBankData?.forEach(payment => {
        if (payment.agent && payment.agent.fullName) {
          agentsSet.add(JSON.stringify({
            username: payment.agent.username,
            fullName: payment.agent.fullName
          }));
        }
      });

      const agentsList = Array.from(agentsSet).map(agentStr => JSON.parse(agentStr));
      setAvailableAgents(agentsList);

      // Filter data within the selected weekly target timeframe using normalized dates
      const filteredInvoices = xeroInvoicesData?.filter(invoice => {
        const invoiceDate = normalizeDate(invoice.invoiceDate);
        const isInPeriod = invoiceDate >= startDate && invoiceDate <= endDate;
        const isAgentMatch = agentFilter === 'all' || invoice.agent?.username === agentFilter;
        return isInPeriod && isAgentMatch;
      }) || [];

      const filteredPayments = xeroInBankData?.filter(payment => {
        const paymentDate = normalizeDate(payment.insertDate);
        const isInPeriod = paymentDate >= startDate && paymentDate <= endDate;
        const isAgentMatch = agentFilter === 'all' || payment.agent?.username === agentFilter;
        return isInPeriod && isAgentMatch;
      }) || [];

      console.log('Date range (normalized):', startDate, 'to', endDate);
      console.log('Filtered invoices:', filteredInvoices.length, 'items');
      console.log('Filtered payments:', filteredPayments.length, 'items');

      // Debug: Check if there are transactions on the start date
      const startDateInvoices = filteredInvoices.filter(invoice =>
        normalizeDate(invoice.invoiceDate).getTime() === startDate.getTime()
      );
      const startDatePayments = filteredPayments.filter(payment =>
        normalizeDate(payment.insertDate).getTime() === startDate.getTime()
      );
      console.log('Start date transactions - Invoices:', startDateInvoices.length, 'Payments:', startDatePayments.length);

      // Get targets based on agent filter
      let kpi1Target, kpi2Target;
      if (agentFilter === 'all') {
        // Use company totals from current dashboard data
        kpi1Target = data?.kpi1Target || 0;
        kpi2Target = data?.kpi2Target || 0;
      } else {
        // Find agent's individual target from current dashboard data
        const agent = data?.agentInfos?.find(a => a.profile?.username === agentFilter);
        kpi1Target = agent?.kpi1Target || 0;
        kpi2Target = agent?.kpi2Target || 0;
      }

      console.log('Targets - KPI1:', kpi1Target, 'KPI2:', kpi2Target);

      // Create daily progress data
      const progressData = [];
      const currentDate = new Date(startDate);
      const today = normalizeDate(new Date()); // Normalize today for comparison

      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        const isCurrentOrPastDate = currentDate <= today;

        // Calculate cumulative KPI1 (invoices) up to this date (only if current or past date)
        // Use normalized dates for proper comparison including start date transactions
        const kpi1Cumulative = isCurrentOrPastDate ? filteredInvoices
          .filter(invoice => normalizeDate(invoice.invoiceDate) <= currentDate)
          .reduce((sum, invoice) => sum + (invoice.gross || 0), 0) : null;

        // Calculate cumulative KPI2 (payments) up to this date (only if current or past date)
        // Use normalized dates for proper comparison including start date transactions
        const kpi2Cumulative = isCurrentOrPastDate ? filteredPayments
          .filter(payment => normalizeDate(payment.insertDate) <= currentDate)
          .reduce((sum, payment) => sum + (payment.netAmount || 0), 0) : null;

        // Calculate ideal progress (linear progression from 0 to target)
        const daysPassed = Math.floor((currentDate - startDate) / (1000 * 60 * 60 * 24));
        const totalDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
        const progressRatio = totalDays > 0 ? daysPassed / totalDays : 0;

        const kpi1Ideal = kpi1Target * progressRatio;
        const kpi2Ideal = kpi2Target * progressRatio;

        progressData.push({
          date: dateStr,
          dateLabel: currentDate.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric'
          }),
          kpi1Actual: kpi1Cumulative, // null for future dates
          kpi1Ideal: kpi1Ideal,
          kpi2Actual: kpi2Cumulative, // null for future dates
          kpi2Ideal: kpi2Ideal,
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }

      console.log('Progress data:', progressData);

      // If no progress data (no transactions in period), create a simple start/end chart
      if (progressData.length === 0) {
        console.log('No transactions in period, creating simple progress chart');

        const todayNormalized = normalizeDate(new Date());

        progressData = [
          {
            dateLabel: startDate.toLocaleDateString('en-GB', {
              day: '2-digit',
              month: 'short',
              year: 'numeric'
            }),
            kpi1Actual: startDate <= todayNormalized ? 0 : null,
            kpi1Ideal: 0,
            kpi2Actual: startDate <= todayNormalized ? 0 : null,
            kpi2Ideal: 0,
          },
          {
            dateLabel: endDate.toLocaleDateString('en-GB', {
              day: '2-digit',
              month: 'short',
              year: 'numeric'
            }),
            kpi1Actual: endDate <= todayNormalized ? 0 : null,
            kpi1Ideal: kpi1Target,
            kpi2Actual: endDate <= todayNormalized ? 0 : null,
            kpi2Ideal: kpi2Target,
          }
        ];
      }

      // Prepare chart data with proper styling to match design (similar to ProgressTab)
      const chartData = {
        labels: progressData.map(item => item.dateLabel),
        datasets: [
          {
            label: "KPI1 Ideal",
            data: progressData.map(item => item.kpi1Ideal),
            borderColor: "rgba(110, 57, 203, 0.3)",
            borderDash: [5, 5],
            borderWidth: 2,
            pointRadius: 0,
            fill: false,
            tension: 0.1,
          },
          {
            label: "KPI1 Actual",
            data: progressData.map(item => item.kpi1Actual),
            borderColor: "rgba(110, 57, 203, 1)",
            backgroundColor: "rgba(110, 57, 203, 0.1)",
            borderWidth: 2,
            pointRadius: 3,
            fill: true,
            tension: 0.1,
          },
          {
            label: "KPI2 Ideal",
            data: progressData.map(item => item.kpi2Ideal),
            borderColor: "rgba(59, 130, 246, 0.3)",
            borderDash: [5, 5],
            borderWidth: 2,
            pointRadius: 0,
            fill: false,
            tension: 0.1,
          },
          {
            label: "KPI2 Actual",
            data: progressData.map(item => item.kpi2Actual),
            borderColor: "rgba(59, 130, 246, 1)",
            backgroundColor: "rgba(59, 130, 246, 0.1)",
            borderWidth: 2,
            pointRadius: 3,
            fill: true,
            tension: 0.1,
          },
        ],
      };

      console.log('Chart Data:', chartData);
      setProgressChartData(chartData);
    } catch (error) {
      console.error('Error preparing progress chart data:', error);
    } finally {
      setIsLoadingProgressData(false);
    }
  };

  // Load progress data when Xero data and weekly targets are available
  useEffect(() => {
    if (selectedWeeklyTarget && xeroInvoicesData && xeroInBankData && !isLoadingXeroInvoices && !isLoadingXeroInBank) {
      prepareProgressOverTimeData(selectedAgent);
    }
  }, [selectedWeeklyTarget, selectedAgent, xeroInvoicesData, xeroInBankData, isLoadingXeroInvoices, isLoadingXeroInBank, computedStartDate, computedEndDate, data]);

  // Helper function to prepare analysis data
  const prepareAnalysisData = () => {
    return {
      timeframe: {
        title: selectedWeeklyTarget?.title,
        startDate: computedStartDate,
        endDate: computedEndDate,
        isCurrentPeriod: selectedWeeklyTarget ?
          new Date() >= new Date(selectedWeeklyTarget.startDate) &&
          new Date() <= new Date(selectedWeeklyTarget.endDate) : false
      },
      kpi1: {
        target: kpi1Target,
        actual: kpi1ActualInvoice,
        percentage: kpi1Target > 0 ? Math.round((kpi1ActualInvoice / kpi1Target) * 100) : 0,
        agents: sortedKPI1Agents.map(agent => ({
          name: agent.name,
          target: agent.target,
          actual: agent.actual,
          percentage: agent.percentage,
          achieved: agent.status === "achieved"
        }))
      },
      kpi2: {
        target: kpi2Target,
        actual: kpi2MoneyBank,
        percentage: kpi2Target > 0 ? Math.round((kpi2MoneyBank / kpi2Target) * 100) : 0,
        agents: sortedKPI2Agents.map(agent => ({
          name: agent.name,
          target: agent.target,
          actual: agent.actual,
          percentage: agent.percentage,
          achieved: agent.status === "achieved"
        }))
      },
      summary: {
        totalAgents: sortedKPI1Agents.length,
        kpi1Achievers: sortedKPI1Agents.filter(a => a.status === "achieved").length,
        kpi2Achievers: sortedKPI2Agents.filter(a => a.status === "achieved").length,
        bothKPIAchievers: sortedKPI1Agents.filter(a => {
          const kpi2Agent = sortedKPI2Agents.find(k2 => k2.name === a.name);
          return a.status === "achieved" && kpi2Agent?.status === "achieved";
        }).length
      }
    };
  };

  // Function to generate report data using Google's Gemini API
  const generateReportData = async () => {
    setIsLoadingReport(true);
    setReportError(null);

    try {
      const analysisData = prepareAnalysisData();

      // API key for Google's Gemini API
      const API_KEY = "AIzaSyBfHT1ajPGzXXakJl8y88Vgb9f8piHg2c4";

      // Initialize the Gemini API client
      const genAI = new GoogleGenerativeAI(API_KEY);

      // Get the model
      const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });

      // Prepare the prompt for report generation
      const prompt =  `
Analyze this comprehensive sales performance data and provide detailed insights with projections.
The output MUST be a single HTML file, including all CSS within a <style> tag in the <head>.

**Goal for HTML Design & Data Presentation:**
- **User-Friendly, Concise & Compact:** The design must be minimalist, clean, intuitive, and avoid unnecessary visual bulk. Aim for a compact presentation that is easy to scan and not overly large.
- **Highly Legible Data:** Data must be in a clear, scannable format. Tables are preferred for structured data and should have a simple design.
- **Accessibility:** Maintain good color contrast and readable font sizes, even within a compact design.

**Current Performance Data (for your analysis):**
- Time Period: ${analysisData.timeframe.title} (${new Date(analysisData.timeframe.startDate).toLocaleDateString()} - ${new Date(analysisData.timeframe.endDate).toLocaleDateString()})
- Is Current Period: ${analysisData.timeframe.isCurrentPeriod}

**KPI1 (Revenue Generation):**
- Target: $${analysisData.kpi1.target.toLocaleString()}
- Actual: $${analysisData.kpi1.actual.toLocaleString()}
- Achievement: ${analysisData.kpi1.percentage}%
- Agents achieving target: ${analysisData.summary.kpi1Achievers}/${analysisData.summary.totalAgents}

**KPI2 (Money in Bank):**
- Target: $${analysisData.kpi2.target.toLocaleString()}
- Actual: $${analysisData.kpi2.actual.toLocaleString()}
- Achievement: ${analysisData.kpi2.percentage}%
- Agents achieving target: ${analysisData.summary.kpi2Achievers}/${analysisData.summary.totalAgents}

**Team Performance:**
- Total Agents: ${analysisData.summary.totalAgents}
- Agents achieving both KPIs: ${analysisData.summary.bothKPIAchievers}

**Individual Agent Performance (KPI1):**
${analysisData.kpi1.agents.map(agent =>
  `- ${agent.name}: KPI1 ${agent.percentage}% (${agent.achieved ? 'MET' : 'NOT MET'})`
).join('\n')}

**Please structure your HTML analysis as follows:**

**HTML Structure & Styling Guidelines:**
- Use a single "<html>" document with a "<head>" and "<body>".
- All CSS styles must be within a single "<style>" tag in the "<head>". Do NOT use inline styles on individual elements unless absolutely necessary for dynamic status coloring (e.g., performance indicators).
- **Layout:**
    - Use a single-column layout.
    - Set max-width: 750px;" on the main container/body and center it on the page for optimal readability and a concise footprint.
- **Typography:**
    - **Font:** Use a clean, highly legible sans-serif font family (e.g., Arial, Helvetica, system-ui, sans-serif). Set a base font size of "15px" for the "<body>" for a balance of readability and compactness.
    - **Headings/Titles:** **Do NOT use "<h1>", "<h2>", "<h3>", or any "h" tags.** Instead, use "<div>" or "<p>" elements styled with CSS classes to create a visual hierarchy. Define and use the following classes:
        - ".report-main-title": For the overall report title (e.g., "Sales Performance Analysis"). Style: "font-size: 1.6em; font-weight: bold; margin-bottom: 1.2em; text-align: center; color: #333;"
        - ".section-title": For major section titles (e.g., "Executive Summary", "KPI Analysis"). Style: "font-size: 1.3em; font-weight: bold; margin-top: 1.8em; margin-bottom: 0.8em; color: #444; border-bottom: 1px solid #ddd; padding-bottom: 0.3em;"
        - ".subsection-title": For sub-section titles (e.g., "KPI1: Revenue Generation"). Style: "font-size: 1.1em; font-weight: bold; margin-top: 1.2em; margin-bottom: 0.6em; color: #555;"
    - **Paragraphs:** Ensure adequate line spacing (e.g., "line-height: 1.5;").
- **Colors:**
    - Use a simple, professional color palette. Primarily shades of grey for text and backgrounds, with accent colors for performance indicators.
    - Background: "body { background-color: #f4f4f4; }" with a white "#fff" content container.
    - Text: "body { color: #333; }"
    - **Performance Indicators:**
        - Green (e.g., "#4CAF50" or "rgb(76, 175, 80)"): For positive outcomes, targets met.
        - Yellow (e.g., "#FFC107" or "rgb(255, 193, 7)"): For cautionary notes, near misses.
        - Red (e.g., "#F44336" or "rgb(244, 67, 54)"): For concerns, targets missed.
    - Apply these colors subtly (e.g., to text or small status indicators). Ensure text on colored backgrounds has sufficient contrast (e.g., white text on dark red/green).
- **Data Presentation:**
    - **Tables:** Use HTML "<table>" elements. Style for simplicity and clarity:
        - "width: 100%; border-collapse: collapse; margin-bottom: 1em;"
        - "th, td { text-align: left; padding: 8px 10px; border: 1px solid #ddd; }"
        - "th { background-color: #e9e9e9; font-weight: bold; }"
        - Consider "tbody tr:nth-child(even) { background-color: #f9f9f9; }" for scannability if tables are long, but keep it light.
    - **Lists:** Use standard "<ul>" and "<li>" for bullet points. Style with "padding-left: 20px;" and "margin-bottom: 0.5em;" for list items.
    - **Numbers:** Ensure monetary values are formatted with dollar signs and commas (e.g., $1,234.56). Percentages should include the '%' sign.
- **Whitespace:** Use sufficient padding and margins for clarity, but avoid excessive empty space to maintain a compact feel. (e.g., main content container "padding: 20px;").
- **Status Indicators:** For "MET" / "NOT MET" status, use styled "<span>" elements with appropriate background colors and text color for contrast (e.g., "padding: 3px 7px; border-radius: 4px; color: white; font-weight: bold;").

**Analysis Sections (Populate these within the HTML body, using the specified div classes for titles):**

html
<!-- Example structure of the body content -->
<body>
    <div style="max-width: 750px; margin: 20px auto; background-color: #fff; padding: 25px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
        <div class="report-main-title">Sales Performance Analysis</div>
        <p style="text-align: center; margin-top: -1em; margin-bottom: 1.5em; font-size: 0.9em; color: #777;">
            Time Period: ${analysisData.timeframe.title} (${new Date(analysisData.timeframe.startDate).toLocaleDateString()} - ${new Date(analysisData.timeframe.endDate).toLocaleDateString()})
        </p>

        <div class="section-title">Executive Summary</div>
        <!-- Your analysis here -->
        <p>Overall assessment...</p>

        <div class="section-title">KPI Analysis</div>
        <div class="subsection-title">KPI1: Revenue Generation</div>
        <!-- Table and brief explanation -->
        <div class="subsection-title">KPI2: Money in Bank</div>
        <!-- Table and brief explanation -->
        <div class="subsection-title">Overall Team Metrics</div>
        <!-- Table/list -->

        <div class="section-title">Leaderboard Insights & Individual Performance</div>
        <!-- Table for Individual Agent Performance (Name, KPI1 %, Status with color-coding) -->
        <!-- Analysis of rankings and patterns -->

        <div class="section-title">Strategic Recommendations (5-7 Actionable Points)</div>
        <!-- ul/li for recommendations -->

        <div class="section-title">Risk Assessment</div>
        <!-- ul/li for risks and mitigations -->

        <div class="section-title">Success Factors</div>
        <!-- ul/li for success factors -->
    </div>
</body>

Report Main Title: (e.g., "Sales Performance Analysis") followed by the Time Period.
Executive Summary (<div class="section-title">Executive Summary</div>)
Overall performance assessment.
Key highlights (achievements and critical concerns).
KPI Analysis (<div class="section-title">KPI Analysis</div>)
KPI1: Revenue Generation (<div class="subsection-title">KPI1: Revenue Generation</div>)
Present Target, Actual, Achievement %, and Agents Achieving Target in a simple, clear table.
Brief explanation of performance against this KPI.
KPI2: Money in Bank (<div class="subsection-title">KPI2: Money in Bank</div>)
Present Target, Actual, Achievement %, and Agents Achieving Target in a simple, clear table.
Brief explanation of performance against this KPI.
Overall Team Metrics (<div class="subsection-title">Overall Team Metrics</div>)
Table/list for Total Agents, Agents achieving both KPIs.
Leaderboard Insights & Individual Performance (<div class="section-title">Leaderboard Insights & Individual Performance</div>)
Present a table for Individual Agent Performance (Name, KPI1 %, Status).
Use color-coded <span> elements for 'Status' (MET/NOT MET) or achievement percentage.
Interpretation of current rankings/individual performances.
Observed patterns (e.g., top performer consistency, common struggles).
Strategic Recommendations (5-7 Actionable Points) (<div class="section-title">Strategic Recommendations (5-7 Actionable Points)</div>)
Provide specific, actionable steps using <ul> and <li>.
Link recommendations to specific findings.
Risk Assessment (<div class="section-title">Risk Assessment</div>)
Potential challenges/threats using <ul> and <li>.
Brief mitigation strategies for each risk.
Success Factors (<div class="section-title">Success Factors</div>)
What is working well using <ul> and <li>.
How to reinforce or replicate these.
Important Considerations for your Analysis:
Language: Use professional, clear, and concise business language.
Focus: Be actionable and forward-looking, not just descriptive.
Data Integrity: Ensure all numbers from the input data are accurately represented.


Generate the HTML output based on these instructions and the provided data.
`;

      // Generate content using the API
      const result = await model.generateContent(prompt);
      const response = result.response;
      const responseText = response.text();

      // Clean up the response by removing markdown code block markers
      const cleanedResponse = responseText
        .replace(/^"""html\s*/i, '')  // Remove opening """html
        .replace(/\s*"""\s*$/, '');   // Remove closing """

      setAiInsights(cleanedResponse);
    } catch (error) {
      console.error('Error generating report data:', error);
      setReportError(error.message || 'Failed to generate report data');
    } finally {
      setIsLoadingReport(false);
    }
  };

  // Main function to orchestrate AI analysis
  const generateAIInsights = async () => {
    setIsAiModalOpen(true);
    await generateReportData();
  };

  // 9) Loading and error states
  if (isLoading || targetsLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Source of Truth Dashboard</h1>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 mb-6 flex justify-center items-center">
          <div className="text-center">
            <FontAwesomeIcon icon={faSpinner} spin className="h-8 w-8 text-[#6E39CB] mb-4" />
            <p className="text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (isError || targetsError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Source of Truth Dashboard</h1>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="text-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="h-8 w-8 text-red-500 mb-4" />
            <p className="text-red-500 font-medium">Error loading dashboard data</p>
            <p className="text-gray-600 mt-2">{error?.message || "Please try again later"}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Source of Truth Dashboard</h1>

        <div className="flex items-center space-x-4">
          {/* Target selection dropdown */}
          {weeklyTargets && weeklyTargets.length > 0 && (
            <select
              className="bg-[#F4F5F9] px-4 py-2 rounded-md text-sm font-medium border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#6E39CB]"
              value={selectedTargetId || ''}
              onChange={e => {
                const value = typeof weeklyTargets[0].id === 'number' ? Number(e.target.value) : e.target.value;
                const target = weeklyTargets.find(t => t.id === value);
                if (target) {
                  setSelectedWeeklyTarget(target);
                  setSelectedTargetId(target.id);
                  setComputedStartDate(target.startDate);
                  setComputedEndDate(target.endDate);
                }
              }}
            >
              {weeklyTargets.map(target => (
                <option key={target.id} value={target.id}>
                  {target.title} ({new Date(target.startDate).toLocaleDateString()} - {new Date(target.endDate).toLocaleDateString()})
                </option>
              ))}
            </select>
          )}

          {selectedWeeklyTarget && (
            <div className="bg-[#F4F5F9] px-4 py-2 rounded-md text-sm font-medium flex items-center">
              <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-[#6E39CB]" />
              <span>
                {selectedWeeklyTarget.title} ({new Date(selectedWeeklyTarget.startDate).toLocaleDateString()} - {new Date(selectedWeeklyTarget.endDate).toLocaleDateString()})
              </span>
            </div>
          )}
        </div>
      </div>

      {/* TOP ROW - 4 KPI CARDS */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* 1) KPI1 TARGET */}
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">KPI1 Target</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            ${kpi1Target.toLocaleString()}
          </p>
          <div className="flex items-center mt-2 text-xs">
            <span className="text-gray-500">Invoice Revenue Target</span>
          </div>
        </div>

        {/* 2) KPI1 ACTUAL */}
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">KPI1 Actual</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className={"text-2xl font-bold ${kpi1ActualInvoice >= kpi1Target ? 'text-green-600' : 'text-gray-900'}"}>
            ${kpi1ActualInvoice.toLocaleString()}
          </p>
          <div className="flex items-center mt-2 text-xs">
            <span className={"${kpi1ActualInvoice >= kpi1Target ? 'text-green-600' : 'text-orange-500'} font-medium"}>
              {kpi1Target > 0 ? Math.round((kpi1ActualInvoice / kpi1Target) * 100) : 0}% of Target
            </span>
          </div>
        </div>

        {/* 3) KPI2 TARGET */}
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">KPI2 Target</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faMoneyBillWave} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            ${kpi2Target.toLocaleString()}
          </p>
          <div className="flex items-center mt-2 text-xs">
            <span className="text-gray-500">Money in Bank Target</span>
          </div>
        </div>

        {/* 4) KPI2 ACTUAL */}
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">KPI2 Actual</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faMoneyBillWave} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className={"text-2xl font-bold ${kpi2MoneyBank >= kpi2Target ? 'text-green-600' : 'text-gray-900'}"}>
            ${kpi2MoneyBank.toLocaleString()}
          </p>
          <div className="flex items-center mt-2 text-xs">
            <span className={"${kpi2MoneyBank >= kpi2Target ? 'text-green-600' : 'text-orange-500'} font-medium"}>
              {kpi2Target > 0 ? Math.round((kpi2MoneyBank / kpi2Target) * 100) : 0}% of Target
            </span>
          </div>
        </div>
      </div>

      {/* PROGRESS OVER TIME CHARTS */}
      <div className="mb-6">
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h5 className="text-md font-medium text-gray-900">Progress Over Time</h5>
              {selectedWeeklyTarget && (
                <p className="text-xs text-gray-500 mt-1">
                  {new Date(computedStartDate).toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                  })} - {new Date(computedEndDate).toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                  })}
                </p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              {/* Agent Filter Dropdown */}
              <select
                value={selectedAgent}
                onChange={(e) => setSelectedAgent(e.target.value)}
                className="bg-[#F4F5F9] px-3 py-2 rounded-md text-sm font-medium border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#6E39CB]"
              >
                <option value="all">All Agents</option>
                {availableAgents.map(agent => (
                  <option key={agent.username} value={agent.username}>
                    {agent.fullName}
                  </option>
                ))}
              </select>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                <FontAwesomeIcon icon={faChartLine} className="mr-1 h-3 w-3" /> Trend Analysis
              </span>
            </div>
          </div>

          {isLoadingProgressData || isLoadingXeroInvoices || isLoadingXeroInBank ? (
            <div className="flex items-center justify-center h-64">
              <FontAwesomeIcon icon={faSpinner} className="text-[#6E39CB] text-2xl animate-spin mr-3" />
              <span className="text-gray-500">Loading progress data...</span>
            </div>
          ) : progressChartData ? (
            <div className="h-80">
              <Line
                data={progressChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                      ticks: { callback: (value) => `$${value.toLocaleString()}` },
                      title: { display: true, text: "Amount ($)" },
                    },
                    x: {
                      ticks: { autoSkip: true, maxTicksLimit: 10 },
                      title: { display: true, text: "Date" },
                    },
                  },
                  plugins: {
                    tooltip: {
                      filter: function(tooltipItem) {
                        // Don't show tooltip for null values (future dates)
                        return tooltipItem.parsed.y !== null;
                      },
                      callbacks: {
                        label: (context) => {
                          let label = context.dataset.label || "";
                          if (label) label += ": ";
                          if (context.parsed.y !== null) {
                            label += `$${context.parsed.y.toLocaleString()}`;
                          } else {
                            label += "Future date";
                          }
                          return label;
                        },
                      },
                    },
                  },
                  elements: {
                    line: {
                      spanGaps: false, // Don't connect lines across null values
                    },
                  },
                }}
              />
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center text-gray-500">
                <FontAwesomeIcon icon={faExclamationTriangle} className="text-gray-400 text-2xl mb-3" />
                <p>No historical data available for progress tracking</p>
                <p className="text-sm mt-1">Data will appear as more weekly targets are completed</p>
              </div>
            </div>
          )}

          {progressChartData && (
            <div className="mt-6">
              <div className="flex items-center justify-center">
                <div className="flex items-center text-sm text-gray-600">
                  <FontAwesomeIcon icon={faInfoCircle} className="mr-2 text-gray-400" />
                  <span>This chart shows actual vs. ideal progress over the target period</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* KPI1 LEADERBOARD - FULL WIDTH */}
      <div className="mb-6">
        <div id="kpi1-leaderboard-card" className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h5 className="text-md font-medium text-gray-900">KPI1 Performance Leaderboard</h5>
              <p className="text-xs text-gray-500 mt-1">
                Revenue Generation - Ranked by achievement percentage
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <FontAwesomeIcon icon={faCheckCircle} className="mr-1 h-3 w-3" /> Target Achieved
              </span>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                <FontAwesomeIcon icon={faArrowUp} className="mr-1 h-3 w-3" /> Target Pending
              </span>

              <button
                title="Download Screenshot"
                onClick={handleKPI1Screenshot}
                className="ml-2 p-1 rounded hover:bg-gray-200"
                style={{ lineHeight: 0 }}
              >
                <FontAwesomeIcon icon={faPrint} className="text-gray-500 h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-[#F4F5F9]">
                <tr>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                    Rank
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Agent
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Target
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actual
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedKPI1Agents.map((agent, idx) => {
                  const kpi1Met = agent.status === "achieved";

                  // Determine row styling based on rank and achievement
                  let rowClass = "transition-colors";
                  let rankClass = "";

                  if (kpi1Met) {
                    rowClass += " bg-green-50 hover:bg-green-100";
                  } else if (idx >= sortedKPI1Agents.length - 3) {
                    // Bottom 3 performers who didn't meet target
                    rowClass += " bg-gray-50 hover:bg-gray-100";
                  } else {
                    rowClass += " hover:bg-gray-50";
                  }

                  // Special styling for top 3
                  if (idx === 0) {
                    rankClass = "bg-yellow-100 text-yellow-800 border border-yellow-300";
                  } else if (idx === 1) {
                    rankClass = "bg-gray-200 text-gray-800 border border-gray-300";
                  } else if (idx === 2) {
                    rankClass = "bg-orange-100 text-orange-800 border border-orange-300";
                  } else {
                    rankClass = "bg-gray-100 text-gray-600";
                  }

                  return (
                    <tr key={idx} className={rowClass}>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        <div className="flex justify-center">
                          <span className={"h-8 w-8 rounded-full flex items-center justify-center text-sm font-bold ${rankClass}"}>
                            {idx + 1}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                            <span className="text-[#6E39CB] font-medium">
                              {agent.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <span className="font-medium text-sm">
                            {agent.name}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                        ${agent.target.toLocaleString()}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                        <span className={kpi1Met ? 'font-medium text-green-600' : 'font-normal'}>
                          ${agent.actual.toLocaleString()}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center justify-center">
                          <div className="w-full max-w-xs bg-gray-200 rounded-full h-2.5">
                            <div
                              className={"h-2.5 rounded-full ${kpi1Met ? 'bg-green-500' : agent.percentage >= 75 ? 'bg-blue-500' : agent.percentage >= 50 ? 'bg-yellow-500' : 'bg-orange-500'}"}
                              style={{ width: "${Math.min(100, agent.percentage)}%" }}
                            ></div>
                          </div>
                          <span className={"ml-2 text-xs font-medium ${kpi1Met ? 'text-green-600' : agent.percentage >= 75 ? 'text-blue-600' : agent.percentage >= 50 ? 'text-yellow-600' : 'text-orange-500'}"}>
                            {agent.percentage}%
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        {kpi1Met ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> Achieved
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <FontAwesomeIcon icon={faArrowUp} className="mr-1" /> {100 - agent.percentage}% to go
                          </span>
                        )}
                      </td>
                    </tr>
                  );
                })}

                {sortedKPI1Agents.length === 0 && (
                  <tr>
                    <td colSpan="6" className="px-6 py-4 text-center text-sm text-gray-500">
                      No data available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* KPI2 LEADERBOARD - FULL WIDTH */}
      <div className="mb-6">
        <div id="kpi2-leaderboard-card" className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h5 className="text-md font-medium text-gray-900">KPI2 Performance Leaderboard</h5>
              <p className="text-xs text-gray-500 mt-1">
                Money in Bank - Ranked by achievement percentage
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <FontAwesomeIcon icon={faCheckCircle} className="mr-1 h-3 w-3" /> Target Achieved
              </span>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                <FontAwesomeIcon icon={faArrowUp} className="mr-1 h-3 w-3" /> Target Pending
              </span>
              <button
                title="Download Screenshot"
                onClick={handleKPI2Screenshot}
                className="ml-2 p-1 rounded hover:bg-gray-200"
                style={{ lineHeight: 0 }}
              >
                <FontAwesomeIcon icon={faPrint} className="text-gray-500 h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-[#F4F5F9]">
                <tr>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                    Rank
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Agent
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Target
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actual
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedKPI2Agents.map((agent, idx) => {
                  const kpi2Met = agent.status === "achieved";

                  // Determine row styling based on rank and achievement
                  let rowClass = "transition-colors";
                  let rankClass = "";

                  if (kpi2Met) {
                    rowClass += " bg-green-50 hover:bg-green-100";
                  } else if (idx >= sortedKPI2Agents.length - 3) {
                    // Bottom 3 performers who didn't meet target
                    rowClass += " bg-gray-50 hover:bg-gray-100";
                  } else {
                    rowClass += " hover:bg-gray-50";
                  }

                  // Special styling for top 3
                  if (idx === 0) {
                    rankClass = "bg-yellow-100 text-yellow-800 border border-yellow-300";
                  } else if (idx === 1) {
                    rankClass = "bg-gray-200 text-gray-800 border border-gray-300";
                  } else if (idx === 2) {
                    rankClass = "bg-orange-100 text-orange-800 border border-orange-300";
                  } else {
                    rankClass = "bg-gray-100 text-gray-600";
                  }

                  return (
                    <tr key={idx} className={rowClass}>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        <div className="flex justify-center">
                          <span className={"h-8 w-8 rounded-full flex items-center justify-center text-sm font-bold ${rankClass}"}>
                            {idx + 1}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                            <span className="text-[#6E39CB] font-medium">
                              {agent.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <span className="font-medium text-sm">
                            {agent.name}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                        ${agent.target.toLocaleString()}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                        <span className={kpi2Met ? 'font-medium text-green-600' : 'font-normal'}>
                          ${agent.actual.toLocaleString()}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center justify-center">
                          <div className="w-full max-w-xs bg-gray-200 rounded-full h-2.5">
                            <div
                              className={"h-2.5 rounded-full ${kpi2Met ? 'bg-green-500' : agent.percentage >= 75 ? 'bg-blue-500' : agent.percentage >= 50 ? 'bg-yellow-500' : 'bg-orange-500'}"}
                              style={{ width: "${Math.min(100, agent.percentage)}%" }}
                            ></div>
                          </div>
                          <span className={"ml-2 text-xs font-medium ${kpi2Met ? 'text-green-600' : agent.percentage >= 75 ? 'text-blue-600' : agent.percentage >= 50 ? 'text-yellow-600' : 'text-orange-500'}"}>
                            {agent.percentage}%
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        {kpi2Met ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> Achieved
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <FontAwesomeIcon icon={faArrowUp} className="mr-1" /> {100 - agent.percentage}% to go
                          </span>
                        )}
                      </td>
                    </tr>
                  );
                })}

                {sortedKPI2Agents.length === 0 && (
                  <tr>
                    <td colSpan="6" className="px-6 py-4 text-center text-sm text-gray-500">
                      No data available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* AI PERFORMANCE ANALYSIS & PROJECTIONS */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h5 className="text-md font-medium text-gray-900">AI Performance Analysis Report</h5>
            <p className="text-xs text-gray-500 mt-1">
              Executive summary, KPI analysis, leaderboard insights, recommendations, and risk assessment based on current performance data
            </p>
          </div>
          <button
            onClick={generateAIInsights}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            disabled={isLoadingReport}
          >
            {isLoadingReport ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="mr-2 animate-spin" />
                Generating Report...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faRobot} className="mr-2" />
                Generate AI Analysis Report
              </>
            )}
          </button>
        </div>
        <div className="bg-gray-50 rounded-lg p-4 min-h-[200px]">
          <div className="text-center text-gray-500 py-12">
            <FontAwesomeIcon icon={faRobot} className="text-[#6E39CB] text-4xl mb-4" />
            <p className="text-lg mb-2">Click "Generate AI Analysis Report" to get comprehensive insights</p>
            <p className="text-sm">This will provide:</p>
            <ul className="text-sm mt-2 space-y-1">
              <li>• Executive summary and KPI analysis</li>
              <li>• Strategic recommendations and risk assessment</li>
              <li>• Leaderboard insights and success factors</li>
            </ul>
          </div>
        </div>
      </div>

      {/* AI Analysis Modal */}
      <AIAnalysisModal
        isOpen={isAiModalOpen}
        onClose={() => setIsAiModalOpen(false)}
        isLoading={isLoadingReport}
        error={reportError}
        reportData={aiInsights}
      />
    </div>
  );
};

export default SourceOfTruthDashboard;
