package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.core.models.enums.Status;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ApplicationRequestDTO {
    private String applicantName;
    private String applicantEmail;
    private String applicantPhone;
    private String applicantAddress;

    private String leadPhone;

    private Status status;
    private String otherInformation;

    private List<SoldQualificationDTO> soldQualifications;

    private LocalDateTime createdOn;

    public String getApplicantPhone() {
        return applicantPhone.trim();
    }
}
