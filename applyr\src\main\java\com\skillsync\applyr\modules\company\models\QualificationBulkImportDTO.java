package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class QualificationBulkImportDTO {
    private String category;           // Maps to type
    private String code;              // Maps to qualificationId
    private String qualificationName;
    private double rplLow;            // Maps to rplPrice
    private double rplHigh;           // Maps to rtoPriceHigh
    private double enrollment;        // Maps to enrollmentPrice
    private double offshoreLow;       // Maps to offshorePrice
    private String notes;
    private String processingTime;
    private String demand;
}
