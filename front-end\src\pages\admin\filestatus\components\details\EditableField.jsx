import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faCheck, faTimes, faSpinner } from "@fortawesome/free-solid-svg-icons";

/**
 * EditableField component for inline editing of text fields
 * @param {string} label - Field label
 * @param {string} value - Current value
 * @param {function} onSave - Function to call when saving (receives new value)
 * @param {boolean} isLoading - Loading state
 * @param {string} type - Input type (text, date, select)
 * @param {array} options - Options for select type
 * @param {boolean} disabled - Whether the field is disabled
 */
const EditableField = ({
  label,
  value,
  onSave,
  isLoading = false,
  type = "text",
  options = [],
  disabled = false,
  className = "",
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value || "");

  const handleEdit = () => {
    if (disabled) return;
    setEditValue(value || "");
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(value || "");
  };

  const handleSave = async () => {
    if (editValue === value) {
      setIsEditing(false);
      return;
    }
    
    try {
      await onSave(editValue);
      setIsEditing(false);
    } catch (error) {
      console.error("Error saving field:", error);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      handleCancel();
    }
  };

  return (
    <div className={`mb-4 ${className}`}>
      <div className="flex justify-between items-center mb-1">
        <label className="text-sm font-medium text-gray-500">{label}</label>
        {!isEditing && !disabled && (
          <button
            onClick={handleEdit}
            className="text-gray-400 hover:text-[#6E39CB] transition-colors"
            title="Edit"
          >
            <FontAwesomeIcon icon={faEdit} />
          </button>
        )}
      </div>

      {isEditing ? (
        <div className="flex items-center">
          {type === "select" ? (
            <select
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              disabled={isLoading}
            >
              <option value="">Select...</option>
              {options.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          ) : type === "date" ? (
            <input
              type="date"
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyDown}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              disabled={isLoading}
            />
          ) : (
            <input
              type="text"
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyDown}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#6E39CB] focus:border-[#6E39CB] sm:text-sm"
              disabled={isLoading}
            />
          )}

          <div className="flex ml-2">
            <button
              onClick={handleSave}
              className="text-green-500 hover:text-green-700 mr-2"
              disabled={isLoading}
              title="Save"
            >
              {isLoading ? (
                <FontAwesomeIcon icon={faSpinner} spin />
              ) : (
                <FontAwesomeIcon icon={faCheck} />
              )}
            </button>
            <button
              onClick={handleCancel}
              className="text-red-500 hover:text-red-700"
              disabled={isLoading}
              title="Cancel"
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </div>
        </div>
      ) : (
        <div className="text-sm text-gray-900 py-2 px-1 bg-gray-50 rounded-md">
          {value || "Not set"}
        </div>
      )}
    </div>
  );
};

export default EditableField;
