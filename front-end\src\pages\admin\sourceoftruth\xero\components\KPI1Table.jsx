import React, { useState, useMemo } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faFileInvoice,
  faUpload,
  faSpinner,
  faCircle,
  faCheck,
  faExclamationTriangle,
  faSort,
  faSortUp,
  faSortDown
} from "@fortawesome/free-solid-svg-icons";

const KPI1Table = ({ data, isLoading, onImport }) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return dateString; // Already formatted as 'dd MMM yyyy'
  };

  // Format currency for display
  const formatCurrency = (amount) => {
    if (amount === undefined || amount === null) return "-";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Handle sorting
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Get sort icon
  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return faSort;
    }
    return sortConfig.direction === 'asc' ? faSortUp : faSortDown;
  };

  // Sort data
  const sortedData = useMemo(() => {
    if (!sortConfig.key || !data) return data;

    return [...data].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle different data types
      if (sortConfig.key === 'invoiceDate' || sortConfig.key === 'invoiceExpiryDate') {
        aValue = aValue ? new Date(aValue) : new Date(0);
        bValue = bValue ? new Date(bValue) : new Date(0);
      } else if (sortConfig.key === 'gross' || sortConfig.key === 'balance') {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      } else {
        aValue = aValue ? aValue.toString().toLowerCase() : '';
        bValue = bValue ? bValue.toString().toLowerCase() : '';
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [data, sortConfig]);

  // Get status badge color
  const getStatusBadge = (status) => {
    if (!status) return { color: "gray", icon: null };

    switch (status.toLowerCase()) {
      case "paid":
        return { color: "green", icon: faCheck };
      case "approved":
        return { color: "blue", icon: null };
      case "partially paid":
        return { color: "orange", icon: null };
      case "pending":
        return { color: "yellow", icon: faExclamationTriangle };
      default:
        return { color: "gray", icon: null };
    }
  };

  // Get invoice sent badge color
  const getInvoiceSentBadge = (status) => {
    if (!status) return { color: "gray", icon: null };

    return status.toLowerCase() === "sent"
      ? { color: "green", icon: faCheck }
      : { color: "red", icon: faExclamationTriangle };
  };

  // No longer need agents list since filter moved to XeroFilterBar

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-[#3A3541]">
          <FontAwesomeIcon icon={faFileInvoice} className="mr-2 text-[#6E39CB]" />
          KPI1 Invoicing Data
        </h2>
        <div className="text-sm text-gray-500">
          {data.length} {data.length === 1 ? 'record' : 'records'} found
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-[#DBDCDE] overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full divide-y divide-[#DBDCDE]">
            <thead className="bg-[#F9FAFB]">
              <tr>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('invoiceNumber')}
                >
                  <div className="flex items-center gap-1">
                    Invoice #
                    <FontAwesomeIcon icon={getSortIcon('invoiceNumber')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('contact')}
                >
                  <div className="flex items-center gap-1">
                    Contact
                    <FontAwesomeIcon icon={getSortIcon('contact')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('invoiceDate')}
                >
                  <div className="flex items-center gap-1">
                    Inv. Date
                    <FontAwesomeIcon icon={getSortIcon('invoiceDate')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('invoiceExpiryDate')}
                >
                  <div className="flex items-center gap-1">
                    Due Date
                    <FontAwesomeIcon icon={getSortIcon('invoiceExpiryDate')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('reference')}
                >
                  <div className="flex items-center gap-1">
                    Reference
                    <FontAwesomeIcon icon={getSortIcon('reference')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('gross')}
                >
                  <div className="flex items-center gap-1">
                    Gross
                    <FontAwesomeIcon icon={getSortIcon('gross')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('balance')}
                >
                  <div className="flex items-center gap-1">
                    Balance
                    <FontAwesomeIcon icon={getSortIcon('balance')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center gap-1">
                    Status
                    <FontAwesomeIcon icon={getSortIcon('status')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('source')}
                >
                  <div className="flex items-center gap-1">
                    Source
                    <FontAwesomeIcon icon={getSortIcon('source')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('invoiceStatus')}
                >
                  <div className="flex items-center gap-1">
                    Inv. Sent
                    <FontAwesomeIcon icon={getSortIcon('invoiceStatus')} className="text-[#89868D]" />
                  </div>
                </th>
                <th
                  className="px-3 py-3 text-left text-xs font-medium text-[#3A3541] uppercase tracking-wider cursor-pointer hover:bg-[#F4F5F9] transition-colors"
                  onClick={() => handleSort('agent')}
                >
                  <div className="flex items-center gap-1">
                    Agent
                    <FontAwesomeIcon icon={getSortIcon('agent')} className="text-[#89868D]" />
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-[#DBDCDE]">
              {isLoading ? (
                <tr>
                  <td colSpan="11" className="px-3 py-8 text-center text-[#89868D]">
                    <div className="flex flex-col items-center">
                      <FontAwesomeIcon icon={faSpinner} className="text-3xl mb-2 text-[#6E39CB] animate-spin" />
                      <p className="text-[#3A3541] font-medium">Loading invoice data...</p>
                    </div>
                  </td>
                </tr>
              ) : sortedData && sortedData.length > 0 ? (
                sortedData.map((item, index) => {
                  const statusBadge = getStatusBadge(item.status);
                  const invoiceSentBadge = getInvoiceSentBadge(item.invoiceStatus);

                  return (
                    <tr key={index} className="hover:bg-[#F9FAFB] transition-colors">
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                        {item.invoiceNumber || "-"}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                        {item.contact || "-"}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                        {formatDate(item.invoiceDate)}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                        {formatDate(item.invoiceExpiryDate)}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                        {item.reference || "-"}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                        {formatCurrency(item.gross)}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                        {formatCurrency(item.balance)}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${statusBadge.color}-100 text-${statusBadge.color}-800`}>
                          {statusBadge.icon && <FontAwesomeIcon icon={statusBadge.icon} className="mr-1" />}
                          {item.status || "Unknown"}
                        </span>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                        {item.source || "-"}
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${invoiceSentBadge.color}-100 text-${invoiceSentBadge.color}-800`}>
                          {invoiceSentBadge.icon && <FontAwesomeIcon icon={invoiceSentBadge.icon} className="mr-1" />}
                          {item.invoiceStatus || "Unknown"}
                        </span>
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-[#3A3541]">
                        {item.agent || "-"}
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan="11" className="px-3 py-8 text-center text-[#89868D]">
                    <div className="flex flex-col items-center">
                      <FontAwesomeIcon icon={faFileInvoice} className="text-3xl mb-2 text-[#DBDCDE]" />
                      <p className="text-[#3A3541] font-medium">No invoice data found</p>
                      <p className="text-sm mt-1">Import data or adjust your filters</p>
                      <button
                        onClick={onImport}
                        className="mt-4 flex items-center gap-2 px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-opacity-90 transition-colors"
                      >
                        <FontAwesomeIcon icon={faUpload} />
                        Import Data
                      </button>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
};

export default KPI1Table;
