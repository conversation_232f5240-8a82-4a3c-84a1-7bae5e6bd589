import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUser,
  faLock,
  faBell,
  faEnvelope,
  faShieldAlt,
  faSave,
  faTimesCircle
} from '@fortawesome/free-solid-svg-icons';
import { showValidationError, showSuccessToast } from '../../../utils/toastUtils';

const OperationsSettings = () => {
  const [activeTab, setActiveTab] = useState('profile');

  // Profile settings state
  const [profileForm, setProfileForm] = useState({
    fullName: 'Operations User',
    email: '<EMAIL>',
    phone: '+****************',
    jobTitle: 'Operations Manager',
    department: 'Operations',
    bio: 'Experienced operations manager with a focus on efficiency and process improvement.'
  });

  // Password settings state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Notification settings state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    applicationUpdates: true,
    quoteRequests: true,
    invoiceUpdates: true,
    systemAlerts: false
  });

  // Handle profile form changes
  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle password form changes
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle notification toggle
  const handleNotificationToggle = (setting) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  // Handle profile form submission
  const handleProfileSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the updated profile to your API
    showSuccessToast('Profile updated successfully!');
  };

  // Handle password form submission
  const handlePasswordSubmit = (e) => {
    e.preventDefault();

    // Basic validation
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      showValidationError('New passwords do not match!');
      return;
    }

    if (passwordForm.newPassword.length < 8) {
      showValidationError('Password must be at least 8 characters long!');
      return;
    }

    // Here you would typically send the password update to your API
    showSuccessToast('Password updated successfully!');

    // Reset form
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
  };

  return (
    <div className="w-full">
      {/* Header Section */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage your account settings and preferences
        </p>
      </div>

      {/* Settings Container */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-50 overflow-hidden">
        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'profile' ? 'text-[#6E39CB] border-b-2 border-[#6E39CB]' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('profile')}
          >
            <FontAwesomeIcon icon={faUser} className="mr-2" />
            Profile
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'password' ? 'text-[#6E39CB] border-b-2 border-[#6E39CB]' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('password')}
          >
            <FontAwesomeIcon icon={faLock} className="mr-2" />
            Password
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'notifications' ? 'text-[#6E39CB] border-b-2 border-[#6E39CB]' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('notifications')}
          >
            <FontAwesomeIcon icon={faBell} className="mr-2" />
            Notifications
          </button>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {/* Profile Settings */}
          {activeTab === 'profile' && (
            <form onSubmit={handleProfileSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="fullName"
                    value={profileForm.fullName}
                    onChange={handleProfileChange}
                    className="block w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={profileForm.email}
                    onChange={handleProfileChange}
                    className="block w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={profileForm.phone}
                    onChange={handleProfileChange}
                    className="block w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Job Title
                  </label>
                  <input
                    type="text"
                    name="jobTitle"
                    value={profileForm.jobTitle}
                    onChange={handleProfileChange}
                    className="block w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Department
                  </label>
                  <input
                    type="text"
                    name="department"
                    value={profileForm.department}
                    onChange={handleProfileChange}
                    className="block w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bio
                  </label>
                  <textarea
                    name="bio"
                    value={profileForm.bio}
                    onChange={handleProfileChange}
                    rows="4"
                    className="block w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  ></textarea>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  type="button"
                  className="mr-3 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
                >
                  <FontAwesomeIcon icon={faTimesCircle} className="mr-2" />
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#6E39CB] hover:bg-[#5930a8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
                >
                  <FontAwesomeIcon icon={faSave} className="mr-2" />
                  Save Changes
                </button>
              </div>
            </form>
          )}

          {/* Password Settings */}
          {activeTab === 'password' && (
            <form onSubmit={handlePasswordSubmit}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Password
                  </label>
                  <input
                    type="password"
                    name="currentPassword"
                    value={passwordForm.currentPassword}
                    onChange={handlePasswordChange}
                    className="block w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    New Password
                  </label>
                  <input
                    type="password"
                    name="newPassword"
                    value={passwordForm.newPassword}
                    onChange={handlePasswordChange}
                    className="block w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                    required
                    minLength="8"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Password must be at least 8 characters long and include a mix of letters, numbers, and symbols.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Confirm New Password
                  </label>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={passwordForm.confirmPassword}
                    onChange={handlePasswordChange}
                    className="block w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                    required
                  />
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  type="button"
                  className="mr-3 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
                >
                  <FontAwesomeIcon icon={faTimesCircle} className="mr-2" />
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#6E39CB] hover:bg-[#5930a8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
                >
                  <FontAwesomeIcon icon={faLock} className="mr-2" />
                  Update Password
                </button>
              </div>
            </form>
          )}

          {/* Notification Settings */}
          {activeTab === 'notifications' && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Email Notifications</h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between py-3 border-b border-gray-100">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
                    <p className="text-sm text-gray-500">Receive email notifications for important updates</p>
                  </div>
                  <div className="relative inline-block w-10 mr-2 align-middle select-none">
                    <input
                      type="checkbox"
                      id="emailNotifications"
                      checked={notificationSettings.emailNotifications}
                      onChange={() => handleNotificationToggle('emailNotifications')}
                      className="sr-only"
                    />
                    <label
                      htmlFor="emailNotifications"
                      className={`block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.emailNotifications ? 'bg-[#6E39CB]' : 'bg-gray-300'}`}
                    >
                      <span
                        className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${notificationSettings.emailNotifications ? 'translate-x-4' : 'translate-x-0'}`}
                      ></span>
                    </label>
                  </div>
                </div>

                <div className="flex items-center justify-between py-3 border-b border-gray-100">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Application Updates</h4>
                    <p className="text-sm text-gray-500">Get notified when applications are updated</p>
                  </div>
                  <div className="relative inline-block w-10 mr-2 align-middle select-none">
                    <input
                      type="checkbox"
                      id="applicationUpdates"
                      checked={notificationSettings.applicationUpdates}
                      onChange={() => handleNotificationToggle('applicationUpdates')}
                      className="sr-only"
                    />
                    <label
                      htmlFor="applicationUpdates"
                      className={`block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.applicationUpdates ? 'bg-[#6E39CB]' : 'bg-gray-300'}`}
                    >
                      <span
                        className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${notificationSettings.applicationUpdates ? 'translate-x-4' : 'translate-x-0'}`}
                      ></span>
                    </label>
                  </div>
                </div>

                <div className="flex items-center justify-between py-3 border-b border-gray-100">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Quote Requests</h4>
                    <p className="text-sm text-gray-500">Get notified about new quote requests</p>
                  </div>
                  <div className="relative inline-block w-10 mr-2 align-middle select-none">
                    <input
                      type="checkbox"
                      id="quoteRequests"
                      checked={notificationSettings.quoteRequests}
                      onChange={() => handleNotificationToggle('quoteRequests')}
                      className="sr-only"
                    />
                    <label
                      htmlFor="quoteRequests"
                      className={`block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.quoteRequests ? 'bg-[#6E39CB]' : 'bg-gray-300'}`}
                    >
                      <span
                        className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${notificationSettings.quoteRequests ? 'translate-x-4' : 'translate-x-0'}`}
                      ></span>
                    </label>
                  </div>
                </div>

                <div className="flex items-center justify-between py-3 border-b border-gray-100">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Invoice Updates</h4>
                    <p className="text-sm text-gray-500">Get notified about invoice status changes</p>
                  </div>
                  <div className="relative inline-block w-10 mr-2 align-middle select-none">
                    <input
                      type="checkbox"
                      id="invoiceUpdates"
                      checked={notificationSettings.invoiceUpdates}
                      onChange={() => handleNotificationToggle('invoiceUpdates')}
                      className="sr-only"
                    />
                    <label
                      htmlFor="invoiceUpdates"
                      className={`block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.invoiceUpdates ? 'bg-[#6E39CB]' : 'bg-gray-300'}`}
                    >
                      <span
                        className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${notificationSettings.invoiceUpdates ? 'translate-x-4' : 'translate-x-0'}`}
                      ></span>
                    </label>
                  </div>
                </div>

                <div className="flex items-center justify-between py-3 border-b border-gray-100">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">System Alerts</h4>
                    <p className="text-sm text-gray-500">Get notified about system maintenance and updates</p>
                  </div>
                  <div className="relative inline-block w-10 mr-2 align-middle select-none">
                    <input
                      type="checkbox"
                      id="systemAlerts"
                      checked={notificationSettings.systemAlerts}
                      onChange={() => handleNotificationToggle('systemAlerts')}
                      className="sr-only"
                    />
                    <label
                      htmlFor="systemAlerts"
                      className={`block overflow-hidden h-6 rounded-full cursor-pointer ${notificationSettings.systemAlerts ? 'bg-[#6E39CB]' : 'bg-gray-300'}`}
                    >
                      <span
                        className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${notificationSettings.systemAlerts ? 'translate-x-4' : 'translate-x-0'}`}
                      ></span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  type="button"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#6E39CB] hover:bg-[#5930a8] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6E39CB]"
                >
                  <FontAwesomeIcon icon={faSave} className="mr-2" />
                  Save Preferences
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OperationsSettings;
