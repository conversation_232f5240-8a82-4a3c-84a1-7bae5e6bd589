package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.Comment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CommentRepository extends JpaRepository<Comment, Long> {
    
    List<Comment> findByLeadPhoneOrderByCreatedDateDesc(String leadPhone);
    
    Optional<Comment> findByIdAndLeadPhone(Long commentId, String leadPhone);
}
