package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.models.entities.*;
import com.skillsync.applyr.modules.company.models.AgentTargetResponseDTO;
import com.skillsync.applyr.modules.company.models.DashboardDTO;

import com.skillsync.applyr.modules.company.repositories.*;
import com.skillsync.applyr.core.utills.UserUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Optional;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

@Service
public class DashboardService {

    private final WeeklyTargetsCombinedRepository weeklyTargetsCombinedRepository;
    private final WeeklyTargetRepository weeklyTargetRepository;
    private final ApplicationRepository applicationRepository;
    private final SalesAgentRepository salesAgentRepository;

    private final TrueXeroInBankRepository trueXeroInBankRepository;
    private final TrueXeroInvoiceRespository trueXeroInvoiceRespository;

    public DashboardService(WeeklyTargetsCombinedRepository weeklyTargetsCombinedRepository,
                            WeeklyTargetRepository weeklyTargetRepository,
                            ApplicationRepository applicationRepository,
                            SalesAgentRepository salesAgentRepository, TrueXeroInBankRepository trueXeroInBankRepository, TrueXeroInvoiceRespository trueXeroInvoiceRespository) {
        this.weeklyTargetsCombinedRepository = weeklyTargetsCombinedRepository;
        this.weeklyTargetRepository = weeklyTargetRepository;
        this.applicationRepository = applicationRepository;
        this.salesAgentRepository = salesAgentRepository;

        this.trueXeroInBankRepository = trueXeroInBankRepository;
        this.trueXeroInvoiceRespository = trueXeroInvoiceRespository;
    }

    public DashboardDTO getAgentDashboard(LocalDateTime startDate, LocalDateTime endDate) {
        List<WeeklyTargetCombined> weeklyTargetCombined;
        if (startDate.getDayOfMonth() == endDate.getDayOfMonth() &&
                startDate.getMonthValue() == endDate.getMonthValue() &&
                startDate.getYear() == endDate.getYear()) {
            // If start and end date are the same day, use current time as end date
            endDate = LocalDateTime.now();
            // Find targets that include this specific date
            weeklyTargetCombined = weeklyTargetsCombinedRepository
                    .findAllByStartDateLessThanEqualAndEndDateGreaterThanEqual(startDate, startDate);
        } else {
            // Find targets that overlap with the given date range
            weeklyTargetCombined = weeklyTargetsCombinedRepository
                    .findByDateRangeOverlap(startDate, endDate);
        }
        return createSourceOfTrueDashboardDTO(false, weeklyTargetCombined, startDate, endDate);
    }

    public DashboardDTO getAdminDashboard(LocalDateTime startDate, LocalDateTime endDate) {
        List<WeeklyTargetCombined> weeklyTargetCombined;
        if (endDate == null) {
            // If end date is not provided, use current time
            endDate = LocalDateTime.now();
            // Find targets that include this specific date
            weeklyTargetCombined = weeklyTargetsCombinedRepository
                    .findAllByStartDateLessThanEqualAndEndDateGreaterThanEqual(startDate, startDate);
        } else if (startDate.equals(endDate)) {
            // If start and end date are the same, find targets that include this specific date
            weeklyTargetCombined = weeklyTargetsCombinedRepository
                    .findAllByStartDateLessThanEqualAndEndDateGreaterThanEqual(startDate, startDate);
        } else {
            // Find targets that overlap with the given date range
            weeklyTargetCombined = weeklyTargetsCombinedRepository
                    .findByDateRangeOverlap(startDate, endDate);
        }
        return createSourceOfTrueDashboardDTO(true, weeklyTargetCombined, startDate, endDate);
    }

    public DashboardDTO getAdminDashboardByTargetId(Long targetId) {
        Optional<WeeklyTargetCombined> targetOpt = weeklyTargetsCombinedRepository.findById(targetId);
        if (targetOpt.isEmpty()) {
            throw new RuntimeException("Weekly target not found with ID: " + targetId);
        }

        WeeklyTargetCombined target = targetOpt.get();
        List<WeeklyTargetCombined> weeklyTargetCombined = new ArrayList<>();
        weeklyTargetCombined.add(target);

        return createSourceOfTrueDashboardDTO(true, weeklyTargetCombined, target.getStartDate(), target.getEndDate());
    }

    public DashboardDTO getAgentDashboardByTargetId(Long targetId) {
        Optional<WeeklyTargetCombined> targetOpt = weeklyTargetsCombinedRepository.findById(targetId);
        if (targetOpt.isEmpty()) {
            throw new RuntimeException("Weekly target not found with ID: " + targetId);
        }

        WeeklyTargetCombined target = targetOpt.get();
        List<WeeklyTargetCombined> weeklyTargetCombined = new ArrayList<>();
        weeklyTargetCombined.add(target);

        return createSourceOfTrueDashboardDTO(false, weeklyTargetCombined, target.getStartDate(), target.getEndDate());
    }

    // New methods for multiple target selection
    public DashboardDTO getAdminDashboardByMultipleTargetIds(List<Long> targetIds) {
        List<WeeklyTargetCombined> weeklyTargetCombined = new ArrayList<>();
        LocalDateTime earliestStart = null;
        LocalDateTime latestEnd = null;

        for (Long targetId : targetIds) {
            Optional<WeeklyTargetCombined> targetOpt = weeklyTargetsCombinedRepository.findById(targetId);
            if (targetOpt.isPresent()) {
                WeeklyTargetCombined target = targetOpt.get();
                weeklyTargetCombined.add(target);

                if (earliestStart == null || target.getStartDate().isBefore(earliestStart)) {
                    earliestStart = target.getStartDate();
                }
                if (latestEnd == null || target.getEndDate().isAfter(latestEnd)) {
                    latestEnd = target.getEndDate();
                }
            }
        }

        if (weeklyTargetCombined.isEmpty()) {
            throw new RuntimeException("No valid weekly targets found for the provided IDs");
        }

        return createSourceOfTrueDashboardDTO(true, weeklyTargetCombined, earliestStart, latestEnd);
    }

    public DashboardDTO getAgentDashboardByMultipleTargetIds(List<Long> targetIds) {
        List<WeeklyTargetCombined> weeklyTargetCombined = new ArrayList<>();
        LocalDateTime earliestStart = null;
        LocalDateTime latestEnd = null;

        for (Long targetId : targetIds) {
            Optional<WeeklyTargetCombined> targetOpt = weeklyTargetsCombinedRepository.findById(targetId);
            if (targetOpt.isPresent()) {
                WeeklyTargetCombined target = targetOpt.get();
                weeklyTargetCombined.add(target);

                if (earliestStart == null || target.getStartDate().isBefore(earliestStart)) {
                    earliestStart = target.getStartDate();
                }
                if (latestEnd == null || target.getEndDate().isAfter(latestEnd)) {
                    latestEnd = target.getEndDate();
                }
            }
        }

        if (weeklyTargetCombined.isEmpty()) {
            throw new RuntimeException("No valid weekly targets found for the provided IDs");
        }

        return createSourceOfTrueDashboardDTO(false, weeklyTargetCombined, earliestStart, latestEnd);
    }

    // New methods for predefined date ranges
    public DashboardDTO getAdminDashboardThisMonth() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfMonth = now.withDayOfMonth(now.toLocalDate().lengthOfMonth()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);

        return getAdminDashboard(startOfMonth, endOfMonth);
    }

    public DashboardDTO getAgentDashboardThisMonth() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfMonth = now.withDayOfMonth(now.toLocalDate().lengthOfMonth()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);

        return getAgentDashboard(startOfMonth, endOfMonth);
    }

    public DashboardDTO getAdminDashboardThisYear() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfYear = now.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfYear = now.withDayOfYear(now.toLocalDate().lengthOfYear()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);

        return getAdminDashboard(startOfYear, endOfYear);
    }

    public DashboardDTO getAgentDashboardThisYear() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfYear = now.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfYear = now.withDayOfYear(now.toLocalDate().lengthOfYear()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);

        return getAgentDashboard(startOfYear, endOfYear);
    }

    private DashboardDTO createSourceOfTrueDashboardDTO(boolean isAdmin, List<WeeklyTargetCombined> weeklyTargetCombined, LocalDateTime startDate, LocalDateTime endDate) {
        DashboardDTO dashboardDTO = new DashboardDTO();
        for (WeeklyTargetCombined raw : weeklyTargetCombined) {
            dashboardDTO.appendTitle(raw.getTitle());
        }
        List<AgentTargetResponseDTO> agentTargetResponseDTOs = new ArrayList<>();
        List<SalesAgent> salesAgents = salesAgentRepository.findAll();

        double kpi1TotalTarget = 0;
        double kpi2TotalTarget = 0;
        double kpi1TotalActual = 0;
        double kpi2TotalActual = 0;
        int quotesTotal = 0;
        int invoicesTotal = 0;


        List<TrueXeroInvoice> trueXeroInvoices = trueXeroInvoiceRespository
                .findAllByInvoiceDateBetween(startDate.toLocalDate().atStartOfDay(), endDate);

        List<TrueXeroInBank> trueXeroInBanks = trueXeroInBankRepository
                .findAllByInsertDateBetween(startDate.toLocalDate().atStartOfDay(), endDate);

        for (TrueXeroInvoice trueXeroInvoice : trueXeroInvoices) {
            if(isAdmin) {
                kpi1TotalActual += trueXeroInvoice.getGross();
            } else {
                if (Objects.equals(trueXeroInvoice.getAgentUsername(), UserUtils.getUsernameFromToken())) {
                    kpi1TotalActual += trueXeroInvoice.getGross();
                }
            }
        }

        for(TrueXeroInBank trueXeroInBank : trueXeroInBanks) {
            if(isAdmin) {
                kpi2TotalActual += trueXeroInBank.getDebitAmount();
            } else {
                if (Objects.equals(trueXeroInBank.getAgentUsername(), UserUtils.getUsernameFromToken())) {
                    kpi2TotalActual += trueXeroInBank.getDebitAmount();
                }
            }
        }

        for (SalesAgent salesAgent : salesAgents) {
            AgentTargetResponseDTO agentTargetResponseDTO = new AgentTargetResponseDTO();
            agentTargetResponseDTO.setProfile(EmployeeProfileServiceHelper.fromAgentToProfileDTO(salesAgent));
            trueXeroInvoices.stream()
                    .filter(trueXeroInvoice -> Objects.equals(trueXeroInvoice.getAgentUsername(), salesAgent.getUser().getUsername()))
                    .forEach(trueXeroInvoice -> {
                        agentTargetResponseDTO.setKpi1Actual(agentTargetResponseDTO.getKpi1Actual() + trueXeroInvoice.getGross());
                    });
            trueXeroInBanks.stream()
                    .filter(trueXeroInBank -> Objects.equals(trueXeroInBank.getAgentUsername(), salesAgent.getUser().getUsername()))
                    .forEach(trueXeroInBank -> {

                        agentTargetResponseDTO.setKpi2Actual(agentTargetResponseDTO.getKpi2Actual() + trueXeroInBank.getDebitAmount());
                    });

            List<WeeklyTarget> weeklyTargets = weeklyTargetRepository.findAllByAgentUsernameAndWeeklyTargetsCombinedStartDateAfterAndWeeklyTargetsCombinedEndDateBefore(
                    salesAgent.getUser().getUsername(), startDate.minusDays(1).toLocalDate().atStartOfDay(), endDate.plusDays(1).toLocalDate().atStartOfDay());
            double kpi1Target = 0;
            double kpi2Target = 0;
            for (WeeklyTarget weeklyTarget : weeklyTargets) {
                kpi1Target += weeklyTarget.getKpi1();
                kpi2Target += weeklyTarget.getKpi2();
            }
            agentTargetResponseDTO.setKpi1Target(kpi1Target);
            agentTargetResponseDTO.setKpi2Target(kpi2Target);

            agentTargetResponseDTOs.add(agentTargetResponseDTO);
        }


        for(WeeklyTargetCombined weeklyTargetCo : weeklyTargetCombined) {
            for (WeeklyTarget weeklyTarget : weeklyTargetCo.getWeeklyTargets()) {


                if (isAdmin) {
                    kpi1TotalTarget += weeklyTarget.getKpi1();
                    kpi2TotalTarget += weeklyTarget.getKpi2();
                } else {
                    if (Objects.equals(weeklyTarget.getAgentUsername(), UserUtils.getUsernameFromToken())) {
                        kpi1TotalTarget += weeklyTarget.getKpi1();
                        kpi2TotalTarget += weeklyTarget.getKpi2();
                    }
                }
            }
        }


        dashboardDTO.setInvoices(invoicesTotal);
        dashboardDTO.setQuotes(quotesTotal);

        dashboardDTO.setKpi1Target(kpi1TotalTarget);
        dashboardDTO.setKpi2Target(kpi2TotalTarget);

        dashboardDTO.setKpi1Actual(kpi1TotalActual);
        dashboardDTO.setKpi2Actual(kpi2TotalActual);

        dashboardDTO.setAgentInfos(agentTargetResponseDTOs);

        return dashboardDTO;
    }


    private DashboardDTO createDashboardDTO(boolean isAdmin, List<WeeklyTargetCombined> weeklyTargetCombined, LocalDateTime startDate, LocalDateTime endDate) {
        DashboardDTO dashboardDTO = new DashboardDTO();

        if (weeklyTargetCombined.size() == 1) {
            dashboardDTO.setWeekTitle(weeklyTargetCombined.get(0).getTitle());
            List<AgentTargetResponseDTO> agentTargetResponseDTOs = new ArrayList<>();
            double kpi1TotalTarget = 0;
            double kpi2TotalTarget = 0;
            double kpi1TotalActual = 0;
            double kpi2TotalActual = 0;
            int quotesTotal = 0;
            int invoicesTotal = 0;
            for (WeeklyTarget weeklyTarget : weeklyTargetCombined.get(0).getWeeklyTargets()) {
                AgentTargetResponseDTO agentTargetResponseDTO = new AgentTargetResponseDTO();
                var salesAgentOpt = salesAgentRepository.getSalesAgentByUserUsername(weeklyTarget.getAgentUsername());
                if (salesAgentOpt.isPresent()) {
                    agentTargetResponseDTO.setProfile(EmployeeProfileServiceHelper.fromAgentToProfileDTO(salesAgentOpt.get()));
                }
                agentTargetResponseDTO.setKpi1Target(weeklyTarget.getKpi1());
                agentTargetResponseDTO.setKpi2Target(weeklyTarget.getKpi2());
                List<Application> applications = applicationRepository
                        .findAllByAgentUsernameAndCreatedDateAfterAndCreatedDateBefore(
                                weeklyTarget.getAgentUsername(),
                                weeklyTargetCombined.get(0).getStartDate().minusDays(1).toLocalDate().atStartOfDay(),
                                weeklyTargetCombined.get(0).getEndDate().plusDays(1).toLocalDate().atStartOfDay());
                List<Application> applicationsKPI2 = applicationRepository
                        .findDistinctByAgentUsernameAndInstallmentsCreatedDateBetween(
                                weeklyTarget.getAgentUsername(),
                                weeklyTargetCombined.get(0).getStartDate().minusDays(1).toLocalDate().atStartOfDay(),
                                weeklyTargetCombined.get(0).getEndDate().plusDays(1).toLocalDate().atStartOfDay());
                double kpi1Actual = 0;
                double kpi2Actual = 0;
                int quoteRaised = 0;
                int invoicesRaised = 0;
                for (Application application : applications) {
                    if (!(application.getQuoteRefNumber() == null || application.getQuoteRefNumber().isEmpty())) {
                        quoteRaised++;
                    }
                    if (!(application.getInvoiceRefNumber() == null || application.getInvoiceRefNumber().isEmpty())) {
                        kpi1Actual += application.getPrice();
                        invoicesRaised++;
                    }
                }
                for (Application application : applicationsKPI2) {
                    kpi2Actual += application.getPaidAmount();
                }
                agentTargetResponseDTO.setKpi1Actual(kpi1Actual);
                agentTargetResponseDTO.setKpi2Actual(kpi2Actual);
                agentTargetResponseDTO.setQuotesRaised(quoteRaised);
                agentTargetResponseDTO.setInvoiceRaised(invoicesRaised);
                agentTargetResponseDTOs.add(agentTargetResponseDTO);
                if (isAdmin || Objects.equals(agentTargetResponseDTO.getProfile().getUsername(), UserUtils.getUsernameFromToken())) {
                    kpi1TotalTarget += weeklyTarget.getKpi1();
                    kpi2TotalTarget += weeklyTarget.getKpi2();
                    kpi1TotalActual += kpi1Actual;
                    kpi2TotalActual += kpi2Actual;
                    quotesTotal += quoteRaised;
                    invoicesTotal += invoicesRaised;
                }
            }
            dashboardDTO.setInvoices(invoicesTotal);
            dashboardDTO.setQuotes(quotesTotal);
            dashboardDTO.setKpi1Target(kpi1TotalTarget);
            dashboardDTO.setKpi2Target(kpi2TotalTarget);
            dashboardDTO.setKpi1Actual(kpi1TotalActual);
            dashboardDTO.setKpi2Actual(kpi2TotalActual);
            dashboardDTO.setAgentInfos(agentTargetResponseDTOs);

        } else if (weeklyTargetCombined.size() > 1) {
            for (WeeklyTargetCombined raw : weeklyTargetCombined) {
                dashboardDTO.appendTitle(raw.getTitle());
            }
            List<SalesAgent> salesAgents = salesAgentRepository.findAll();
            List<AgentTargetResponseDTO> agentTargetResponseDTOs = new ArrayList<>();
            double kpi1TotalTarget = 0;
            double kpi2TotalTarget = 0;
            double kpi1TotalActual = 0;
            double kpi2TotalActual = 0;
            int quotesTotal = 0;
            int invoicesTotal = 0;
            for (SalesAgent salesAgent : salesAgents) {
                AgentTargetResponseDTO agentTargetResponseDTO = new AgentTargetResponseDTO();
                agentTargetResponseDTO.setProfile(EmployeeProfileServiceHelper.fromAgentToProfileDTO(salesAgent));
                List<WeeklyTarget> weeklyTargets = weeklyTargetRepository.findAllByAgentUsernameAndWeeklyTargetsCombinedStartDateAfterAndWeeklyTargetsCombinedEndDateBefore(
                        salesAgent.getUser().getUsername(), startDate, endDate);
                double kpi1Target = 0;
                double kpi2Target = 0;
                for (WeeklyTarget weeklyTarget : weeklyTargets) {
                    kpi1Target += weeklyTarget.getKpi1();
                    kpi2Target += weeklyTarget.getKpi2();
                }
                agentTargetResponseDTO.setKpi1Target(kpi1Target);
                agentTargetResponseDTO.setKpi2Target(kpi2Target);
                List<Application> applications = applicationRepository
                        .findAllByAgentUsernameAndCreatedDateAfterAndCreatedDateBefore(
                                salesAgent.getUser().getUsername(), startDate, endDate.plusDays(1).toLocalDate().atStartOfDay());
                List<Application> applicationsKPI2 = applicationRepository
                        .findDistinctByAgentUsernameAndInstallmentsCreatedDateBetween(
                                salesAgent.getUser().getUsername(), startDate, endDate.plusDays(1).toLocalDate().atStartOfDay());
                double kpi1Actual = 0;
                double kpi2Actual = 0;
                int quoteRaised = 0;
                int invoicesRaised = 0;
                for (Application application : applications) {
                    if (!(application.getQuoteRefNumber() == null || application.getQuoteRefNumber().isEmpty())) {
                        quoteRaised++;
                    }
                    if (!(application.getInvoiceRefNumber() == null || application.getInvoiceRefNumber().isEmpty())) {
                        kpi1Actual += application.getPrice();
                        invoicesRaised++;
                    }
                }
                for (Application application : applicationsKPI2) {
                    for (PaymentInstallment installment : application.getInstallments()) {
                        if (installment.getCreatedDate().isAfter(startDate.toLocalDate().atStartOfDay()) && installment.getCreatedDate().isBefore(endDate.plusDays(1).toLocalDate().atStartOfDay())) {
                            kpi2Actual += installment.getAmount();
                        }
                    }
                }
                agentTargetResponseDTO.setKpi1Actual(kpi1Actual);
                agentTargetResponseDTO.setKpi2Actual(kpi2Actual);
                agentTargetResponseDTO.setQuotesRaised(quoteRaised);
                agentTargetResponseDTO.setInvoiceRaised(invoicesRaised);
                agentTargetResponseDTOs.add(agentTargetResponseDTO);
                if (isAdmin || Objects.equals(agentTargetResponseDTO.getProfile().getUsername(), UserUtils.getUsernameFromToken())) {
                    kpi1TotalTarget += kpi1Target;
                    kpi2TotalTarget += kpi2Target;
                    kpi1TotalActual += kpi1Actual;
                    kpi2TotalActual += kpi2Actual;
                    quotesTotal += quoteRaised;
                    invoicesTotal += invoicesRaised;
                }
            }
            dashboardDTO.setKpi1Target(kpi1TotalTarget);
            dashboardDTO.setKpi2Target(kpi2TotalTarget);
            dashboardDTO.setKpi1Actual(kpi1TotalActual);
            dashboardDTO.setKpi2Actual(kpi2TotalActual);
            dashboardDTO.setQuotes(quotesTotal);
            dashboardDTO.setInvoices(invoicesTotal);
            dashboardDTO.setAgentInfos(agentTargetResponseDTOs);
        }
        return dashboardDTO;
    }
}
