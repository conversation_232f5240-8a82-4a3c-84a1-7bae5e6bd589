package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.SoldQualifications;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface SoldQualificationsRepository extends JpaRepository<SoldQualifications, Long> {
    Optional<SoldQualifications> findByApplicationApplicationIdAndQualificationQualificationId(String applicationId, String qualificationId);
}
