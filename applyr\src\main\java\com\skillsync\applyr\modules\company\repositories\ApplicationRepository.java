package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.Application;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ApplicationRepository extends JpaRepository<Application, Long> {
    List<Application> findAllByAgentUsername(String username);

    Optional<Application> findByApplicationId(String applicationId);

    List<Application> findAllByLeadPhone(String leadPhone);

    List<Application> findAllByAgentUsernameAndCreatedDateAfter(String agentUsername, LocalDateTime createdDateAfter);

    int countApplicationsByAgentUsernameAndCreatedDateAfterAndCreatedDateBefore(String agentUsername, LocalDateTime createdDateAfter, LocalDateTime createdDateBefore);

    int countApplicationsByCreatedDateAfter(LocalDateTime createdDateAfter);

    List<Application> findAllByCreatedDateAfter(LocalDateTime createdDateAfter);

    List<Application> findAllByAgentUsernameAndCreatedDateAfterAndCreatedDateBefore(String agentUsername, LocalDateTime createdDateAfter, LocalDateTime createdDateBefore);

    List<Application> findAllByAgentUsernameAndLastModifiedDateAfterAndLastModifiedDateBefore(String agentUsername, LocalDateTime localDateTime, LocalDateTime localDateTime1);

    List<Application> findDistinctByAgentUsernameAndInstallmentsCreatedDateBetween(
            String agentUsername,
            LocalDateTime startDate,
            LocalDateTime endDate);


    Optional<Application> findByInvoiceRefNumber(String reference);
}
