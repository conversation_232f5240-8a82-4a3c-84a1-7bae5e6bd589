import React, { useRef, useEffect } from "react";
import { FaTimes } from "react-icons/fa";

const BulkLeadUploadModal = ({
  isOpen,
  onClose,
  uploadFile,
  selectedSalesReps,
  salesRepSearch,
  isSalesRepDropdownOpen,
  setIsSalesRepDropdownOpen,
  employees,
  onFileSelect,
  onFileDrop,
  onDragOver,
  onDragEnter,
  onSalesRepSearchChange,
  onToggleSalesRepSelection,
  onSubmit,
  isUploading = false
}) => {
  const salesRepDropdownRef = useRef(null);
  const fileInputRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (salesRepDropdownRef.current && !salesRepDropdownRef.current.contains(event.target)) {
        setIsSalesRepDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [setIsSalesRepDropdownOpen]);

  if (!isOpen) return null;

  const filteredSalesReps = employees.filter(rep =>
    rep.fullName.toLowerCase().includes(salesRepSearch.toLowerCase())
  );

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-30 z-40" onClick={onClose} />
      <div className="fixed inset-0 flex items-center justify-center z-50" onClick={(e) => e.stopPropagation()}>
        <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl mx-4 sm:mx-0">
          <div className="flex items-center justify-between p-4 border-b">
            <h3 className="text-lg font-semibold">Add Leads (Bulk)</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700 text-xl">
              <FaTimes />
            </button>
          </div>
          <div className="p-4">
            <div
              className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center cursor-pointer mb-4"
              onDrop={onFileDrop}
              onDragOver={onDragOver}
              onDragEnter={onDragEnter}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={onFileSelect}
                className="hidden"
              />
              <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              <div className="mt-4">
                <p className="text-sm text-gray-600">
                  {uploadFile ? uploadFile.name : "Click to upload or drag and drop"}
                </p>
                <p className="text-xs text-gray-500 mt-1">CSV or Excel files only</p>
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Assign to Sales Representatives (Optional)
              </label>
              <div className="relative" ref={salesRepDropdownRef}>
                <button
                  type="button"
                  onClick={() => setIsSalesRepDropdownOpen(!isSalesRepDropdownOpen)}
                  className="w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-left focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                >
                  <span className="block truncate">
                    {selectedSalesReps.length > 0
                      ? `${selectedSalesReps.length} sales rep(s) selected`
                      : "Select sales representatives"}
                  </span>
                  <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3z" clipRule="evenodd" />
                    </svg>
                  </span>
                </button>

                {isSalesRepDropdownOpen && (
                  <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
                    <div className="sticky top-0 z-10 bg-white px-3 py-2 border-b">
                      <input
                        type="text"
                        className="w-full border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                        placeholder="Search sales reps..."
                        value={salesRepSearch}
                        onChange={onSalesRepSearchChange}
                      />
                    </div>
                    {filteredSalesReps.map((rep) => (
                      <div
                        key={rep.username}
                        className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50"
                        onClick={() => onToggleSalesRepSelection(rep.username)}
                      >
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={selectedSalesReps.includes(rep.username)}
                            onChange={() => onToggleSalesRepSelection(rep.username)}
                            className="h-4 w-4 text-[#6E39CB] focus:ring-[#6E39CB] border-gray-300 rounded mr-3"
                          />
                          <span className="font-normal block truncate">{rep.fullName}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {selectedSalesReps.length > 0 && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Selected Sales Representatives:</label>
                <div className="flex flex-wrap gap-2">
                  {selectedSalesReps.map((username) => {
                    const rep = employees.find(e => e.username === username);
                    return (
                      <span
                        key={username}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#6E39CB] text-white"
                      >
                        {rep?.fullName}
                        <button
                          type="button"
                          onClick={() => onToggleSalesRepSelection(username)}
                          className="flex-shrink-0 ml-1.5 h-4 w-4 rounded-full inline-flex items-center justify-center text-white hover:bg-[#5E2CB8] focus:outline-none"
                        >
                          <span className="sr-only">Remove {rep?.fullName}</span>
                          <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                            <path strokeLinecap="round" strokeWidth="1.5" d="m1 1 6 6m0-6-6 6" />
                          </svg>
                        </button>
                      </span>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
          <div className="flex items-center justify-end p-4 border-t">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md mr-2 hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              onClick={onSubmit}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              disabled={isUploading}
            >
              {isUploading ? "Uploading..." : "Upload Leads"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default BulkLeadUploadModal;
