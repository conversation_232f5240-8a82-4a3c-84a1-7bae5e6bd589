import React from "react";

const ExportLeadsHeader = ({
  selectedCount,
  totalCount,
  filteredCount,
  onExport,
  isExporting,
  onClearSelections
}) => {
  const getExportButtonText = () => {
    if (selectedCount > 0) return `Export ${selectedCount} Selected`;
    return `Export All ${totalCount}`;
  };

  return (
    <div className="mb-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="mb-4 sm:mb-0">
          <h1 className="text-3xl font-semibold text-gray-900">Export Leads</h1>
          <div className="mt-2 space-y-1">
            <p className="text-gray-600">
              {selectedCount > 0
                ? `${selectedCount} of ${totalCount} leads selected for export`
                : `${totalCount} leads available for export`}
            </p>
            {selectedCount > 0 && (
              <p className="text-sm text-[#6E39CB] font-medium">
                ✓ {selectedCount} leads selected across all pages
              </p>
            )}
            {filteredCount !== undefined && filteredCount !== totalCount && (
              <p className="text-sm text-blue-600">
                Showing {filteredCount} filtered results from {totalCount} total leads
              </p>
            )}
          </div>
        </div>

        <div className="flex gap-3">
          {selectedCount > 0 && onClearSelections && (
            <button
              onClick={onClearSelections}
              className="inline-flex items-center px-4 py-3 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-all duration-200"
            >
              Clear Selections
            </button>
          )}
          <button
            onClick={onExport}
            disabled={totalCount === 0}
            className={`
              inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg transition-all duration-200
              ${totalCount === 0
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-[#6E39CB] text-white hover:bg-[#5A2FA3] shadow-sm hover:shadow-md'
              }
            `}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            {getExportButtonText()}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportLeadsHeader;
