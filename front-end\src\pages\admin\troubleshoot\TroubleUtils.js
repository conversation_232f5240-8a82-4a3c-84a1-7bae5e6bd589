export const convertStatus = (status) => {
    switch (status) {
      case "RESOLVED":
        return "resolved";
      case "PENDING":
        return "pending";
      case "IN_PROGRESS":
        return "inProgress";
      case "NO_SOLUTION":
        return "noSolution";
      default:
        return status.toLowerCase();
    }
  };
  
  export const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.toLocaleString("default", { month: "long" });
    const year = date.getFullYear();
  
    // Determine ordinal suffix
    let ordinal = "th";
    if (day % 10 === 1 && day !== 11) ordinal = "st";
    else if (day % 10 === 2 && day !== 12) ordinal = "nd";
    else if (day % 10 === 3 && day !== 13) ordinal = "rd";
  
    const timeString = date.toLocaleString("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });
  
    return `${day}${ordinal} ${month} ${year}, ${timeString}`;
  };
  