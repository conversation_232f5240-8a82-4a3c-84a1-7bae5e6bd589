package com.skillsync.applyr.core.models.entities;

import com.skillsync.applyr.modules.company.models.WeeklyTargetDTO;
import jakarta.persistence.*;
import lombok.*;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WeeklyTarget {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private double kpi1;
    private double kpi2;
    private String agentUsername;

    @ManyToOne
    @JoinColumn(name = "weekly_targets_combined_id")
    private WeeklyTargetCombined weeklyTargetsCombined;

    public WeeklyTarget(WeeklyTargetDTO dto) {
        this.kpi1 = dto.getKpi1();
        this.kpi2 = dto.getKpi2();
        this.agentUsername = dto.getAgentUsername();
    }
}
