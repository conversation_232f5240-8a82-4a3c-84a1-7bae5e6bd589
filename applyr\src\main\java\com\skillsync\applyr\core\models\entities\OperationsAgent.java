package com.skillsync.applyr.core.models.entities;


import com.skillsync.applyr.modules.company.models.AdminRegisterDTO;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "operations_agent")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class OperationsAgent {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private String fullName;

    @Column(nullable = false)
    private String gender;

    @Column(nullable = false, unique = true)
    private String emailAddress;

    @Column()
    private String phoneNumber;

    @Column()
    private String address;

    @Column()
    private boolean active;

    @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(referencedColumnName = "id", nullable = false)
    private User user;

    public OperationsAgent(AdminRegisterDTO registerInfo) {
        this.fullName = registerInfo.getFullName();
        this.gender = registerInfo.getGender();
        this.emailAddress = registerInfo.getEmail();
        this.phoneNumber = registerInfo.getPhone();
        this.address = registerInfo.getAddress();
        this.active = true;
    }



}
