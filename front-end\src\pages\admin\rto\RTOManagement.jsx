import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faSearch,
  faPlus,
  faSync,
  faBuilding,
  faInfoCircle,
  faTable,
  faTh,
  faUserFriends
} from "@fortawesome/free-solid-svg-icons";
import { useNavigate } from "react-router-dom";
import { useGetAllRTOsQuery, useDeleteRTOMutation } from "../../../services/CompanyAPIService";
import RTOCard from "./components/RTOCard";
import AddRTOModal from "./components/AddRTOModal";
import LoadingSpinner from "../../../components/common/LoadingSpinner";
import EmptyState from "../../../components/common/EmptyState";
import ConfirmationModal from "../../../components/modal/ConfirmationModal";

const RTOManagement = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedRTO, setSelectedRTO] = useState(null);
  const [viewMode, setViewMode] = useState("card"); // "card" or "table"

  // Fetch RTOs
  const { data: rtos = [], isLoading, refetch } = useGetAllRTOsQuery();
  const [deleteRTO, { isLoading: isDeleting }] = useDeleteRTOMutation();

  // Filter RTOs based on search term
  const filteredRTOs = rtos.filter(
    (rto) =>
      rto.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rto.legalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rto.businessName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle view RTO details
  const handleViewRTO = (rtoCode) => {
    navigate(`/admin/rto/${rtoCode}`);
  };

  // Handle delete RTO
  const handleDeleteClick = (rto) => {
    setSelectedRTO(rto);
    setDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteRTO(selectedRTO.code).unwrap();
      setDeleteModalOpen(false);
      setSelectedRTO(null);
    } catch (error) {
      console.error("Failed to delete RTO:", error);
      alert("Failed to delete RTO. Please try again.");
    }
  };

  // Handle external link to training.gov.au
  const handleExternalLink = (rtoCode) => {
    window.open(`https://training.gov.au/Organisation/Details/${rtoCode}`, "_blank");
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="w-full py-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">RTO Management</h1>
            <p className="mt-1 text-sm text-gray-500">
              Connect and manage Registered Training Organizations
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center space-x-3">
            <button
              onClick={refetch}
              className="flex items-center text-gray-700 bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FontAwesomeIcon icon={faSync} className="h-5 w-5 mr-2" />
              Refresh
            </button>
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
            >
              <FontAwesomeIcon icon={faPlus} className="h-5 w-5 mr-2" />
              Add RTO
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6">
        <div className="relative w-full sm:w-96">
          <input
            type="text"
            placeholder="Search by ID or Name"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
            value={searchTerm}
            onChange={handleSearchChange}
          />
          <FontAwesomeIcon
            icon={faSearch}
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          />
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode("card")}
            className={`p-2 rounded-lg ${viewMode === "card" ? "bg-[#F4F5F9] text-[#6E39CB]" : "bg-white border border-gray-300 text-gray-600"}`}
            title="Card View"
          >
            <FontAwesomeIcon icon={faTh} />
          </button>
          <button
            onClick={() => setViewMode("table")}
            className={`p-2 rounded-lg ${viewMode === "table" ? "bg-[#F4F5F9] text-[#6E39CB]" : "bg-white border border-gray-300 text-gray-600"}`}
            title="Table View"
          >
            <FontAwesomeIcon icon={faTable} />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div>
        {isLoading ? (
          <LoadingSpinner />
        ) : filteredRTOs.length === 0 ? (
          <EmptyState
            message={
              searchTerm
                ? "No RTOs found matching your search criteria."
                : "No RTOs have been added yet. Add your first RTO to get started."
            }
            icon={faBuilding}
          />
        ) : viewMode === "card" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredRTOs.map((rto) => (
              <RTOCard
                key={rto.code}
                rto={rto}
                onView={() => handleViewRTO(rto.code)}
                onDelete={() => handleDeleteClick(rto)}
                onExternalLink={() => handleExternalLink(rto.code)}
              />
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm overflow-x-auto">
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900">RTO List</h2>
            </div>
            <table className="min-w-full">
              <thead>
                <tr className="bg-[#F4F5F9]">
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contacts
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredRTOs.map((rto, index) => (
                  <tr key={rto.code} className={index % 2 === 0 ? "bg-white" : "bg-[#F4F5F9]"}>
                    <td className="px-6 py-3 whitespace-nowrap">
                      <span className="text-sm font-medium text-[#6E39CB]">
                        {rto.code}
                      </span>
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{rto.legalName}</div>
                      {rto.businessName && (
                        <div className="text-sm text-gray-500">{rto.businessName}</div>
                      )}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-500">
                      {rto.rtoType || "RTO"}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faUserFriends} className="text-gray-400 mr-2" />
                        <span>{rto.contacts?.length || 0}</span>
                      </div>
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleViewRTO(rto.code)}
                        className="text-[#6E39CB] hover:text-[#5E2CB8]"
                        title="View Details"
                      >
                        <FontAwesomeIcon icon={faInfoCircle} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Main Content End */}

      {/* Add RTO Modal */}
      <AddRTOModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={() => {
          setIsAddModalOpen(false);
          refetch();
        }}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Delete RTO"
        message={`Are you sure you want to delete ${selectedRTO?.legalName || selectedRTO?.code}? This action cannot be undone.`}
        confirmText="Delete"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
        isLoading={isDeleting}
      />
    </div>
  );
};

export default RTOManagement;
