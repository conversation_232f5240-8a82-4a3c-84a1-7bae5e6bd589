import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faCheck, faTimes, faCalendarAlt } from "@fortawesome/free-solid-svg-icons";
import {
  useUpdateRTOInfoMutation,
  useUpdateRTOPaymentDateMutation,
  useUpdateLodgedToRTOMutation
} from "../../../../services/CompanyAPIService";
import { useGetAllRTOsQuery } from "../../../../services/CompanyAPIService";

const RTOInfoSection = ({
  rtoData,
  rtoCharge,
  rtoPaymentStatus,
  lodgedToRTO,
  lodgedDate,
  rtoPaymentDate,
  formatDate,
  applicationId,
  qualificationId,
  showToast
}) => {
  // Get all RTOs for dropdown
  const { data: allRTOs = [] } = useGetAllRTOsQuery();

  // State for editing RTO info
  const [isEditingRTOInfo, setIsEditingRTOInfo] = useState(false);
  const [selectedRTOCode, setSelectedRTOCode] = useState(rtoData?.code || "");
  const [editedRTOCharge, setEditedRTOCharge] = useState(rtoCharge || 0);
  const [editedPaymentStatus, setEditedPaymentStatus] = useState(rtoPaymentStatus || "UNPAID");

  // State for editing payment date
  const [isEditingPaymentDate, setIsEditingPaymentDate] = useState(false);
  const [editedPaymentDate, setEditedPaymentDate] = useState(
    rtoPaymentDate ? new Date(rtoPaymentDate).toISOString().split('T')[0] : ""
  );

  // State for editing lodged status
  const [isEditingLodgedStatus, setIsEditingLodgedStatus] = useState(false);
  const [editedLodgedStatus, setEditedLodgedStatus] = useState(lodgedToRTO || false);
  const [editedLodgedDate, setEditedLodgedDate] = useState(
    lodgedDate ? new Date(lodgedDate).toISOString().split('T')[0] : ""
  );

  // API mutations
  const [updateRTOInfo, { isLoading: isUpdatingRTOInfo }] = useUpdateRTOInfoMutation();
  const [updateRTOPaymentDate, { isLoading: isUpdatingPaymentDate }] = useUpdateRTOPaymentDateMutation();
  const [updateLodgedToRTO, { isLoading: isUpdatingLodgedStatus }] = useUpdateLodgedToRTOMutation();

  // Handle RTO info update
  const handleRTOInfoUpdate = async () => {
    try {
      await updateRTOInfo({
        applicationId,
        qualificationId,
        rtoCode: selectedRTOCode,
        rtoCharge: parseFloat(editedRTOCharge),
        paymentStatus: editedPaymentStatus
      }).unwrap();

      showToast("RTO information updated successfully");
      setIsEditingRTOInfo(false);
    } catch (error) {
      showToast("Failed to update RTO information", "error");
      console.error("Error updating RTO info:", error);
    }
  };

  // Handle payment date update
  const handlePaymentDateUpdate = async () => {
    try {
      await updateRTOPaymentDate({
        applicationId,
        qualificationId,
        paymentDate: editedPaymentDate
      }).unwrap();

      showToast("Payment date updated successfully");
      setIsEditingPaymentDate(false);
    } catch (error) {
      showToast("Failed to update payment date", "error");
      console.error("Error updating payment date:", error);
    }
  };

  // Handle lodged status update
  const handleLodgedStatusUpdate = async () => {
    try {
      await updateLodgedToRTO({
        applicationId,
        qualificationId,
        lodgedToRTO: editedLodgedStatus,
        lodgedDate: editedLodgedDate
      }).unwrap();

      showToast("Lodged status updated successfully");
      setIsEditingLodgedStatus(false);
    } catch (error) {
      showToast("Failed to update lodged status", "error");
      console.error("Error updating lodged status:", error);
    }
  };

  // Cancel editing functions
  const cancelEditingRTOInfo = () => {
    setSelectedRTOCode(rtoData?.code || "");
    setEditedRTOCharge(rtoCharge || 0);
    setEditedPaymentStatus(rtoPaymentStatus || "UNPAID");
    setIsEditingRTOInfo(false);
  };

  const cancelEditingPaymentDate = () => {
    setEditedPaymentDate(rtoPaymentDate ? new Date(rtoPaymentDate).toISOString().split('T')[0] : "");
    setIsEditingPaymentDate(false);
  };

  const cancelEditingLodgedStatus = () => {
    setEditedLodgedStatus(lodgedToRTO || false);
    setEditedLodgedDate(lodgedDate ? new Date(lodgedDate).toISOString().split('T')[0] : "");
    setIsEditingLodgedStatus(false);
  };
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* RTO Details */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">RTO Information</h3>

        {rtoData ? (
          <div className="space-y-4">
            <div className="flex flex-col">
              <span className="text-sm font-medium text-gray-500 mb-1">RTO Code:</span>
              <span className="text-sm text-gray-900">{rtoData.code || "N/A"}</span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm font-medium text-gray-500 mb-1">Legal Name:</span>
              <span className="text-sm text-gray-900">{rtoData.legalName || "N/A"}</span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm font-medium text-gray-500 mb-1">Business Name:</span>
              <span className="text-sm text-gray-900">{rtoData.businessName || "N/A"}</span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm font-medium text-gray-500 mb-1">Address:</span>
              <span className="text-sm text-gray-900">{rtoData.address || "N/A"}</span>
            </div>

            <div className="flex flex-col">
              <span className="text-sm font-medium text-gray-500 mb-1">RTO Type:</span>
              <span className="text-sm text-gray-900">{rtoData.rtoType || "N/A"}</span>
            </div>
          </div>
        ) : (
          <p className="text-sm text-gray-500">No RTO information available</p>
        )}
      </div>

      {/* RTO Payment & Lodgement */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">RTO Payment & Lodgement</h3>
          {!isEditingRTOInfo ? (
            <button
              onClick={() => setIsEditingRTOInfo(true)}
              className="text-blue-600 hover:text-blue-800"
              title="Edit RTO information"
            >
              <FontAwesomeIcon icon={faEdit} />
            </button>
          ) : (
            <div className="flex space-x-2">
              <button
                onClick={handleRTOInfoUpdate}
                className="text-green-600 hover:text-green-800"
                title="Save changes"
                disabled={isUpdatingRTOInfo}
              >
                <FontAwesomeIcon icon={faCheck} />
              </button>
              <button
                onClick={cancelEditingRTOInfo}
                className="text-red-600 hover:text-red-800"
                title="Cancel"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
          )}
        </div>

        <div className="space-y-4">
          {/* RTO Selection */}
          <div className="flex flex-col">
            <span className="text-sm font-medium text-gray-500 mb-1">RTO:</span>
            {!isEditingRTOInfo ? (
              <span className="text-sm text-gray-900">{rtoData?.legalName || "Not selected"}</span>
            ) : (
              <select
                value={selectedRTOCode}
                onChange={(e) => setSelectedRTOCode(e.target.value)}
                className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select an RTO</option>
                {allRTOs.map((rto) => (
                  <option key={rto.code} value={rto.code}>
                    {rto.legalName} ({rto.code})
                  </option>
                ))}
              </select>
            )}
          </div>

          {/* RTO Charge */}
          <div className="flex flex-col">
            <span className="text-sm font-medium text-gray-500 mb-1">RTO Charge:</span>
            {!isEditingRTOInfo ? (
              <span className="text-sm text-gray-900">${rtoCharge || "0.00"}</span>
            ) : (
              <input
                type="number"
                value={editedRTOCharge}
                onChange={(e) => setEditedRTOCharge(e.target.value)}
                className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                step="0.01"
                min="0"
              />
            )}
          </div>

          {/* Payment Status */}
          <div className="flex flex-col">
            <span className="text-sm font-medium text-gray-500 mb-1">Payment Status:</span>
            {!isEditingRTOInfo ? (
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                rtoPaymentStatus === "PAID"
                  ? "bg-green-100 text-green-800"
                  : "bg-red-100 text-red-800"
              }`}>
                {rtoPaymentStatus === "PAID" ? "Paid" : "Unpaid"}
              </span>
            ) : (
              <select
                value={editedPaymentStatus}
                onChange={(e) => setEditedPaymentStatus(e.target.value)}
                className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="UNPAID">Unpaid</option>
                <option value="PAID">Paid</option>
              </select>
            )}
          </div>

          {/* Payment Date */}
          <div className="flex flex-col">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-500 mb-1">Payment Date:</span>
              {!isEditingPaymentDate && !isEditingRTOInfo ? (
                <button
                  onClick={() => setIsEditingPaymentDate(true)}
                  className="text-blue-600 hover:text-blue-800 text-xs"
                  title="Edit payment date"
                >
                  <FontAwesomeIcon icon={faCalendarAlt} />
                </button>
              ) : isEditingPaymentDate && !isEditingRTOInfo ? (
                <div className="flex space-x-2">
                  <button
                    onClick={handlePaymentDateUpdate}
                    className="text-green-600 hover:text-green-800 text-xs"
                    title="Save changes"
                    disabled={isUpdatingPaymentDate}
                  >
                    <FontAwesomeIcon icon={faCheck} />
                  </button>
                  <button
                    onClick={cancelEditingPaymentDate}
                    className="text-red-600 hover:text-red-800 text-xs"
                    title="Cancel"
                  >
                    <FontAwesomeIcon icon={faTimes} />
                  </button>
                </div>
              ) : null}
            </div>
            {!isEditingPaymentDate ? (
              <span className="text-sm text-gray-900">{formatDate(rtoPaymentDate)}</span>
            ) : (
              <input
                type="date"
                value={editedPaymentDate}
                onChange={(e) => setEditedPaymentDate(e.target.value)}
                className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            )}
          </div>

          {/* Lodged to RTO */}
          <div className="flex flex-col">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-500 mb-1">Lodged to RTO:</span>
              {!isEditingLodgedStatus && !isEditingRTOInfo ? (
                <button
                  onClick={() => setIsEditingLodgedStatus(true)}
                  className="text-blue-600 hover:text-blue-800 text-xs"
                  title="Edit lodged status"
                >
                  <FontAwesomeIcon icon={faEdit} />
                </button>
              ) : isEditingLodgedStatus && !isEditingRTOInfo ? (
                <div className="flex space-x-2">
                  <button
                    onClick={handleLodgedStatusUpdate}
                    className="text-green-600 hover:text-green-800 text-xs"
                    title="Save changes"
                    disabled={isUpdatingLodgedStatus}
                  >
                    <FontAwesomeIcon icon={faCheck} />
                  </button>
                  <button
                    onClick={cancelEditingLodgedStatus}
                    className="text-red-600 hover:text-red-800 text-xs"
                    title="Cancel"
                  >
                    <FontAwesomeIcon icon={faTimes} />
                  </button>
                </div>
              ) : null}
            </div>
            {!isEditingLodgedStatus ? (
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                lodgedToRTO
                  ? "bg-green-100 text-green-800"
                  : "bg-red-100 text-red-800"
              }`}>
                {lodgedToRTO ? "Yes" : "No"}
              </span>
            ) : (
              <select
                value={editedLodgedStatus.toString()}
                onChange={(e) => setEditedLodgedStatus(e.target.value === "true")}
                className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="false">No</option>
                <option value="true">Yes</option>
              </select>
            )}
          </div>

          {/* Lodged Date */}
          <div className="flex flex-col">
            <span className="text-sm font-medium text-gray-500 mb-1">Lodged Date:</span>
            {!isEditingLodgedStatus ? (
              <span className="text-sm text-gray-900">{formatDate(lodgedDate)}</span>
            ) : (
              <input
                type="date"
                value={editedLodgedDate}
                onChange={(e) => setEditedLodgedDate(e.target.value)}
                className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            )}
          </div>
        </div>
      </div>

      {/* RTO Contacts */}
      {rtoData && rtoData.contacts && rtoData.contacts.length > 0 && (
        <div className="lg:col-span-2 bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">RTO Contacts</h3>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Phone
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {rtoData.contacts.map((contact, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {contact.firstName} {contact.lastName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {contact.role || contact.jobTitle || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {contact.contactType || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {contact.phone || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {contact.email || "N/A"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default RTOInfoSection;
