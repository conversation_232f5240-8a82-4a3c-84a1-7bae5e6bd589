import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom"; // Import useLocation
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faSchool,
  faBuildingColumns,
  faUserGraduate,
  faBell,
  faGear,
} from "@fortawesome/free-solid-svg-icons";
import logo from "../../assets/Logo.png";
import dp from "../../assets/images.jfif";
import routes from "../../routes";
import { jwtDecode } from "jwt-decode";
import { getToken, removeToken } from "../../services/LocalStorageService";
import { useGetLoggedUserQuery } from "../../services/profileInfoApi";
import { useGetProfileQuery } from "../../services/CompanyAPIService";

const Navbar = () => {
  const [open, setOpen] = useState(false);
  const [showMegaMenu, setShowMegaMenu] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [profileLink, setProfileLink] = useState("");
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [expandedNotification, setExpandedNotification] = useState(null);
  const [fullName, setFullName] = useState("");
  const { data: adminProfileData, isLoading, error } = useGetProfileQuery();

  const notifications = [
    {
      title: "New Message Received",
      description:
        "You have received a new message from the Admin. Check your inbox for more details.",
      createdAt: new Date("2025-01-10T10:15:00"),
      seen: true,
    },
    {
      title: "Profile Approved",
      description:
        "Your profile has been reviewed and approved successfully. You can now access additional features.",
      createdAt: new Date("2025-01-09T14:30:00"),
      seen: true,
    },
    {
      title: "Task Reminder",
      description:
        "Reminder: Please complete your pending task by the end of the week.",
      createdAt: new Date("2025-01-08T08:00:00"),
      seen: false,
    },
    {
      title: "System Update Scheduled",
      description:
        "System maintenance is scheduled for January 15th, 2025, from 2:00 AM to 4:00 AM. Please save your work.",
      createdAt: new Date("2025-01-07T16:45:00"),
      seen: true,
    },
  ];

  const navigate = useNavigate();
  const location = useLocation();
  const trigger = useRef(null);
  const dropdown = useRef(null);
  const token = getToken();
  const isAgentIdPage = location.pathname === "/agenid";

  const {
    data: userDataResponse,
    isLoading: userLoading,
    isError: userError,
  } = useGetLoggedUserQuery(token);

  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  useEffect(() => {
    const token = getToken();
    if (token) {
      const decodedToken = jwtDecode(token);
      const roles = decodedToken.roles || [];

      if (roles.includes("ROLE_ADMIN")) {
        setProfileLink("/agent/profile");
        if (adminProfileData && adminProfileData.fullName) {
          setFullName(adminProfileData.fullName);
        }
      } else if (roles.includes("ROLE_SALES")) {
        setProfileLink("/admin/profile");
        if (userDataResponse && userDataResponse.fullName) {
          setFullName(userDataResponse.fullName);
        }
      }

      setIsLoggedIn(true);
    } else {
      setIsLoggedIn(false);
    }
  }, [adminProfileData, userDataResponse]);

  const toggleNotifications = () => {
    const allSeen = notifications.every((notification) => notification.seen);
    if (allSeen) {
      navigate("/notifications");
    } else {
      setNotificationsOpen(!notificationsOpen);
    }
  };

  const handleExpandToggle = (index) => {
    setExpandedNotification(expandedNotification === index ? null : index);
  };
  const handleLogout = () => {
    removeToken("token");
    setIsLoggedIn(false);
    navigate("/");
  };

  return (
    <header className="sticky top-0 z-50">
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className={`${isLoggedIn ? "" : "container"} mx-auto`}>
          <div className="flex items-center justify-between h-16 px-4 lg:px-6">
            <div className="flex items-center lg:hidden">
              <a href="#" className="flex items-center">
                <img
                  src={logo}
                  alt="Logo"
                  className="h-8"
                />
              </a>
            </div>
            <div className="flex w-full items-center justify-between px-4">
              <div>
                <button
                  onClick={() => setOpen(!open)}
                  id="navbarToggler"
                  className={`${
                    open && "navbarTogglerActive"
                  } absolute right-4 top-1/2 block -translate-y-1/2 rounded-lg px-3 py-[6px] ring-primary focus:ring-2 lg:hidden`}
                >
                  <span className="relative my-[6px] block h-[2px] w-[30px] bg-black "></span>
                  <span className="relative my-[6px] block h-[2px] w-[30px] bg-black "></span>
                  <span className="relative my-[6px] block h-[2px] w-[30px] bg-black "></span>
                </button>

                {!isLoggedIn && !isAgentIdPage && (
                  <nav
                    id="navbarCollapse"
                    className={`absolute right-4 top-full w-full max-w-[300px] rounded-lg bg-white px-6 py-5 shadow lg:static lg:block lg:w-full lg:max-w-full lg:shadow-none  ${
                      !open && "hidden"
                    }`}
                  ></nav>
                )}
              </div>
              <div className="hidden justify-end gap-3 pr-16 sm:flex lg:pr-0">
                {!isLoggedIn ? (
                  <>
                    <Link
                      to="/signin"
                      className="rounded-lg bg-[#6E39CB] px-5 py-2.5 text-base font-medium text-white hover:bg-[#5E2CB8] "
                    >
                      Login
                    </Link>
                  </>
                ) : (
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center mr-4">
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="Search anything here..."
                          className="w-64 pl-10 pr-4 py-2 rounded-full border border-gray-200 focus:outline-none focus:border-[#6E39CB]"
                        />
                        <svg
                          className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                        <button
                          onClick={toggleNotifications}
                          className="p-2 rounded-full text-gray-500 hover:bg-[#F4F5F9] focus:outline-none relative"
                        >
                          <FontAwesomeIcon
                            icon={faBell}
                            className="text-lg"
                          />
                          {notifications.some(
                            (notification) => !notification.seen
                          ) && (
                            <span className="absolute top-1 right-1 inline-flex items-center justify-center h-3 w-3 text-xs font-bold leading-none text-white bg-[#6E39CB] rounded-full"></span>
                          )}
                        </button>
                        {notificationsOpen && (
                          <div className="absolute right-0 top-16 mt-2 w-80 bg-white shadow-lg rounded-lg py-2 border border-gray-100">
                            <div className="px-4 py-2 border-b border-gray-100">
                              <h3 className="font-semibold text-gray-900">Notifications</h3>
                            </div>
                            <div className="max-h-96 overflow-y-auto">
                              {notifications.map((notification, index) => (
                                <div
                                  key={index}
                                  className="p-3 border-b border-gray-100 hover:bg-[#F4F5F9]"
                                >
                                  <div className="flex justify-between items-center">
                                    <p className="font-medium text-sm text-gray-900">
                                      {notification.title}
                                    </p>
                                    <button
                                      onClick={() => handleExpandToggle(index)}
                                      className="text-gray-400 hover:text-[#6E39CB]"
                                    >
                                      {expandedNotification === index
                                        ? "▲"
                                        : "▼"}
                                    </button>
                                  </div>
                                  {expandedNotification === index && (
                                    <div className="mt-1">
                                      <p className="text-sm text-gray-600">
                                        {notification.description}
                                      </p>
                                      <p className="text-xs text-gray-400 mt-1">
                                        {notification.createdAt.toLocaleString()}
                                      </p>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                            <div className="px-4 py-2 border-t border-gray-100">
                              <button
                                onClick={() => navigate("/notifications")}
                                className="w-full text-sm text-center text-[#6E39CB] hover:text-[#5E2CB8] font-medium"
                              >
                                View all notifications
                              </button>
                            </div>
                          </div>
                        )}

                        {/* Profile Dropdown */}
                        <div className="relative">
                          <button
                            ref={trigger}
                            onClick={() => setDropdownOpen(!dropdownOpen)}
                            className="flex items-center gap-2 focus:outline-none p-1 rounded-lg hover:bg-[#F4F5F9]"
                          >
                            <div className="relative h-9 w-9 rounded-full overflow-hidden border-2 border-[#F4F5F9]">
                              <img
                                src={dp}
                                alt="avatar"
                                className="h-full w-full object-cover"
                              />
                            </div>
                            <div className="hidden md:block">
                              <span className="text-sm font-medium text-gray-700">
                                {fullName}
                              </span>
                            </div>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className={`text-gray-500 ${dropdownOpen ? "rotate-180" : ""}`}
                            >
                              <path d="m6 9 6 6 6-6"/>
                            </svg>
                          </button>

                          <div
                            ref={dropdown}
                            className={`absolute right-0 mt-2 w-56 rounded-lg border border-gray-100 bg-white shadow-lg overflow-hidden ${
                              dropdownOpen ? "block" : "hidden"
                            }`}
                          >
                            <div className="py-2">
                              <Link
                                to={profileLink}
                                className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-[#F4F5F9]"
                              >
                                <FontAwesomeIcon icon={faUserGraduate} className="text-[#6E39CB]" />
                                <span>My Profile</span>
                              </Link>

                              <Link
                                to="/agent/settings"
                                className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-[#F4F5F9]"
                              >
                                <FontAwesomeIcon icon={faGear} className="text-[#6E39CB]" />
                                <span>Settings</span>
                              </Link>

                              <div className="border-t border-gray-100 my-1"></div>

                              <button
                                onClick={handleLogout}
                                className="flex w-full items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-[#F4F5F9]"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-500">
                                  <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                                  <polyline points="16 17 21 12 16 7"/>
                                  <line x1="21" y1="12" x2="9" y2="12"/>
                                </svg>
                                <span>Logout</span>
                              </button>
                            </div>
                          </div>
                        </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
