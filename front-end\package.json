{"name": "skillsync", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@google/generative-ai": "^0.24.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@reduxjs/toolkit": "^2.4.0", "@tanstack/react-table": "^8.21.2", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "figma-developer-mcp": "^0.1.15", "flowbite": "^2.5.2", "flowbite-react": "^0.10.2", "framer-motion": "^11.11.9", "html2canvas": "^1.4.1", "html2pdf": "^0.0.11", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "localforage": "^1.10.0", "lucide-react": "^0.487.0", "match-sorter": "^6.3.4", "papaparse": "^5.5.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.0.0", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-modal": "^3.16.3", "react-qr-code": "^2.0.15", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-resizable": "^3.0.5", "react-router-dom": "^6.26.2", "react-select": "^5.9.0", "react-table": "^7.8.0", "react-to-print": "^3.0.6", "react-toastify": "^11.0.3", "react-webcam": "^7.2.0", "recharts": "^2.15.1", "sort-by": "^1.2.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@shadcn/ui": "^0.0.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "electron": "^34.0.2", "electron-builder": "^25.1.8", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "vite": "^5.4.1"}}