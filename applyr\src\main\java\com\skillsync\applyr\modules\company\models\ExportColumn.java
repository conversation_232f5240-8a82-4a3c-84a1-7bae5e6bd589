package com.skillsync.applyr.modules.company.models;

public enum ExportColumn {
    COMPANY_NAME("companyName", "Company Name"),
    LEAD_NAME("leadName", "Lead Name"),
    PHONE("phone", "Phone"),
    EMAIL("email", "Email"),
    ADDRESS("address", "Address"),
    STATUS("status", "Status"),
    APPLICATION_COUNT("applicationCount", "Application Count"),
    CREATED_DATE("createdDate", "Created Date"),
    ASSIGNED_AGENTS("assignedAgents", "Assigned Agents");

    private final String fieldName;
    private final String displayName;

    ExportColumn(String fieldName, String displayName) {
        this.fieldName = fieldName;
        this.displayName = displayName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static ExportColumn fromFieldName(String fieldName) {
        for (ExportColumn column : values()) {
            if (column.fieldName.equals(fieldName)) {
                return column;
            }
        }
        return null;
    }
}
