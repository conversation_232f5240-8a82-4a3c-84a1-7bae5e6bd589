import React, { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCalendarAlt,
  faChartLine,
  faChartPie,
  faSearch,
  faEye,
  faEdit,
  faPlus,
  faSyncAlt,
  faCheckCircle,
  faTimesCircle,
  faArrowUp,
  faArrowDown,
  faUsers,
  faMoneyBillWave,
  faFileInvoiceDollar,
  faListAlt,
} from "@fortawesome/free-solid-svg-icons";

// Charting imports (if needed in this file)
import { Doughnut, Bar, Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  RadialLinearScale,
  Filler,
} from "chart.js";

ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  RadialLinearScale,
  Filler
);

// API hooks
import {
  useGetAllTargetsQuery,
  useCreateTargetMutation,
  useUpdateTargetMutation,
} from "../../../services/CompanyAPIService";
import { useGetAllAgentsQuery } from "../../../services/AdminAPIService";

// Import the separated modal components
import CreateTargetModal from "./CreateTargetModal";
import EditTargetModal from "./EditTargetModal";
import DetailsModal from "./DetailsModal";

const WeeklyTargetPage = () => {
  // Fetch weekly targets and agents
  const {
    data: weeklyTargets,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetAllTargetsQuery();
  const {
    data: agents,
    isLoading: agentsLoading,
    isError: agentsError,
  } = useGetAllAgentsQuery();

  // Mutations for create and update
  const [createTarget] = useCreateTargetMutation();
  const [updateTarget] = useUpdateTargetMutation();

  // Local state for modals and details
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [detailsTarget, setDetailsTarget] = useState(null);
  const [editTarget, setEditTarget] = useState(null);

  // Handlers for modal toggling
  const handleOpenCreateModal = () => setShowCreateModal(true);
  const handleCloseCreateModal = () => setShowCreateModal(false);

  const handleOpenDetails = (target) => {
    setDetailsTarget(target);
    setShowDetailsModal(true);
  };
  const handleCloseDetails = () => {
    setDetailsTarget(null);
    setShowDetailsModal(false);
  };

  const handleOpenEdit = (target) => {
    setEditTarget(target);
    setShowEditModal(true);
  };
  const handleCloseEdit = () => {
    setEditTarget(null);
    setShowEditModal(false);
  };

  // Callbacks for create and update mutations
  const handleCreateTarget = async (requestBody) => {
    try {
      await createTarget(requestBody).unwrap();
      setShowCreateModal(false);
      refetch();
    } catch (err) {
      console.error("Failed to create target", err);
    }
  };

  const handleUpdateTarget = async (updatedTarget) => {
    try {
      await updateTarget(updatedTarget).unwrap();
      setShowEditModal(false);
      refetch();
    } catch (err) {
      console.error("Failed to update target", err);
    }
  };

  // State for search/filter and sorting
  const [searchQuery, setSearchQuery] = useState("");
  const [dateFilter, setDateFilter] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: "startDate", direction: "desc" });

  // Request sort handler
  const requestSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  // Filter and sort targets
  const filteredTargets = weeklyTargets
    ? [...weeklyTargets].filter((target) => {
        // Search filter
        const matchesSearch = target.title.toLowerCase().includes(searchQuery.toLowerCase());

        // Date filter
        let matchesDate = true;
        if (dateFilter) {
          const targetStartDate = new Date(target.startDate);
          const targetEndDate = new Date(target.endDate);
          const filterDate = new Date(dateFilter);
          matchesDate = filterDate >= targetStartDate && filterDate <= targetEndDate;
        }
        return matchesSearch && matchesDate;
      })
    : [];

  const sortedTargets = [...filteredTargets].sort((a, b) => {
    if (sortConfig.key === "title") {
      return sortConfig.direction === "asc"
        ? a.title.localeCompare(b.title)
        : b.title.localeCompare(a.title);
    } else if (["startDate", "endDate"].includes(sortConfig.key)) {
      return sortConfig.direction === "asc"
        ? new Date(a[sortConfig.key]) - new Date(b[sortConfig.key])
        : new Date(b[sortConfig.key]) - new Date(a[sortConfig.key]);
    } else if (["kpi1Target", "kpi1Actual", "kpi2Target", "kpi2Actual"].includes(sortConfig.key)) {
      const aTotal =
        a.targets?.reduce((sum, t) => sum + (t[sortConfig.key] ?? 0), 0) || 0;
      const bTotal =
        b.targets?.reduce((sum, t) => sum + (t[sortConfig.key] ?? 0), 0) || 0;
      return sortConfig.direction === "asc" ? aTotal - bTotal : bTotal - aTotal;
    }
    return 0;
  });

  // Overall statistics for the summary cards
  const overallStats = weeklyTargets
    ? {
        totalTargets: weeklyTargets.length,
        activeTargets: weeklyTargets.filter((t) => new Date(t.endDate) >= new Date()).length,
        completedTargets: weeklyTargets.filter((t) => new Date(t.endDate) < new Date()).length,
        totalAgents: agents?.length || 0,
        totalKPI1Target: weeklyTargets.reduce((sum, wt) => {
          return sum + (wt.targets?.reduce((s, t) => s + (t.kpi1Target ?? 0), 0) || 0);
        }, 0),
        totalKPI1Actual: weeklyTargets.reduce((sum, wt) => {
          return sum + (wt.targets?.reduce((s, t) => s + (t.kpi1Actual ?? 0), 0) || 0);
        }, 0),
        totalKPI2Target: weeklyTargets.reduce((sum, wt) => {
          return sum + (wt.targets?.reduce((s, t) => s + (t.kpi2Target ?? 0), 0) || 0);
        }, 0),
        totalKPI2Actual: weeklyTargets.reduce((sum, wt) => {
          return sum + (wt.targets?.reduce((s, t) => s + (t.kpi2Actual ?? 0), 0) || 0);
        }, 0),
      }
    : null;

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#6E39CB]"></div>
          <span className="ml-4 text-lg text-gray-600">Loading Weekly Targets...</span>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error! </strong>
          <span className="block sm:inline">
            Error fetching Weekly Targets: {error?.data?.message || error?.message}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Weekly Targets</h1>
          <p className="text-sm text-gray-500 mt-1">
            Manage and track employee performance targets
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <button
            onClick={() => refetch()}
            className="flex items-center bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
          >
            <FontAwesomeIcon icon={faSyncAlt} className="mr-2" />
            Refresh
          </button>
          <button
            onClick={handleOpenCreateModal}
            className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors"
          >
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Create Target
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {overallStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          {/* Targets Card */}
          <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
            <div className="flex items-center mb-4">
              <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
                <FontAwesomeIcon icon={faListAlt} className="h-5 w-5 text-[#6E39CB]" />
              </div>
              <h3 className="text-sm font-medium text-gray-700">Target Periods</h3>
            </div>
            <div className="flex items-end justify-between">
              <p className="text-2xl font-bold text-gray-900">{overallStats.totalTargets}</p>
              <div className="flex flex-col items-end">
                <span className="flex items-center text-xs text-green-600">
                  <FontAwesomeIcon icon={faCheckCircle} className="mr-1" />
                  {overallStats.activeTargets} Active
                </span>
                <span className="flex items-center text-xs text-gray-500 mt-1">
                  <FontAwesomeIcon icon={faTimesCircle} className="mr-1" />
                  {overallStats.completedTargets} Completed
                </span>
              </div>
            </div>
          </div>
          {/* KPI1 Card */}
          <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
            <div className="flex items-center mb-4">
              <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
                <FontAwesomeIcon icon={faMoneyBillWave} className="h-5 w-5 text-[#6E39CB]" />
              </div>
              <h3 className="text-sm font-medium text-gray-700">KPI1 Progress</h3>
            </div>
            <div className="flex items-end justify-between">
              <p className="text-2xl font-bold text-gray-900">
                ${overallStats.totalKPI1Actual.toLocaleString()}
              </p>
              <div className="flex flex-col items-end">
                <span className="flex items-center text-xs text-gray-500">
                  Target: ${overallStats.totalKPI1Target.toLocaleString()}
                </span>
                <span
                  className={`flex items-center text-xs mt-1 ${
                    overallStats.totalKPI1Actual >= overallStats.totalKPI1Target
                      ? "text-green-600"
                      : "text-orange-500"
                  }`}
                >
                  {Math.round(
                    (overallStats.totalKPI1Actual / overallStats.totalKPI1Target) * 100
                  )}
                  % Complete
                </span>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-[#6E39CB] h-2 rounded-full"
                  style={{
                    width: `${Math.min(
                      100,
                      Math.round(
                        (overallStats.totalKPI1Actual / overallStats.totalKPI1Target) * 100
                      )
                    )}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>
          {/* KPI2 Card */}
          <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
            <div className="flex items-center mb-4">
              <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
                <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-5 w-5 text-[#6E39CB]" />
              </div>
              <h3 className="text-sm font-medium text-gray-700">KPI2 Progress</h3>
            </div>
            <div className="flex items-end justify-between">
              <p className="text-2xl font-bold text-gray-900">
                ${overallStats.totalKPI2Actual.toLocaleString()}
              </p>
              <div className="flex flex-col items-end">
                <span className="flex items-center text-xs text-gray-500">
                  Target: ${overallStats.totalKPI2Target.toLocaleString()}
                </span>
                <span
                  className={`flex items-center text-xs mt-1 ${
                    overallStats.totalKPI2Actual >= overallStats.totalKPI2Target
                      ? "text-green-600"
                      : "text-orange-500"
                  }`}
                >
                  {Math.round(
                    (overallStats.totalKPI2Actual / overallStats.totalKPI2Target) * 100
                  )}
                  % Complete
                </span>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-[#6E39CB] h-2 rounded-full"
                  style={{
                    width: `${Math.min(
                      100,
                      Math.round(
                        (overallStats.totalKPI2Actual / overallStats.totalKPI2Target) * 100
                      )
                    )}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>
          {/* Agents Card */}
          <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
            <div className="flex items-center mb-4">
              <div className="bg-[#F4F5F9] p-3 rounded-full mr-3">
                <FontAwesomeIcon icon={faUsers} className="h-5 w-5 text-[#6E39CB]" />
              </div>
              <h3 className="text-sm font-medium text-gray-700">Agents</h3>
            </div>
            <div className="flex items-end justify-between">
              <p className="text-2xl font-bold text-gray-900">{overallStats.totalAgents}</p>
              <p className="text-sm text-gray-500">with targets</p>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Controls */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Search and Date Filter */}
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 w-full md:w-auto">
            <div className="relative w-full sm:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search by title"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
            </div>
            <div className="w-full sm:w-auto">
              <DatePicker
                selected={dateFilter}
                onChange={(date) => setDateFilter(date)}
                dateFormat="MMMM d, yyyy"
                placeholderText="Filter by date"
                isClearable
                className="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] w-full"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              <span className="font-medium">{filteredTargets.length}</span> of{" "}
              {weeklyTargets?.length || 0} targets
            </div>
          </div>
        </div>
      </div>

      {/* Main Table */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th
                onClick={() => requestSort("title")}
                className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              >
                <div className="flex items-center">
                  <span>Title</span>
                  {sortConfig.key === "title" && (
                    <FontAwesomeIcon
                      icon={sortConfig.direction === "asc" ? faArrowUp : faArrowDown}
                      className="ml-1 h-3 w-3 text-[#6E39CB]"
                    />
                  )}
                </div>
              </th>
              <th
                onClick={() => requestSort("startDate")}
                className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              >
                <div className="flex items-center">
                  <span>Start Date</span>
                  {sortConfig.key === "startDate" && (
                    <FontAwesomeIcon
                      icon={sortConfig.direction === "asc" ? faArrowUp : faArrowDown}
                      className="ml-1 h-3 w-3 text-[#6E39CB]"
                    />
                  )}
                </div>
              </th>
              <th
                onClick={() => requestSort("endDate")}
                className="px-6 py-3 bg-[#F4F5F9] text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              >
                <div className="flex items-center">
                  <span>End Date</span>
                  {sortConfig.key === "endDate" && (
                    <FontAwesomeIcon
                      icon={sortConfig.direction === "asc" ? faArrowUp : faArrowDown}
                      className="ml-1 h-3 w-3 text-[#6E39CB]"
                    />
                  )}
                </div>
              </th>
              <th
                onClick={() => requestSort("kpi1Target")}
                className="px-6 py-3 bg-[#F4F5F9] text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              >
                <div className="flex justify-end items-center">
                  <span>KPI1 Target</span>
                  {sortConfig.key === "kpi1Target" && (
                    <FontAwesomeIcon
                      icon={sortConfig.direction === "asc" ? faArrowUp : faArrowDown}
                      className="ml-1 h-3 w-3 text-[#6E39CB]"
                    />
                  )}
                </div>
              </th>
              <th
                onClick={() => requestSort("kpi1Actual")}
                className="px-6 py-3 bg-[#F4F5F9] text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              >
                <div className="flex justify-end items-center">
                  <span>KPI1 Actual</span>
                  {sortConfig.key === "kpi1Actual" && (
                    <FontAwesomeIcon
                      icon={sortConfig.direction === "asc" ? faArrowUp : faArrowDown}
                      className="ml-1 h-3 w-3 text-[#6E39CB]"
                    />
                  )}
                </div>
              </th>
              <th
                onClick={() => requestSort("kpi2Target")}
                className="px-6 py-3 bg-[#F4F5F9] text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              >
                <div className="flex justify-end items-center">
                  <span>KPI2 Target</span>
                  {sortConfig.key === "kpi2Target" && (
                    <FontAwesomeIcon
                      icon={sortConfig.direction === "asc" ? faArrowUp : faArrowDown}
                      className="ml-1 h-3 w-3 text-[#6E39CB]"
                    />
                  )}
                </div>
              </th>
              <th
                onClick={() => requestSort("kpi2Actual")}
                className="px-6 py-3 bg-[#F4F5F9] text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              >
                <div className="flex justify-end items-center">
                  <span>KPI2 Actual</span>
                  {sortConfig.key === "kpi2Actual" && (
                    <FontAwesomeIcon
                      icon={sortConfig.direction === "asc" ? faArrowUp : faArrowDown}
                      className="ml-1 h-3 w-3 text-[#6E39CB]"
                    />
                  )}
                </div>
              </th>
              <th className="px-6 py-3 bg-[#F4F5F9] text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedTargets.length > 0 ? (
              sortedTargets.map((wt, idx) => {
                const totalKPI1Target = wt.targets?.reduce(
                  (sum, t) => sum + (t.kpi1Target ?? 0),
                  0
                );
                const totalKPI1Actual = wt.targets?.reduce(
                  (sum, t) => sum + (t.kpi1Actual ?? 0),
                  0
                );
                const totalKPI2Target = wt.targets?.reduce(
                  (sum, t) => sum + (t.kpi2Target ?? 0),
                  0
                );
                const totalKPI2Actual = wt.targets?.reduce(
                  (sum, t) => sum + (t.kpi2Actual ?? 0),
                  0
                );

                const start = wt.startDate ? new Date(wt.startDate).toLocaleDateString() : "";
                const end = wt.endDate ? new Date(wt.endDate).toLocaleDateString() : "";
                const kpi1Progress = totalKPI1Target > 0 ? Math.round((totalKPI1Actual / totalKPI1Target) * 100) : 0;
                const kpi2Progress = totalKPI2Target > 0 ? Math.round((totalKPI2Actual / totalKPI2Target) * 100) : 0;
                const isActive = new Date(wt.endDate) >= new Date();

                return (
                  <tr key={idx} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className={`h-2.5 w-2.5 rounded-full ${isActive ? "bg-green-500" : "bg-gray-300"} mr-2`}></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{wt.title}</p>
                          <p className="text-xs text-gray-500">
                            {wt.targets?.length || 0} agents
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{start}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{end}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      ${totalKPI1Target?.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div>
                        <div className="flex items-center justify-end">
                          <span className="text-sm font-medium text-gray-900">
                            ${totalKPI1Actual?.toLocaleString()}
                          </span>
                          <span
                            className={`ml-2 text-xs ${
                              kpi1Progress >= 100 ? "text-green-600" : "text-orange-500"
                            }`}
                          >
                            {kpi1Progress}%
                          </span>
                        </div>
                        <div className="w-24 bg-gray-200 rounded-full h-1.5 mt-1 ml-auto">
                          <div
                            className={`h-1.5 rounded-full ${
                              kpi1Progress >= 100 ? "bg-green-500" : "bg-[#6E39CB]"
                            }`}
                            style={{ width: `${Math.min(100, kpi1Progress)}%` }}
                          ></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      ${totalKPI2Target?.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div>
                        <div className="flex items-center justify-end">
                          <span className="text-sm font-medium text-gray-900">
                            ${totalKPI2Actual?.toLocaleString()}
                          </span>
                          <span
                            className={`ml-2 text-xs ${
                              kpi2Progress >= 100 ? "text-green-600" : "text-orange-500"
                            }`}
                          >
                            {kpi2Progress}%
                          </span>
                        </div>
                        <div className="w-24 bg-gray-200 rounded-full h-1.5 mt-1 ml-auto">
                          <div
                            className={`h-1.5 rounded-full ${
                              kpi2Progress >= 100 ? "bg-green-500" : "bg-[#6E39CB]"
                            }`}
                            style={{ width: `${Math.min(100, kpi2Progress)}%` }}
                          ></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                      <div className="flex justify-center space-x-2">
                        <button
                          onClick={() => handleOpenDetails(wt)}
                          className="flex items-center bg-[#6E39CB] text-white px-3 py-1.5 rounded-lg hover:bg-[#5E2CB8] transition-colors"
                          title="View Details"
                        >
                          <FontAwesomeIcon icon={faEye} className="mr-1" />
                          Details
                        </button>
                        <button
                          onClick={() => handleOpenEdit(wt)}
                          className="flex items-center bg-gray-100 text-gray-700 px-3 py-1.5 rounded-lg hover:bg-gray-200 transition-colors"
                          title="Edit Target"
                        >
                          <FontAwesomeIcon icon={faEdit} className="mr-1" />
                          Edit
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan="8" className="px-6 py-8 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <div className="bg-[#F4F5F9] p-4 rounded-full mb-4">
                      <FontAwesomeIcon icon={faListAlt} className="h-8 w-8 text-[#6E39CB]" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Targets Found</h3>
                    <p className="text-gray-500 mb-4 max-w-md text-center">
                      {searchQuery || dateFilter
                        ? "No targets match your search criteria. Try adjusting your filters."
                        : "No weekly targets found. Create one to get started."}
                    </p>
                    {(searchQuery || dateFilter) && (
                      <button
                        onClick={() => {
                          setSearchQuery("");
                          setDateFilter(null);
                        }}
                        className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        Clear Filters
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Modals */}
      {showCreateModal && (
        <CreateTargetModal
          agents={agents}
          agentsLoading={agentsLoading}
          agentsError={agentsError}
          onClose={handleCloseCreateModal}
          onCreate={handleCreateTarget}
        />
      )}

      {showEditModal && editTarget && (
        <EditTargetModal
          target={editTarget}
          agents={agents}
          agentsLoading={agentsLoading}
          agentsError={agentsError}
          onClose={handleCloseEdit}
          onUpdate={handleUpdateTarget}
        />
      )}

      {showDetailsModal && detailsTarget && (
        <DetailsModal targetData={detailsTarget} onClose={handleCloseDetails} />
      )}
    </div>
  );
};

export default WeeklyTargetPage;