import React from "react";
import DateEditor from "./DateEditor";
import {
  useUpdateDocumentReceivedDateMutation,
  useUpdateSoftCopyReceivedDateMutation,
  useUpdateSoftCopyReleasedDateMutation,
  useUpdateHardCopyReceivedDateMutation,
  useUpdateHardCopyMailedDateMutation
} from "../../../../../services/CompanyAPIService";

/**
 * DocumentDatesSection component for managing document processing dates
 * @param {object} fileStatus - File status data
 * @param {function} showToast - Function to show toast notifications
 * @param {function} refetch - Function to refetch data
 */
const DocumentDatesSection = ({ fileStatus, showToast, refetch }) => {
  // API mutations
  const [updateDocumentReceivedDate, { isLoading: isUpdatingDocumentDate }] = useUpdateDocumentReceivedDateMutation();
  const [updateSoftCopyReceivedDate, { isLoading: isUpdatingSoftCopyReceivedDate }] = useUpdateSoftCopyReceivedDateMutation();
  const [updateSoftCopyReleasedDate, { isLoading: isUpdatingSoftCopyReleasedDate }] = useUpdateSoftCopyReleasedDateMutation();
  const [updateHardCopyReceivedDate, { isLoading: isUpdatingHardCopyReceivedDate }] = useUpdateHardCopyReceivedDateMutation();
  const [updateHardCopyMailedDate, { isLoading: isUpdatingHardCopyMailedDate }] = useUpdateHardCopyMailedDateMutation();

  // Handle document received date update
  const handleDocumentReceivedDateUpdate = async (date) => {
    try {
      await updateDocumentReceivedDate({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        documentReceivedDate: date
      }).unwrap();
      
      showToast("Document received date updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update document received date", "error");
      console.error("Error updating document received date:", error);
    }
  };

  // Handle soft copy received date update
  const handleSoftCopyReceivedDateUpdate = async (date) => {
    try {
      await updateSoftCopyReceivedDate({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        softCopyReceivedDate: date
      }).unwrap();
      
      showToast("Soft copy received date updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update soft copy received date", "error");
      console.error("Error updating soft copy received date:", error);
    }
  };

  // Handle soft copy released date update
  const handleSoftCopyReleasedDateUpdate = async (date) => {
    try {
      await updateSoftCopyReleasedDate({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        softCopyReleasedDate: date
      }).unwrap();
      
      showToast("Soft copy released date updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update soft copy released date", "error");
      console.error("Error updating soft copy released date:", error);
    }
  };

  // Handle hard copy received date update
  const handleHardCopyReceivedDateUpdate = async (date) => {
    try {
      await updateHardCopyReceivedDate({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        hardCopyReceivedDate: date
      }).unwrap();
      
      showToast("Hard copy received date updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update hard copy received date", "error");
      console.error("Error updating hard copy received date:", error);
    }
  };

  // Handle hard copy mailed date update
  const handleHardCopyMailedDateUpdate = async (date) => {
    try {
      await updateHardCopyMailedDate({
        applicationId: fileStatus.application?.applicationId,
        qualificationId: fileStatus.qualificationCode,
        hardCopyMailedDate: date
      }).unwrap();
      
      showToast("Hard copy mailed date updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update hard copy mailed date", "error");
      console.error("Error updating hard copy mailed date:", error);
    }
  };

  return (
    <div className="mt-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Processing Timeline</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Document Received Date */}
        <DateEditor
          label="Document Received"
          date={fileStatus.documentReceivedDate}
          onUpdate={handleDocumentReceivedDateUpdate}
          icon="📄"
          isLoading={isUpdatingDocumentDate}
        />

        {/* Soft Copy Received Date */}
        <DateEditor
          label="Soft Copy Received"
          date={fileStatus.softCopyReceivedDate}
          onUpdate={handleSoftCopyReceivedDateUpdate}
          icon="📱"
          isLoading={isUpdatingSoftCopyReceivedDate}
        />

        {/* Soft Copy Released Date */}
        <DateEditor
          label="Soft Copy Released"
          date={fileStatus.softCopyReleasedDate}
          onUpdate={handleSoftCopyReleasedDateUpdate}
          icon="📤"
          isLoading={isUpdatingSoftCopyReleasedDate}
        />

        {/* Hard Copy Received Date */}
        <DateEditor
          label="Hard Copy Received"
          date={fileStatus.hardCopyReceivedDate}
          onUpdate={handleHardCopyReceivedDateUpdate}
          icon="📦"
          isLoading={isUpdatingHardCopyReceivedDate}
        />

        {/* Hard Copy Mailed Date */}
        <DateEditor
          label="Hard Copy Mailed"
          date={fileStatus.hardCopyMailedDate}
          onUpdate={handleHardCopyMailedDateUpdate}
          icon="✉️"
          isLoading={isUpdatingHardCopyMailedDate}
        />
      </div>
    </div>
  );
};

export default DocumentDatesSection;
