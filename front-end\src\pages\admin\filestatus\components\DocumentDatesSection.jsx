import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faCheck, faTimes } from "@fortawesome/free-solid-svg-icons";
import {
  useUpdateDocumentReceivedDateMutation,
  useUpdateSoftCopyReceivedDateMutation,
  useUpdateSoftCopyReleasedDateMutation,
  useUpdateHardCopyReceivedDateMutation,
  useUpdateHardCopyMailedDateMutation
} from "../../../../services/CompanyAPIService";

const DocumentDatesSection = ({ fileStatus, formatDate, showToast }) => {
  return (
    <div className="mt-8 bg-white rounded-lg border border-gray-100 shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Processing Dates</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Document Received Date */}
        <DateCard
          label="Document Received"
          date={fileStatus.documentReceivedDate}
          formatDate={formatDate}
          icon="📄"
          applicationId={fileStatus.application?.applicationId}
          qualificationId={fileStatus.qualificationCode}
          dateType="documentReceivedDate"
          showToast={showToast}
        />

        {/* Soft Copy Received Date */}
        <DateCard
          label="Soft Copy Received"
          date={fileStatus.softCopyReceivedDate}
          formatDate={formatDate}
          icon="📱"
          applicationId={fileStatus.application?.applicationId}
          qualificationId={fileStatus.qualificationCode}
          dateType="softCopyReceivedDate"
          showToast={showToast}
        />

        {/* Soft Copy Released Date */}
        <DateCard
          label="Soft Copy Released"
          date={fileStatus.softCopyReleasedDate}
          formatDate={formatDate}
          icon="📤"
          applicationId={fileStatus.application?.applicationId}
          qualificationId={fileStatus.qualificationCode}
          dateType="softCopyReleasedDate"
          showToast={showToast}
        />

        {/* Hard Copy Received Date */}
        <DateCard
          label="Hard Copy Received"
          date={fileStatus.hardCopyReceivedDate}
          formatDate={formatDate}
          icon="📦"
          applicationId={fileStatus.application?.applicationId}
          qualificationId={fileStatus.qualificationCode}
          dateType="hardCopyReceivedDate"
          showToast={showToast}
        />

        {/* Hard Copy Mailed Date */}
        <DateCard
          label="Hard Copy Mailed"
          date={fileStatus.hardCopyMailedDate}
          formatDate={formatDate}
          icon="✉️"
          applicationId={fileStatus.application?.applicationId}
          qualificationId={fileStatus.qualificationCode}
          dateType="hardCopyMailedDate"
          showToast={showToast}
        />

        {/* Tracking Number */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <span className="text-2xl mr-3">🔍</span>
            <h5 className="text-md font-medium text-gray-900">Tracking Number</h5>
          </div>
          <p className="text-sm text-gray-700 mt-2">
            {fileStatus.hardCopyTrackingNumber || "Not available"}
          </p>
        </div>
      </div>
    </div>
  );
};

// Helper component for date cards
const DateCard = ({
  label,
  date,
  formatDate,
  icon,
  applicationId,
  qualificationId,
  dateType,
  showToast
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newDate, setNewDate] = useState(date ? new Date(date).toISOString().split('T')[0] : '');

  // API mutations for different date types
  const [updateDocumentReceivedDate] = useUpdateDocumentReceivedDateMutation();
  const [updateSoftCopyReceivedDate] = useUpdateSoftCopyReceivedDateMutation();
  const [updateSoftCopyReleasedDate] = useUpdateSoftCopyReleasedDateMutation();
  const [updateHardCopyReceivedDate] = useUpdateHardCopyReceivedDateMutation();
  const [updateHardCopyMailedDate] = useUpdateHardCopyMailedDateMutation();

  // Handle date update based on date type
  const handleDateUpdate = async () => {
    try {
      const params = {
        applicationId,
        qualificationId,
      };

      switch (dateType) {
        case 'documentReceivedDate':
          await updateDocumentReceivedDate({ ...params, documentReceivedDate: newDate }).unwrap();
          break;
        case 'softCopyReceivedDate':
          await updateSoftCopyReceivedDate({ ...params, softCopyReceivedDate: newDate }).unwrap();
          break;
        case 'softCopyReleasedDate':
          await updateSoftCopyReleasedDate({ ...params, softCopyReleasedDate: newDate }).unwrap();
          break;
        case 'hardCopyReceivedDate':
          await updateHardCopyReceivedDate({ ...params, hardCopyReceivedDate: newDate }).unwrap();
          break;
        case 'hardCopyMailedDate':
          await updateHardCopyMailedDate({ ...params, hardCopyMailedDate: newDate }).unwrap();
          break;
        default:
          throw new Error('Unknown date type');
      }

      showToast(`${label} updated successfully`);
      setIsEditing(false);
    } catch (error) {
      showToast(`Failed to update ${label}`, 'error');
      console.error(`Error updating ${dateType}:`, error);
    }
  };

  // Cancel editing
  const cancelEditing = () => {
    setNewDate(date ? new Date(date).toISOString().split('T')[0] : '');
    setIsEditing(false);
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <span className="text-2xl mr-3">{icon}</span>
          <h4 className="text-md font-medium text-gray-900">{label}</h4>
        </div>
        {!isEditing ? (
          <button
            onClick={() => setIsEditing(true)}
            className="text-blue-600 hover:text-blue-800"
            title="Edit date"
          >
            <FontAwesomeIcon icon={faEdit} />
          </button>
        ) : (
          <div className="flex space-x-2">
            <button
              onClick={handleDateUpdate}
              className="text-green-600 hover:text-green-800"
              title="Save changes"
            >
              <FontAwesomeIcon icon={faCheck} />
            </button>
            <button
              onClick={cancelEditing}
              className="text-red-600 hover:text-red-800"
              title="Cancel"
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </div>
        )}
      </div>
      <p className="text-sm text-gray-500">Date:</p>
      {!isEditing ? (
        <p className="text-sm text-gray-700 mt-1">
          {formatDate(date)}
        </p>
      ) : (
        <input
          type="date"
          value={newDate}
          onChange={(e) => setNewDate(e.target.value)}
          className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
        />
      )}
    </div>
  );
};

export default DocumentDatesSection;
