import React, { useState, useMemo } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCalendarAlt } from "@fortawesome/free-solid-svg-icons";
import { showValidationError } from "../../../utils/toastUtils";

const CreateTargetModal = ({ agents, agentsLoading, agentsError, onClose, onCreate }) => {
  const [title, setTitle] = useState("");
  const [dateRange, setDateRange] = useState([null, null]);
  const [kpiMap, setKpiMap] = useState({});
  const [startDate, endDate] = dateRange;

  // Filter agents to only include SALES role
  const salesAgents = useMemo(() => {
    if (!agents) return [];
    return agents.filter(agent => agent.role === "SALES AGENT");
  }, [agents]);

  const handleKpiChange = (username, field, value) => {
    setKpiMap((prev) => ({
      ...prev,
      [username]: {
        ...prev[username],
        [field]: parseFloat(value) || 0,
      },
    }));
  };

  const handleCreate = () => {
    if (!title) {
      showValidationError("Please enter a title.");
      return;
    }
    if (!startDate || !endDate) {
      showValidationError("Please select a valid date range.");
      return;
    }
    const requestBody = {
      title,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      targets: Array.isArray(salesAgents)
        ? salesAgents.map((agent) => ({
            agentUsername: agent.username,
            kpi1: kpiMap[agent.username]?.kpi1 || 0,
            kpi2: kpiMap[agent.username]?.kpi2 || 0,
          }))
        : [],
    };
    onCreate(requestBody);
  };

  if (agentsLoading) return <p className="p-4 bg-white">Loading agents...</p>;
  if (agentsError) return <p className="p-4 bg-white">Error loading agents.</p>;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white w-full max-w-3xl p-6 rounded-lg relative">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-xl font-bold"
        >
          &times;
        </button>
        <h2 className="text-xl font-bold mb-4">Create Weekly Target</h2>
        <div className="mb-4">
          <label className="block mb-1 font-medium">Title</label>
          <input
            type="text"
            className="w-full border p-2 rounded"
            placeholder='e.g. "Week 1, February 2025"'
            value={title}
            onChange={(e) => setTitle(e.target.value)}
          />
        </div>
        <div className="mb-4">
          <label className="block mb-1 font-medium">Date Range</label>
          <DatePicker
            selectsRange
            startDate={startDate}
            endDate={endDate}
            onChange={(update) => setDateRange(update)}
            isClearable
            placeholderText="Select start and end date"
            className="w-full border p-2 rounded"
          />
        </div>
        <div className="mb-4 overflow-x-auto">
          <h3 className="font-medium mb-2">Set KPI for Each Sales Agent</h3>
          {salesAgents?.length === 0 ? (
            <p>No sales agents found.</p>
          ) : (
            <table className="min-w-full border text-sm">
              <thead className="bg-gray-100 border-b">
                <tr>
                  <th className="p-2 text-left">Agent Name</th>
                  <th className="p-2 text-right">KPI1 ($)</th>
                  <th className="p-2 text-right">KPI2 ($)</th>
                </tr>
              </thead>
              <tbody>
                {salesAgents.map((agent) => (
                  <tr key={agent.username} className="border-b">
                    <td className="p-2">{agent.fullName}</td>
                    <td className="p-2 text-right">
                      <input
                        type="number"
                        className="w-20 border p-1 rounded text-right"
                        value={kpiMap[agent.username]?.kpi1 || ""}
                        onChange={(e) =>
                          handleKpiChange(agent.username, "kpi1", e.target.value)
                        }
                      />
                    </td>
                    <td className="p-2 text-right">
                      <input
                        type="number"
                        className="w-20 border p-1 rounded text-right"
                        value={kpiMap[agent.username]?.kpi2 || ""}
                        onChange={(e) =>
                          handleKpiChange(agent.username, "kpi2", e.target.value)
                        }
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
        <div className="flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Cancel
          </button>
          <button
            onClick={handleCreate}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Create
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateTargetModal;
