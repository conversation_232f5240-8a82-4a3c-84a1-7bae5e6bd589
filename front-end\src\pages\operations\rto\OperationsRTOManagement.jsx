import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faSearch,
  faPlus,
  faSync,
  faEye,
  faExternalLinkAlt,
  faBuilding,
  faTable,
  faTh,
  faUserFriends
} from "@fortawesome/free-solid-svg-icons";
import { useNavigate } from "react-router-dom";
import { useGetAllRTOsQuery } from "../../../services/CompanyAPIService";
import RTOCard from "../../admin/rto/components/RTOCard";
import AddRTOModal from "../../admin/rto/components/AddRTOModal";
import LoadingSpinner from "../../../components/common/LoadingSpinner";
import EmptyState from "../../../components/common/EmptyState";

const OperationsRTOManagement = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState("card"); // "card" or "table"

  // Fetch RTOs
  const { data: rtos = [], isLoading, refetch } = useGetAllRTOsQuery();

  // Filter RTOs based on search term
  const filteredRTOs = rtos.filter(
    (rto) =>
      rto.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rto.legalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rto.businessName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle view RTO details
  const handleViewRTO = (rtoCode) => {
    navigate(`/operations/rto/${rtoCode}`);
  };

  // Handle external link to training.gov.au
  const handleExternalLink = (rtoCode) => {
    window.open(`https://training.gov.au/Organisation/Details/${rtoCode}`, "_blank");
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <div className="flex items-center">
              <FontAwesomeIcon icon={faBuilding} className="text-blue-600 text-xl mr-3" />
              <h1 className="text-2xl font-bold text-gray-800">RTO Management</h1>
            </div>
            <p className="mt-2 text-gray-600">
              Connect and manage Registered Training Organizations
            </p>
          </div>
          <div className="flex items-center">
            <div className="bg-blue-50 rounded-lg p-3 flex items-center mr-4">
              <FontAwesomeIcon icon={faBuilding} className="text-blue-600 mr-2" />
              <span className="font-bold text-gray-800">{rtos.length} RTOs</span>
            </div>
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Add RTO
            </button>
          </div>
        </div>
      </div>

      {/* Search and View Controls */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="relative w-full sm:w-96">
            <input
              type="text"
              placeholder="Search by RTO code or name..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <FontAwesomeIcon
              icon={faSearch}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
          </div>
          <div className="flex space-x-2">
            <div className="flex border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode("card")}
                className={`px-3 py-2 flex items-center ${viewMode === "card" ? "bg-blue-50 text-blue-600" : "bg-white text-gray-600"}`}
                title="Card View"
              >
                <FontAwesomeIcon icon={faTh} />
              </button>
              <button
                onClick={() => setViewMode("table")}
                className={`px-3 py-2 flex items-center ${viewMode === "table" ? "bg-blue-50 text-blue-600" : "bg-white text-gray-600"}`}
                title="Table View"
              >
                <FontAwesomeIcon icon={faTable} />
              </button>
            </div>
            <button
              onClick={refetch}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg flex items-center transition-colors duration-200"
              title="Refresh"
            >
              <FontAwesomeIcon icon={faSync} />
            </button>
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Add RTO
            </button>
          </div>
        </div>

        {isLoading ? (
          <LoadingSpinner />
        ) : filteredRTOs.length === 0 ? (
          <EmptyState
            message={
              searchTerm
                ? "No RTOs found matching your search criteria."
                : "No RTOs have been added yet. Add your first RTO to get started."
            }
            icon={faBuilding}
          />
        ) : viewMode === "card" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredRTOs.map((rto) => (
              <RTOCard
                key={rto.code}
                rto={rto}
                onView={() => handleViewRTO(rto.code)}
                onExternalLink={() => handleExternalLink(rto.code)}
                // Operations users don't have delete access
                onDelete={null}
              />
            ))}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    RTO Code
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contacts
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRTOs.map((rto) => (
                  <tr key={rto.code} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {rto.code}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{rto.legalName}</div>
                      {rto.businessName && (
                        <div className="text-sm text-gray-500">{rto.businessName}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {rto.rtoType || "RTO"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faUserFriends} className="text-blue-500 mr-2" />
                        <span>{rto.contacts?.length || 0}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleViewRTO(rto.code)}
                          className="text-blue-600 hover:text-blue-900"
                          title="View Details"
                        >
                          <FontAwesomeIcon icon={faEye} />
                        </button>
                        <button
                          onClick={() => handleExternalLink(rto.code)}
                          className="text-gray-600 hover:text-gray-900"
                          title="View on training.gov.au"
                        >
                          <FontAwesomeIcon icon={faExternalLinkAlt} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add RTO Modal */}
      <AddRTOModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={() => {
          setIsAddModalOpen(false);
          refetch();
        }}
      />
    </div>
  );
};

export default OperationsRTOManagement;
