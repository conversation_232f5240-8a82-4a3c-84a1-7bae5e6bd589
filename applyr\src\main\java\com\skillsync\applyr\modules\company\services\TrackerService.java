package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Tracker;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.core.utills.UserUtils;
import com.skillsync.applyr.modules.company.models.TrackerDataDTO;
import com.skillsync.applyr.modules.company.repositories.SalesAgentRepository;
import com.skillsync.applyr.modules.company.repositories.TrackerRepository;
import com.skillsync.applyr.modules.company.models.ProfileDTO;
import com.skillsync.applyr.modules.company.services.EmployeeProfileServiceHelper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class TrackerService {

    private final TrackerRepository trackerRepository;
    private final SalesAgentRepository salesAgentRepository;

    public TrackerService(TrackerRepository trackerRepository, SalesAgentRepository salesAgentRepository) {
        this.trackerRepository = trackerRepository;
        this.salesAgentRepository = salesAgentRepository;
    }

    public LocalDate updateTimeTracker() {
        ZoneId aestZone = ZoneId.of("Australia/Sydney");
        LocalDateTime aestNow = LocalDateTime.now(aestZone);
        LocalDate aestToday = LocalDate.now(aestZone);

        String username = UserUtils.getUsernameFromToken();
        Optional<Tracker> tracker = trackerRepository.findByUsernameAndCreatedDateAfter(username, aestToday.atStartOfDay());
        if (tracker.isPresent()) {
            tracker.get().updateTimeStamp();
            trackerRepository.save(tracker.get());
        } else {
            Tracker newTracker = new Tracker(username, aestNow);
            trackerRepository.save(newTracker);
        }
        return aestToday;
    }

    public List<TrackerDataDTO> getTrackerOfUserByMonthYear(String username, int month, int year) {
        var salesAgentOpt = salesAgentRepository.getSalesAgentByUserUsername(username);
        if (salesAgentOpt.isPresent()) {
            List<TrackerDataDTO> trackerDTOs = new ArrayList<>();
            List<Tracker> trackers = trackerRepository.findAllByUsernameAndCreatedDateAfterAndCreatedDateBefore(
                    username,
                    LocalDate.of(year, month, 1).atStartOfDay(),
                    LocalDate.of(year, month + 1, 1).atStartOfDay()
            );
            for (Tracker tracker : trackers) {
                TrackerDataDTO trackerDataDTO = new TrackerDataDTO();
                trackerDataDTO.setProfile(EmployeeProfileServiceHelper.fromAgentToProfileDTO(salesAgentOpt.get()));
                trackerDataDTO.setFirstLogin(tracker.getFirstLogin().toString());
                trackerDataDTO.setLastTime(tracker.getLastLogin().toString());
                trackerDataDTO.setTotalSeconds(tracker.getTotalTime());
                trackerDataDTO.setCaptureDate(tracker.getCreatedDate().toLocalDate().atStartOfDay().toString());
                trackerDTOs.add(trackerDataDTO);
            }
            return trackerDTOs;
        }
        throw new AppRTException("Unable to fetch sales agent", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public List<TrackerDataDTO> getAllTrackersOfToday() {
        ZoneId aestZone = ZoneId.of("Australia/Sydney");
        LocalDate aestToday = LocalDate.now(aestZone);

        List<TrackerDataDTO> trackers = new ArrayList<>();
        salesAgentRepository.findAll().forEach(salesAgent -> {
            Optional<Tracker> trackerOpt = trackerRepository.findByUsernameAndCreatedDateAfter(salesAgent.getUser().getUsername(), aestToday.atStartOfDay());
            ProfileDTO profileDTO = EmployeeProfileServiceHelper.fromAgentToProfileDTO(salesAgent);
            TrackerDataDTO trackerDataDTO = new TrackerDataDTO();
            trackerDataDTO.setProfile(profileDTO);
            if (trackerOpt.isPresent()) {
                Tracker tracker = trackerOpt.get();
                trackerDataDTO.setFirstLogin(tracker.getFirstLogin().toString());
                trackerDataDTO.setLastTime(tracker.getLastLogin().toString());
                trackerDataDTO.setTotalSeconds(tracker.getTotalTime());
                trackerDataDTO.setCaptureDate(tracker.getCreatedDate().toLocalDate().atStartOfDay().toString());
            } else {
                trackerDataDTO.setFirstLogin(null);
                trackerDataDTO.setLastTime(null);
                trackerDataDTO.setTotalSeconds(0);
                trackerDataDTO.setCaptureDate(aestToday.atStartOfDay().toString());
            }
            trackers.add(trackerDataDTO);
        });
        return trackers;
    }
}
