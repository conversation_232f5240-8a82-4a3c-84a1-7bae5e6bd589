import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCalendarAlt, faSearch, faUsers, faFlag, faBuilding } from "@fortawesome/free-solid-svg-icons";

const ExportLeadsFilters = ({
  leadSearch,
  setLeadSearch,
  selectedAgentFilter,
  setSelectedAgentFilter,
  leadStatusFilter,
  setLeadStatusFilter,
  dateFilterType,
  setDateFilterType,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  specificDate,
  setSpecificDate,
  applicationFilter,
  setApplicationFilter,
  agentNames
}) => {
  const hasActiveFilters =
    leadSearch ||
    selectedAgentFilter ||
    leadStatusFilter !== 'all' ||
    dateFilterType !== 'all' ||
    applicationFilter !== 'all';

  const clearAllFilters = () => {
    setLeadSearch('');
    setSelectedAgentFilter('');
    setLeadStatusFilter('all');
    setDateFilterType('all');
    setStartDate('');
    setEndDate('');
    setSpecificDate('');
    setApplicationFilter('all');
  };

  return (
    <div className="bg-white rounded-xl border border-gray-100 mb-6 shadow-sm">
      <div className="p-6">
        {/* Filter Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Filter Leads</h3>
          {hasActiveFilters && (
            <button
              onClick={clearAllFilters}
              className="text-sm text-[#6E39CB] hover:text-[#5A2FA3] font-medium transition-colors"
            >
              Clear all filters
            </button>
          )}
        </div>

        {/* Primary Filters Row */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
          {/* Search Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FontAwesomeIcon icon={faSearch} className="mr-2 text-[#8B7EC8]" />
              Search
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="Company, name, or email..."
                value={leadSearch}
                onChange={(e) => setLeadSearch(e.target.value)}
                className="w-full pl-4 pr-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent transition-all duration-200 text-sm"
              />
            </div>
          </div>

          {/* Date Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-[#6B9BD8]" />
              Date Range
            </label>
            <select
              value={dateFilterType}
              onChange={(e) => setDateFilterType(e.target.value)}
              className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent transition-all duration-200 text-sm"
            >
              <option value="all">All Dates</option>
              <option value="today">Today</option>
              <option value="thisWeek">This Week</option>
              <option value="thisMonth">This Month</option>
              <option value="thisYear">This Year</option>
              <option value="custom">Custom Range</option>
              <option value="specificDate">Specific Date</option>
            </select>
          </div>

          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FontAwesomeIcon icon={faFlag} className="mr-2 text-[#8B7EC8]" />
              Status
            </label>
            <select
              value={leadStatusFilter}
              onChange={(e) => setLeadStatusFilter(e.target.value)}
              className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent transition-all duration-200 text-sm"
            >
              <option value="all">All Statuses</option>
              <option value="fresh">Fresh</option>
              <option value="warm">Warm</option>
              <option value="hot">Hot</option>
              <option value="cold">Cold</option>
              <option value="closed">Closed</option>
            </select>
          </div>

          {/* Application Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FontAwesomeIcon icon={faBuilding} className="mr-2 text-[#6B9BD8]" />
              Applications
            </label>
            <select
              value={applicationFilter}
              onChange={(e) => setApplicationFilter(e.target.value)}
              className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent transition-all duration-200 text-sm"
            >
              <option value="all">All Leads</option>
              <option value="hasApplications">Has Applications</option>
              <option value="noApplications">No Applications</option>
            </select>
          </div>
        </div>

        {/* Secondary Filters Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
          {/* Agent Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FontAwesomeIcon icon={faUsers} className="mr-2 text-[#8B7EC8]" />
              Assigned Agent
            </label>
            <select
              value={selectedAgentFilter}
              onChange={(e) => setSelectedAgentFilter(e.target.value)}
              className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent transition-all duration-200 text-sm"
            >
              <option value="">All Agents</option>
              {agentNames.map((agentName) => (
                <option key={agentName} value={agentName}>
                  {agentName}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Date Range Inputs */}
        {dateFilterType === "custom" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Date
              </label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent transition-all duration-200 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Date
              </label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent transition-all duration-200 text-sm"
              />
            </div>
          </div>
        )}

        {/* Specific Date Input */}
        {dateFilterType === "specificDate" && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="max-w-md">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Date
              </label>
              <input
                type="date"
                value={specificDate}
                onChange={(e) => setSpecificDate(e.target.value)}
                className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent transition-all duration-200 text-sm"
              />
            </div>
          </div>
        )}

        {/* Active Filters */}
        {hasActiveFilters && (
          <div className="pt-4 border-t border-gray-100">
            <div className="flex items-center flex-wrap gap-2">
              <span className="text-sm font-medium text-gray-600">Active filters:</span>

              {leadSearch && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-[#6E39CB] text-white">
                  Search: "{leadSearch}"
                  <button
                    onClick={() => setLeadSearch('')}
                    className="ml-2 text-white hover:text-gray-200 transition-colors"
                  >
                    ×
                  </button>
                </span>
              )}

              {dateFilterType !== 'all' && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                  Date: {dateFilterType === 'thisWeek' ? 'This Week' :
                         dateFilterType === 'thisMonth' ? 'This Month' :
                         dateFilterType === 'thisYear' ? 'This Year' :
                         dateFilterType === 'today' ? 'Today' :
                         dateFilterType === 'custom' ? 'Custom Range' :
                         dateFilterType === 'specificDate' ? 'Specific Date' : dateFilterType}
                  <button
                    onClick={() => {
                      setDateFilterType('all');
                      setStartDate('');
                      setEndDate('');
                      setSpecificDate('');
                    }}
                    className="ml-2 text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    ×
                  </button>
                </span>
              )}

              {leadStatusFilter !== 'all' && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-green-100 text-green-800">
                  Status: {leadStatusFilter.charAt(0).toUpperCase() + leadStatusFilter.slice(1)}
                  <button
                    onClick={() => setLeadStatusFilter('all')}
                    className="ml-2 text-green-600 hover:text-green-800 transition-colors"
                  >
                    ×
                  </button>
                </span>
              )}

              {applicationFilter !== 'all' && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-orange-100 text-orange-800">
                  Apps: {applicationFilter === 'hasApplications' ? 'Has Applications' : 'No Applications'}
                  <button
                    onClick={() => setApplicationFilter('all')}
                    className="ml-2 text-orange-600 hover:text-orange-800 transition-colors"
                  >
                    ×
                  </button>
                </span>
              )}

              {selectedAgentFilter && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                  Agent: {selectedAgentFilter}
                  <button
                    onClick={() => setSelectedAgentFilter('')}
                    className="ml-2 text-purple-600 hover:text-purple-800 transition-colors"
                  >
                    ×
                  </button>
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExportLeadsFilters;
