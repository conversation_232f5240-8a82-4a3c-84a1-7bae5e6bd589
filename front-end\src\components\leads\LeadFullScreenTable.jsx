import React from "react";
import { FaCompress } from "react-icons/fa";
import LeadTable from "./LeadTable";
import LeadSearchFilters from "./LeadSearchFilters";
import LeadStatusTabs from "./LeadStatusTabs";
import LeadPageHeader from "./LeadPageHeader";

const LeadFullScreenTable = ({
  isOpen,
  onClose,
  leads,
  onProfileRedirect,
  onStatusChange,
  onEditLead,
  onOpenDrawer,
  onDeleteLead,
  sortField,
  sortDirection,
  onSort,
  leadSearch,
  setLeadSearch,
  selectedAgentFilter,
  setSelectedAgentFilter,
  leadTypeFilter,
  setLeadTypeFilter,
  agentNames,
  isAdmin = false,
  isLoading = false,
  isUpdatingStatus = false,
  error = null,
  // New props for tabs and header
  leadTab,
  onTabChange,
  totalLeads,
  leadsWithApplications,
  statusCounts,
  onOpenBulkModal,
  onOpenSingleModal
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-white p-6 z-50 overflow-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Leads</h1>
        <button
          onClick={onClose}
          className="flex items-center text-gray-700 bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <FaCompress className="mr-2" /> Exit Fullscreen
        </button>
      </div>

      {/* Page Header with Add Lead buttons */}
      {onOpenBulkModal && onOpenSingleModal && (
        <div className="mb-6">
          <LeadPageHeader
            onOpenBulkModal={onOpenBulkModal}
            onOpenSingleModal={onOpenSingleModal}
            isAdmin={isAdmin}
          />
        </div>
      )}

      {/* Status Tabs */}
      {leadTab && onTabChange && totalLeads !== undefined && (
        <div className="mb-6">
          <LeadStatusTabs
            leadTab={leadTab}
            onTabChange={onTabChange}
            totalLeads={totalLeads}
            leadsWithApplications={leadsWithApplications}
            statusCounts={statusCounts}
          />
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm border border-gray-50 overflow-x-auto">
        <LeadSearchFilters
          leadSearch={leadSearch}
          setLeadSearch={setLeadSearch}
          selectedAgentFilter={selectedAgentFilter}
          setSelectedAgentFilter={setSelectedAgentFilter}
          leadTypeFilter={leadTypeFilter}
          setLeadTypeFilter={setLeadTypeFilter}
          agentNames={agentNames}
          isFullScreen={true}
          setIsFullScreen={() => {}} // No-op since we're already in fullscreen
        />
        <div className="overflow-x-auto w-full">
          <div className="w-full inline-block align-middle">
            <div className="overflow-hidden">
              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6E39CB]"></div>
                  <span className="ml-3 text-gray-500">Loading leads...</span>
                </div>
              ) : error ? (
                <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-700">
                        Error loading leads: {error?.data?.message || error?.message || 'Unknown error'}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <LeadTable
                  leads={leads}
                  onProfileRedirect={onProfileRedirect}
                  onStatusChange={onStatusChange}
                  onEditLead={onEditLead}
                  onOpenDrawer={onOpenDrawer}
                  onDeleteLead={onDeleteLead}
                  sortField={sortField}
                  sortDirection={sortDirection}
                  onSort={onSort}
                  isAdmin={isAdmin}
                  isLoading={isLoading}
                  isUpdatingStatus={isUpdatingStatus}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeadFullScreenTable;
