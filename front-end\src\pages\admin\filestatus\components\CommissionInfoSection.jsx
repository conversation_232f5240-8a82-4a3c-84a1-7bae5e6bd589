import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faCheck, faTimes, faCalendarAlt } from "@fortawesome/free-solid-svg-icons";
import {
  useUpdateCommissionInfoMutation,
  useUpdateCommissionPaymentStatusMutation,
  useUpdateCommissionPaymentDateMutation
} from "../../../../services/CompanyAPIService";

const CommissionInfoSection = ({
  externalCommissionName,
  externalCommissionContactInfo,
  commissionAmount,
  commissionPaymentStatus,
  commissionPaymentDate,
  formatDate,
  applicationId,
  qualificationId,
  showToast
}) => {
  // State for editing commission info
  const [isEditingCommissionInfo, setIsEditingCommissionInfo] = useState(false);
  const [editedCommissionName, setEditedCommissionName] = useState(externalCommissionName || "");
  const [editedCommissionContactInfo, setEditedCommissionContactInfo] = useState(externalCommissionContactInfo || "");
  const [editedCommissionAmount, setEditedCommissionAmount] = useState(commissionAmount || 0);

  // State for editing payment status
  const [isEditingPaymentStatus, setIsEditingPaymentStatus] = useState(false);
  const [editedPaymentStatus, setEditedPaymentStatus] = useState(commissionPaymentStatus || "UNPAID");

  // State for editing payment date
  const [isEditingPaymentDate, setIsEditingPaymentDate] = useState(false);
  const [editedPaymentDate, setEditedPaymentDate] = useState(
    commissionPaymentDate ? new Date(commissionPaymentDate).toISOString().split('T')[0] : ""
  );

  // API mutations
  const [updateCommissionInfo, { isLoading: isUpdatingCommissionInfo }] = useUpdateCommissionInfoMutation();
  const [updatePaymentStatus, { isLoading: isUpdatingPaymentStatus }] = useUpdateCommissionPaymentStatusMutation();
  const [updatePaymentDate, { isLoading: isUpdatingPaymentDate }] = useUpdateCommissionPaymentDateMutation();

  // Handle commission info update
  const handleCommissionInfoUpdate = async () => {
    try {
      await updateCommissionInfo({
        applicationId,
        qualificationId,
        externalCommissionName: editedCommissionName,
        externalCommissionContactInfo: editedCommissionContactInfo,
        commissionAmount: parseFloat(editedCommissionAmount)
      }).unwrap();

      showToast("Commission information updated successfully");
      setIsEditingCommissionInfo(false);
    } catch (error) {
      showToast("Failed to update commission information", "error");
      console.error("Error updating commission info:", error);
    }
  };

  // Handle payment status update
  const handlePaymentStatusUpdate = async () => {
    try {
      await updatePaymentStatus({
        applicationId,
        qualificationId,
        paymentStatus: editedPaymentStatus
      }).unwrap();

      showToast("Payment status updated successfully");
      setIsEditingPaymentStatus(false);
    } catch (error) {
      showToast("Failed to update payment status", "error");
      console.error("Error updating payment status:", error);
    }
  };

  // Handle payment date update
  const handlePaymentDateUpdate = async () => {
    try {
      await updatePaymentDate({
        applicationId,
        qualificationId,
        paymentDate: editedPaymentDate
      }).unwrap();

      showToast("Payment date updated successfully");
      setIsEditingPaymentDate(false);
    } catch (error) {
      showToast("Failed to update payment date", "error");
      console.error("Error updating payment date:", error);
    }
  };

  // Cancel editing functions
  const cancelEditingCommissionInfo = () => {
    setEditedCommissionName(externalCommissionName || "");
    setEditedCommissionContactInfo(externalCommissionContactInfo || "");
    setEditedCommissionAmount(commissionAmount || 0);
    setIsEditingCommissionInfo(false);
  };

  const cancelEditingPaymentStatus = () => {
    setEditedPaymentStatus(commissionPaymentStatus || "UNPAID");
    setIsEditingPaymentStatus(false);
  };

  const cancelEditingPaymentDate = () => {
    setEditedPaymentDate(commissionPaymentDate ? new Date(commissionPaymentDate).toISOString().split('T')[0] : "");
    setIsEditingPaymentDate(false);
  };
  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Commission Information</h3>
        {externalCommissionName ? (
          !isEditingCommissionInfo ? (
            <button
              onClick={() => setIsEditingCommissionInfo(true)}
              className="text-blue-600 hover:text-blue-800"
              title="Edit commission information"
            >
              <FontAwesomeIcon icon={faEdit} />
            </button>
          ) : (
            <div className="flex space-x-2">
              <button
                onClick={handleCommissionInfoUpdate}
                className="text-green-600 hover:text-green-800"
                title="Save changes"
                disabled={isUpdatingCommissionInfo}
              >
                <FontAwesomeIcon icon={faCheck} />
              </button>
              <button
                onClick={cancelEditingCommissionInfo}
                className="text-red-600 hover:text-red-800"
                title="Cancel"
              >
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
          )
        ) : null}
      </div>

      {externalCommissionName || isEditingCommissionInfo ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Commission Details */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">Commission Details</h4>

            <div className="space-y-4">
              {/* Commission Name */}
              <div className="flex flex-col">
                <span className="text-sm font-medium text-gray-500 mb-1">Commission Name:</span>
                {!isEditingCommissionInfo ? (
                  <span className="text-sm text-gray-900">{externalCommissionName}</span>
                ) : (
                  <input
                    type="text"
                    value={editedCommissionName}
                    onChange={(e) => setEditedCommissionName(e.target.value)}
                    className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter commission name"
                  />
                )}
              </div>

              {/* Contact Information */}
              <div className="flex flex-col">
                <span className="text-sm font-medium text-gray-500 mb-1">Contact Information:</span>
                {!isEditingCommissionInfo ? (
                  <span className="text-sm text-gray-900">{externalCommissionContactInfo || "N/A"}</span>
                ) : (
                  <input
                    type="text"
                    value={editedCommissionContactInfo}
                    onChange={(e) => setEditedCommissionContactInfo(e.target.value)}
                    className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter contact information"
                  />
                )}
              </div>

              {/* Commission Amount */}
              <div className="flex flex-col">
                <span className="text-sm font-medium text-gray-500 mb-1">Commission Amount:</span>
                {!isEditingCommissionInfo ? (
                  <span className="text-sm text-gray-900">${commissionAmount || "0.00"}</span>
                ) : (
                  <input
                    type="number"
                    value={editedCommissionAmount}
                    onChange={(e) => setEditedCommissionAmount(e.target.value)}
                    className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    step="0.01"
                    min="0"
                    placeholder="Enter commission amount"
                  />
                )}
              </div>
            </div>
          </div>

          {/* Payment Status */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">Payment Status</h4>

            <div className="space-y-4">
              {/* Payment Status */}
              <div className="flex flex-col">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500 mb-1">Payment Status:</span>
                  {!isEditingPaymentStatus && !isEditingCommissionInfo ? (
                    <button
                      onClick={() => setIsEditingPaymentStatus(true)}
                      className="text-blue-600 hover:text-blue-800 text-xs"
                      title="Edit payment status"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </button>
                  ) : isEditingPaymentStatus && !isEditingCommissionInfo ? (
                    <div className="flex space-x-2">
                      <button
                        onClick={handlePaymentStatusUpdate}
                        className="text-green-600 hover:text-green-800 text-xs"
                        title="Save changes"
                        disabled={isUpdatingPaymentStatus}
                      >
                        <FontAwesomeIcon icon={faCheck} />
                      </button>
                      <button
                        onClick={cancelEditingPaymentStatus}
                        className="text-red-600 hover:text-red-800 text-xs"
                        title="Cancel"
                      >
                        <FontAwesomeIcon icon={faTimes} />
                      </button>
                    </div>
                  ) : null}
                </div>
                {!isEditingPaymentStatus ? (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    commissionPaymentStatus === "PAID"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}>
                    {commissionPaymentStatus === "PAID" ? "Paid" : "Unpaid"}
                  </span>
                ) : (
                  <select
                    value={editedPaymentStatus}
                    onChange={(e) => setEditedPaymentStatus(e.target.value)}
                    className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="UNPAID">Unpaid</option>
                    <option value="PAID">Paid</option>
                  </select>
                )}
              </div>

              {/* Payment Date */}
              <div className="flex flex-col">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500 mb-1">Payment Date:</span>
                  {!isEditingPaymentDate && !isEditingCommissionInfo && !isEditingPaymentStatus ? (
                    <button
                      onClick={() => setIsEditingPaymentDate(true)}
                      className="text-blue-600 hover:text-blue-800 text-xs"
                      title="Edit payment date"
                    >
                      <FontAwesomeIcon icon={faCalendarAlt} />
                    </button>
                  ) : isEditingPaymentDate && !isEditingCommissionInfo && !isEditingPaymentStatus ? (
                    <div className="flex space-x-2">
                      <button
                        onClick={handlePaymentDateUpdate}
                        className="text-green-600 hover:text-green-800 text-xs"
                        title="Save changes"
                        disabled={isUpdatingPaymentDate}
                      >
                        <FontAwesomeIcon icon={faCheck} />
                      </button>
                      <button
                        onClick={cancelEditingPaymentDate}
                        className="text-red-600 hover:text-red-800 text-xs"
                        title="Cancel"
                      >
                        <FontAwesomeIcon icon={faTimes} />
                      </button>
                    </div>
                  ) : null}
                </div>
                {!isEditingPaymentDate ? (
                  <span className="text-sm text-gray-900">{formatDate(commissionPaymentDate)}</span>
                ) : (
                  <input
                    type="date"
                    value={editedPaymentDate}
                    onChange={(e) => setEditedPaymentDate(e.target.value)}
                    className="mt-1 block w-full text-sm border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500">No commission information available for this file.</p>
          <button
            onClick={() => setIsEditingCommissionInfo(true)}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Add Commission Information
          </button>
        </div>
      )}
    </div>
  );
};

export default CommissionInfoSection;
