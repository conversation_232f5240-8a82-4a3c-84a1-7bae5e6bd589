import React, { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft } from "@fortawesome/free-solid-svg-icons";
import { useGetSingleApplicationFileQuery, useUpdateFileStatusMutation } from "../../../services/CompanyAPIService";

// Import redesigned components
import StatusDropdown from "./components/details/StatusDropdown";
import ApplicationInfoSection from "./components/details/ApplicationInfoSection";
import QualificationInfoSection from "./components/details/QualificationInfoSection";
import RTOInfoSection from "./components/details/RTOInfoSection";
import DocumentDatesSection from "./components/details/DocumentDatesSection";
import CommissionInfoSection from "./components/details/CommissionInfoSection";
import FileDetailsForm from "./components/details/FileDetailsForm";
import Toast from "./components/details/Toast";

const FileStatusDetail = () => {
  const { applicationId, qualificationId } = useParams();
  const navigate = useNavigate();

  // Fetch file status data
  const {
    data: fileStatus,
    isLoading,
    error,
    refetch
  } = useGetSingleApplicationFileQuery({ applicationId, qualificationId });

  // Mutations for updating file status
  const [updateFileStatus, { isLoading: isUpdatingStatus }] = useUpdateFileStatusMutation();

  // State for active tab
  const [activeTab, setActiveTab] = useState("overview");

  // Toast notification state
  const [toast, setToast] = useState({ visible: false, message: "", type: "success" });

  // Show toast notification
  const showToast = (message, type = "success") => {
    setToast({ visible: true, message, type });
  };

  // Hide toast notification
  const hideToast = () => {
    setToast({ visible: false, message: "", type: "success" });
  };

  // Handle file status update
  const handleStatusUpdate = async (newStatus) => {
    try {
      await updateFileStatus({
        applicationId,
        qualificationId,
        status: newStatus
      }).unwrap();

      showToast("File status updated successfully");
      refetch();
    } catch (error) {
      showToast("Failed to update file status", "error");
      console.error("Error updating file status:", error);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="w-16 h-16 border-4 border-[#6E39CB] border-t-transparent rounded-full animate-spin mb-4"></div>
        <p className="text-lg font-medium text-gray-700">Loading file status details...</p>
        <p className="text-sm text-gray-500 mt-2">This may take a moment</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="bg-red-100 text-red-700 p-4 rounded-lg mb-4">
          <p className="font-medium">Error loading file status details</p>
          <p className="text-sm mt-1">Please try again later or contact support if the problem persists.</p>
        </div>
        <button
          onClick={() => navigate(-1)}
          className="mt-4 px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Toast Notification */}
      {toast.visible && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={hideToast}
        />
      )}

      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div className="flex items-center mb-4 md:mb-0">
          <button
            onClick={() => navigate(-1)}
            className="mr-4 p-2 rounded-full text-gray-500 hover:bg-gray-100 transition-colors"
          >
            <FontAwesomeIcon icon={faArrowLeft} />
          </button>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">File Status Details</h1>
            <p className="text-sm text-gray-500">
              {fileStatus.application?.applicantName || "N/A"} - {fileStatus.qualificationName || "N/A"}
            </p>
          </div>
        </div>

        <div className="flex items-center">
          <StatusDropdown
            currentStatus={fileStatus.fileStatus}
            onStatusUpdate={handleStatusUpdate}
            isLoading={isUpdatingStatus}
            fileStatus={fileStatus}
            showToast={showToast}
            refetch={refetch}
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="mt-6">
        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab("overview")}
              className={`py-4 px-6 font-medium text-sm border-b-2 ${
                activeTab === "overview"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab("details")}
              className={`py-4 px-6 font-medium text-sm border-b-2 ${
                activeTab === "details"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              File Details
            </button>
            <button
              onClick={() => setActiveTab("rto")}
              className={`py-4 px-6 font-medium text-sm border-b-2 ${
                activeTab === "rto"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              RTO Information
            </button>
            <button
              onClick={() => setActiveTab("commission")}
              className={`py-4 px-6 font-medium text-sm border-b-2 ${
                activeTab === "commission"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Commission
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
          {/* Overview Tab */}
          {activeTab === "overview" && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-6">
                {/* Application Information */}
                <ApplicationInfoSection
                  application={fileStatus.application}
                />

                {/* Qualification Information */}
                <QualificationInfoSection
                  qualificationCode={fileStatus.qualificationCode}
                  qualificationName={fileStatus.qualificationName}
                />
              </div>

              {/* Right Column */}
              <div className="space-y-6">
                {/* Status Timeline */}
                <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Status Timeline</h3>
                  <div className="relative pl-6 border-l-2 border-gray-200 space-y-6">
                    <TimelineItem
                      label="Documents Received"
                      date={fileStatus.documentReceivedDate}
                      completed={!!fileStatus.documentReceivedDate}
                    />
                    <TimelineItem
                      label="Lodged to RTO"
                      date={fileStatus.lodgedDate}
                      completed={!!fileStatus.lodgedDate}
                    />
                    <TimelineItem
                      label="Soft Copy Received"
                      date={fileStatus.softCopyReceivedDate}
                      completed={!!fileStatus.softCopyReceivedDate}
                    />
                    <TimelineItem
                      label="Soft Copy Released"
                      date={fileStatus.softCopyReleasedDate}
                      completed={!!fileStatus.softCopyReleasedDate}
                    />
                    <TimelineItem
                      label="Hard Copy Received"
                      date={fileStatus.hardCopyReceivedDate}
                      completed={!!fileStatus.hardCopyReceivedDate}
                    />
                    <TimelineItem
                      label="Hard Copy Mailed"
                      date={fileStatus.hardCopyMailedDate}
                      completed={!!fileStatus.hardCopyMailedDate}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* File Details Tab */}
          {activeTab === "details" && (
            <div className="space-y-8">
              <FileDetailsForm
                fileStatus={fileStatus}
                showToast={showToast}
                refetch={refetch}
              />

              <DocumentDatesSection
                fileStatus={fileStatus}
                showToast={showToast}
                refetch={refetch}
              />
            </div>
          )}

          {/* RTO Information Tab */}
          {activeTab === "rto" && (
            <RTOInfoSection
              fileStatus={fileStatus}
              showToast={showToast}
              refetch={refetch}
            />
          )}

          {/* Commission Tab */}
          {activeTab === "commission" && (
            <CommissionInfoSection
              fileStatus={fileStatus}
              showToast={showToast}
              refetch={refetch}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// Timeline Item Component
const TimelineItem = ({ label, date, completed }) => {
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "Not completed";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="relative">
      {/* Timeline dot */}
      <div className="absolute -left-9 mt-1">
        <div className={`h-5 w-5 rounded-full border-4 border-white ${
          completed ? "bg-green-500" : "bg-gray-300"
        }`}></div>
      </div>

      {/* Content */}
      <div className={`p-4 rounded-lg ${
        completed ? "bg-green-50 border border-green-100" : "bg-gray-50 border border-gray-100"
      }`}>
        <h4 className="text-sm font-semibold text-gray-900">{label}</h4>
        <p className={`text-xs mt-1 ${
          completed ? "text-green-600" : "text-gray-500"
        }`}>
          {formatDate(date)}
        </p>
      </div>
    </div>
  );
};

export default FileStatusDetail;
