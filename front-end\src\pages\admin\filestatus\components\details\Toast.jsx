import React, { useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheckCircle, faExclamationCircle, faTimes } from "@fortawesome/free-solid-svg-icons";

/**
 * Toast component for displaying notifications
 * @param {string} message - Toast message
 * @param {string} type - Toast type (success, error, warning, info)
 * @param {function} onClose - Function to close the toast
 * @param {number} duration - Duration in milliseconds before auto-closing
 */
const Toast = ({ message, type = "success", onClose, duration = 3000 }) => {
  // Auto-close after duration
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [onClose, duration]);

  // Get toast styles based on type
  const getToastStyles = () => {
    switch (type) {
      case "error":
        return {
          bg: "bg-red-100",
          text: "text-red-800",
          icon: faExclamationCircle,
          iconColor: "text-red-500"
        };
      case "warning":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-800",
          icon: faExclamationCircle,
          iconColor: "text-yellow-500"
        };
      case "info":
        return {
          bg: "bg-blue-100",
          text: "text-blue-800",
          icon: faCheckCircle,
          iconColor: "text-blue-500"
        };
      case "success":
      default:
        return {
          bg: "bg-green-100",
          text: "text-green-800",
          icon: faCheckCircle,
          iconColor: "text-green-500"
        };
    }
  };

  const styles = getToastStyles();

  return (
    <div className="fixed top-4 right-4 z-50 animate-fade-in-down">
      <div className={`${styles.bg} ${styles.text} px-4 py-3 rounded-lg shadow-md flex items-center max-w-md`}>
        <FontAwesomeIcon icon={styles.icon} className={`${styles.iconColor} mr-3`} />
        <div className="flex-grow">{message}</div>
        <button
          onClick={onClose}
          className="ml-4 text-gray-400 hover:text-gray-600 focus:outline-none"
          title="Close"
        >
          <FontAwesomeIcon icon={faTimes} />
        </button>
      </div>
    </div>
  );
};

export default Toast;
