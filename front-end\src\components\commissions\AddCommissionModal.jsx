import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUser,
  faPhone,
  faMoneyBillWave,
  faSearch,
  faFileInvoice,
  faTimes,
  faCheckCircle,
} from "@fortawesome/free-solid-svg-icons";
import CommissionModal from "./CommissionModal";
import { useGetAllApplicationsQuery, useGetAllApplicationsOfAgentQuery } from "../../services/CompanyAPIService";
import LoadingSpinner from "../common/LoadingSpinner";
import { getToken } from "../../services/LocalStorageService";
import { jwtDecode } from "jwt-decode";
import { showValidationError } from "../../utils/toastUtils";

const AddCommissionModal = ({ isOpen, onClose, onSubmit, initialData }) => {
  // Get user role from JWT token
  const [userRole, setUserRole] = useState("");

  useEffect(() => {
    const token = getToken();
    if (token) {
      try {
        const decodedToken = jwtDecode(token);
        const roles = decodedToken.roles || [];
        if (roles.includes("ROLE_SALES")) {
          setUserRole("AGENT");
        } else if (roles.includes("ROLE_OPERATIONS")) {
          setUserRole("OPERATIONS");
        } else if (roles.includes("ROLE_ADMIN")) {
          setUserRole("ADMIN");
        }
      } catch (error) {
        console.error("Error decoding token:", error);
      }
    }
  }, []);

  const [step, setStep] = useState(initialData && initialData.applicationId ? 2 : 1);
  const [formData, setFormData] = useState(initialData || {
    name: "",
    contactInfo: "",
    commissionAmount: "",
    applicationId: "",
  });
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch applications based on role
  const { data: adminApplications = [], isLoading: isAdminLoading } = useGetAllApplicationsQuery(
    undefined,
    { skip: userRole === "AGENT" }
  );
  const { data: agentApplications = [], isLoading: isAgentLoading } = useGetAllApplicationsOfAgentQuery(
    undefined,
    { skip: userRole !== "AGENT" }
  );

  const applications = userRole === "AGENT" ? agentApplications : adminApplications;
  const isLoading = userRole === "AGENT" ? isAgentLoading : isAdminLoading;

  // Filter applications based on search term
  const filteredApplications = applications.filter(
    (app) =>
      app.applicationId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.applicantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (app.quoteRefNumber && app.quoteRefNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (app.invoiceRefNumber && app.invoiceRefNumber.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Reset form when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setStep(1);
      setFormData(initialData || {
        name: "",
        contactInfo: "",
        commissionAmount: "",
        applicationId: "",
      });
      setSearchTerm("");
    }
  }, [isOpen, initialData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleNext = () => {
    if (validateStep1()) {
      setStep(2);
    }
  };

  const validateStep1 = () => {
    if (!formData.name.trim()) {
      showValidationError("Please enter a name");
      return false;
    }
    if (!formData.contactInfo.trim()) {
      showValidationError("Please enter contact information");
      return false;
    }
    if (!formData.commissionAmount || isNaN(formData.commissionAmount) || parseFloat(formData.commissionAmount) <= 0) {
      showValidationError("Please enter a valid commission amount");
      return false;
    }
    return true;
  };

  const handleSubmit = () => {
    if (!formData.applicationId) {
      showValidationError("Please select an application");
      return;
    }

    onSubmit({
      ...formData,
      commissionAmount: parseFloat(formData.commissionAmount),
    });
  };

  const handleSelectApplication = (applicationId) => {
    setFormData((prev) => ({
      ...prev,
      applicationId,
    }));
  };

  return (
    <CommissionModal
      isOpen={isOpen}
      onClose={onClose}
      title={step === 1 ? "Add External Commission" : "Select Application"}
    >
      {step === 1 ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              External Referral Name*
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faUser} className="text-gray-400" />
              </div>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="pl-10 w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter referral name"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Contact Information*
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faPhone} className="text-gray-400" />
              </div>
              <input
                type="text"
                name="contactInfo"
                value={formData.contactInfo}
                onChange={handleChange}
                className="pl-10 w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Phone or email"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Commission Amount*
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faMoneyBillWave} className="text-gray-400" />
              </div>
              <input
                type="number"
                name="commissionAmount"
                value={formData.commissionAmount}
                onChange={handleChange}
                className="pl-10 w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter amount"
                min="0"
                step="0.01"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={onClose}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md"
            >
              Cancel
            </button>
            <button
              onClick={handleNext}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
            >
              Next
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search applications..."
            />
          </div>

          {isLoading ? (
            <div className="py-10">
              <LoadingSpinner />
            </div>
          ) : filteredApplications.length === 0 ? (
            <div className="text-center py-10 text-gray-500">
              No applications found matching your search criteria.
            </div>
          ) : (
            <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-md">
              {filteredApplications.map((app) => (
                <div
                  key={app.applicationId}
                  className={`p-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer ${
                    formData.applicationId === app.applicationId ? "bg-blue-50" : ""
                  }`}
                  onClick={() => handleSelectApplication(app.applicationId)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium text-gray-800">{app.applicantName}</div>
                      <div className="text-sm text-gray-500">ID: {app.applicationId}</div>
                      {app.invoiceRefNumber && (
                        <div className="text-sm text-gray-500 flex items-center mt-1">
                          <FontAwesomeIcon icon={faFileInvoice} className="mr-1 text-blue-500" />
                          Invoice: {app.invoiceRefNumber}
                        </div>
                      )}
                    </div>
                    {formData.applicationId === app.applicationId && (
                      <div className="bg-blue-500 text-white rounded-full p-1">
                        <FontAwesomeIcon icon={faCheckCircle} />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="flex justify-between space-x-3 pt-4">
            <button
              onClick={() => setStep(1)}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md"
            >
              Back
            </button>
            <button
              onClick={handleSubmit}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
              disabled={!formData.applicationId}
            >
              Submit
            </button>
          </div>
        </div>
      )}
    </CommissionModal>
  );
};

export default AddCommissionModal;
