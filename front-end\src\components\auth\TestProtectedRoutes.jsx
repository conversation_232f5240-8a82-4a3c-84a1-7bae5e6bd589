import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { jwtDecode } from 'jwt-decode';
import { getToken } from '../../services/LocalStorageService';
import routes from '../../routes';

/**
 * A component to test protected routes
 */
const TestProtectedRoutes = () => {
  const [userRoles, setUserRoles] = useState([]);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    const token = getToken();
    if (token) {
      try {
        const decodedToken = jwtDecode(token);
        const roles = decodedToken.roles || [];
        setUserRoles(roles);
        setIsLoggedIn(true);
      } catch (error) {
        console.error('Error decoding token:', error);
        setIsLoggedIn(false);
      }
    } else {
      setIsLoggedIn(false);
    }
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Protected Routes Test</h1>
      
      {isLoggedIn ? (
        <div className="mb-6 p-4 bg-green-100 border border-green-300 rounded-md">
          <p className="text-green-700">
            <strong>Logged in</strong> with roles: {userRoles.join(', ')}
          </p>
        </div>
      ) : (
        <div className="mb-6 p-4 bg-yellow-100 border border-yellow-300 rounded-md">
          <p className="text-yellow-700">
            <strong>Not logged in.</strong> You need to log in to access protected routes.
          </p>
          <Link to={routes.login} className="text-blue-600 underline">
            Go to Login
          </Link>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="border rounded-md p-4">
          <h2 className="text-xl font-semibold mb-3">Admin Routes</h2>
          <p className="text-sm text-gray-600 mb-3">
            Requires <code className="bg-gray-100 px-1 rounded">ROLE_ADMIN</code>
          </p>
          <ul className="space-y-2">
            <li>
              <Link to={routes.adminDashboard} className="text-blue-600 hover:underline">
                Admin Dashboard
              </Link>
            </li>
            <li>
              <Link to={routes.adminEmployees} className="text-blue-600 hover:underline">
                Admin Employees
              </Link>
            </li>
            <li>
              <Link to={routes.adminSettings} className="text-blue-600 hover:underline">
                Admin Settings
              </Link>
            </li>
          </ul>
        </div>

        <div className="border rounded-md p-4">
          <h2 className="text-xl font-semibold mb-3">Agent Routes</h2>
          <p className="text-sm text-gray-600 mb-3">
            Requires <code className="bg-gray-100 px-1 rounded">ROLE_SALES</code> or <code className="bg-gray-100 px-1 rounded">ROLE_ADMIN</code>
          </p>
          <ul className="space-y-2">
            <li>
              <Link to={routes.agentDashboard} className="text-blue-600 hover:underline">
                Agent Dashboard
              </Link>
            </li>
            <li>
              <Link to={routes.agentLeads} className="text-blue-600 hover:underline">
                Agent Leads
              </Link>
            </li>
            <li>
              <Link to={routes.agentSettings} className="text-blue-600 hover:underline">
                Agent Settings
              </Link>
            </li>
          </ul>
        </div>

        <div className="border rounded-md p-4">
          <h2 className="text-xl font-semibold mb-3">Operations Routes</h2>
          <p className="text-sm text-gray-600 mb-3">
            Requires <code className="bg-gray-100 px-1 rounded">ROLE_OPERATIONS</code> or <code className="bg-gray-100 px-1 rounded">ROLE_ADMIN</code>
          </p>
          <ul className="space-y-2">
            <li>
              <Link to={routes.operationsDashboard} className="text-blue-600 hover:underline">
                Operations Dashboard
              </Link>
            </li>
            <li>
              <Link to={routes.operationsApplications} className="text-blue-600 hover:underline">
                Operations Applications
              </Link>
            </li>
            <li>
              <Link to={routes.operationsSettings} className="text-blue-600 hover:underline">
                Operations Settings
              </Link>
            </li>
          </ul>
        </div>
      </div>

      <div className="mt-6 p-4 bg-gray-100 rounded-md">
        <h2 className="text-lg font-semibold mb-2">Testing Instructions</h2>
        <p className="mb-2">
          Click on the links above to test if the route protection is working correctly. You should:
        </p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Be able to access routes that match your role</li>
          <li>Be redirected to the Access Denied page for routes that don't match your role</li>
        </ul>
      </div>
    </div>
  );
};

export default TestProtectedRoutes;
