package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.modules.authentication.models.RegisterRequestDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class AdminRegisterDTO {
    private RegisterRequestDTO registerRequestDTO;

    private String fullName;
    private String gender;
    private String email;
    private String phone;
    private String address;
}
