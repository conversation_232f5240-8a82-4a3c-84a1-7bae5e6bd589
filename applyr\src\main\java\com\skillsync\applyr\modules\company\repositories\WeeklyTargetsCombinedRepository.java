package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.WeeklyTargetCombined;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface WeeklyTargetsCombinedRepository extends JpaRepository<WeeklyTargetCombined, Long> {
    List<WeeklyTargetCombined> findAllByStartDateAfterAndEndDateBefore(LocalDateTime startDateAfter, LocalDateTime endDateBefore);

    List<WeeklyTargetCombined> findAllByStartDateBetweenAndEndDateBetween(LocalDateTime startDateAfter, LocalDateTime startDateBefore, LocalDateTime endDateAfter, LocalDateTime endDateBefore);

    List<WeeklyTargetCombined> findAllByStartDateLessThanEqualAndEndDateGreaterThanEqual(LocalDateTime startDateIsLessThan, LocalDateTime endDateIsGreaterThan);

    // Find targets that overlap with the given date range
    @Query("SELECT w FROM WeeklyTargetCombined w WHERE " +
           "(w.startDate <= :endDate AND w.endDate >= :startDate) OR " +
           "(w.startDate >= :startDate AND w.endDate <= :endDate)")
    List<WeeklyTargetCombined> findByDateRangeOverlap(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    // Find a specific weekly target by ID - using Optional for safety
    Optional<WeeklyTargetCombined> findById(long id);
}
