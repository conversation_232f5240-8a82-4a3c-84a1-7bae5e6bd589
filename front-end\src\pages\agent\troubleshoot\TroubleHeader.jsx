import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faThLarge, faTable } from "@fortawesome/free-solid-svg-icons";

const TroubleHeader = ({ selectedTab, setSelectedTab, viewMode, setViewMode, openModal }) => {
  return (
    <>
      <div className="flex flex-col md:flex-row justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-semibold text-[#3A3541] mb-2">
            Troubleshoot Tracker
          </h2>
          <p className="text-sm text-[#89868D]">
            Manage and track support questions and issues
          </p>
        </div>
        <div className="flex items-center gap-3 mt-4 md:mt-0">
          <div className="flex rounded-md border border-[#DBDCDE] overflow-hidden">
            <button
              onClick={() => setViewMode("card")}
              className={`flex items-center justify-center p-2 ${
                viewMode === "card"
                  ? "bg-[#6E39CB] text-white"
                  : "bg-white text-[#89868D] hover:bg-[#F4F5F9]"
              }`}
              title="Card View"
            >
              <FontAwesomeIcon icon={faThLarge} />
            </button>
            <button
              onClick={() => setViewMode("table")}
              className={`flex items-center justify-center p-2 ${
                viewMode === "table"
                  ? "bg-[#6E39CB] text-white"
                  : "bg-white text-[#89868D] hover:bg-[#F4F5F9]"
              }`}
              title="Table View"
            >
              <FontAwesomeIcon icon={faTable} />
            </button>
          </div>
          <button
            onClick={openModal}
            className="flex items-center justify-center gap-2 rounded-md bg-[#6E39CB] py-3 px-6 text-center font-medium text-white hover:bg-opacity-90"
          >
            <FontAwesomeIcon icon={faPlus} />
            Add New Question
          </button>
        </div>
      </div>
      <div className="bg-white rounded-t-lg border border-b-0 border-[#DBDCDE] mb-0">
        <nav className="flex" aria-label="Tabs">
          <button
            className={`py-4 px-6 text-sm font-medium ${
              selectedTab === "all"
                ? "text-[#6E39CB] border-b-2 border-[#6E39CB]"
                : "text-[#89868D] hover:text-[#3A3541]"
            }`}
            onClick={() => setSelectedTab("all")}
          >
            All Questions
          </button>
          <button
            className={`py-4 px-6 text-sm font-medium ${
              selectedTab === "assigned"
                ? "text-[#6E39CB] border-b-2 border-[#6E39CB]"
                : "text-[#89868D] hover:text-[#3A3541]"
            }`}
            onClick={() => setSelectedTab("assigned")}
          >
            Assigned to Me
            {selectedTab === "assigned" && (
              <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-[#6E39CB] rounded-full">
                Active
              </span>
            )}
          </button>
        </nav>
      </div>
    </>
  );
};

export default TroubleHeader;
