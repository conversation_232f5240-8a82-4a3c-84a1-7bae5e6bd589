import React, { useState, useEffect } from "react";
import ReactSelect from "react-select";
import { useGetLeadByPhoneQuery } from "../../services/CompanyAPIService";
import { showValidationError } from "../../utils/toastUtils";

const CreateApplicationLeadProfileModal = ({
  isOpen,
  onClose,
  onCreate,
  leadNumber,
  qualifications,
}) => {
  const [applicantName, setApplicantName] = useState("");
  const [applicantEmail, setApplicantEmail] = useState("");
  const [applicantPhone, setApplicantPhone] = useState("");
  const [applicantAddress, setApplicantAddress] = useState("");
  const [leadPhone, setLeadPhone] = useState("");
  const [otherInformation, setOtherInformation] = useState("");
  const [soldQualifications, setSoldQualifications] = useState([]);

  const totalPrice = soldQualifications.reduce((acc, curr) => {
    return acc + (parseFloat(curr.price) || 0);
  }, 0);

  // Avoid hooks being called conditionally
  const { data: lead, error, isLoading } = useGetLeadByPhoneQuery(leadNumber);

  if (!isOpen) return null;

  const addQualificationRow = () => {
    setSoldQualifications((prev) => [
      ...prev,
      { qualificationId: "", price: "" },
    ]);
  };

  const removeQualificationRow = (index) => {
    setSoldQualifications((prev) => {
      const copy = [...prev];
      copy.splice(index, 1);
      return copy;
    });
  };

  const updateQualificationRow = (index, field, value) => {
    setSoldQualifications((prev) => {
      const copy = [...prev];
      copy[index] = { ...copy[index], [field]: value };
      return copy;
    });
  };

  // Validation functions
  const validatePhone = (phoneNumber) => {
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    return cleanPhone.length >= 8 && cleanPhone.length <= 15;
  };

  const validateEmail = (emailAddress) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(emailAddress);
  };

  // Handle phone input - only allow numbers
  const handlePhoneChange = (e) => {
    const value = e.target.value;
    const cleanValue = value.replace(/[^\d\s\-\(\)]/g, '');
    setApplicantPhone(cleanValue);
  };

  const isFormValid =
    applicantName.trim() &&
    applicantEmail.trim() &&
    validateEmail(applicantEmail) &&
    applicantPhone.trim() &&
    validatePhone(applicantPhone) &&
    soldQualifications.length > 0 &&
    soldQualifications.every((sq) => parseFloat(sq.price) > 0);

  const handleSubmit = () => {
    if (!applicantName.trim()) {
      showValidationError("Please enter applicant name.");
      return;
    }
    if (!applicantEmail.trim()) {
      showValidationError("Please enter applicant email.");
      return;
    }
    if (!validateEmail(applicantEmail)) {
      showValidationError("Please enter a valid email address.");
      return;
    }
    if (!applicantPhone.trim()) {
      showValidationError("Please enter applicant phone number.");
      return;
    }
    if (!validatePhone(applicantPhone)) {
      showValidationError("Please enter a valid phone number (8-15 digits).");
      return;
    }
    if (soldQualifications.length === 0) {
      showValidationError("Please add at least one qualification.");
      return;
    }
    if (!soldQualifications.every((sq) => parseFloat(sq.price) > 0)) {
      showValidationError("Please provide a valid price for all qualifications.");
      return;
    }

    const finalData = {
      applicantName,
      applicantEmail,
      applicantPhone,
      applicantAddress,
      leadPhone,
      otherInformation,
      soldQualifications: soldQualifications.map((sq) => {
        // Find the qualification by ID instead of using array index
        const qualification = qualifications.find(q => q.id === sq.qualificationId);
        return {
          qualificationId: qualification ? qualification.qualificationId : sq.qualificationId,
          price: parseFloat(sq.price) || 0,
        };
      }),
    };

    onCreate(finalData);

    setApplicantName("");
    setApplicantEmail("");
    setApplicantPhone("");
    setApplicantAddress("");
    setLeadPhone("");
    setOtherInformation("");
    setSoldQualifications([]);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white w-full max-w-5xl mx-4 rounded-lg shadow-xl max-h-[90vh] overflow-hidden">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Create New Application</h2>
              <p className="text-sm text-gray-600 mt-1">Lead information is already selected</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-md hover:bg-gray-100 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="px-6 py-4 max-h-[calc(90vh-160px)] overflow-y-auto">
          <div className="space-y-6">
            {/* Applicant Information Section */}
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Applicant Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Applicant Name */}
                <div>
                  <label className="block text-gray-700 font-medium mb-2">
                    Applicant Name<span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={applicantName}
                    onChange={(e) => setApplicantName(e.target.value)}
                    placeholder="Enter applicant name"
                  />
                </div>

                {/* Applicant Email */}
                <div>
                  <label className="block text-gray-700 font-medium mb-2">
                    Applicant Email<span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="email"
                    className="w-full border border-gray-300 rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={applicantEmail}
                    onChange={(e) => setApplicantEmail(e.target.value)}
                    placeholder="Enter email address"
                  />
                </div>

                {/* Applicant Phone */}
                <div>
                  <label className="block text-gray-700 font-medium mb-2">
                    Applicant Phone<span className="text-red-500 ml-1">*</span>
                  </label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={applicantPhone}
                    onChange={handlePhoneChange}
                    placeholder="Enter phone number (numbers only)"
                  />
                </div>

                {/* Applicant Address */}
                <div>
                  <label className="block text-gray-700 font-medium mb-2">
                    Hardcopy Address <span className="text-gray-500 text-sm">(Optional)</span>
                  </label>
                  <textarea
                    className="w-full border border-gray-300 rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                    value={applicantAddress}
                    onChange={(e) => setApplicantAddress(e.target.value)}
                    rows={3}
                    placeholder="Enter hardcopy address (optional)"
                  />
                </div>
              </div>
            </div>

            {/* Lead Selection Section */}
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Lead Association</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-700 font-medium mb-2">
                    Select Lead<span className="text-red-500 ml-1">*</span>
                  </label>
                  <div className="flex space-x-3">
                    <div className="flex-1">
                      <ReactSelect
                        options={
                          lead
                            ? [
                                {
                                  label: `${lead.leadName} (${lead.phone})`,
                                  value: lead.phone,
                                },
                              ]
                            : []
                        }
                        value={
                          leadPhone
                            ? {
                                label: `${lead.leadName} (${lead.phone})`,
                                value: leadPhone,
                              }
                            : null
                        }
                        onChange={(selected) =>
                          setLeadPhone(selected ? selected.value : "")
                        }
                        isClearable
                        isSearchable
                        placeholder="Search or select a lead..."
                        className="react-select-container"
                        classNamePrefix="react-select"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        if (!lead) {
                          showValidationError("Lead information not available");
                          return;
                        }

                        // For Company type leads, copy email and phone
                        if (lead.companyName) {
                          setApplicantEmail(lead.email || "");
                          setApplicantPhone(lead.phone || "");
                        }
                        // For Individual type leads, copy name, email, phone, and address
                        else {
                          setApplicantName(lead.leadName || "");
                          setApplicantEmail(lead.email || "");
                          setApplicantPhone(lead.phone || "");
                          setApplicantAddress(lead.address || "");
                        }

                        // Set the lead phone regardless of type
                        setLeadPhone(lead.phone || "");
                      }}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                    >
                      Import Lead Info
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Other Information Section */}
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Additional Information</h3>
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Other Information <span className="text-gray-500 text-sm">(Optional)</span>
                </label>
                <textarea
                  className="w-full border border-gray-300 rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  value={otherInformation}
                  onChange={(e) => setOtherInformation(e.target.value)}
                  rows={3}
                  placeholder="Enter any additional information about the application"
                />
              </div>
            </div>

            {/* Qualifications Section */}
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-800">Qualifications</h3>
                <button
                  type="button"
                  onClick={addQualificationRow}
                  className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors font-medium"
                >
                  + Add Qualification
                </button>
              </div>

              {soldQualifications.length === 0 ? (
                <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                  <div className="text-gray-400 mb-2">
                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <p className="text-gray-500 font-medium">No qualifications added yet</p>
                  <p className="text-gray-400 text-sm">Click "Add Qualification" to get started</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {soldQualifications.map((sq, index) => {
                    const selectedQualification = qualifications.find(
                      (q) => q.id === sq.qualificationId
                    );

                    return (
                      <div
                        key={index}
                        className="flex flex-col lg:flex-row lg:items-center space-y-3 lg:space-y-0 lg:space-x-4 p-4 border border-gray-200 rounded-lg bg-gray-50"
                      >
                        <div className="flex-1">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Qualification</label>
                          <ReactSelect
                            options={qualifications.map((q) => ({
                              label: q.qualificationName,
                              value: q.id,
                            }))}
                            value={
                              selectedQualification
                                ? {
                                    label: selectedQualification.qualificationName,
                                    value: selectedQualification.id,
                                  }
                                : null
                            }
                            onChange={(selected) =>
                              updateQualificationRow(
                                index,
                                "qualificationId",
                                selected.value
                              )
                            }
                            isSearchable
                            placeholder="Search or select a qualification..."
                            className="react-select-container"
                            classNamePrefix="react-select"
                          />
                        </div>

                        <div className="w-full lg:w-40">
                          <label className="block text-sm font-medium text-gray-700 mb-1">Price ($)</label>
                          <input
                            type="number"
                            placeholder="0.00"
                            className="w-full border border-gray-300 rounded-lg p-3 transition-all duration-200 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                            value={sq.price}
                            onChange={(e) =>
                              updateQualificationRow(index, "price", e.target.value)
                            }
                          />
                        </div>

                        <div className="flex lg:flex-col justify-end">
                          <button
                            type="button"
                            className="text-red-600 hover:text-red-800 p-2 rounded-full hover:bg-red-50 transition-colors"
                            onClick={() => removeQualificationRow(index)}
                            title="Remove qualification"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    );
                  })}

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold text-blue-900">Total Price:</span>
                      <span className="text-2xl font-bold text-blue-900">${totalPrice.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                isFormValid
                  ? "bg-green-600 text-white hover:bg-green-700"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
              disabled={!isFormValid}
            >
              Create Application
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateApplicationLeadProfileModal;
