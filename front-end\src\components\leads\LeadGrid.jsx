import React from "react";
import LeadCard from "./LeadCard";

const LeadGrid = ({
  leads,
  onProfileRedirect,
  onStatusChange,
  onEditLead,
  isAdmin = false,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {[...Array(8)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 animate-pulse">
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="space-y-1">
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
              <div className="flex flex-col items-end space-y-2">
                <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                <div className="h-4 bg-gray-200 rounded w-12"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (leads.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-50 p-8 text-center">
        <p className="text-gray-500">No leads found matching your criteria.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {leads.map((lead, index) => (
        <LeadCard
          key={lead.phone || index}
          lead={lead}
          onProfileRedirect={onProfileRedirect}
          onStatusChange={onStatusChange}
          onEditLead={onEditLead}
          isAdmin={isAdmin}
        />
      ))}
    </div>
  );
};

export default LeadGrid;
