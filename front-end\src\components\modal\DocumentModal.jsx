// src/components/DocumentModal.jsx

import React from "react";

// Custom Modal Component
const DocumentModal = ({ isOpen, onClose, document }) => {
  if (!isOpen || !document) return null;

  const isPdf = document.fileLink.toLowerCase().endsWith(".pdf");

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-auto relative flex flex-col md:flex-row">
        {/* Document Display Section */}
        <div className="w-full md:w-2/3 p-6 border-b md:border-b-0 md:border-r">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-600 hover:text-gray-900 text-2xl"
            aria-label="Close Modal"
          >
            ✕
          </button>

          {/* Document Title */}
          <h2 className="text-xl font-bold mb-4">{document.documentName}</h2>

          {/* Document Content */}
          <div className="w-full h-[70vh]">
            {isPdf ? (
              <iframe
                src={document.fileLink}
                title={document.documentName}
                className="w-full h-full"
              >
                <p>
                  Unable to display PDF.{" "}
                  <a href={document.fileLink} target="_blank" rel="noopener noreferrer">
                    Download PDF
                  </a>
                </p>
              </iframe>
            ) : (
              <img
                src={document.fileLink}
                alt={document.documentName}
                className="w-full h-full object-contain"
              />
            )}
          </div>
        </div>

        {/* Feedback Section */}
        <div className="w-full md:w-1/3 p-6 bg-gray-50 overflow-auto">
          <h3 className="text-lg font-semibold mb-4">Feedback</h3>
          {document.issues && document.issues.length > 0 ? (
            <div className="space-y-4">
              {document.issues.map((issue, index) => (
                <div
                  key={index}
                  className="bg-white p-4 rounded-lg shadow-sm border"
                >
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-sm text-gray-700">
                      {issue.createdBy || "Admin"}
                    </span>
                    <span className="text-xs text-gray-500">
                      {issue.createdAt
                        ? new Date(issue.createdAt).toLocaleDateString()
                        : "Recent"}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{issue.description}</p>
                  {issue.status && (
                    <span
                      className={`inline-block mt-2 px-2 py-1 rounded text-xs ${
                        issue.status === "RESOLVED"
                          ? "bg-green-100 text-green-800"
                          : issue.status === "PENDING"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {issue.status}
                    </span>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">
              No feedback available
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentModal;
