import { toast } from 'react-toastify';

/**
 * Toast Utility Service
 * Provides consistent toast notifications with proper error message extraction
 */

/**
 * Extract error message from various error response structures
 * @param {Object} error - Error object from API response
 * @param {string} fallbackMessage - Default message if no specific error found
 * @returns {string} - Extracted or fallback error message
 */
export const extractErrorMessage = (error, fallbackMessage = 'An error occurred. Please try again.') => {
  // Handle RTK Query errors
  if (error?.data?.message) {
    return error.data.message;
  }
  
  // Handle nested error structures
  if (error?.error?.data?.message) {
    return error.error.data.message;
  }
  
  // Handle direct message property
  if (error?.message) {
    return error.message;
  }
  
  // Handle response data message
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  
  // Handle Spring Boot error responses
  if (error?.response?.data?.error) {
    return error.response.data.error;
  }
  
  // Handle validation errors array
  if (error?.data?.errors && Array.isArray(error.data.errors)) {
    return error.data.errors.join(', ');
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    return error;
  }
  
  return fallbackMessage;
};

/**
 * Show success toast notification
 * @param {string} message - Success message to display
 * @param {Object} options - Additional toast options
 */
export const showSuccessToast = (message, options = {}) => {
  toast.success(message, {
    position: "top-right",
    autoClose: 3000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    ...options
  });
};

/**
 * Show error toast notification with proper error extraction
 * @param {Object|string} error - Error object or message
 * @param {string} fallbackMessage - Default message if no specific error found
 * @param {Object} options - Additional toast options
 */
export const showErrorToast = (error, fallbackMessage = 'An error occurred. Please try again.', options = {}) => {
  const errorMessage = extractErrorMessage(error, fallbackMessage);
  
  toast.error(errorMessage, {
    position: "top-right",
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    ...options
  });
};

/**
 * Show warning toast notification
 * @param {string} message - Warning message to display
 * @param {Object} options - Additional toast options
 */
export const showWarningToast = (message, options = {}) => {
  toast.warning(message, {
    position: "top-right",
    autoClose: 4000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    ...options
  });
};

/**
 * Show info toast notification
 * @param {string} message - Info message to display
 * @param {Object} options - Additional toast options
 */
export const showInfoToast = (message, options = {}) => {
  toast.info(message, {
    position: "top-right",
    autoClose: 3000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    ...options
  });
};

/**
 * Show validation error toast (specific styling for form validation)
 * @param {string} message - Validation error message
 * @param {Object} options - Additional toast options
 */
export const showValidationError = (message, options = {}) => {
  toast.error(message, {
    position: "top-right",
    autoClose: 4000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    className: 'validation-error-toast',
    ...options
  });
};

/**
 * Show loading toast that can be updated
 * @param {string} message - Loading message
 * @returns {string} - Toast ID for updating
 */
export const showLoadingToast = (message = 'Processing...') => {
  return toast.loading(message, {
    position: "top-right",
    closeOnClick: false,
    pauseOnHover: false,
    draggable: false,
  });
};

/**
 * Update an existing toast (useful for loading states)
 * @param {string} toastId - ID of toast to update
 * @param {string} message - New message
 * @param {string} type - Toast type ('success', 'error', 'warning', 'info')
 * @param {Object} options - Additional options
 */
export const updateToast = (toastId, message, type = 'success', options = {}) => {
  toast.update(toastId, {
    render: message,
    type: type,
    isLoading: false,
    autoClose: type === 'error' ? 5000 : 3000,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    ...options
  });
};

/**
 * Dismiss a specific toast
 * @param {string} toastId - ID of toast to dismiss
 */
export const dismissToast = (toastId) => {
  toast.dismiss(toastId);
};

/**
 * Dismiss all toasts
 */
export const dismissAllToasts = () => {
  toast.dismiss();
};

// Common validation messages
export const VALIDATION_MESSAGES = {
  REQUIRED_FIELD: 'This field is required.',
  INVALID_EMAIL: 'Please enter a valid email address.',
  PASSWORD_MISMATCH: 'Passwords do not match.',
  INVALID_PHONE: 'Please enter a valid phone number.',
  INVALID_DATE: 'Please select a valid date.',
  INVALID_NUMBER: 'Please enter a valid number.',
  MIN_LENGTH: (min) => `Must be at least ${min} characters long.`,
  MAX_LENGTH: (max) => `Must be no more than ${max} characters long.`,
};

// Common success messages
export const SUCCESS_MESSAGES = {
  CREATED: 'Created successfully!',
  UPDATED: 'Updated successfully!',
  DELETED: 'Deleted successfully!',
  SAVED: 'Saved successfully!',
  COPIED: 'Copied to clipboard!',
  SENT: 'Sent successfully!',
};

// Common error messages
export const ERROR_MESSAGES = {
  GENERIC: 'An error occurred. Please try again.',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_FAILED: 'Please check your input and try again.',
};
