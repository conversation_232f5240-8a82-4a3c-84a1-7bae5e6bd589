import React, { useState } from "react";
import { useChangeQuoteStatusMutation, useChangeInvoiceStatusMutation } from "../../services/CompanyAPIService";

const EditReferenceModal = ({ isOpen, onClose, type, applicationId, currentRefNumber, onSuccess }) => {
  const [refNumber, setRefNumber] = useState(currentRefNumber || "");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // Use the appropriate mutation hook based on the type
  const [changeQuoteStatus, { isLoading: isQuoteLoading }] = useChangeQuoteStatusMutation();
  const [changeInvoiceStatus, { isLoading: isInvoiceLoading }] = useChangeInvoiceStatusMutation();

  const isLoading = isQuoteLoading || isInvoiceLoading;

  if (!isOpen) return null;

  const handleSubmit = async () => {
    if (!refNumber.trim()) {
      setError("Reference number cannot be empty");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const requestDTO = {
        applicationId,
        refNumber: refNumber.trim(),
        // Keep the current status
        status: type === "quote" ? "SENT" : "SENT" // Default to SENT if not provided
      };

      if (type === "quote") {
        await changeQuoteStatus(requestDTO).unwrap();
      } else if (type === "invoice") {
        await changeInvoiceStatus(requestDTO).unwrap();
      }

      // Call onSuccess callback if provided
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess();
      }

      onClose();
    } catch (err) {
      console.error(`Failed to update ${type} reference number:`, err);
      setError(err.data?.message || `Failed to update ${type} reference number. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white w-full max-w-md p-6 rounded shadow-lg">
        <div className="sticky top-0 bg-white z-10 p-4">
          <h2 className="text-xl font-bold mb-4">
            Edit {type === "quote" ? "Quote" : "Invoice"} Reference
          </h2>
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
        </div>

        {/* Reference Number Input */}
        <div className="mb-4">
          <label className="block font-semibold text-sm mb-1">
            Reference Number
          </label>
          <input
            type="text"
            value={refNumber}
            onChange={(e) => setRefNumber(e.target.value)}
            className="border p-2 rounded w-full"
            placeholder={`Enter ${type} reference number`}
          />
        </div>

        <div className="flex justify-end mt-4">
          <button
            onClick={() => onClose()}
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded mr-2"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || isSubmitting}
            className={`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded ${
              isLoading || isSubmitting ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isLoading || isSubmitting ? "Processing..." : "Update"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditReferenceModal;
