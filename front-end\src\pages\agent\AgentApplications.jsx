import React, { useState, useEffect } from "react";
import logo from "../../assets/Logo.png";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { FaCommentDots } from "react-icons/fa";

import { faFileExport, faPlus } from "@fortawesome/free-solid-svg-icons";
import ApplicationCommentsDrawer from "../admin/applications/components/ApplicationCommentsDrawer";

import editIcon from "../../assets/edit.png";
import detailsIcon from "../../assets/details.png";
import deleteIcon from "../../assets/delete.png";
import {
  useGetAllApplicationsOfAgentQuery,
  useCreateApplicationMutation,
  useChangeApplicationStatusMutation,
  useUpdateApplicationQuoteMutation,
  useUpdateApplicationInvoiceMutation,
  useGetAgentLeadsQuery,
  useUpdateApplicationPaymentStatusMutation,
  useUpdateApplicationPaidAmountMutation,
} from "../../services/CompanyAPIService";

import { useGetQualificationsQuery } from "../../services/SalesAPIService";

import MultiStepCreateApplicationModal from "../../components/modal/MultiStepCreateApplicationModal";
import { showErrorToast, showSuccessToast } from "../../utils/toastUtils";

const AllApplicationsScreen = () => {
  const [filter, setFilter] = useState("");
  const [selectedLeadFilter, setSelectedLeadFilter] = useState("");
  const [selectedStatusFilter, setSelectedStatusFilter] = useState("");
  const [selectedCreatedByFilter, setSelectedCreatedByFilter] = useState([]);
  const [selectedQualificationFilter, setSelectedQualificationFilter] = useState("");
  const [sortConfig, setSortConfig] = useState({ key: 'createdOn', direction: 'desc' });
  const [isLoadingScreen, setIsLoadingScreen] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const [selectedPaymentStatusFilter, setSelectedPaymentStatusFilter] =
    useState("");

  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState("");
  const [paymentApplication, setPaymentApplication] = useState(null);

  // Date filter states
  const [dateFilterType, setDateFilterType] = useState("all");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState(null);

  // Comments drawer state
  const [isCommentsDrawerOpen, setIsCommentsDrawerOpen] = useState(false);
  const [selectedApplicationForComments, setSelectedApplicationForComments] = useState(null);

  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const [updateModalType, setUpdateModalType] = useState("");
  const [selectedApplicationForUpdate, setSelectedApplicationForUpdate] =
    useState(null);
  const [referenceNumber, setReferenceNumber] = useState("");

  // NEW: Tab state
  const [selectedTab, setSelectedTab] = useState("all"); // "all", "pending", "closed"

  const [isDropdownVisible, setIsDropdownVisible] = useState(false); // Track dropdown visibility

  const navigate = useNavigate();

  const {
    data: applicationsData = [],
    isLoading: isAppsLoading,
    refetch,
  } = useGetAllApplicationsOfAgentQuery();

  const [createApplication] = useCreateApplicationMutation();
  const [changeApplicationStatus] = useChangeApplicationStatusMutation();
  const [updateApplicationQuote] = useUpdateApplicationQuoteMutation();
  const [updateApplicationInvoice] = useUpdateApplicationInvoiceMutation();
  const [updateApplicationPaymentStatus] =
    useUpdateApplicationPaymentStatusMutation();
  const [updateApplicationPaidAmount] =
    useUpdateApplicationPaidAmountMutation();

  const { data: agentLeads = [] } = useGetAgentLeadsQuery();
  const { data: qualificationsData = [] } = useGetQualificationsQuery();

  const [applications, setApplications] = useState([]);

  useEffect(() => {
    setIsLoadingScreen(isAppsLoading);
  }, [isAppsLoading]);

  useEffect(() => {
    if (applicationsData && Array.isArray(applicationsData)) {
      setApplications(applicationsData);
    }
  }, [applicationsData]);

  // Distinct dropdown options
  const distinctLeadNames = Array.from(
    new Set(applications.map((app) => app.lead?.leadName).filter(Boolean))
  );
  const distinctStatuses = Array.from(
    new Set(applications.map((app) => app.status).filter(Boolean))
  );
  const distinctCreatedBy = Array.from(
    new Set(applications.map((app) => app.createdBy?.fullName).filter(Boolean))
  );

  const paymentStatusOptions = [
    "PENDING",
    "PARTIALLY_PAID",
    "FULLY_PAID",
    "INVOICE_EXPIRED",
  ];

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return (
        <svg className="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
        </svg>
      );
    }
    return sortConfig.direction === 'asc' ? (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
      </svg>
    ) : (
      <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
      </svg>
    );
  };

  // Get payment status color
  const getPaymentStatusColor = (status) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "PARTIALLY_PAID":
        return "bg-blue-100 text-blue-800";
      case "FULLY_PAID":
        return "bg-green-100 text-green-800";
      case "INVOICE_EXPIRED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get application status color
  const getApplicationStatusColor = (status) => {
    switch (status) {
      case "DOCUMENT_PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "QUOTE_RAISED":
        return "bg-blue-100 text-blue-800";
      case "INVOICE_RAISED":
        return "bg-purple-100 text-purple-800";
      case "IN_PROGRESS":
        return "bg-indigo-100 text-indigo-800";
      case "SOFT_COPY_READY":
      case "HARD_COPY_READY":
        return "bg-orange-100 text-orange-800";
      case "SOFT_COPY_SENT":
      case "HARD_COPY_SENT":
        return "bg-green-100 text-green-800";
      case "FALLOUT":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handlePaymentStatusChange = async (application, newStatus) => {
    if (newStatus === "PARTIALLY_PAID") {
      setPaymentApplication(application);
      setPaymentModalOpen(true);
    } else {
      try {
        await updateApplicationPaymentStatus({
          applicationId: application.applicationId,
          status: newStatus,
        }).unwrap();
        refetch();
      } catch (error) {
        console.error("Error updating payment status:", error);
        showErrorToast(error, "Unable to update payment status. Please try again later.");
      }
    }
  };

  // Determine which statuses are considered "closed"
  const closedStatuses = [
    "CLOSED",
    "SOFT_COPY_READY",
    "SOFT_COPY_SENT",
    "HARD_COPY_READY",
    "HARD_COPY_SENT",
  ];

  // Filter logic including new "lastWeek" filter
  const filteredApplications = applications
    // Text filter
    .filter((app) => {
      if (!filter) return true;
      const lowerFilter = filter.toLowerCase();
      return (
        (app.applicantName?.toLowerCase() || "").includes(lowerFilter) ||
        (app.applicantEmail?.toLowerCase() || "").includes(lowerFilter) ||
        (app.applicantPhone?.toLowerCase() || "").includes(lowerFilter) ||
        (app.leadPhone?.toLowerCase() || "").includes(lowerFilter) ||
        (String(app.totalPrice)?.toLowerCase() || "").includes(lowerFilter) ||
        (app.status?.toLowerCase() || "").includes(lowerFilter) ||
        (app.soldQualifications || []).some(qual =>
          (qual.qualificationName?.toLowerCase() || "").includes(lowerFilter) ||
          (qual.qualificationId?.toLowerCase() || "").includes(lowerFilter)
        )
      );
    })
    // Lead filter
    .filter((app) => {
      if (!selectedLeadFilter) return true;
      return app.lead?.leadName === selectedLeadFilter;
    })
    // Status filter
    .filter((app) => {
      if (!selectedStatusFilter) return true;
      return app.status === selectedStatusFilter;
    })
    // Created By filter
    .filter((app) => {
      if (!selectedCreatedByFilter.length) return true; // No filter applied, show all
      return selectedCreatedByFilter.includes(app.createdBy?.fullName);
    })

    // Payment status filter
    .filter((app) => {
      if (!selectedPaymentStatusFilter) return true;
      return app.paidStatus === selectedPaymentStatusFilter;
    })
    // Qualification filter
    .filter((app) => {
      if (!selectedQualificationFilter) return true;
      return (app.soldQualifications || []).some(qual =>
        qual.qualificationName?.toLowerCase().includes(selectedQualificationFilter.toLowerCase()) ||
        qual.qualificationId?.toLowerCase().includes(selectedQualificationFilter.toLowerCase())
      );
    })
    // Date filter
    .filter((app) => {
      // If "All Dates" is selected, don't filter by date
      if (dateFilterType === "all") return true;

      let filterStartDate, filterEndDate;

      if (dateFilterType === "today") {
        // Today filter
        const now = new Date();
        filterStartDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          0,
          0,
          0,
          0
        );
        filterEndDate = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          23,
          59,
          59,
          999
        );
      } else if (dateFilterType === "thisWeek") {
        const now = new Date();
        const dayOfWeek = now.getDay();
        const diffToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
        filterStartDate = new Date(now);
        filterStartDate.setDate(now.getDate() - diffToMonday);
        filterStartDate.setHours(0, 0, 0, 0);
        filterEndDate = new Date(filterStartDate);
        filterEndDate.setDate(filterStartDate.getDate() + 6);
        filterEndDate.setHours(23, 59, 59, 999);
      } else if (dateFilterType === "lastWeek") {
        const now = new Date();
        const dayOfWeek = now.getDay();
        const diffToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
        // Current week's Monday
        const currentMonday = new Date(now);
        currentMonday.setDate(now.getDate() - diffToMonday);
        currentMonday.setHours(0, 0, 0, 0);
        // Last week's Monday and Sunday
        filterStartDate = new Date(currentMonday);
        filterStartDate.setDate(currentMonday.getDate() - 7);
        filterEndDate = new Date(currentMonday);
        filterEndDate.setDate(currentMonday.getDate() - 1);
        filterEndDate.setHours(23, 59, 59, 999);
      } else if (dateFilterType === "thisMonth") {
        const now = new Date();
        filterStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
        filterEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        filterEndDate.setHours(23, 59, 59, 999);
      } else if (dateFilterType === "custom") {
        // If no startDate/endDate is chosen, ignore date filter
        if (!startDate && !endDate) return true;
        if (startDate) {
          filterStartDate = new Date(startDate);
          filterStartDate.setHours(0, 0, 0, 0);
        }
        if (endDate) {
          filterEndDate = new Date(endDate);
          filterEndDate.setHours(23, 59, 59, 999);
        }
      }

      // If app has no creation date, don't filter it out
      if (!app.createdAt) return true;

      const appDate = new Date(app.createdAt);
      if (filterStartDate && appDate < filterStartDate) return false;
      if (filterEndDate && appDate > filterEndDate) return false;
      return true;
    })
    // NEW: Tab-based filter (pending vs closed vs failed)
    .filter((app) => {
      if (selectedTab === "pending") {
        return !closedStatuses.includes(app.status) && app.status !== "FALLOUT";
      } else if (selectedTab === "closed") {
        return closedStatuses.includes(app.status);
      } else if (selectedTab === "failed") {
        return app.status === "FALLOUT";
      }
      return true; // "all"
    })
    // Sort by selected column
    .sort((a, b) => {
      if (!sortConfig.key) return 0;

      let aValue, bValue;

      switch (sortConfig.key) {
        case 'applicant':
          aValue = (a.applicantName || "").toLowerCase();
          bValue = (b.applicantName || "").toLowerCase();
          break;
        case 'createdOn':
          aValue = new Date(a.createdAt || 0);
          bValue = new Date(b.createdAt || 0);
          break;
        case 'totalPrice':
          aValue = a.totalPrice || 0;
          bValue = b.totalPrice || 0;
          break;
        case 'duePayment':
          aValue = (a.totalPrice || 0) - (a.paidAmount || 0);
          bValue = (b.totalPrice || 0) - (b.paidAmount || 0);
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });

  const handleStatusChange = async (applicationId, newStatus) => {
    if (newStatus === "QUOTE_RAISED" || newStatus === "INVOICE_RAISED") {
      const appForUpdate = applications.find(
        (app) => app.applicationId === applicationId
      );
      setSelectedApplicationForUpdate(appForUpdate);
      setUpdateModalType(newStatus === "QUOTE_RAISED" ? "quote" : "invoice");
      setIsUpdateModalOpen(true);
      return;
    }
    try {
      await changeApplicationStatus({
        applicationId,
        status: newStatus,
      }).unwrap();
      refetch();
    } catch (error) {
      console.error("Error changing status:", error);
      showErrorToast(error, "Unable to change status. Please try again later.");
    }
  };

  const handleReferenceSubmit = async () => {
    if (!selectedApplicationForUpdate || !referenceNumber) return;
    try {
      if (updateModalType === "quote") {
        await updateApplicationQuote({
          applicationId: selectedApplicationForUpdate.applicationId,
          quote: referenceNumber,
        }).unwrap();
      } else if (updateModalType === "invoice") {
        await updateApplicationInvoice({
          applicationId: selectedApplicationForUpdate.applicationId,
          invoice: referenceNumber,
        }).unwrap();
      }
      setIsUpdateModalOpen(false);
      setReferenceNumber("");
      refetch();
    } catch (error) {
      console.error("Error updating reference number:", error);
      showErrorToast(error, "Failed to update reference number. Please try again.");
    }
  };

  const handleCreateApplication = async (formValues) => {
    try {
      const newApp = await createApplication(formValues).unwrap();
      setApplications((prev) => [newApp, ...prev]);
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error("Error creating application:", error);
      showErrorToast(error, "Unable to create application. Please try again.");
    }
  };

  const openDrawer = (application) => {
    setSelectedApplication(application);
    setIsDrawerOpen(true);
  };

  const closeDrawer = () => {
    setSelectedApplication(null);
    setIsDrawerOpen(false);
  };

  const handleAddCreator = (name) => {
    if (!selectedCreatedByFilter.includes(name)) {
      setSelectedCreatedByFilter((prev) => [...prev, name]);
    }
    setIsDropdownVisible(false); // Hide dropdown after selection
  };

  const handleRemoveCreator = (name) => {
    setSelectedCreatedByFilter((prev) =>
      prev.filter((creator) => creator !== name)
    );
  };

  const toggleDropdown = () => {
    setIsDropdownVisible((prev) => !prev);
  };

  if (isLoadingScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-50 z-50">
        <div className="text-center">
          <img
            src={logo}
            alt="Loading Logo"
            className="mx-auto mb-4 w-48 h-auto"
          />
          <p className="mt-2 text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 mb-1">
            Applications Management
          </h1>
          <p className="text-sm text-gray-500">
            Manage and track all applications in the system
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="flex items-center bg-white border border-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors shadow-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-gray-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Export
          </button>
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="flex items-center bg-[#6E39CB] text-white px-4 py-2 rounded-lg hover:bg-[#5E2CB8] transition-colors shadow-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
            Create Application
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white rounded-lg border border-gray-100 p-6 shadow-sm">
          <div className="flex items-center">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-[#6E39CB]"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">
                Total Applications
              </p>
              <p className="text-2xl font-semibold text-gray-900">
                {applications.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-100 p-6 shadow-sm">
          <div className="flex items-center">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-[#6E39CB]"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">
                Pending Applications
              </p>
              <p className="text-2xl font-semibold text-gray-900">
                {applications.filter((app) => app.status === "PENDING").length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-100 p-6 shadow-sm">
          <div className="flex items-center">
            <div className="bg-[#F4F5F9] p-3 rounded-full mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-[#6E39CB]"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">
                Closed Applications
              </p>
              <p className="text-2xl font-semibold text-gray-900">
                {
                  applications.filter((app) =>
                    closedStatuses.includes(app.status)
                  ).length
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm mb-6">
        <div className="border-b border-gray-100 px-6 py-4">
          <nav className="flex flex-wrap -mb-px space-x-6" aria-label="Tabs">
            <button
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                selectedTab === "all"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap`}
              onClick={() => setSelectedTab("all")}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              All Applications
            </button>
            <button
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                selectedTab === "pending"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap`}
              onClick={() => setSelectedTab("pending")}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Pending Applications
            </button>
            <button
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                selectedTab === "closed"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap`}
              onClick={() => setSelectedTab("closed")}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Closed Applications
            </button>
            <button
              className={`inline-flex items-center px-4 py-3 border-b-2 font-medium text-sm ${
                selectedTab === "failed"
                  ? "border-[#6E39CB] text-[#6E39CB]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap`}
              onClick={() => setSelectedTab("failed")}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
              Failed Applications
            </button>
          </nav>
        </div>

        {/* Filters section */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="relative">
              <input
                type="text"
                placeholder="Search applications..."
                onChange={(e) => setFilter(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
              <svg
                className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>

            <div className="relative">
              <input
                type="text"
                placeholder="Search by qualification..."
                value={selectedQualificationFilter}
                onChange={(e) => setSelectedQualificationFilter(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
              <svg
                className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>

            <div>
              <select
                value={selectedLeadFilter}
                onChange={(e) => setSelectedLeadFilter(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              >
                <option value="">All Leads</option>
                {distinctLeadNames.map((name) => (
                  <option key={name} value={name}>
                    {name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <select
                value={selectedStatusFilter}
                onChange={(e) => setSelectedStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              >
                <option value="">All App Statuses</option>
                {distinctStatuses.map((status) => (
                  <option key={status} value={status}>
                    {status}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <select
                value={selectedPaymentStatusFilter}
                onChange={(e) =>
                  setSelectedPaymentStatusFilter(e.target.value)
                }
                className="px-3 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              >
                <option value="">All Payment Statuses</option>
                {paymentStatusOptions.map((status) => (
                  <option key={status} value={status}>
                    {status}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <select
                value={dateFilterType}
                onChange={(e) => setDateFilterType(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              >
                <option value="all">All Dates</option>
                <option value="today">Today</option>
                <option value="thisWeek">This Week</option>
                <option value="lastWeek">Last Week</option>
                <option value="thisMonth">This Month</option>
                <option value="custom">Custom Range</option>
              </select>
            </div>

            <div></div> {/* Empty div for spacing */}
          </div>

          <div className="flex flex-wrap items-center justify-between">
            <div className="relative mb-4 md:mb-0">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Filter by creator..."
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full md:w-64 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  onFocus={toggleDropdown}
                />
                <svg
                  className="absolute left-3 top-2.5 h-5 w-5 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
              </div>

              {/* Show selected names inside the input as tags */}
              {selectedCreatedByFilter.length > 0 && (
                <div className="absolute left-0 right-0 mt-2 bg-white p-2 border border-gray-200 rounded-lg shadow-sm z-10">
                  {selectedCreatedByFilter.map((name) => (
                    <span
                      key={name}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full bg-[#F4F5F9] text-xs font-medium text-[#6E39CB] mr-2 mb-2"
                    >
                      {name}
                      <button
                        onClick={() => handleRemoveCreator(name)}
                        className="ml-1.5 text-[#6E39CB] hover:text-[#5E2CB8]"
                      >
                        &times;
                      </button>
                    </span>
                  ))}
                </div>
              )}

              {/* Dropdown for selecting creators */}
              {isDropdownVisible && (
                <div className="absolute left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-60 overflow-y-auto">
                  {distinctCreatedBy.length > 0 ? (
                    distinctCreatedBy
                      .filter((name) =>
                        name.toLowerCase().includes(filter.toLowerCase())
                      )
                      .map((name) => (
                        <div
                          key={name}
                          className="p-3 hover:bg-[#F4F5F9] cursor-pointer border-b border-gray-100 last:border-0"
                          onClick={() => handleAddCreator(name)}
                        >
                          {name}
                        </div>
                      ))
                  ) : (
                    <div className="p-3 text-gray-500 text-center">
                      No creators found
                    </div>
                  )}
                </div>
              )}
            </div>

            {dateFilterType === "custom" && (
              <div className="flex space-x-3 mb-4 md:mb-0">
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
                <span className="self-center text-gray-500">to</span>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
              </div>
            )}

            <div className="text-sm text-gray-600">
              Click column headers to sort
            </div>
          </div>
        </div>
      </div>

      {/* Applications Table */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 table-fixed">
            <thead>
              <tr className="bg-[#F4F5F9]">
                <th className="w-[10%] px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice #
                </th>
                <th className="w-[10%] px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quote #
                </th>
                <th className="w-[15%] px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <button
                    onClick={() => handleSort('applicant')}
                    className="flex items-center hover:text-gray-700 transition-colors"
                  >
                    Applicant
                    {getSortIcon('applicant')}
                  </button>
                </th>
                <th className="w-[10%] px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <button
                    onClick={() => handleSort('createdOn')}
                    className="flex items-center hover:text-gray-700 transition-colors"
                  >
                    Created On
                    {getSortIcon('createdOn')}
                  </button>
                </th>
                <th className="w-[10%] px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <button
                    onClick={() => handleSort('totalPrice')}
                    className="flex items-center hover:text-gray-700 transition-colors"
                  >
                    Total Price
                    {getSortIcon('totalPrice')}
                  </button>
                </th>
                <th className="w-[10%] px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <button
                    onClick={() => handleSort('duePayment')}
                    className="flex items-center hover:text-gray-700 transition-colors"
                  >
                    Due Payment
                    {getSortIcon('duePayment')}
                  </button>
                </th>
                <th className="w-[10%] px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase">
                  Payment Status
                </th>
                <th className="w-[10%] px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  App Status
                </th>
                <th className="w-[5%] px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredApplications.length === 0 ? (
                <tr>
                  <td colSpan={9} className="px-6 py-8 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-12 w-12 text-gray-300 mb-3"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <p className="text-gray-500">No applications found.</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredApplications.map((app) => {
                  const duePayment =
                    (app.totalPrice || 0) - (app.paidAmount || 0);
                  return (
                    <tr
                      key={app.applicationId}
                      className="hover:bg-[#F4F5F9] transition-colors"
                    >
                      <td className="px-6 py-4 text-sm">
                        <div
                          className="truncate max-w-[120px]"
                          title={app.invoiceRefNumber || "N/A"}
                        >
                          {app.invoiceRefNumber ? (
                            <span className="font-medium text-gray-900">
                              {app.invoiceRefNumber}
                            </span>
                          ) : (
                            <span className="text-gray-400 italic">N/A</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm">
                        <div
                          className="truncate max-w-[120px]"
                          title={app.quoteRefNumber || "N/A"}
                        >
                          {app.quoteRefNumber ? (
                            <span className="font-medium text-gray-900">
                              {app.quoteRefNumber}
                            </span>
                          ) : (
                            <span className="text-gray-400 italic">N/A</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm">
                        <div className="flex items-center">
                          <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-2 flex-shrink-0">
                            <span className="text-[#6E39CB] font-medium">
                              {app.applicantName
                                ? app.applicantName.charAt(0).toUpperCase()
                                : "?"}
                            </span>
                          </div>
                          <div
                            className="truncate max-w-[150px]"
                            title={app.applicantName}
                          >
                            <span className="font-medium text-gray-900">
                              {app.applicantName}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-700">
                        {app.createdAt
                          ? new Date(app.createdAt).toLocaleDateString(
                              "en-GB",
                              {
                                day: "numeric",
                                month: "short",
                                year: "numeric",
                              }
                            )
                          : "N/A"}
                      </td>
                      <td className="px-6 py-4 text-sm">
                        <span className="font-medium text-[#6E39CB]">
                          ${app.totalPrice ? app.totalPrice.toFixed(2) : "0.00"}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm">
                        <span
                          className={`font-medium ${
                            duePayment > 0 ? "text-red-600" : "text-green-600"
                          }`}
                        >
                          ${duePayment.toFixed(2)}
                        </span>
                      </td>
                      {/* Payment Status */}
                      <td className="px-6 py-4 text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(app.paidStatus)}`}>
                          {app.paidStatus || "PENDING"}
                        </span>
                      </td>
                      {/* Application Status */}
                      <td className="px-6 py-4 text-sm">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getApplicationStatusColor(app.status)}`}>
                          {app.status ? app.status.replace(/_/g, " ") : "N/A"}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-center">
                        <div className="flex justify-center space-x-1">
                          <button
                            onClick={() =>
                              navigate(
                                `/agent/application/profile/${app.applicationId}`
                              )
                            }
                            className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100"
                            title="View Details"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              />
                            </svg>
                          </button>
                          <button
                            onClick={() => {
                              setSelectedApplicationForComments(app);
                              setIsCommentsDrawerOpen(true);
                            }}
                            className="text-[#6E39CB] hover:text-[#5E2CB8] p-1 rounded-full hover:bg-[#F4F5F9]"
                            title="View & Add Comments"
                          >
                            <FaCommentDots size={14} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment Modal (for Partial Payment) */}
      {paymentModalOpen && paymentApplication && (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                Partial Payment
              </h2>
              <button
                onClick={() => {
                  setPaymentModalOpen(false);
                  setPaymentAmount("");
                }}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Total Price
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500">$</span>
                </div>
                <input
                  type="text"
                  value={
                    paymentApplication.totalPrice ||
                    paymentApplication.soldQualifications?.reduce(
                      (acc, sq) => acc + (sq.price || 0),
                      0
                    ) ||
                    0
                  }
                  readOnly
                  className="pl-8 pr-4 py-2 w-full border border-gray-200 rounded-lg bg-gray-50 focus:outline-none"
                />
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Amount
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500">$</span>
                </div>
                <input
                  type="number"
                  value={paymentAmount}
                  onChange={(e) => setPaymentAmount(e.target.value)}
                  placeholder="Enter amount"
                  className="pl-8 pr-4 py-2 w-full border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setPaymentModalOpen(false);
                  setPaymentAmount("");
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={async () => {
                  try {
                    await updateApplicationPaidAmount({
                      applicationId: paymentApplication.applicationId,
                      paid: paymentAmount,
                    }).unwrap();

                    await updateApplicationPaymentStatus({
                      applicationId: paymentApplication.applicationId,
                      status: "PARTIALLY_PAID",
                    }).unwrap();

                    setPaymentModalOpen(false);
                    setPaymentAmount("");
                    refetch();
                  } catch (error) {
                    console.error("Error updating partial payment:", error);
                    showErrorToast(error, "Failed to update partial payment. Try again.");
                  }
                }}
                className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors"
              >
                Submit Payment
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Application Modal */}
      <MultiStepCreateApplicationModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreate={handleCreateApplication}
        qualifications={qualificationsData}
      />

      {/* Side Drawer for Application Details */}
      {isDrawerOpen && selectedApplication && (
        <div className="fixed inset-0 flex justify-end z-50">
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={closeDrawer}
          />
          <div className="relative w-full max-w-md bg-white h-full shadow-xl flex flex-col">
            <div className="px-4 py-4 border-b flex justify-between items-center">
              <h2 className="text-lg font-semibold">Application Details</h2>
              <button
                onClick={closeDrawer}
                className="text-gray-600 hover:text-gray-800"
              >
                X
              </button>
            </div>
            <div className="p-4 overflow-y-auto flex-1">
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">
                  Applicant Name
                </h3>
                <p>{selectedApplication.applicantName}</p>
              </div>
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">Email</h3>
                <p>{selectedApplication.applicantEmail}</p>
              </div>
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">Address</h3>
                <p>{selectedApplication.applicantAddress ?? "N/A"}</p>
              </div>
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">
                  Total Price
                </h3>
                <p>{selectedApplication.totalPrice || 0}</p>
              </div>
              {/* Show Payment Info */}
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">
                  Payment Status
                </h3>
                <p>{selectedApplication.paidStatus || "N/A"}</p>
              </div>
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">
                  Paid Amount
                </h3>
                <p>${selectedApplication.paidAmount || 0}</p>
              </div>
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">
                  Quote Reference Number
                </h3>
                <p>{selectedApplication.quoteRefNumber ?? "N/A"}</p>
              </div>
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">
                  Invoice Reference Number
                </h3>
                <p>{selectedApplication.invoiceRefNumber ?? "N/A"}</p>
              </div>
              {/* Lead Info */}
              {selectedApplication.lead && (
                <div className="mb-4">
                  <h3 className="text-sm font-semibold text-gray-500">
                    Lead Information
                  </h3>
                  <p>
                    <span className="font-semibold">Name: </span>
                    {selectedApplication.lead.leadName}
                  </p>
                  <p>
                    <span className="font-semibold">Company: </span>
                    {selectedApplication.lead.companyName || "N/A"}
                  </p>
                  <p>
                    <span className="font-semibold">Phone: </span>
                    {selectedApplication.lead.phone}
                  </p>
                  <p>
                    <span className="font-semibold">Email: </span>
                    {selectedApplication.lead.email}
                  </p>
                </div>
              )}
              {/* Created By Section */}
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">
                  Created By
                </h3>
                <p>
                  <span className="font-semibold">Username: </span>
                  {selectedApplication.createdBy?.username || "N/A"}
                </p>
                <p>
                  <span className="font-semibold">Full Name: </span>
                  {selectedApplication.createdBy?.fullName || "N/A"}
                </p>
                <p>
                  <span className="font-semibold">Email: </span>
                  {selectedApplication.createdBy?.email || "N/A"}
                </p>
                <p>
                  <span className="font-semibold">Phone: </span>
                  {selectedApplication.createdBy?.phone || "N/A"}
                </p>
                <p>
                  <span className="font-semibold">Role: </span>
                  {selectedApplication.createdBy?.role || "N/A"}
                </p>
              </div>
              {/* Created At */}
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">
                  Created At
                </h3>
                <p>
                  {selectedApplication.createdAt
                    ? new Date(
                        selectedApplication.createdAt
                      ).toLocaleDateString("en-GB", {
                        day: "numeric",
                        month: "long",
                        year: "numeric",
                      })
                    : "N/A"}
                </p>
              </div>
              {/* Sold Qualifications */}
              <div className="mb-4">
                <h3 className="text-sm font-semibold text-gray-500">
                  Sold Qualifications
                </h3>
                {selectedApplication.soldQualifications?.length > 0 ? (
                  <ul className="list-disc list-inside">
                    {selectedApplication.soldQualifications.map((sq, index) => (
                      <li key={index} className="mb-1">
                        {sq.qualificationName} - ${sq.price}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>No qualifications.</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Update Reference Modal (for Quote/Invoice) */}
      {isUpdateModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-sm">
            <h2 className="text-lg font-semibold mb-4">
              {updateModalType === "quote"
                ? "Enter Quote Reference Number"
                : "Enter Invoice Reference Number"}
            </h2>
            <input
              type="text"
              value={referenceNumber}
              onChange={(e) => setReferenceNumber(e.target.value)}
              placeholder="Reference Number"
              className="border border-gray-300 rounded p-2 w-full mb-4"
            />
            <div className="flex justify-end">
              <button
                onClick={() => {
                  setIsUpdateModalOpen(false);
                  setReferenceNumber("");
                }}
                className="mr-2 px-4 py-2 border rounded"
              >
                Cancel
              </button>
              <button
                onClick={handleReferenceSubmit}
                className="px-4 py-2 bg-blue-500 text-white rounded"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Comments Drawer */}
      <ApplicationCommentsDrawer
        isOpen={isCommentsDrawerOpen}
        onClose={() => {
          setIsCommentsDrawerOpen(false);
          setSelectedApplicationForComments(null);
        }}
        application={selectedApplicationForComments}
      />
    </div>
  );
};

export default AllApplicationsScreen;
