package com.skillsync.applyr.core.models.entities;


import com.skillsync.applyr.core.models.enums.InvoiceSentStatus;
import com.skillsync.applyr.modules.company.xero_models.TrueXeroInvoiceRequestDTO;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "true_xero_invoice")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class TrueXeroInvoice {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    private String invoiceNumber;
    private String quoteNumber;
    private String contactName;

    private double gross;
    private double balance;
    private String status;

    private String applicationId;
    private String agentUsername;

    private LocalDateTime invoiceDate;
    private LocalDateTime invoiceExpiryDate;
    private String source;



    @Enumerated(EnumType.STRING)
    private InvoiceSentStatus invoiceSentStatus;


    public TrueXeroInvoice(TrueXeroInvoiceRequestDTO requestDTO) {
        this.invoiceNumber = requestDTO.getInvoiceNumber();
        this.quoteNumber = requestDTO.getQuoteNumber();
        this.contactName = requestDTO.getContactName();

        this.gross = requestDTO.getGross();
        this.balance = requestDTO.getBalance();
        this.status = requestDTO.getStatus();
        this.applicationId = requestDTO.getApplicationId();
        this.agentUsername = requestDTO.getAgentUsername();
        this.invoiceDate = LocalDateTime.parse(requestDTO.getInvoiceDate());

        this.invoiceExpiryDate = LocalDateTime.parse(requestDTO.getInvoiceExpiryDate());
        this.source = requestDTO.getSource();
        this.invoiceSentStatus = InvoiceSentStatus.valueOf(requestDTO.getInvoiceSentStatus().toUpperCase());
    }

}
