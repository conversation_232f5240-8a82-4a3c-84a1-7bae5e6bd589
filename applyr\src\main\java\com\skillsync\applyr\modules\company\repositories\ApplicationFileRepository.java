package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.ApplicationFile;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface ApplicationFileRepository extends JpaRepository<ApplicationFile, Long> {
    Optional<ApplicationFile> findByApplicationIdAndQualificationQualificationId(String applicationId, String qualificationId);
}
