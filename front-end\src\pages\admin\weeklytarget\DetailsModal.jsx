import React, { useState, useRef, useMemo } from "react";
import { <PERSON>hn<PERSON>, Bar, Line } from "react-chartjs-2";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCalendarAlt,
  faChartPie,
  faChartLine,
  faListAlt,
  faUsers,
  faArrowDown,
  faTasks,
  faMoneyBillWave,
  faFileInvoiceDollar,
  faPrint,
  faCheckCircle,
  faTrophy,
  faLightbulb,
} from "@fortawesome/free-solid-svg-icons";
import { useReactToPrint } from "react-to-print";

// Import tab components from the sub-folder "tabs"
import OverviewTab from "./tabs/OverviewTab";
import ProgressTab from "./tabs/ProgressTab";
import AgentsTab from "./tabs/AgentsTab";
import ApplicationsTab from "./tabs/ApplicationsTab";

const DetailsModal = ({ targetData, onClose }) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [agentFilter, setAgentFilter] = useState("ALL");
  const [applicationStatusFilter, setApplicationStatusFilter] = useState("ALL");
  const [sortConfig, setSortConfig] = useState({ key: "kpi1Actual", direction: "desc" });

  const componentRef = useRef();

  // Handle printing
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: `${targetData.title} - Weekly Target Report`,
    onAfterPrint: () => console.log("Print completed"),
  });

  // Memoized calculations
  const totals = useMemo(() => {
    const totalKPI1Target = targetData.targets.reduce((sum, t) => sum + t.kpi1Target, 0);
    const totalKPI1Actual = targetData.targets.reduce((sum, t) => sum + t.kpi1Actual, 0);
    const totalKPI2Target = targetData.targets.reduce((sum, t) => sum + t.kpi2Target, 0);
    const totalKPI2Actual = targetData.targets.reduce((sum, t) => sum + t.kpi2Actual, 0);

    const kpi1Progress = totalKPI1Target > 0 ? Math.round((totalKPI1Actual / totalKPI1Target) * 100) : 0;
    const kpi2Progress = totalKPI2Target > 0 ? Math.round((totalKPI2Actual / totalKPI2Target) * 100) : 0;

    return { totalKPI1Target, totalKPI1Actual, totalKPI2Target, totalKPI2Actual, kpi1Progress, kpi2Progress };
  }, [targetData.targets]);

  const sortedPerformers = useMemo(() => {
    const sortedByKPI1 = [...targetData.targets].sort((a, b) => b.kpi1Actual - a.kpi1Actual);
    const sortedByKPI2 = [...targetData.targets].sort((a, b) => b.kpi2Actual - a.kpi2Actual);
    return {
      topKPI1Performers: sortedByKPI1.slice(0, 5),
      topKPI2Performers: sortedByKPI2.slice(0, 5),
    };
  }, [targetData.targets]);

  const formattedStart = targetData.startDate
    ? new Date(targetData.startDate).toLocaleDateString()
    : "";
  const formattedEnd = targetData.endDate
    ? new Date(targetData.endDate).toLocaleDateString()
    : "";

  // Doughnut chart data
  const kpi1DonutData = {
    labels: ["Remaining", "Achieved"],
    datasets: [
      {
        data: [Math.max(0, totals.totalKPI1Target - totals.totalKPI1Actual), totals.totalKPI1Actual],
        backgroundColor: ["#e5e7eb", "#6E39CB"],
        borderWidth: 0,
        cutout: "75%",
      },
    ],
  };

  const kpi2DonutData = {
    labels: ["Remaining", "Achieved"],
    datasets: [
      {
        data: [Math.max(0, totals.totalKPI2Target - totals.totalKPI2Actual), totals.totalKPI2Actual],
        backgroundColor: ["#e5e7eb", "#6E39CB"],
        borderWidth: 0,
        cutout: "75%",
      },
    ],
  };

  const donutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: {
        callbacks: {
          label: (context) => {
            const label = context.label || "";
            const value = context.raw || 0;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = Math.round((value / total) * 100);
            return `${label}: $${value.toLocaleString()} (${percentage}%)`;
          },
        },
      },
    },
  };

  // Bar Chart data and options
  const barLabels = targetData.targets.map(
    (a) => a.profile?.fullName || a.profile?.username || "Agent"
  );
  const barData = {
    labels: barLabels,
    datasets: [
      {
        label: "KPI1 Target",
        data: targetData.targets.map((a) => a.kpi1Target),
        backgroundColor: "rgba(110, 57, 203, 0.2)",
        borderColor: "rgba(110, 57, 203, 1)",
        borderWidth: 1,
      },
      {
        label: "KPI1 Actual",
        data: targetData.targets.map((a) => a.kpi1Actual),
        backgroundColor: "rgba(110, 57, 203, 0.8)",
        borderColor: "rgba(110, 57, 203, 1)",
        borderWidth: 1,
      },
      {
        label: "KPI2 Target",
        data: targetData.targets.map((a) => a.kpi2Target),
        backgroundColor: "rgba(59, 130, 246, 0.2)",
        borderColor: "rgba(59, 130, 246, 1)",
        borderWidth: 1,
      },
      {
        label: "KPI2 Actual",
        data: targetData.targets.map((a) => a.kpi2Actual),
        backgroundColor: "rgba(59, 130, 246, 0.8)",
        borderColor: "rgba(59, 130, 246, 1)",
        borderWidth: 1,
      },
    ],
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        ticks: { callback: (value) => `$${value.toLocaleString()}` },
      },
      x: {
        ticks: { autoSkip: true, maxRotation: 45, minRotation: 45 },
      },
    },
    plugins: {
      legend: { position: "top" },
      tooltip: {
        callbacks: {
          label: (context) => {
            let label = context.dataset.label || "";
            if (label) label += ": ";
            if (context.parsed.y !== null) {
              label += `$${context.parsed.y.toLocaleString()}`;
            }
            return label;
          },
        },
      },
    },
  };

  // Line Chart for progress over time
  const lineChartData = useMemo(() => {
    const today = new Date();
    const start = new Date(targetData.startDate);
    const end = new Date(targetData.endDate);
    const daysDiff = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    const daysPassed = Math.min(
      daysDiff,
      Math.ceil((today - start) / (1000 * 60 * 60 * 24))
    );

    const dateLabels = [];
    const idealKPI1 = [];
    const idealKPI2 = [];
    const actualKPI1 = [];
    const actualKPI2 = [];

    for (let i = 0; i <= daysDiff; i++) {
      const date = new Date(start);
      date.setDate(date.getDate() + i);
      dateLabels.push(
        date.toLocaleDateString("en-US", { month: "short", day: "numeric" })
      );
      idealKPI1.push((totals.totalKPI1Target / daysDiff) * i);
      idealKPI2.push((totals.totalKPI2Target / daysDiff) * i);
      if (i <= daysPassed) {
        const progressRatio = i / daysPassed;
        const randomFactor = 0.8 + Math.random() * 0.4;
        actualKPI1.push(totals.totalKPI1Actual * progressRatio * randomFactor);
        actualKPI2.push(totals.totalKPI2Actual * progressRatio * randomFactor);
      } else {
        actualKPI1.push(null);
        actualKPI2.push(null);
      }
    }

    return {
      labels: dateLabels,
      datasets: [
        {
          label: "KPI1 Ideal",
          data: idealKPI1,
          borderColor: "rgba(110, 57, 203, 0.3)",
          borderDash: [5, 5],
          borderWidth: 2,
          pointRadius: 0,
          fill: false,
          tension: 0.1,
        },
        {
          label: "KPI1 Actual",
          data: actualKPI1,
          borderColor: "rgba(110, 57, 203, 1)",
          backgroundColor: "rgba(110, 57, 203, 0.1)",
          borderWidth: 2,
          pointRadius: 3,
          fill: true,
          tension: 0.1,
        },
        {
          label: "KPI2 Ideal",
          data: idealKPI2,
          borderColor: "rgba(59, 130, 246, 0.3)",
          borderDash: [5, 5],
          borderWidth: 2,
          pointRadius: 0,
          fill: false,
          tension: 0.1,
        },
        {
          label: "KPI2 Actual",
          data: actualKPI2,
          borderColor: "rgba(59, 130, 246, 1)",
          backgroundColor: "rgba(59, 130, 246, 0.1)",
          borderWidth: 2,
          pointRadius: 3,
          fill: true,
          tension: 0.1,
        },
      ],
    };
  }, [targetData.startDate, targetData.endDate, totals]);

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        ticks: { callback: (value) => `$${value.toLocaleString()}` },
        title: { display: true, text: "Amount ($)" },
      },
      x: {
        ticks: { autoSkip: true, maxTicksLimit: 10 },
        title: { display: true, text: "Date" },
      },
    },
    plugins: {
      tooltip: {
        callbacks: {
          label: (context) => {
            let label = context.dataset.label || "";
            if (label) label += ": ";
            if (context.parsed.y !== null) {
              label += `$${context.parsed.y.toLocaleString()}`;
            }
            return label;
          },
        },
      },
    },
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center overflow-hidden"
    >
      <div className="bg-white w-full h-full rounded-none relative flex flex-col">
        <div ref={componentRef} className="overflow-y-auto flex-grow">
          {/* Header - Enlarged for full-screen */}
          <div className="px-8 py-6 border-b border-gray-200 flex justify-between items-center bg-gray-50">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{targetData.title}</h2>
              <div className="flex flex-wrap items-center mt-2 gap-3">
                <div className="bg-[#F4F5F9] px-4 py-2 rounded-md text-sm font-medium">
                  <FontAwesomeIcon
                    icon={faCalendarAlt}
                    className="mr-2 text-[#6E39CB]"
                  />
                  {formattedStart} - {formattedEnd}
                </div>
                <div className="bg-[#F4F5F9] px-4 py-2 rounded-md text-sm font-medium">
                  <FontAwesomeIcon
                    icon={faUsers}
                    className="mr-2 text-[#6E39CB]"
                  />
                  {targetData.targets.length} Agents
                </div>
                <div className="bg-[#F4F5F9] px-4 py-2 rounded-md text-sm font-medium">
                  <FontAwesomeIcon
                    icon={faMoneyBillWave}
                    className="mr-2 text-[#6E39CB]"
                  />
                  KPI1: ${totals.totalKPI1Actual.toLocaleString()} / $
                  {totals.totalKPI1Target.toLocaleString()}
                </div>
                <div className="bg-[#F4F5F9] px-4 py-2 rounded-md text-sm font-medium">
                  <FontAwesomeIcon
                    icon={faFileInvoiceDollar}
                    className="mr-2 text-[#6E39CB]"
                  />
                  KPI2: ${totals.totalKPI2Actual.toLocaleString()} / $
                  {totals.totalKPI2Target.toLocaleString()}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handlePrint}
                className="text-gray-600 hover:text-[#6E39CB] p-2 rounded-full hover:bg-gray-100"
                title="Print Report"
              >
                <FontAwesomeIcon icon={faPrint} className="h-5 w-5" />
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 p-2 rounded-full hover:bg-gray-100"
                title="Close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
          {/* Tabs - Enhanced for full-screen */}
          <div className="border-b border-gray-200 px-8 bg-white">
            <div className="flex flex-wrap space-x-4 md:space-x-8">
              <button
                onClick={() => setActiveTab("overview")}
                className={`py-4 px-4 border-b-2 font-medium text-base flex items-center ${
                  activeTab === "overview"
                    ? "border-[#6E39CB] text-[#6E39CB]"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <FontAwesomeIcon icon={faChartPie} className="mr-2" />
                <span>Overview</span>
              </button>
              <button
                onClick={() => setActiveTab("agents")}
                className={`py-4 px-4 border-b-2 font-medium text-base flex items-center ${
                  activeTab === "agents"
                    ? "border-[#6E39CB] text-[#6E39CB]"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <FontAwesomeIcon icon={faUsers} className="mr-2" />
                <span>Agents</span>
                <span className="ml-2 bg-gray-100 text-gray-600 text-xs px-2 py-0.5 rounded-full">
                  {targetData.targets.length}
                </span>
              </button>
              <button
                onClick={() => setActiveTab("progress")}
                className={`py-4 px-4 border-b-2 font-medium text-base flex items-center ${
                  activeTab === "progress"
                    ? "border-[#6E39CB] text-[#6E39CB]"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <FontAwesomeIcon icon={faChartLine} className="mr-2" />
                <span>Progress</span>
              </button>
              <button
                onClick={() => setActiveTab("applications")}
                className={`py-4 px-4 border-b-2 font-medium text-base flex items-center ${
                  activeTab === "applications"
                    ? "border-[#6E39CB] text-[#6E39CB]"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <FontAwesomeIcon icon={faListAlt} className="mr-2" />
                <span>Applications</span>
                <span className="ml-2 bg-gray-100 text-gray-600 text-xs px-2 py-0.5 rounded-full">
                  {targetData.targets.reduce(
                    (sum, t) => sum + (t.applications?.length || 0),
                    0
                  )}
                </span>
              </button>
            </div>
          </div>
          {/* Tab Content - Full-screen */}
          <div
            className="p-8 overflow-y-auto flex-grow"
            style={{ height: "calc(100vh - 180px)", overflowY: "auto" }}
          >
            {activeTab === "overview" && (
              <OverviewTab
                targetData={targetData}
                formattedStart={formattedStart}
                formattedEnd={formattedEnd}
                totals={totals}
                sortedPerformers={sortedPerformers}
                kpi1DonutData={kpi1DonutData}
                kpi2DonutData={kpi2DonutData}
                donutOptions={donutOptions}
                barData={barData}
                barOptions={barOptions}
              />
            )}
            {activeTab === "progress" && (
              <ProgressTab
                targetData={targetData}
                lineChartData={lineChartData}
                lineOptions={lineOptions}
                barData={barData}
                barOptions={barOptions}
                formattedStart={formattedStart}
                formattedEnd={formattedEnd}
              />
            )}
            {activeTab === "agents" && (
              <AgentsTab
                targetData={targetData}
                agentFilter={agentFilter}
                setAgentFilter={setAgentFilter}
                kpi2DonutData={kpi2DonutData}
                donutOptions={donutOptions}
                barData={barData}
                barOptions={barOptions}
              />
            )}
            {activeTab === "applications" && (
              <ApplicationsTab
                targetData={targetData}
                agentFilter={agentFilter}
                setAgentFilter={setAgentFilter}
                applicationStatusFilter={applicationStatusFilter}
                setApplicationStatusFilter={setApplicationStatusFilter}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsModal;
