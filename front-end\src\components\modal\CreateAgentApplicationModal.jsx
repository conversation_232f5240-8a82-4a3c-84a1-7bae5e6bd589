import React, { useState } from "react";
import ReactSelect from "react-select";
import { showValidationError } from "../../utils/toastUtils";

const CreateAgentApplicationModal = ({
  isOpen,
  onClose,
  onCreate, // callback from parent
  qualifications, // array of qualifications from backend
  leads, // array of leads from agentLeads
}) => {
  const [applicantName, setApplicantName] = useState("");
  const [applicantEmail, setApplicantEmail] = useState("");
  const [applicantPhone, setApplicantPhone] = useState("");
  const [applicantAddress, setApplicantAddress] = useState("");
  const [leadPhone, setLeadPhone] = useState("");
  const [createdOn, setCreatedOn] = useState("");
  const [isIndividual, setIsIndividual] = useState(false);
  const [otherInformation, setOtherInformation] = useState("");

  // soldQualifications: [ { qualificationId, price }, ... ]
  const [soldQualifications, setSoldQualifications] = useState([]);

  const totalPrice = soldQualifications.reduce((acc, curr) => {
    return acc + (parseFloat(curr.price) || 0);
  }, 0);

  if (!isOpen) return null;

  // Add a new row for { qualificationId: "", price: "" }
  const addQualificationRow = () => {
    setSoldQualifications((prev) => [
      ...prev,
      { qualificationId: "", price: "" },
    ]);
  };

  // Remove a row by index
  const removeQualificationRow = (index) => {
    setSoldQualifications((prev) => {
      const copy = [...prev];
      copy.splice(index, 1);
      return copy;
    });
  };

  // Update a single field (qualificationId or price) in a specific row
  const updateQualificationRow = (index, field, value) => {
    setSoldQualifications((prev) => {
      const copy = [...prev];
      copy[index] = { ...copy[index], [field]: value };
      return copy;
    });
  };

  // Separate validations for clarity
  const isQualificationsValid =
    soldQualifications.length > 0 &&
    soldQualifications.every((sq) => parseFloat(sq.price) > 0);
  const isLeadSelected = leadPhone.trim() !== "";

  // If not individual, lead must be selected; if individual, no lead is needed
  const isFormValid = isQualificationsValid && (isIndividual || isLeadSelected) && createdOn.trim() !== "";

  // Handle the submit button
  const handleSubmit = () => {
    if (!isIndividual && !isLeadSelected) {
      showValidationError("Please select a lead.");
      return;
    }
    if (!isQualificationsValid) {
      showValidationError("Please provide a valid price for all qualifications.");
      return;
    }
    if (createdOn.trim() === "") {
      showValidationError("Please provide a creation date.");
      return;
    }



    const finalData = {
      applicantName,
      applicantEmail,
      applicantPhone,
      applicantAddress,
      leadPhone: isIndividual ? null : leadPhone,
      createdOn,
      otherInformation,
      soldQualifications: soldQualifications.map((sq) => {
        // Find the qualification by ID instead of using array index
        const qualification = qualifications.find(q => q.id === sq.qualificationId);
        return {
          qualificationId: qualification ? qualification.qualificationId : sq.qualificationId,
          price: parseFloat(sq.price) || 0,
        };
      }),
    };

    console.log(finalData);

    onCreate(finalData);

    // Clear & close
    setApplicantName("");
    setApplicantEmail("");
    setApplicantPhone("");
    setApplicantAddress("");
    setLeadPhone("");
    setCreatedOn("");
    setOtherInformation("");
    setSoldQualifications([]);
    setIsIndividual(false);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-700 bg-opacity-75">
      <div className="bg-white w-full max-w-lg p-6 rounded shadow-lg">
        <div className="sticky top-0 bg-white z-10 p-4">
          <h2 className="text-xl font-bold mb-4">Create New Application</h2>
        </div>

        {/* Scrollable Content */}
        <div className="overflow-auto max-h-[calc(100vh-200px)]">
          {/* Applicant Name */}
          <div className="mb-3">
            <label className="block font-semibold text-sm mb-1">
              Applicant Name
            </label>
            <input
              type="text"
              className="border w-full p-2 rounded"
              value={applicantName}
              onChange={(e) => setApplicantName(e.target.value)}
            />
          </div>

          {/* Applicant Email */}
          <div className="mb-3">
            <label className="block font-semibold text-sm mb-1">
              Applicant Email
            </label>
            <input
              type="email"
              className="border w-full p-2 rounded"
              value={applicantEmail}
              onChange={(e) => setApplicantEmail(e.target.value)}
            />
          </div>

          {/* Applicant Phone */}
          <div className="mb-3">
            <label className="block font-semibold text-sm mb-1">
              Applicant Phone
            </label>
            <input
              type="text"
              className="border w-full p-2 rounded"
              value={applicantPhone}
              onChange={(e) => setApplicantPhone(e.target.value)}
            />
          </div>

          {/* Hardcopy Address */}
          <div className="mb-3">
            <label className="block font-semibold text-sm mb-1">
              Hardcopy Address
            </label>
            <input
              type="text"
              className="border w-full p-2 rounded"
              value={applicantAddress}
              onChange={(e) => setApplicantAddress(e.target.value)}
            />
          </div>

          {/* Created On Field */}
          <div className="mb-3">
            <label className="block font-semibold text-sm mb-1">
              Created On
            </label>
            <input
              type="datetime-local"
              className="border w-full p-2 rounded"
              value={createdOn}
              onChange={(e) => setCreatedOn(e.target.value)}
            />
          </div>

          {/* Other Information Field */}
          <div className="mb-3">
            <label className="block font-semibold text-sm mb-1">
              Other Information
            </label>
            <textarea
              className="border w-full p-2 rounded"
              value={otherInformation}
              onChange={(e) => setOtherInformation(e.target.value)}
              rows="3"
              placeholder="Enter any additional information about the application"
            />
          </div>

          {/* Individual Checkbox */}
          <div className="mb-3">
            <label className="flex items-center font-semibold text-sm">
              <input
                type="checkbox"
                checked={isIndividual}
                onChange={(e) => {
                  setIsIndividual(e.target.checked);
                  if (e.target.checked) {
                    setLeadPhone("");
                  }
                }}
                className="mr-2"
              />
              Individual
            </label>
          </div>

          {/* Lead (Searchable) */}
          <div className="mb-3">
            <label className="block font-semibold text-sm mb-1">
              Select Lead <span className="text-red-500">*</span>
            </label>
            <div className="flex space-x-2">
              <div className="flex-1">
                <ReactSelect
                  options={leads.map((lead) => ({
                    label: `${lead.leadName} (${lead.companyName} - ${lead.phone})`,
                    value: lead.phone,
                  }))}
                  value={
                    leadPhone
                      ? {
                          label:
                            leads.find((l) => l.phone === leadPhone)?.leadName +
                            ` (${leadPhone})`,
                          value: leadPhone,
                        }
                      : null
                  }
                  onChange={(selected) =>
                    setLeadPhone(selected ? selected.value : "")
                  }
                  isClearable
                  isSearchable
                  placeholder="Search or select a lead..."
                  isDisabled={isIndividual} // Gray out when individual is checked
                />
              </div>
              <button
                type="button"
                onClick={() => {
                  if (!leadPhone) {
                    showValidationError("Please select a lead first");
                    return;
                  }

                  const selectedLead = leads.find(l => l.phone === leadPhone);
                  if (!selectedLead) return;

                  // For Company type leads, copy email and phone
                  if (selectedLead.companyName) {
                    setApplicantEmail(selectedLead.email || "");
                    setApplicantPhone(selectedLead.phone || "");
                  }
                  // For Individual type leads, copy name, email, phone, and address
                  else {
                    setApplicantName(selectedLead.leadName || "");
                    setApplicantEmail(selectedLead.email || "");
                    setApplicantPhone(selectedLead.phone || "");
                    setApplicantAddress(selectedLead.address || "");
                  }
                }}
                className={`bg-blue-500 text-white px-3 py-2 rounded ${!leadPhone || isIndividual ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={!leadPhone || isIndividual}
              >
                Import Lead Info
              </button>
            </div>
          </div>

          {/* Qualifications Section with Scrollable Content */}
          <div className="mb-3">
            <label className="block font-semibold text-sm mb-1">
              Qualifications
            </label>

            {soldQualifications.map((sq, index) => {
              // For ReactSelect "value", we find the qualification from the list
              const selectedQualification = qualifications.find(
                (q) => q.id === sq.qualificationId
              );

              return (
                <div
                  key={index}
                  className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 mb-3"
                >
                  {/* Single-select ReactSelect (searchable) */}
                  <div className="flex-1 ">
                    <ReactSelect
                      options={qualifications.map((q) => ({
                        label: q.qualificationName,
                        value: q.id,
                      }))}
                      value={
                        selectedQualification
                          ? {
                              label: selectedQualification.qualificationName,
                              value: selectedQualification.id,
                            }
                          : null
                      }
                      onChange={(selected) =>
                        updateQualificationRow(
                          index,
                          "qualificationId",
                          selected.value
                        )
                      }
                      isSearchable
                      placeholder="Search or select a qualification..."
                    />
                  </div>

                  {/* Price Input */}
                  <div className="w-full sm:w-32">
                    <input
                      type="number"
                      placeholder="Price"
                      className="border p-2 rounded w-full"
                      value={sq.price}
                      onChange={(e) =>
                        updateQualificationRow(index, "price", e.target.value)
                      }
                    />
                  </div>

                  {/* Remove Row Button */}
                  <button
                    type="button"
                    className="text-red-500 font-bold pr-2"
                    onClick={() => removeQualificationRow(index)}
                  >
                    X
                  </button>
                </div>
              );
            })}

            <button
              type="button"
              onClick={addQualificationRow}
              className="bg-blue-500 text-white px-3 py-1 rounded"
            >
              + Add Qualification
            </button>
          </div>

          <div className="mt-3 mb-3">
            <strong>Total Price: </strong>${totalPrice.toFixed(2)}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end mt-4">
          <button
            onClick={onClose}
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded mr-2"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className={`${
              isFormValid ? "bg-green-500" : "bg-gray-300"
            } text-white px-4 py-2 rounded`}
            disabled={!isFormValid}
          >
            Create
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateAgentApplicationModal;
