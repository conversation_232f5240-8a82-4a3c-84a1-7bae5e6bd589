package com.skillsync.applyr.core.models.entities;

import com.skillsync.applyr.core.models.enums.TroubleStatus;
import com.skillsync.applyr.modules.sales.models.CreateTroubleDTO;
import com.skillsync.applyr.modules.sales.models.TroubleAnswerReqDTO;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "troubles")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class Troubles extends Auditable<String> {

        @Id
        @GeneratedValue(strategy = GenerationType.AUTO)
        private Long id;

    private String question;
    private String allocatedTo;
    private LocalDateTime dueDate;

    @OneToMany(mappedBy = "trouble", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<TroubleAnswer> answers;

    private TroubleStatus status;

    public Troubles(CreateTroubleDTO troubleDTO) {
        super();
        this.question = troubleDTO.getQuestion();
        this.allocatedTo = troubleDTO.getAssignedTo();
        this.question = troubleDTO.getQuestion();
        this.dueDate = troubleDTO.getDueDate();
        this.answers = new ArrayList<>();
        this.status = TroubleStatus.PENDING;
    }

    public void addAnswer(TroubleAnswerReqDTO answer) {
        if(answers == null) {
            answers = new ArrayList<>();
        }
        TroubleAnswer troubleAnswer = new TroubleAnswer();
        troubleAnswer.setAnswer(answer.getAnswer());
        troubleAnswer.setTrouble(this);
        answers.add(troubleAnswer);
    }
}
