package com.skillsync.applyr.modules.company.services;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.RTO;
import com.skillsync.applyr.core.models.entities.RTOContact;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.modules.company.repositories.RTOContactRepository;
import com.skillsync.applyr.modules.company.repositories.RTORepository;
import com.skillsync.applyr.modules.company.models.RTODataDTO;
import com.skillsync.applyr.modules.company.models.RTOContactDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class RTOServices {

    private final RTORepository rtoRepository;
    private final RTOContactRepository rtoContactRepository;


    public RTOServices(RTORepository rtoRepository, RTOContactRepository rtoContactRepository) {
        this.rtoRepository = rtoRepository;
        this.rtoContactRepository = rtoContactRepository;
    }

    public SuccessResponse fetchAndSaveRTOData(String rtoCode) {
        Optional<RTO> existingRTO = rtoRepository.findByCode(rtoCode);
        if (existingRTO.isPresent()) {
            throw new AppRTException("RTO with this code already exists", HttpStatus.CONFLICT);
        }

        String url = "https://training.gov.au/api/organisation/" + rtoCode + "?api-version=1.0&include=all";
        RestTemplate restTemplate = new RestTemplate();
        String responseBody;

        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            if (response.getStatusCode() == HttpStatus.NOT_FOUND) {
                throw new AppRTException("RTO not found", HttpStatus.NOT_FOUND);
            }
            responseBody = response.getBody();
        } catch (Exception e) {
            throw new AppRTException("RTO not found", HttpStatus.NOT_FOUND);
        }

        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode rootNode = mapper.readTree(responseBody);
            RTO rto = new RTO();
            rto.setCode(rtoCode);

            // Extract legal and trading names, address, and classifications
            JsonNode legalNames = rootNode.path("legalNames");
            if (legalNames.isArray() && !legalNames.isEmpty()) {
                rto.setLegalName(legalNames.get(0).path("name").asText());
            }
            JsonNode tradingNames = rootNode.path("tradingNames");
            if (tradingNames.isArray() && !tradingNames.isEmpty()) {
                rto.setBusinessName(tradingNames.get(0).path("name").asText());
            }
            JsonNode addresses = rootNode.path("addresses");
            if (addresses.isArray() && !addresses.isEmpty()) {
                rto.setAddress(addresses.get(0).path("address").path("fullAddress").asText());
            }
            JsonNode classifications = rootNode.path("classifications");
            if (classifications.isArray() && !classifications.isEmpty()) {
                rto.setRtoType(classifications.get(0).path("valueDescription").asText());
            }

            // Create sets to track added emails and phones for this request.
            Set<String> processedEmails = new HashSet<>();
            Set<String> processedPhones = new HashSet<>();

            JsonNode contacts = rootNode.path("contacts");
            if (contacts.isArray()) {
                for (JsonNode contactNode : contacts) {
                    // Check mandatory values
                    if (contactNode.hasNonNull("firstName") &&
                            contactNode.hasNonNull("lastName") &&
                            (contactNode.hasNonNull("email") || contactNode.hasNonNull("phone"))) {

                        String email = contactNode.hasNonNull("email") ? contactNode.path("email").asText() : null;
                        String phone = contactNode.hasNonNull("phone") ? contactNode.path("phone").asText() : null;

                        // Check in-memory for duplicates
                        if ((email != null && processedEmails.contains(email)) ||
                                (phone != null && processedPhones.contains(phone))) {
                            continue; // Skip duplicate found in current payload
                        }

                        // Still check if the contact is already in the repository
                        Optional<RTOContact> existingContact = Optional.empty();
                        if(email != null) {
                            existingContact = rtoContactRepository.findByEmail(email);
                        } else if(phone != null) {
                            existingContact = rtoContactRepository.findByPhone(phone);
                        }

                        if (existingContact.isPresent()) {
                            rto.addContact(existingContact.get());
                            // Optionally add to sets if you wish
                            if (email != null) processedEmails.add(email);
                            if (phone != null) processedPhones.add(phone);
                            continue;
                        }

                        // Create new contact and populate its fields.
                        RTOContact rtoContact = new RTOContact();
                        rtoContact.setContactType(contactNode.path("contactType").asText());
                        rtoContact.setFirstName(contactNode.path("firstName").asText());
                        rtoContact.setLastName(contactNode.path("lastName").asText());
                        rtoContact.setJobTitle(contactNode.path("jobTitle").asText(null));
                        rtoContact.setRole(contactNode.path("roleDescription").asText());
                        rtoContact.setPhone(phone);
                        rtoContact.setEmail(email);
                        rto.addContact(rtoContact);
                        rtoContact.setRto(rto);

                        // Record that this contact has been processed
                        if (email != null) processedEmails.add(email);
                        if (phone != null) processedPhones.add(phone);
                    }
                }
            }

            rtoRepository.save(rto);
            return new SuccessResponse("RTO data saved successfully");
        } catch (Exception e) {
            throw new AppRTException("Error parsing RTO data: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }




    public List<RTODataDTO> getAllRTOs() {
        List<RTO> rtos = rtoRepository.findAll();
        return rtos.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    public RTODataDTO getRTOByCode(String rtoCode) {
        Optional<RTO> rto = rtoRepository.findByCode(rtoCode);
        if (rto.isEmpty()) {
            throw new AppRTException("RTO not found", HttpStatus.NOT_FOUND);
        }
        return convertToDTO(rto.get());
    }

    public SuccessResponse reloadRTOInfo(String rtoCode) {
        Optional<RTO> existingRTO = rtoRepository.findByCode(rtoCode);
        if (existingRTO.isEmpty()) {
            throw new AppRTException("RTO not found", HttpStatus.NOT_FOUND);
        }
        
        rtoRepository.delete(existingRTO.get());
        
        return fetchAndSaveRTOData(rtoCode);
    }

    public SuccessResponse updateRTOContact(Long contactId, RTOContactDTO contactDTO) {
        Optional<RTOContact> existingContact = rtoContactRepository.findById(contactId);
        if (existingContact.isEmpty()) {
            throw new AppRTException("Contact not found", HttpStatus.NOT_FOUND);
        }
        
        RTOContact contact = existingContact.get();
        contact.setContactType(contactDTO.getContactType());
        contact.setFirstName(contactDTO.getFirstName());
        contact.setLastName(contactDTO.getLastName());
        contact.setJobTitle(contactDTO.getJobTitle());
        contact.setRole(contactDTO.getRole());
        contact.setPhone(contactDTO.getPhone());
        contact.setEmail(contactDTO.getEmail());
        
        rtoContactRepository.save(contact);
        return new SuccessResponse("RTO contact updated successfully");
    }

    public SuccessResponse addRTOContact(String rtoCode, RTOContactDTO contactDTO) {
        Optional<RTO> existingRTO = rtoRepository.findByCode(rtoCode);
        if (existingRTO.isEmpty()) {
            throw new AppRTException("RTO not found", HttpStatus.NOT_FOUND);
        }
        
        RTOContact contact = new RTOContact();
        contact.setContactType(contactDTO.getContactType());
        contact.setFirstName(contactDTO.getFirstName());
        contact.setLastName(contactDTO.getLastName());
        contact.setJobTitle(contactDTO.getJobTitle());
        contact.setRole(contactDTO.getRole());
        contact.setPhone(contactDTO.getPhone());
        contact.setEmail(contactDTO.getEmail());
        
        RTO rto = existingRTO.get();
        rto.addContact(contact);
        
        rtoRepository.save(rto);
        return new SuccessResponse("RTO contact added successfully");
    }

    public SuccessResponse deleteRTO(String rtoCode) {
        Optional<RTO> existingRTO = rtoRepository.findByCode(rtoCode);
        if (existingRTO.isEmpty()) {
            throw new AppRTException("RTO not found", HttpStatus.NOT_FOUND);
        }
        
        rtoRepository.delete(existingRTO.get());
        return new SuccessResponse("RTO deleted successfully");
    }

    public SuccessResponse deleteRTOContact(Long contactId) {
        Optional<RTOContact> existingContact = rtoContactRepository.findById(contactId);
        if (existingContact.isEmpty()) {
            throw new AppRTException("Contact not found", HttpStatus.NOT_FOUND);
        }
        
        RTOContact contact = existingContact.get();
        RTO rto = contact.getRto();
        rto.removeContact(contact);
        
        rtoContactRepository.delete(contact);
        rtoRepository.save(rto);
        return new SuccessResponse("RTO contact deleted successfully");
    }

    private RTODataDTO convertToDTO(RTO rto) {
        RTODataDTO dto = new RTODataDTO();
        dto.setId(rto.getId());
        dto.setCode(rto.getCode());
        dto.setLegalName(rto.getLegalName());
        dto.setBusinessName(rto.getBusinessName());
        dto.setAddress(rto.getAddress());
        dto.setRtoType(rto.getRtoType());

        List<RTOContactDTO> contactDTOs = rto.getContacts().stream()
            .map(this::convertContactToDTO)
            .collect(Collectors.toList());
        dto.setContacts(contactDTOs);
        
        return dto;
    }

    private RTOContactDTO convertContactToDTO(RTOContact contact) {
        RTOContactDTO dto = new RTOContactDTO();
        dto.setId(contact.getId());
        dto.setContactType(contact.getContactType());
        dto.setFirstName(contact.getFirstName());
        dto.setLastName(contact.getLastName());
        dto.setJobTitle(contact.getJobTitle());
        dto.setRole(contact.getRole());
        dto.setPhone(contact.getPhone());
        dto.setEmail(contact.getEmail());
        return dto;
    }
}
