package com.skillsync.applyr.modules.emailing.models;

import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class InputStreamMultipartFile implements MultipartFile {
    @Setter
    private String name;
    private final byte[] content;
    private final String originalFilename;
    private final String contentType;

    public InputStreamMultipartFile(InputStream inputStream, String originalFilename, String contentType) throws IOException {
        this.content = inputStream.readAllBytes();
        this.originalFilename = originalFilename;
        this.contentType = (contentType != null) ? contentType : "application/octet-stream";
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public String getOriginalFilename() {
        return this.originalFilename;
    }

    @Override
    public String getContentType() {
        return this.contentType;
    }

    @Override
    public boolean isEmpty() {
        return this.content.length == 0;
    }

    @Override
    public long getSize() {
        return this.content.length;
    }

    @Override
    public byte[] getBytes() {
        return this.content;
    }

    @Override
    public InputStream getInputStream() {
        return new ByteArrayInputStream(this.content);
    }

    @Override
    public void transferTo(File dest) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(dest)) {
            fos.write(this.content);
        }
    }
}
