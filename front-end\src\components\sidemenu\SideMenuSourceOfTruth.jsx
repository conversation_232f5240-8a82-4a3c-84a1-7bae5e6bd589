import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faHouse,
  faChartLine,
  faClock,
  faBullseye,
  faSignOutAlt,
  faChevronLeft,
  faChevronRight,
  faBars,
  faAngleDown,
  faAngleRight,
  faSearchPlus,
  faSearchMinus,
  faExchangeAlt
} from "@fortawesome/free-solid-svg-icons";
import { NavLink, useNavigate, useLocation } from "react-router-dom";
import { getToken, removeToken } from "../../services/LocalStorageService";
import logo from "../../assets/Logo.png";
import { useGetProfileQuery } from "../../services/AgentAPIService";
import routes from "../../routes";

const SideMenuSourceOfTruth = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1); // Default zoom level
  const [expandedCategories, setExpandedCategories] = useState({
    Main: true,
    System: true
  });
  const navigate = useNavigate();
  const location = useLocation();
  const token = getToken();
  const { data: userData } = useGetProfileQuery(token);

  // Zoom levels: 0.8 (small), 1 (normal), 1.2 (large)
  const zoomLevels = [0.8, 1, 1.2];

  // Function to handle zoom in
  const handleZoomIn = () => {
    setZoomLevel(prevZoom => {
      const currentIndex = zoomLevels.indexOf(prevZoom);
      const nextIndex = Math.min(currentIndex + 1, zoomLevels.length - 1);
      const newZoom = zoomLevels[nextIndex];

      // Apply zoom to document root
      document.documentElement.style.fontSize = `${newZoom * 100}%`;

      return newZoom;
    });
  };

  // Function to handle zoom out
  const handleZoomOut = () => {
    setZoomLevel(prevZoom => {
      const currentIndex = zoomLevels.indexOf(prevZoom);
      const nextIndex = Math.max(currentIndex - 1, 0);
      const newZoom = zoomLevels[nextIndex];

      // Apply zoom to document root
      document.documentElement.style.fontSize = `${newZoom * 100}%`;

      return newZoom;
    });
  };

  // Toggle category expansion
  const toggleCategory = (category) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Handle logout
  const handleLogout = () => {
    removeToken();
    navigate("/");
  };

  // Switch to Admin Panel
  const switchToAdminPanel = () => {
    navigate("/admin/dashboard");
  };

  // Navigation items grouped by category
  const navItems = [
    {
      category: "Main",
      items: [
        { name: "Dashboard", icon: faHouse, path: "/admin/sourceoftruth/dashboard" },
        { name: "Xero Import", icon: faChartLine, path: "/admin/sourceoftruth/xero-import" },
        { name: "Weekly Targets", icon: faBullseye, path: "/admin/sourceoftruth/weekly-target" },
        { name: "Time Tracking", icon: faClock, path: "/admin/sourceoftruth/time-tracking" },
      ],
    },
    {
      category: "System",
      items: [
        { name: "Switch to Admin Panel", icon: faExchangeAlt, onClick: switchToAdminPanel },
        { name: "Log out", icon: faSignOutAlt, path: null, onClick: handleLogout, className: "text-red-500" },
      ],
    },
  ];

  // Check if a nav item is active
  const isItemActive = (path) => {
    if (!path) return false;
    return location.pathname === path;
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Toggle sidebar collapse
  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  // Render a navigation item
  const renderNavItem = (item) => {
    const isActive = isItemActive(item.path);

    const content = (
      <div className="flex items-center w-full">
        <div className={`flex items-center justify-center ${collapsed ? 'w-full' : 'w-8'} h-8 ${isActive ? 'text-[#6E39CB]' : 'text-gray-600'}`}>
          <FontAwesomeIcon
            icon={item.icon}
            className={`${item.className || ''} ${isActive ? 'text-[#6E39CB]' : ''} w-5 h-5`}
          />
        </div>
        {!collapsed && (
          <span className={`ml-3 text-sm font-medium whitespace-nowrap transition-all duration-200 ${isActive ? 'text-[#6E39CB]' : 'text-gray-700'}`}>
            {item.name}
          </span>
        )}
      </div>
    );

    if (item.onClick) {
      return (
        <button
          key={item.name}
          onClick={item.onClick}
          className={`flex items-center w-full rounded-md ${collapsed ? 'justify-center p-2' : 'p-2'}
            ${isActive ? 'bg-[#F4F5F9]' : 'hover:bg-[#F4F5F9]'} transition-all duration-200`}
          title={collapsed ? item.name : ""}
        >
          {content}
        </button>
      );
    }

    return (
      <NavLink
        key={item.name}
        to={item.path}
        className={`flex items-center w-full rounded-md ${collapsed ? 'justify-center p-2' : 'p-2'}
          ${isActive ? 'bg-[#F4F5F9]' : 'hover:bg-[#F4F5F9]'} transition-all duration-200`}
        title={collapsed ? item.name : ""}
      >
        {content}
      </NavLink>
    );
  };

  return (
    <>
      {/* Mobile menu backdrop */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden"
          onClick={toggleMobileMenu}
        ></div>
      )}

      {/* Mobile menu toggle button */}
      <button
        className="fixed top-4 left-4 z-30 md:hidden bg-white p-2 rounded-md shadow-md"
        onClick={toggleMobileMenu}
      >
        <FontAwesomeIcon icon={faBars} className="text-gray-700" />
      </button>

      {/* Sidebar */}
      <div
        className={`${
          mobileMenuOpen ? "translate-x-0" : "-translate-x-full"
        } md:translate-x-0 fixed md:sticky top-0 left-0 z-20 flex flex-col h-screen ${
          collapsed ? "w-16" : "w-64"
        } bg-white border-r border-gray-100 shadow-md transition-all duration-300 ease-in-out`}
      >
        {/* Logo and toggle button */}
        <br />
        <div className="relative flex items-center justify-between py-5 px-4 border-b border-gray-100">
          {!collapsed && (
            <div className="flex items-center">
              <img src={logo} alt="Logo" className="h- w-auto object-contain" />
            </div>
          )}
          {collapsed && (
            <div className="flex flex-col justify-center items-center w-full">
              <img src={logo} alt="Logo" className="h-8 w-auto object-contain" />
            </div>
          )}
          <button
            className="absolute -right-3 top-5 bg-white border border-gray-200 rounded-full p-1 shadow-sm md:flex hidden"
            onClick={toggleSidebar}
          >
            <FontAwesomeIcon
              icon={collapsed ? faChevronRight : faChevronLeft}
              className="w-3 h-3 text-gray-400"
            />
          </button>

          {/* Zoom controls - only show when not collapsed */}
          {!collapsed && (
            <div className="flex space-x-2 items-center">
              <button
                onClick={handleZoomOut}
                disabled={zoomLevel === zoomLevels[0]}
                className={`p-1 rounded ${
                  zoomLevel === zoomLevels[0] ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-[#6E39CB]'
                }`}
                title="Zoom out"
              >
                <FontAwesomeIcon icon={faSearchMinus} className="w-3 h-3" />
              </button>
              <button
                onClick={handleZoomIn}
                disabled={zoomLevel === zoomLevels[zoomLevels.length - 1]}
                className={`p-1 rounded ${
                  zoomLevel === zoomLevels[zoomLevels.length - 1] ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-[#6E39CB]'
                }`}
                title="Zoom in"
              >
                <FontAwesomeIcon icon={faSearchPlus} className="w-3 h-3" />
              </button>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex-grow flex flex-col py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
          {navItems.map((section) => {
            const isExpanded = expandedCategories[section.category];
            const hasActiveItem = section.items.some(item => isItemActive(item.path));

            return (
              <div key={section.category} className="px-3 mb-4">
                {/* Category header */}
                <button
                  className={`flex items-center justify-between w-full p-2 text-left text-xs font-semibold uppercase tracking-wider ${
                    hasActiveItem ? 'text-[#6E39CB]' : 'text-gray-500'
                  } hover:text-[#6E39CB] transition-colors duration-200`}
                  onClick={() => toggleCategory(section.category)}
                >
                  {!collapsed && (
                    <>
                      <span>{section.category}</span>
                      <FontAwesomeIcon
                        icon={isExpanded ? faAngleDown : faAngleRight}
                        className="w-3 h-3"
                      />
                    </>
                  )}
                </button>

                {/* Category items */}
                {(isExpanded || collapsed) && (
                  <div className="mt-1 space-y-1">
                    {section.items.map(item => renderNavItem(item))}
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* User profile */}
        {userData && !collapsed && (
          <div className="p-4 border-t border-gray-100 flex items-center">
            <div className="w-8 h-8 rounded-full bg-[#6E39CB] flex items-center justify-center text-white font-medium">
              {userData.fullName?.charAt(0) || "U"}
            </div>
            <div className="ml-3 overflow-hidden">
              <p className="text-sm font-medium text-gray-800 truncate">
                {userData.fullName || "User"}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {userData.email || ""}
              </p>
            </div>
          </div>
        )}

        {/* Collapsed user profile */}
        {userData && collapsed && (
          <div className="p-2 border-t border-gray-100 flex justify-center">
            <div className="w-8 h-8 rounded-full bg-[#6E39CB] flex items-center justify-center text-white font-medium" title={userData.fullName || "User"}>
              {userData.fullName?.charAt(0) || "U"}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default SideMenuSourceOfTruth;
