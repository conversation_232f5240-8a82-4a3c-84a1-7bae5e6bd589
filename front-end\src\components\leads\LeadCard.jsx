import React from "react";
import { <PERSON>aP<PERSON>, FaEnvelope, FaMapMarkerAlt, FaUser, FaBuilding } from "react-icons/fa";

// Helper for status styling
function getStatusClass(status) {
  switch (status) {
    case "HOT":
      return "bg-red-100 text-red-800 border-red-200";
    case "WARM":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "COLD":
    case "FRESH":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "CLOSED":
      return "bg-gray-100 text-gray-800 border-gray-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}

const LeadCard = ({ 
  lead, 
  onProfileRedirect, 
  onStatusChange, 
  onEditLead,
  isAdmin = false 
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            {lead.leadType === "Company" ? (
              <FaBuilding className="text-gray-500 text-sm" />
            ) : (
              <FaUser className="text-gray-500 text-sm" />
            )}
            <h3 
              className="font-semibold text-gray-900 cursor-pointer hover:text-[#6E39CB] transition-colors"
              onClick={() => onProfileRedirect(lead.phone)}
            >
              {lead.leadType === "Company" ? lead.companyName : lead.name}
            </h3>
          </div>
          
          {lead.leadType === "Company" && lead.name && (
            <p className="text-sm text-gray-600 mb-2">Contact: {lead.name}</p>
          )}
          
          <div className="space-y-1 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <FaPhone className="text-gray-400 text-xs" />
              <span>{lead.phone}</span>
            </div>
            
            {lead.email && (
              <div className="flex items-center space-x-2">
                <FaEnvelope className="text-gray-400 text-xs" />
                <span className="truncate">{lead.email}</span>
              </div>
            )}
            
            {lead.address && (
              <div className="flex items-center space-x-2">
                <FaMapMarkerAlt className="text-gray-400 text-xs" />
                <span className="truncate">{lead.address}</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex flex-col items-end space-y-2">
          <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusClass(lead.status)}`}>
            {lead.status}
          </span>
          
          <div className="flex space-x-1">
            <button
              onClick={() => onEditLead(lead)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Edit
            </button>
            
            {isAdmin && (
              <select
                value={lead.status}
                onChange={(e) => onStatusChange(lead.phone, e.target.value)}
                className="text-xs border border-gray-300 rounded px-2 py-1"
              >
                <option value="FRESH">Fresh</option>
                <option value="COLD">Cold</option>
                <option value="WARM">Warm</option>
                <option value="HOT">Hot</option>
                <option value="CLOSED">Closed</option>
              </select>
            )}
          </div>
        </div>
      </div>
      
      {/* Sales Agent Info */}
      {lead.salesAgent && (
        <div className="border-t border-gray-100 pt-2 mt-3">
          <p className="text-xs text-gray-500">
            Assigned to: <span className="font-medium">{lead.salesAgent.fullName}</span>
          </p>
        </div>
      )}
      
      {/* Applications Count */}
      {lead.applications && lead.applications.length > 0 && (
        <div className="border-t border-gray-100 pt-2 mt-3">
          <p className="text-xs text-blue-600 font-medium">
            {lead.applications.length} Application{lead.applications.length !== 1 ? 's' : ''}
          </p>
        </div>
      )}
    </div>
  );
};

export default LeadCard;
