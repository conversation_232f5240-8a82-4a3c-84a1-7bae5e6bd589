import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFileInvoice, faMoneyBillWave } from "@fortawesome/free-solid-svg-icons";

const XeroIntegrationHeader = ({ selectedTab, handleTabChange }) => {
  return (
    <div className="mb-6">
      <h1 className="text-2xl font-semibold text-[#3A3541] mb-2">Xero Integration</h1>
      <p className="text-sm text-[#89868D] mb-6">
        Import and manage Xero data for invoicing and payment tracking
      </p>
      
      <div className="border-b border-[#DBDCDE]">
        <nav className="flex" aria-label="Tabs">
          <button
            className={`py-4 px-6 text-sm font-medium flex items-center gap-2 ${
              selectedTab === "KPI1"
                ? "text-[#6E39CB] border-b-2 border-[#6E39CB]"
                : "text-[#89868D] hover:text-[#3A3541]"
            }`}
            onClick={() => handleTabChange("KPI1")}
          >
            <FontAwesomeIcon icon={faFileInvoice} />
            <span>KPI1 Invoicing</span>
          </button>
          <button
            className={`py-4 px-6 text-sm font-medium flex items-center gap-2 ${
              selectedTab === "KPI2"
                ? "text-[#6E39CB] border-b-2 border-[#6E39CB]"
                : "text-[#89868D] hover:text-[#3A3541]"
            }`}
            onClick={() => handleTabChange("KPI2")}
          >
            <FontAwesomeIcon icon={faMoneyBillWave} />
            <span>KPI2 Money in Bank</span>
          </button>
        </nav>
      </div>
    </div>
  );
};

export default XeroIntegrationHeader;
