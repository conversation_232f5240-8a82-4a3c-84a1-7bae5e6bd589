import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;


export const ProviderApiService = createApi({
  reducerPath: "ProviderApiService",
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL + "/api/agent",
    prepareHeaders: (headers) => {
      const token = localStorage.getItem("token");
      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getProviderDetails: builder.query({
      query: (token) => {
        return {
          url: "/providers/all",
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
      },
    }),
    addProvider: builder.mutation({
      query: (provider) => ({
        url: "/providers/add",
        method: "POST",
        body: provider,
        headers: {
          "Content-Type": "application/json",
        },
      }),
    }),
  }),
});

export const { useAddProviderMutation, useGetProviderDetailsQuery } =
  ProviderApiService;
