package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.modules.sales.models.ApplicationDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Getter
@Setter
public class DetailedProfileDTO {
    private ProfileDTO profile;
    private List<ApplicationDTO> applications;
    private List<LeadDTO> leads;

    private int totalApplications;
    private int totalLeads;
    private double lifetimeRevenue ;
    private String createdAt;

    public DetailedProfileDTO() {
        this.applications = new ArrayList<>();
        this.leads = new ArrayList<>();
    }



}
