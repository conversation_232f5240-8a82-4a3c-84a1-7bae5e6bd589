package com.skillsync.applyr.modules.company.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ExportRequestDTO {
    // Export configuration
    private String format; // CSV or EXCEL
    private List<String> selectedColumns;
    
    // Filter criteria (embedded)
    private int page = 0;
    private int size = 100;
    private String sortField;
    private String sortDirection = "desc";
    private String search;
    private String agentFilter;
    private String statusFilter;
    private String applicationFilter;
    private String dateFilterType;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private LocalDateTime specificDate;
    private List<String> selectedLeads;
    
    // Convert to LeadFilterCriteria for backward compatibility
    public LeadFilterCriteria toLeadFilterCriteria() {
        LeadFilterCriteria criteria = new LeadFilterCriteria();
        criteria.setPage(this.page);
        criteria.setSize(this.size);
        criteria.setSortField(this.sortField);
        criteria.setSortDirection(this.sortDirection);
        criteria.setSearch(this.search);
        criteria.setAgentFilter(this.agentFilter);
        criteria.setStatusFilter(this.statusFilter);
        criteria.setApplicationFilter(this.applicationFilter);
        criteria.setDateFilterType(this.dateFilterType);
        criteria.setStartDate(this.startDate);
        criteria.setEndDate(this.endDate);
        criteria.setSpecificDate(this.specificDate);
        criteria.setSelectedLeads(this.selectedLeads);
        return criteria;
    }
}
