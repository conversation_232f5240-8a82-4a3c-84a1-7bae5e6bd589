package com.skillsync.applyr.modules.company.xero_models;

import com.skillsync.applyr.modules.company.models.ApplicationResponseDTO;
import com.skillsync.applyr.modules.company.models.ProfileDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TrueXeroInvoiceDTO {
    private String invoiceNumber;
    private String quoteNumber;
    private String contactName;

    private double gross;
    private double balance;
    private String status;

    private String applicationId;

    private ProfileDTO agent;

    private String invoiceDate;
    private String invoiceExpiryDate;
    private String source;

    private String invoiceSentStatus;
}
