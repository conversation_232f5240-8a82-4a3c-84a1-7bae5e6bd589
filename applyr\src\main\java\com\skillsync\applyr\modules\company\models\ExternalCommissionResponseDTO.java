package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.core.models.enums.CommissionPaymentStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ExternalCommissionResponseDTO {
    private ApplicationResponseDTO application;
    private String externalCommissionName;
    private String externalCommissionContactInfo;
    private double commissionAmount;
    private CommissionPaymentStatus paymentStatus;
    private LocalDateTime paymentDate;
}
