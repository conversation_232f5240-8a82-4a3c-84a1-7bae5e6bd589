import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faTrash } from "@fortawesome/free-solid-svg-icons";

const ApplicationComments = ({
  comments,
  newComment,
  setNewComment,
  editingComment,
  editCommentText,
  setEditCommentText,
  handleAddComment,
  handleEditComment,
  handleUpdateComment,
  handleDeleteComment,
  cancelEdit
}) => {
  return (
    <div
      className="p-6"
      role="tabpanel"
      id="comments-panel"
      aria-labelledby="comments-tab"
    >
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-1">Comments</h2>
        <p className="text-sm text-gray-500">
          Add and manage comments for this application
        </p>
      </div>

      {/* Add Comment Section */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <h5 className="text-md font-medium text-gray-900 mb-4">Add New Comment</h5>
        <div className="space-y-4">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Write your comment here..."
            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] resize-none"
            rows="3"
          />
          <div className="flex justify-end">
            <button
              onClick={handleAddComment}
              disabled={!newComment.trim()}
              className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8] transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              Add Comment
            </button>
          </div>
        </div>
      </div>

      {/* Comments List */}
      <div className="space-y-4">
        {comments.length > 0 ? (
          comments.map((comment) => (
            <div key={comment.id} className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className="bg-[#F4F5F9] rounded-full h-10 w-10 flex items-center justify-center">
                    <span className="text-[#6E39CB] text-sm font-medium">
                      {comment.createdBy ? comment.createdBy.charAt(0).toUpperCase() : "?"}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm font-medium text-gray-900">
                        {comment.createdBy || "Unknown User"}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(comment.createdAt).toLocaleDateString()} at {new Date(comment.createdAt).toLocaleTimeString()}
                      </span>
                    </div>
                    {editingComment === comment.id ? (
                      <div className="space-y-3">
                        <textarea
                          value={editCommentText}
                          onChange={(e) => setEditCommentText(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] resize-none"
                          rows="3"
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={handleUpdateComment}
                            className="px-3 py-1 bg-[#6E39CB] text-white text-sm rounded hover:bg-[#5E2CB8] transition-colors"
                          >
                            Save
                          </button>
                          <button
                            onClick={cancelEdit}
                            className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300 transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-700 text-sm leading-relaxed">{comment.content}</p>
                    )}
                  </div>
                </div>
                {editingComment !== comment.id && (
                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => handleEditComment(comment)}
                      className="p-1 text-gray-400 hover:text-[#6E39CB] transition-colors"
                      title="Edit comment"
                    >
                      <FontAwesomeIcon icon={faEdit} className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteComment(comment.id)}
                      className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete comment"
                    >
                      <FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-8 text-center">
            <div className="flex flex-col items-center justify-center">
              <div className="bg-[#F4F5F9] p-4 rounded-full mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-[#6E39CB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Comments Yet</h3>
              <p className="text-gray-500 mb-4">Be the first to add a comment to this application.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApplicationComments;
