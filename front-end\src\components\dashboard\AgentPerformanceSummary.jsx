import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheckCircle, faTimesCircle, faExclamationCircle } from "@fortawesome/free-solid-svg-icons";

const AgentPerformanceSummary = ({ agentInfos }) => {
  if (!agentInfos || agentInfos.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Agent Performance Summary</h2>
        <p className="text-gray-500">No agent data available</p>
      </div>
    );
  }

  // Determine KPI status for each agent
  const agentsWithStatus = agentInfos.map(agent => {
    const kpi1Status = agent.kpi1Actual >= agent.kpi1Target ? "met" : "not-met";
    const kpi2Status = agent.kpi2Actual >= agent.kpi2Target ? "met" : "not-met";
    
    // Overall status logic
    let overallStatus;
    if (kpi1Status === "met" && kpi2Status === "met") {
      overallStatus = "both-met";
    } else if (kpi1Status === "met") {
      overallStatus = "kpi1-only";
    } else if (kpi2Status === "met") {
      overallStatus = "kpi2-only";
    } else {
      overallStatus = "none-met";
    }
    
    return {
      name: agent.profile?.fullName || agent.profile?.username,
      kpi1Status,
      kpi2Status,
      overallStatus,
      kpi1Target: agent.kpi1Target,
      kpi1Actual: agent.kpi1Actual,
      kpi2Target: agent.kpi2Target,
      kpi2Actual: agent.kpi2Actual
    };
  });

  // Sort agents: first by those who met both KPIs, then by those who met one, then by those who met none
  const sortedAgents = [...agentsWithStatus].sort((a, b) => {
    const statusOrder = {
      "both-met": 0,
      "kpi1-only": 1,
      "kpi2-only": 2,
      "none-met": 3
    };
    
    return statusOrder[a.overallStatus] - statusOrder[b.overallStatus];
  });

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Agent Performance Summary</h2>
          <p className="text-sm text-gray-500">Simple overview of all agents and their target achievement status</p>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full text-sm">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="py-3 px-4 text-left font-medium text-gray-500">AGENT</th>
              <th className="py-3 px-4 text-center font-medium text-gray-500">KPI1 STATUS</th>
              <th className="py-3 px-4 text-center font-medium text-gray-500">KPI2 STATUS</th>
              <th className="py-3 px-4 text-center font-medium text-gray-500">OVERALL</th>
            </tr>
          </thead>
          <tbody>
            {sortedAgents.map((agent, idx) => (
              <tr 
                key={idx} 
                className={`border-b border-gray-100 hover:bg-[#F4F5F9] transition-colors ${
                  agent.overallStatus === 'none-met' ? 'bg-red-50' : 
                  agent.overallStatus === 'both-met' ? 'bg-green-50' : ''
                }`}
              >
                <td className="py-4 px-4 font-medium text-gray-900">
                  {agent.name}
                </td>
                <td className="py-4 px-4 text-center">
                  {agent.kpi1Status === "met" ? (
                    <div className="flex items-center justify-center">
                      <FontAwesomeIcon 
                        icon={faCheckCircle} 
                        className="text-green-600 mr-2" 
                      />
                      <span className="text-green-600 font-medium">Met</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <FontAwesomeIcon 
                        icon={faTimesCircle} 
                        className="text-red-500 mr-2" 
                      />
                      <span className="text-red-500 font-medium">Not Met</span>
                      <span className="ml-2 text-xs text-gray-500">
                        ({Math.round((agent.kpi1Actual / agent.kpi1Target) * 100)}%)
                      </span>
                    </div>
                  )}
                </td>
                <td className="py-4 px-4 text-center">
                  {agent.kpi2Status === "met" ? (
                    <div className="flex items-center justify-center">
                      <FontAwesomeIcon 
                        icon={faCheckCircle} 
                        className="text-green-600 mr-2" 
                      />
                      <span className="text-green-600 font-medium">Met</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <FontAwesomeIcon 
                        icon={faTimesCircle} 
                        className="text-red-500 mr-2" 
                      />
                      <span className="text-red-500 font-medium">Not Met</span>
                      <span className="ml-2 text-xs text-gray-500">
                        ({Math.round((agent.kpi2Actual / agent.kpi2Target) * 100)}%)
                      </span>
                    </div>
                  )}
                </td>
                <td className="py-4 px-4 text-center">
                  {agent.overallStatus === "both-met" ? (
                    <div className="flex items-center justify-center">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Both Met
                      </span>
                    </div>
                  ) : agent.overallStatus === "kpi1-only" ? (
                    <div className="flex items-center justify-center">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        KPI1 Only
                      </span>
                    </div>
                  ) : agent.overallStatus === "kpi2-only" ? (
                    <div className="flex items-center justify-center">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                        KPI2 Only
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        None Met
                      </span>
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AgentPerformanceSummary;
