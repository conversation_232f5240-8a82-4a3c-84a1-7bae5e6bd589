import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faPlus, faFileInvoice, faFileContract, faPhone, faEnvelope, faChevronDown, faChevronUp } from "@fortawesome/free-solid-svg-icons";
import CreateInvoiceQuoteModal from "../../components/modal/CreateInvoiceQuoteModal";
import StatusChangeModal from "../../components/modal/StatusChangeModal";
import EditReferenceModal from "../../components/modal/EditReferenceModal";
import { useChangeQuoteStatusMutation, useChangeInvoiceStatusMutation } from "../../services/CompanyAPIService";

const QuoteCard = ({
  onQuoteUpdated, // New prop for handling data refresh
  agentName,
  clientName,
  clientEmail,
  clientPhone,
  candidateName,
  phoneNumber,
  email,
  otherInformation,
  quoteRequestDetail,
  price,
  quoteNumber,
  invoiceNumber,
  quoteRefNumber,
  quoteStatus = "SENT",
  invoiceStatus = "SENT",
  applicationId,
}) => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isQuoteStatusModalOpen, setIsQuoteStatusModalOpen] = useState(false);
  const [isInvoiceStatusModalOpen, setIsInvoiceStatusModalOpen] = useState(false);
  const [isEditQuoteModalOpen, setIsEditQuoteModalOpen] = useState(false);
  const [isEditInvoiceModalOpen, setIsEditInvoiceModalOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  // Use the actual status values from props
  const [localQuoteStatus, setLocalQuoteStatus] = useState(quoteStatus);
  const [localInvoiceStatus, setLocalInvoiceStatus] = useState(invoiceStatus);

  // Use the mutation hooks directly
  const [changeQuoteStatus] = useChangeQuoteStatusMutation();
  const [changeInvoiceStatus] = useChangeInvoiceStatusMutation();

  // Handle open/close of the modals
  const handleOpenCreateModal = () => setIsCreateModalOpen(true);
  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  const handleOpenQuoteStatusModal = () => setIsQuoteStatusModalOpen(true);
  const handleCloseQuoteStatusModal = () => {
    setIsQuoteStatusModalOpen(false);
    // Refresh the data by refetching the query
    // This will update the UI with the latest status
  };

  const handleOpenInvoiceStatusModal = () => setIsInvoiceStatusModalOpen(true);
  const handleCloseInvoiceStatusModal = () => {
    setIsInvoiceStatusModalOpen(false);
    // Refresh the data by refetching the query
    // This will update the UI with the latest status
  };

  // Edit reference modals
  const handleOpenEditQuoteModal = () => setIsEditQuoteModalOpen(true);
  const handleCloseEditQuoteModal = () => setIsEditQuoteModalOpen(false);

  const handleOpenEditInvoiceModal = () => setIsEditInvoiceModalOpen(true);
  const handleCloseEditInvoiceModal = () => setIsEditInvoiceModalOpen(false);

  // Format price to ensure consistent display
  const formattedPrice = typeof price === 'number'
    ? price.toLocaleString('en-AU', { style: 'currency', currency: 'AUD' })
    : price.startsWith('$') ? price : `$${price}`;

  return (
    <div className="border rounded-lg shadow-sm bg-white mb-4 relative hover:shadow-md transition-all duration-200">
      {/* Card header with essential information */}
      <div className="p-4 bg-white border-b border-gray-100 flex justify-between items-center">
        <div className="flex-1">
          <div className="flex items-center">
            <div>
              <div className="mb-1">
                <span className="text-xs font-medium text-gray-500 uppercase">Source</span>
                <h3 className="text-lg font-semibold text-gray-900">{clientName}</h3>
              </div>
              <div className="flex items-center mt-2">
                <div className="mr-4">
                  <span className="text-xs font-medium text-gray-500 uppercase">Candidate</span>
                  <div className="text-sm text-gray-700">{candidateName}</div>
                </div>
                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase">Agent</span>
                  <div className="text-sm text-gray-700">{agentName}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Status badges - only show when there's a quote reference */}
          {quoteRefNumber && (
            <div className="flex space-x-2">
              <span className="inline-flex text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-800">
                Q: {localQuoteStatus}
              </span>
              <span className="inline-flex text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-800">
                I: {localInvoiceStatus}
              </span>
            </div>
          )}

          {/* Raise Quote and Invoice button - show in minimized view when both quote and invoice numbers exist and are not empty */}
          {!isExpanded && quoteNumber && invoiceNumber && quoteNumber.trim() !== '' && invoiceNumber.trim() !== '' && (
            <button
              className="inline-flex items-center bg-blue-600 text-white py-2 px-4 rounded-md shadow hover:bg-blue-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 text-sm font-medium"
              onClick={handleOpenCreateModal}
            >
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Raise Quote & Invoice
            </button>
          )}

          {/* Price */}
          <div className="font-bold text-lg text-gray-800">{formattedPrice}</div>

          {/* Expand/Collapse button */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 ml-4"
            title={isExpanded ? "Collapse" : "Expand"}
          >
            <FontAwesomeIcon
              icon={isExpanded ? faChevronUp : faChevronDown}
              className="text-gray-500"
            />
          </button>
        </div>
      </div>

      {/* Card content - only show when expanded */}
      {isExpanded && (
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left column - Contact information and Quote Request Details */}
            <div>
              <div className="flex items-center mb-3">
                <h4 className="text-sm font-semibold text-gray-700">Contact Information</h4>
                <div className="ml-auto flex space-x-2">
                  {phoneNumber && (
                    <a href={`tel:${phoneNumber}`} className="text-gray-500 hover:text-blue-600 p-1 bg-gray-100 rounded-full">
                      <FontAwesomeIcon icon={faPhone} />
                    </a>
                  )}
                  {email && (
                    <a href={`mailto:${email}`} className="text-gray-500 hover:text-blue-600 p-1 bg-gray-100 rounded-full">
                      <FontAwesomeIcon icon={faEnvelope} />
                    </a>
                  )}
                </div>
              </div>

              <div className="bg-white p-3 rounded-md border border-gray-200 mb-4">
                <div className="grid grid-cols-2 gap-x-3 gap-y-2 text-sm">
                  <h6 className="font-semibold text-gray-700 col-span-2 mb-1 pb-1 border-b border-gray-100">Candidate</h6>
                  <span className="font-medium text-gray-600">Phone:</span>
                  <span className="text-gray-800">{phoneNumber}</span>

                  <span className="font-medium text-gray-600">Email:</span>
                  <span className="text-gray-800 truncate">{email}</span>

                  <h6 className="font-semibold text-gray-700 col-span-2 mt-3 mb-1 pb-1 border-b border-gray-100">Source</h6>
                  <span className="font-medium text-gray-600">Phone:</span>
                  <span className="text-gray-800">{clientPhone || "N/A"}</span>

                  <span className="font-medium text-gray-600">Email:</span>
                  <span className="text-gray-800 truncate">{clientEmail || "N/A"}</span>

                  {otherInformation && (
                    <>
                      <span className="font-medium text-gray-600 col-span-2 mt-3">Other Info:</span>
                      <span className="text-gray-800 col-span-2 whitespace-pre-wrap break-words">{otherInformation}</span>
                    </>
                  )}
                </div>
              </div>

              {/* Quote Request Details */}
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-2">Quote Request Details</h4>
                <div className="bg-white p-3 rounded-md border border-gray-200">
                  <p className="text-gray-800 text-sm">{quoteRequestDetail}</p>
                </div>
              </div>
            </div>

            {/* Right column - Quote and Invoice information */}
            <div>
              {!quoteRefNumber ? (
                <div className="flex flex-col justify-center items-center h-full bg-white p-6 rounded-md border border-dashed border-gray-300">
                  <button
                    className="inline-flex items-center bg-blue-600 text-white py-3 px-6 rounded-md shadow hover:bg-blue-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 text-base font-medium"
                    onClick={handleOpenCreateModal}
                  >
                    <FontAwesomeIcon icon={faPlus} className="mr-2" />
                    Raise Quote and Invoice
                  </button>
                  <p className="text-gray-500 text-sm mt-3">No quote or invoice has been created yet</p>
                </div>
              ) : (
                <div>
                  <h4 className="text-sm font-semibold text-gray-700 mb-3">Quote & Invoice Information</h4>
                  <div className="grid grid-cols-1 gap-4">
                    {/* Quote Card - black and white */}
                    <div className="border border-gray-200 bg-white rounded-lg p-4 shadow-sm">
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center">
                          <div className="bg-gray-100 p-2 rounded-full mr-3">
                            <FontAwesomeIcon icon={faFileContract} className="text-gray-600" />
                          </div>
                          <div>
                            <p className="font-semibold text-gray-700 text-xs uppercase">Quote Reference</p>
                            <p className="text-xl font-bold text-gray-800">{quoteNumber}</p>
                          </div>
                        </div>
                        <button
                          className="text-gray-600 hover:bg-gray-100 p-2 rounded-full transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-gray-400"
                          aria-label="Edit Quote"
                          title="Edit Quote"
                          onClick={handleOpenEditQuoteModal}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </button>
                      </div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">Status</label>
                      <div className="flex items-center">
                        <div className="flex-1 font-medium text-sm text-gray-700">{localQuoteStatus}</div>
                        <button
                          onClick={handleOpenQuoteStatusModal}
                          className="ml-2 text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-1 rounded"
                        >
                          Change
                        </button>
                      </div>
                    </div>

                    {/* Invoice Card - black and white */}
                    <div className="border border-gray-200 bg-white rounded-lg p-4 shadow-sm">
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center">
                          <div className="bg-gray-100 p-2 rounded-full mr-3">
                            <FontAwesomeIcon icon={faFileInvoice} className="text-gray-600" />
                          </div>
                          <div>
                            <p className="font-semibold text-gray-700 text-xs uppercase">Invoice Reference</p>
                            <p className="text-xl font-bold text-gray-800">{invoiceNumber}</p>
                          </div>
                        </div>
                        <button
                          className="text-gray-600 hover:bg-gray-100 p-2 rounded-full transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-gray-400"
                          aria-label="Edit Invoice"
                          title="Edit Invoice"
                          onClick={handleOpenEditInvoiceModal}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </button>
                      </div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">Status</label>
                      <div className="flex items-center">
                        <div className="flex-1 font-medium text-sm text-gray-700">{localInvoiceStatus}</div>
                        <button
                          onClick={handleOpenInvoiceStatusModal}
                          className="ml-2 text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-1 rounded"
                        >
                          Change
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      <CreateInvoiceQuoteModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
        applicationId={applicationId}
        onSuccess={() => {
          // Call the parent's refresh function if provided
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />

      <StatusChangeModal
        isOpen={isQuoteStatusModalOpen}
        onClose={handleCloseQuoteStatusModal}
        type="quote"
        applicationId={applicationId}
        refNumber={quoteNumber}
        currentStatus={localQuoteStatus}
        onSuccess={() => {
          // Call the parent's refresh function if provided
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />

      <StatusChangeModal
        isOpen={isInvoiceStatusModalOpen}
        onClose={handleCloseInvoiceStatusModal}
        type="invoice"
        applicationId={applicationId}
        refNumber={invoiceNumber}
        currentStatus={localInvoiceStatus}
        onSuccess={() => {
          // Call the parent's refresh function if provided
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />

      {/* Edit Reference Modals */}
      <EditReferenceModal
        isOpen={isEditQuoteModalOpen}
        onClose={handleCloseEditQuoteModal}
        type="quote"
        applicationId={applicationId}
        currentRefNumber={quoteNumber}
        onSuccess={() => {
          // Call the parent's refresh function if provided
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />

      <EditReferenceModal
        isOpen={isEditInvoiceModalOpen}
        onClose={handleCloseEditInvoiceModal}
        type="invoice"
        applicationId={applicationId}
        currentRefNumber={invoiceNumber}
        onSuccess={() => {
          // Call the parent's refresh function if provided
          if (onQuoteUpdated && typeof onQuoteUpdated === 'function') {
            onQuoteUpdated();
          }
        }}
      />
    </div>
  );
};

export default QuoteCard;
