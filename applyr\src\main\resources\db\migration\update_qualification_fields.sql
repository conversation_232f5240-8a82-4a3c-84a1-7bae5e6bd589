-- Migration script to handle new fields in Qualification entity
-- This script should be run manually if there are existing records in the qualifications table

-- Update existing records to set default values for new fields
UPDATE qualifications 
SET 
    rto_price_high = 0,
    processing_time = '',
    demand = ''
WHERE 
    rto_price_high IS NULL 
    OR processing_time IS NULL 
    OR demand IS NULL;

-- Note: The checklist field will be handled automatically by Hibernate as it creates a separate table
-- for @ElementCollection fields
