// Helper functions for leads functionality

// Helper for status styling
export function getStatusClass(status) {
  switch (status) {
    case "HOT":
      return "bg-red-100 text-red-600";
    case "WARM":
      return "bg-yellow-100 text-yellow-600";
    case "COLD":
      return "bg-blue-100 text-blue-600";
    case "FRESH":
      return "bg-green-100 text-green-600";
    case "CLOSED":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-600";
  }
}

// Helper for date formatting (dd MMM yyyy)
export function formatDate(dateString) {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "N/A";

  const day = date.getDate().toString().padStart(2, '0');
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const month = months[date.getMonth()];
  const year = date.getFullYear();

  return `${day} ${month} ${year}`;
}

// Date filtering utilities
export function getDateRange(dateFilterType, startDate = null, endDate = null, specificDate = null) {
  const now = new Date();

  switch (dateFilterType) {
    case "today":
      const todayStart = new Date(now);
      todayStart.setHours(0, 0, 0, 0);
      const todayEnd = new Date(now);
      todayEnd.setHours(23, 59, 59, 999);
      return { start: todayStart, end: todayEnd };

    case "thisWeek":
      const dayOfWeek = now.getDay();
      const diffToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - diffToMonday);
      weekStart.setHours(0, 0, 0, 0);
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);
      return { start: weekStart, end: weekEnd };

    case "thisMonth":
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      monthStart.setHours(0, 0, 0, 0);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      monthEnd.setHours(23, 59, 59, 999);
      return { start: monthStart, end: monthEnd };

    case "thisYear":
      const yearStart = new Date(now.getFullYear(), 0, 1);
      yearStart.setHours(0, 0, 0, 0);
      const yearEnd = new Date(now.getFullYear(), 11, 31);
      yearEnd.setHours(23, 59, 59, 999);
      return { start: yearStart, end: yearEnd };

    case "custom":
      if (startDate && endDate) {
        const customStart = new Date(startDate);
        customStart.setHours(0, 0, 0, 0);
        const customEnd = new Date(endDate);
        customEnd.setHours(23, 59, 59, 999);
        return { start: customStart, end: customEnd };
      }
      return null;

    case "specificDate":
      if (specificDate) {
        const dateStart = new Date(specificDate);
        dateStart.setHours(0, 0, 0, 0);
        const dateEnd = new Date(specificDate);
        dateEnd.setHours(23, 59, 59, 999);
        return { start: dateStart, end: dateEnd };
      }
      return null;

    default:
      return null;
  }
}

// Check if a date falls within a date range
export function isDateInRange(dateString, dateRange) {
  if (!dateString || !dateRange) return true;

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return true;

  return date >= dateRange.start && date <= dateRange.end;
}

// Filter leads based on search criteria
export function filterLeads(leads, filters) {
  const {
    leadSearch,
    selectedAgentFilter,
    leadStatusFilter,
    leadTypeFilter,
    dateFilterType,
    startDate,
    endDate,
    specificDate,
    applicationFilter
  } = filters;

  return leads.filter(lead => {
    const matchesSearch =
      !leadSearch ||
      (lead.companyName && lead.companyName.toLowerCase().includes(leadSearch.toLowerCase())) ||
      (lead.leadName && lead.leadName.toLowerCase().includes(leadSearch.toLowerCase())) ||
      (lead.email && lead.email.toLowerCase().includes(leadSearch.toLowerCase()));

    const matchesAgent =
      !selectedAgentFilter ||
      (lead.assignedAgents && lead.assignedAgents.some(agent => agent.fullName === selectedAgentFilter));

    // Handle status filtering (for export page)
    const matchesStatus =
      !leadStatusFilter || leadStatusFilter === "all" ||
      (lead.status && lead.status.toLowerCase() === leadStatusFilter.toLowerCase());

    // Handle type filtering (for main leads page - B2B vs Direct)
    const matchesLeadType =
      !leadTypeFilter || leadTypeFilter === "all" ||
      (leadTypeFilter === "B2B" && lead.companyName && lead.companyName.toLowerCase() !== "n/a") ||
      (leadTypeFilter === "Direct" && (!lead.companyName || lead.companyName.toLowerCase() === "n/a"));

    // Handle date filtering
    const matchesDate = (() => {
      if (!dateFilterType || dateFilterType === "all") return true;

      const dateRange = getDateRange(dateFilterType, startDate, endDate, specificDate);
      return isDateInRange(lead.createdDate, dateRange);
    })();

    // Handle application filtering
    const matchesApplication = (() => {
      if (!applicationFilter || applicationFilter === "all") return true;

      if (applicationFilter === "hasApplications") {
        return lead.applicationCount > 0;
      } else if (applicationFilter === "noApplications") {
        return lead.applicationCount === 0;
      }

      return true;
    })();

    return matchesSearch && matchesAgent && matchesStatus && matchesLeadType && matchesDate && matchesApplication;
  });
}

// Filter leads by tab status
export function filterLeadsByTab(leads, leadTab) {
  return leads.filter(lead => {
    if (leadTab === "All") return true;
    if (leadTab === "Leads with Applications" && lead.applicationCount <= 0) return false;
    if (leadTab === "COLD_FRESH" && lead.status !== "COLD" && lead.status !== "FRESH") return false;
    if (leadTab === "CLOSED" && lead.status !== "CLOSED") return false;
    if (leadTab !== "Leads with Applications" && leadTab !== "COLD_FRESH" && leadTab !== "CLOSED" && lead.status !== leadTab) return false;
    return true;
  });
}

// Sort leads based on field and direction
export function sortLeads(leads, sortField, sortDirection) {
  return [...leads].sort((a, b) => {
    // If no sort field is selected, sort by creation time (newest first)
    if (!sortField) {
      const timeA = new Date(a.updatedAt || a.createdAt);
      const timeB = new Date(b.updatedAt || b.createdAt);
      return timeB - timeA;
    }

    // Handle sorting by selected field
    let aValue = '';
    let bValue = '';

    switch (sortField) {
      case 'companyName':
        aValue = (a.companyName || '').toLowerCase();
        bValue = (b.companyName || '').toLowerCase();
        break;
      case 'leadName':
        aValue = (a.leadName || '').toLowerCase();
        bValue = (b.leadName || '').toLowerCase();
        break;
      case 'phone':
        aValue = (a.phone || '').toLowerCase();
        bValue = (b.phone || '').toLowerCase();
        break;
      case 'email':
        aValue = (a.email || '').toLowerCase();
        bValue = (b.email || '').toLowerCase();
        break;
      case 'createdDate':
        aValue = new Date(a.createdDate || 0);
        bValue = new Date(b.createdDate || 0);
        break;
      default:
        return 0;
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });
}

// Calculate lead statistics
export function calculateLeadStats(leads) {
  const totalLeads = leads.length;
  const statusCounts = leads.reduce((counts, lead) => {
    counts[lead.status] = (counts[lead.status] || 0) + 1;
    return counts;
  }, { HOT: 0, WARM: 0, COLD: 0, FRESH: 0, CLOSED: 0 });
  const leadsWithApplications = leads.filter(lead => lead.applicationCount > 0).length;

  return {
    totalLeads,
    statusCounts,
    leadsWithApplications
  };
}

// Get unique agent names from leads
export function getUniqueAgentNames(leads) {
  return Array.from(new Set(leads.flatMap(lead => (lead.assignedAgents || []).map(agent => agent.fullName))));
}
