package com.skillsync.applyr.modules.company.xero_models;

import com.skillsync.applyr.modules.company.models.ProfileDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TrueXeroInBankDTO {
    private Long id;
    private String agentUsername;
    private ProfileDTO agent;
    private String invoiceNumber;
    private String contactName;
    private String applicationId;
    private LocalDateTime insertDate;

    private String description;
    private double debitAmount;
    private double creditAmount;
    private double netAmount;
    private String source;
}

