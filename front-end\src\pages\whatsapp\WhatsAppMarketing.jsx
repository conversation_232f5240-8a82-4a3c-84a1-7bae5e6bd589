import React, { useState, useRef, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useGetLeadsQuery, useGetAgentLeadsQuery } from '../../services/CompanyAPIService';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUpload,
  faCalendarAlt,
  faPaperPlane,
  faPlus,
  faTrash,
  faDatabase,
  faCommentDots,
  faInfoCircle,
  faSearch,
  faCheck,
  faPhone,
  faEnvelope,
  faBuilding,
  faUser,
  faFilter
} from '@fortawesome/free-solid-svg-icons';

const WhatsAppMarketing = () => {
  const location = useLocation();
  const isAdmin = location.pathname.includes('/admin/');

  // API queries
  const { data: adminLeads, isLoading: adminLeadsLoading } = useGetLeadsQuery(undefined, {
    skip: !isAdmin
  });
  const { data: agentLeads, isLoading: agentLeadsLoading } = useGetAgentLeadsQuery(undefined, {
    skip: isAdmin
  });

  // Determine which leads to use based on user role
  const leads = isAdmin ? adminLeads : agentLeads;
  const leadsLoading = isAdmin ? adminLeadsLoading : agentLeadsLoading;

  // State for file upload
  const [file, setFile] = useState(null);
  const [isUsingLeads, setIsUsingLeads] = useState(false);
  const fileInputRef = useRef(null);

  // State for lead selection
  const [showLeadSelectionModal, setShowLeadSelectionModal] = useState(false);
  const [selectedLeads, setSelectedLeads] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');

  // State for message template
  const [messageTemplate, setMessageTemplate] = useState('e.g. Hey \{\{Name\}\}, congratulations on your new position as \{\{Position\}\}!');

  // State for keywords
  const [keywords, setKeywords] = useState('');

  // State for scheduled messages
  const [schedules, setSchedules] = useState([]);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [currentSchedule, setCurrentSchedule] = useState({
    date: '',
    time: '',
    description: ''
  });

  // State for sent log
  const [sentLog, setShowSentLog] = useState(false);
  const [sentMessages, setSentMessages] = useState([]);

  // Effect to reset selected leads when leads data changes
  useEffect(() => {
    if (leads && isUsingLeads) {
      // Keep only the selected leads that still exist in the updated leads data
      setSelectedLeads(prevSelected =>
        prevSelected.filter(selected =>
          leads.some(lead => lead.phone === selected.phone)
        )
      );
    }
  }, [leads, isUsingLeads]);

  // Handle file selection
  const handleFileChange = (e) => {
    if (e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setIsUsingLeads(false);
    }
  };

  // Handle file upload button click
  const handleUploadClick = () => {
    fileInputRef.current.click();
  };

  // Handle using leads data
  const handleUseLeadsData = () => {
    setFile(null);
    setIsUsingLeads(true);
    setShowLeadSelectionModal(true);
  };

  // Handle lead selection
  const handleLeadSelection = (lead) => {
    setSelectedLeads(prev => {
      const isAlreadySelected = prev.some(item => item.phone === lead.phone);
      if (isAlreadySelected) {
        return prev.filter(item => item.phone !== lead.phone);
      } else {
        return [...prev, lead];
      }
    });
  };

  // Filter leads based on search term and status
  const filteredLeads = leads ? leads.filter(lead => {
    const matchesSearch =
      lead.leadName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.phone?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.email?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'ALL' || lead.status === statusFilter;

    return matchesSearch && matchesStatus;
  }) : [];

  // Handle message template change
  const handleMessageTemplateChange = (e) => {
    setMessageTemplate(e.target.value);
  };

  // Handle keywords change
  const handleKeywordsChange = (e) => {
    setKeywords(e.target.value);
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'FRESH':
        return 'bg-blue-100 text-blue-800';
      case 'CONTACTED':
        return 'bg-purple-100 text-purple-800';
      case 'INTERESTED':
        return 'bg-green-100 text-green-800';
      case 'NOT_INTERESTED':
        return 'bg-red-100 text-red-800';
      case 'CONVERTED':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle schedule form change
  const handleScheduleChange = (e) => {
    const { name, value } = e.target;
    setCurrentSchedule(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Add a new schedule
  const addSchedule = () => {
    if (currentSchedule.date && currentSchedule.time) {
      setSchedules([
        ...schedules,
        {
          id: Date.now(),
          ...currentSchedule
        }
      ]);
      setCurrentSchedule({
        date: '',
        time: '',
        description: ''
      });
      setShowScheduleModal(false);
    }
  };

  // Remove a schedule
  const removeSchedule = (id) => {
    setSchedules(schedules.filter(schedule => schedule.id !== id));
  };

  // Send messages
  const sendMessages = () => {
    // Determine the recipients based on the data source
    let recipients = [];

    if (isUsingLeads && selectedLeads.length > 0) {
      // Use selected leads as recipients
      recipients = selectedLeads.map(lead => ({
        name: lead.leadName,
        phone: lead.phone,
        email: lead.email,
        company: lead.companyName
      }));
    } else if (file) {
      // In a real implementation, we would parse the CSV file here
      // For now, just show a placeholder message
      alert('CSV file would be processed here');
      recipients = [{ name: 'CSV Recipients', phone: 'Multiple' }];
    } else {
      alert('Please select a data source (CSV file or leads)');
      return;
    }

    // This would be replaced with actual API call
    alert(`Messages will be sent to ${recipients.length} recipients${schedules.length > 0 ? ' as scheduled' : ' immediately'}!`);

    // Add to sent log for demo purposes
    const newSentMessages = recipients.map(recipient => ({
      id: Date.now() + Math.random(),
      recipient: recipient.phone,
      recipientName: recipient.name,
      status: schedules.length > 0 ? 'Scheduled' : 'Pending',
      timestamp: new Date().toLocaleString()
    }));

    setSentMessages([...newSentMessages, ...sentMessages]);
  };

  // Toggle sent log
  const toggleSentLog = () => {
    setShowSentLog(!sentLog);
  };

  return (
    <div className="w-full">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">WhatsApp Marketing</h1>
          <p className="mt-1 text-sm text-gray-500">
            Automate your WhatsApp messages for marketing campaigns
          </p>
        </div>

        <div className="mt-4 md:mt-0 flex items-center gap-3">
          <button
            onClick={toggleSentLog}
            className={`px-4 py-2 rounded-md ${sentLog ? 'bg-[#6E39CB] text-white' : 'bg-white border border-gray-200 text-gray-700'}`}
          >
            <FontAwesomeIcon icon={faPaperPlane} className="mr-2" />
            Sent Log
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          {/* Main Content */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FontAwesomeIcon icon={faCommentDots} className="text-green-500 mr-2" />
              WhatsApp Message Campaign
            </h2>

            {/* Data Source Selection */}
            <div className="mb-6">
              <h5 className="text-md font-medium text-gray-800 mb-3">Data Source</h5>
              <div className="flex flex-col md:flex-row gap-4">
                <button
                  onClick={handleUploadClick}
                  className={`flex-1 flex items-center justify-center p-4 rounded-lg border-2 border-dashed transition-colors ${!isUsingLeads && file ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-[#6E39CB] hover:bg-[#F4F5F9]'}`}
                >
                  <div className="text-center">
                    <FontAwesomeIcon icon={faUpload} className={`text-2xl mb-2 ${!isUsingLeads && file ? 'text-green-500' : 'text-gray-400'}`} />
                    <p className="text-sm font-medium text-gray-700">
                      {!isUsingLeads && file ? file.name : 'Upload CSV File'}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {!isUsingLeads && file ? `${(file.size / 1024).toFixed(2)} KB` : 'CSV should include name and phone columns'}
                    </p>
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept=".csv"
                    className="hidden"
                  />
                </button>

                <button
                  onClick={handleUseLeadsData}
                  className={`flex-1 flex items-center justify-center p-4 rounded-lg border-2 border-dashed transition-colors ${isUsingLeads ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-[#6E39CB] hover:bg-[#F4F5F9]'}`}
                >
                  <div className="text-center">
                    <FontAwesomeIcon icon={faDatabase} className={`text-2xl mb-2 ${isUsingLeads ? 'text-green-500' : 'text-gray-400'}`} />
                    <p className="text-sm font-medium text-gray-700">Use Leads Data</p>
                    {isUsingLeads && selectedLeads.length > 0 ? (
                      <div className="mt-1">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {selectedLeads.length} leads selected
                        </span>
                      </div>
                    ) : (
                      <p className="text-xs text-gray-500 mt-1">Import contacts from your leads database</p>
                    )}
                  </div>
                </button>
              </div>
            </div>

            {/* Message Template */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-3">
                <h5 className="text-md font-medium text-gray-800">AI Base Message</h5>
                <div className="text-xs text-gray-500 flex items-center">
                  <FontAwesomeIcon icon={faInfoCircle} className="mr-1" />
                  Use placeholders like {'{'}{'{'}'Name'{'}'}{'}'}' or {'{'}{'{'}'Position'{'}'}{'}'}' for personalization
                </div>
              </div>
              <textarea
                value={messageTemplate}
                onChange={handleMessageTemplateChange}
                className="w-full h-32 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                placeholder="Enter your message template here..."
              ></textarea>
            </div>

            {/* Keywords */}
            <div className="mb-6">
              <h5 className="text-md font-medium text-gray-800 mb-3">Keywords</h5>
              <input
                type="text"
                value={keywords}
                onChange={handleKeywordsChange}
                className="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                placeholder="e.g. promotion, congratulation"
              />
            </div>

            {/* Schedule Messages */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-3">
                <h5 className="text-md font-medium text-gray-800">Schedule Messages</h5>
                <button
                  onClick={() => setShowScheduleModal(true)}
                  className="px-3 py-1 bg-[#6E39CB] text-white rounded-md text-sm flex items-center"
                >
                  <FontAwesomeIcon icon={faPlus} className="mr-1" />
                  Add Schedule
                </button>
              </div>

              {schedules.length === 0 ? (
                <div className="text-center p-6 bg-gray-50 rounded-lg border border-gray-100">
                  <p className="text-gray-500 text-sm">No schedules added yet. Add a schedule or leave empty to send immediately.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {schedules.map(schedule => (
                    <div key={schedule.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faCalendarAlt} className="text-[#6E39CB] mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-800">{schedule.date} at {schedule.time}</p>
                          {schedule.description && (
                            <p className="text-xs text-gray-500">{schedule.description}</p>
                          )}
                        </div>
                      </div>
                      <button
                        onClick={() => removeSchedule(schedule.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Send Button */}
            <div className="flex justify-center">
              <button
                onClick={sendMessages}
                className="px-6 py-3 bg-[#6E39CB] text-white rounded-md font-medium hover:bg-[#5930a8] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={!((file || (isUsingLeads && selectedLeads.length > 0)) && messageTemplate)}
              >
                <FontAwesomeIcon icon={faPaperPlane} className="mr-2" />
                {schedules.length > 0 ? 'Schedule Messages' : 'Send Messages Now'}
              </button>
            </div>
          </div>
        </div>

        {/* Sent Log Panel */}
        <div className={`lg:col-span-1 ${sentLog ? 'block' : 'hidden lg:block'}`}>
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50 sticky top-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Sent Log</h2>

            {sentMessages.length === 0 ? (
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <p className="text-gray-500">No messages sent yet</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-[600px] overflow-y-auto">
                {sentMessages.map(message => (
                  <div key={message.id} className="p-3 border-b border-gray-100 last:border-0">
                    <div className="flex justify-between items-start">
                      <div>
                        {message.recipientName && (
                          <p className="text-sm font-medium text-gray-800">{message.recipientName}</p>
                        )}
                        <p className="text-sm text-gray-700">{message.recipient}</p>
                        <p className="text-xs text-gray-500">{message.timestamp}</p>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        message.status === 'Delivered' ? 'bg-green-100 text-green-800' :
                        message.status === 'Failed' ? 'bg-red-100 text-red-800' :
                        message.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                        message.status === 'Scheduled' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {message.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Schedule Modal */}
      {showScheduleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Schedule</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input
                  type="date"
                  name="date"
                  value={currentSchedule.date}
                  onChange={handleScheduleChange}
                  className="w-full p-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Time</label>
                <input
                  type="time"
                  name="time"
                  value={currentSchedule.time}
                  onChange={handleScheduleChange}
                  className="w-full p-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                <input
                  type="text"
                  name="description"
                  value={currentSchedule.description}
                  onChange={handleScheduleChange}
                  className="w-full p-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                  placeholder="E.g., Morning campaign"
                />
              </div>
            </div>

            <div className="flex justify-end mt-6 gap-3">
              <button
                onClick={() => setShowScheduleModal(false)}
                className="px-4 py-2 border border-gray-200 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={addSchedule}
                className="px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-[#5930a8]"
                disabled={!currentSchedule.date || !currentSchedule.time}
              >
                Add Schedule
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Lead Selection Modal */}
      {showLeadSelectionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-5xl max-h-[90vh] overflow-hidden flex flex-col">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FontAwesomeIcon icon={faDatabase} className="text-[#6E39CB] mr-2" />
              Select Leads for WhatsApp Campaign
            </h3>

            {/* Search and Filter */}
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search by name, company, phone or email..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                />
              </div>

              <div className="w-full md:w-64">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="block w-full p-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
                >
                  <option value="ALL">All Statuses</option>
                  <option value="FRESH">Fresh</option>
                  <option value="CONTACTED">Contacted</option>
                  <option value="INTERESTED">Interested</option>
                  <option value="NOT_INTERESTED">Not Interested</option>
                  <option value="CONVERTED">Converted</option>
                </select>
              </div>
            </div>

            {/* Leads Table */}
            <div className="overflow-y-auto flex-1 mb-4">
              {leadsLoading ? (
                <div className="flex justify-center items-center h-40">
                  <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#6E39CB]"></div>
                </div>
              ) : filteredLeads.length === 0 ? (
                <div className="text-center p-6 bg-gray-50 rounded-lg">
                  <p className="text-gray-500">No leads found matching your criteria.</p>
                </div>
              ) : (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="w-12 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <span className="sr-only">Select</span>
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredLeads.map((lead) => {
                      const isSelected = selectedLeads.some(item => item.phone === lead.phone);
                      return (
                        <tr
                          key={lead.phone}
                          className={`hover:bg-gray-50 cursor-pointer ${isSelected ? 'bg-purple-50' : ''}`}
                          onClick={() => handleLeadSelection(lead)}
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={() => {}} // Handled by row click
                                className="h-4 w-4 text-[#6E39CB] focus:ring-[#6E39CB] border-gray-300 rounded"
                              />
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-[#F4F5F9] flex items-center justify-center">
                                <FontAwesomeIcon icon={faUser} className="text-[#6E39CB]" />
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{lead.leadName}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {lead.companyName === 'N/A' ? (
                                <span className="text-gray-400 italic">Individual</span>
                              ) : (
                                <div className="flex items-center">
                                  <FontAwesomeIcon icon={faBuilding} className="text-gray-400 mr-2" />
                                  {lead.companyName}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center text-sm text-gray-900">
                              <FontAwesomeIcon icon={faPhone} className="text-gray-400 mr-2" />
                              {lead.phone}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center text-sm text-gray-900">
                              <FontAwesomeIcon icon={faEnvelope} className="text-gray-400 mr-2" />
                              {lead.email}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(lead.status)}`}>
                              {lead.status}
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              )}
            </div>

            {/* Selected Count and Actions */}
            <div className="border-t border-gray-200 pt-4 flex flex-col md:flex-row justify-between items-center">
              <div className="text-sm text-gray-700 mb-4 md:mb-0">
                <span className="font-medium">{selectedLeads.length}</span> leads selected for WhatsApp campaign
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowLeadSelectionModal(false)}
                  className="px-4 py-2 border border-gray-200 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // Close the modal but keep the selected leads
                    setShowLeadSelectionModal(false);
                  }}
                  className="px-4 py-2 bg-[#6E39CB] text-white rounded-md hover:bg-[#5930a8]"
                  disabled={selectedLeads.length === 0}
                >
                  Confirm Selection
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WhatsAppMarketing;
