// src/pages/admin/applications/Application.jsx

import React, { useState, useEffect } from "react";
import dp from "../../../assets/images.jfif"; // Ensure the path is correct
import logo from "../../../assets/logo.png"; // Ensure the path is correct
import DocumentModal from "../../../components/modal/DocumentModal";
import DocumentButton from "../../../components/modal/DocumentButton";
import QRCode from "react-qr-code"; // Importing the QRCode component from react-qr-code

// Importing Font Awesome components and icons
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCopy } from "@fortawesome/free-solid-svg-icons";

// Importing jsPDF and html2canvas for PDF generation
import jsPDF from "jspdf";
import "jspdf-autotable"; // For advanced table handling if needed
import { showErrorToast, showSuccessToast, showValidationError } from "../../../utils/toastUtils";

const Application = () => {
  // State variables
  const [userData, setUserData] = useState({
    applicationId: "APP123456",
    emailAddress: "<EMAIL>",
    name: "John Doe",
    address: "123 Main Street, Sydney, NSW",
    usiNumber: "USI12345",
    dateOfBirth: "1990-01-01",
    gender: "Male",
    phoneNumber: "************",
    status: "INVOICE RAISED",
    issues: [],
    createdAt: "2023-04-01",
  });

  const [qualification, setQualification] = useState("UEE30820"); // Default qualification
  const [provider, setProvider] = useState(""); // Selected Provider
  const [emailTemplate, setEmailTemplate] = useState(""); // Selected Email Template
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [progress, setProgress] = useState(0);
  const [applicationSubmitted, setApplicationSubmitted] = useState(false);
  const [activeTab, setActiveTab] = useState("Documents"); // Default active tab

  // Additional state for Deal inputs
  const [quotaRef, setQuotaRef] = useState("");
  const [invoiceRef, setInvoiceRef] = useState("");

  // Feedbacks State
  const [feedbacks, setFeedbacks] = useState([
    {
      date: "2023-08-15",
      message: "Please provide a copy of your latest utility bill.",
    },
    {
      date: "2023-08-20",
      message: "Ensure all your documents are up-to-date.",
    },
  ]);
  const [newFeedback, setNewFeedback] = useState("");

  // Mock Qualifications List
  const qualificationsList = [
    {
      code: "UEE30820",
      name: "Certificate III in Electrotechnology Electrician",
    },
    {
      code: "MEM31922",
      name: "Certificate III in Engineering - Fabrication Trade",
    },
    {
      code: "MEM30219",
      name: "Certificate III in Engineering – Mechanical Trade",
    },
    {
      code: "UEE32220",
      name: "Certificate III in Air Conditioning and Refrigeration",
    },
    { code: "SHB30321", name: "Certificate III in Nail Technology" },
  ];

  // Mock Providers List
  const providersList = [
    { id: 1, name: "Provider A" },
    { id: 2, name: "Provider B" },
    { id: 3, name: "Provider C" },
  ];

  // Mock Email Templates
  const emailTemplatesList = [
    { id: 1, name: "Template 1" },
    { id: 2, name: "Template 2" },
    { id: 3, name: "Template 3" },
  ];

  // Mock Documents Data
  const documentSections = [
    {
      title: "Identification Documents",
      documents: [
        {
          name: "Passport",
          uploadedDate: "2023-08-15",
          fileLink:
            "https://www.buyglobaldocument.com/wp-content/uploads/2023/08/Australia-Passport.jpg",
        },
        {
          name: "Driver's License",
          uploadedDate: "2023-07-20",
          fileLink:
            "https://clevergrid924.weebly.com/uploads/1/2/5/3/*********/*********.jpg",
        },
      ],
    },
    {
      title: "Academic Documents",
      documents: [
        {
          name: "Certificate II in Electrotechnology Electrician",
          uploadedDate: "2023-06-10",
          fileLink:
            "https://image.isu.pub/211123093303-ab88169dd7924dfab83b5f62ccfe6a1a/jpg/page_1.jpg",
        },
      ],
    },
    {
      title: "Other Documents",
      documents: [
        {
          name: "Utility Bill",
          uploadedDate: "2023-05-05",
          fileLink:
            "https://d33v4339jhl8k0.cloudfront.net/docs/assets/558a2c90e4b01a224b42e196/images/5d6746a62c7d3a7a4d77cfd3/file-cv3givttdG.png",
        },
      ],
    },
  ];

  // Mock Activity Data
  const [activities, setActivities] = useState([
    { date: "2023-09-01", change: "Application Submitted" },
    { date: "2023-09-05", change: "Quota Reference Number Added" },
  ]);

  // Mock Sales Details
  const [salesDetails, setSalesDetails] = useState({
    salesRep: "Sadman Shabab",
    leadName: "Alice Brown",
  });

  // Effect to simulate loading
  useEffect(() => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev + 10;
        console.log(`Progress: ${newProgress}%`); // Debugging
        if (newProgress >= 100) {
          clearInterval(interval);
          setProgress(100);
          // Simulate data loading completion
          return 100;
        }
        return newProgress;
      });
    }, 200); // Increment every 200ms

    return () => clearInterval(interval);
  }, []);

  // Handle checkbox change
  const handleCheckboxChange = () => {
    setAgreeToTerms(!agreeToTerms);
  };

  // Handle Send Application
  const handleSubmit = () => {
    if (agreeToTerms && provider && emailTemplate) {
      // Simulate application submission
      setProgress(0);
      const submitInterval = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + 10;
          console.log(`Submission Progress: ${newProgress}%`); // Debugging
          if (newProgress >= 100) {
            clearInterval(submitInterval);
            setProgress(100);
            setApplicationSubmitted(true);
            // Update user status
            setUserData((prevData) => ({
              ...prevData,
              status: "SUBMITTED",
            }));
            return 100;
          }
          return newProgress;
        });
      }, 200);
    }
  };

  // Handle Tab Change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Handle Qualification Change
  const handleQualificationChange = (e) => {
    setQualification(e.target.value);
  };

  // Helper to get qualification name by code
  const getQualificationName = (code) => {
    const qualificationObj = qualificationsList.find((q) => q.code === code);
    return qualificationObj ? qualificationObj.name : code;
  };

  // Handle Save Quota Reference Number
  const handleUpdateQuota = () => {
    if (quotaRef.trim() !== "") {
      // Simulate updating Quota Reference Number
      showSuccessToast(`Quota Reference Number updated to: ${quotaRef}`);
      setActivities((prevActivities) => [
        {
          date: new Date().toISOString().split("T")[0],
          change: "Quota Reference Number Updated",
        },
        ...prevActivities,
      ]);
      setQuotaRef("");
    } else {
      showValidationError("Quota Reference Number cannot be empty.");
    }
  };

  // Handle Save Invoice Reference Number
  const handleUpdateInvoice = () => {
    if (invoiceRef.trim() !== "") {
      // Simulate updating Invoice Reference Number
      showSuccessToast(`Invoice Number updated to: ${invoiceRef}`);
      setActivities((prevActivities) => [
        {
          date: new Date().toISOString().split("T")[0],
          change: "Invoice Number Updated",
        },
        ...prevActivities,
      ]);
      setInvoiceRef("");
    } else {
      showValidationError("Invoice Number cannot be empty.");
    }
  };

  // Handle Add Feedback
  const handleAddFeedback = () => {
    if (newFeedback.trim() !== "") {
      const currentDate = new Date().toISOString().split("T")[0];
      setFeedbacks((prevFeedbacks) => [
        ...prevFeedbacks,
        { date: currentDate, message: newFeedback.trim() },
      ]);
      setActivities((prevActivities) => [
        { date: currentDate, change: "New Feedback Added" },
        ...prevActivities,
      ]);
      setNewFeedback("");
    } else {
      showValidationError("Feedback cannot be empty.");
    }
  };

  // Modal State
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);

  // Handle Open Modal
  const handleOpenModal = (document) => {
    setSelectedDocument(document);
    setIsModalOpen(true);
  };

  // Construct the upload link
  const uploadLink = `https://apply.skillsync.com.au/upload?applicationId=${userData.applicationId}`;

  // Handle Copy Application Tracker
  const handleCopyTracker = () => {
    navigator.clipboard.writeText(uploadLink).then(
      () => {
        showSuccessToast("Application Tracker link copied to clipboard!");
      },
      (err) => {
        console.error("Could not copy text: ", err);
        showErrorToast("Failed to copy link to clipboard.");
      }
    );
  };

  // Function to fetch image as Data URL
  const getImageDataUrl = async (url) => {
    try {
      const response = await fetch(url, { mode: "cors" });
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          resolve(reader.result);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error("Error fetching image:", error);
      return null;
    }
  };

  // Handle Print Application with Enhanced Formatting
  const handlePrint = async () => {
    // Initialize jsPDF with Times Roman font
    const doc = new jsPDF("p", "mm", "a4");
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 20;
    let yOffset = margin;

    // Application Title
    doc.setFont("Times", "Bold");
    doc.setFontSize(16);
    doc.text(
      `${getQualificationName(qualification)} Application`,
      pageWidth / 2,
      yOffset,
      { align: "center" }
    );
    yOffset += 10;

    // Set font to Times Roman for the rest of the document
    doc.setFont("Times", "Normal");
    doc.setFontSize(12);

    // Basic Information
    const basicInfo = [
      `Application ID: ${userData.applicationId}`,
      `Name: ${userData.name}`,
      `Email: ${userData.emailAddress}`,
      `Address: ${userData.address}`,
      `USI Number: ${userData.usiNumber}`,
      `Date of Birth: ${userData.dateOfBirth}`,
      `Gender: ${userData.gender}`,
      `Phone Number: ${userData.phoneNumber}`,
      `Status: ${userData.status}`,
      `Created At: ${userData.createdAt}`,
    ];

    basicInfo.forEach((info) => {
      doc.text(info, margin, yOffset);
      yOffset += 7;
      // Add new page if near bottom
      if (yOffset > pageHeight - margin - 30) {
        doc.addPage();
        yOffset = margin;
      }
    });

    yOffset += 5;

    // Sales Details
    doc.setFont("Times", "Bold");
    doc.text("Sales Details:", margin, yOffset);
    yOffset += 7;
    doc.setFont("Times", "Normal");

    const salesInfo = [
      `Sales Representative: ${salesDetails.salesRep}`,
      `Lead Name: ${salesDetails.leadName}`,
    ];

    salesInfo.forEach((info) => {
      doc.text(info, margin + 5, yOffset);
      yOffset += 7;
      if (yOffset > pageHeight - margin - 30) {
        doc.addPage();
        yOffset = margin;
      }
    });

    yOffset += 5;

    // Deal Information
    doc.setFont("Times", "Bold");
    doc.text("Deal Information:", margin, yOffset);
    yOffset += 7;
    doc.setFont("Times", "Normal");

    const dealInfo = [
      `Quota Reference Number: ${quotaRef || "N/A"}`,
      `Invoice Reference Number: ${invoiceRef || "N/A"}`,
    ];

    dealInfo.forEach((info) => {
      doc.text(info, margin + 5, yOffset);
      yOffset += 7;
      if (yOffset > pageHeight - margin - 30) {
        doc.addPage();
        yOffset = margin;
      }
    });

    yOffset += 5;

    // Activity
    doc.setFont("Times", "Bold");
    doc.text("Activity:", margin, yOffset);
    yOffset += 7;
    doc.setFont("Times", "Normal");

    activities.forEach((activity) => {
      doc.text(`${activity.date} - ${activity.change}`, margin + 5, yOffset);
      yOffset += 7;
      if (yOffset > pageHeight - margin - 30) {
        doc.addPage();
        yOffset = margin;
      }
    });

    yOffset += 5;

    // Feedbacks
    doc.setFont("Times", "Bold");
    doc.text("Feedbacks:", margin, yOffset);
    yOffset += 7;
    doc.setFont("Times", "Normal");

    feedbacks.forEach((feedback) => {
      doc.text(`${feedback.date}: ${feedback.message}`, margin + 5, yOffset);
      yOffset += 7;
      if (yOffset > pageHeight - margin - 30) {
        doc.addPage();
        yOffset = margin;
      }
    });

    yOffset += 5;

    // Documents with Images
    for (const section of documentSections) {
      doc.setFont("Times", "Bold");
      doc.text(`${section.title}:`, margin, yOffset);
      yOffset += 7;
      doc.setFont("Times", "Normal");

      for (const docItem of section.documents) {
        doc.text(
          `- ${docItem.name} (Uploaded on ${docItem.uploadedDate})`,
          margin + 5,
          yOffset
        );
        yOffset += 7;

        // Fetch and add image
        const imgData = await getImageDataUrl(docItem.fileLink);
        if (imgData) {
          try {
            const imgProps = doc.getImageProperties(imgData);
            const imgWidth = 50; // Adjust as needed
            const imgHeight = (imgProps.height * imgWidth) / imgProps.width;

            // Check if image fits on the current page
            if (yOffset + imgHeight > pageHeight - margin - 30) {
              doc.addPage();
              yOffset = margin;
            }

            // Center the image
            const xPosition = (pageWidth - imgWidth) / 2;
            doc.addImage(
              imgData,
              "JPEG",
              xPosition,
              yOffset,
              imgWidth,
              imgHeight
            );
            yOffset += imgHeight + 10;
          } catch (error) {
            console.error("Error adding image to PDF:", error);
            doc.text("Image not available.", margin + 5, yOffset);
            yOffset += 7;
          }
        } else {
          doc.text("Image not available.", margin + 5, yOffset);
          yOffset += 7;
        }

        // Add space after each document
        yOffset += 3;

        if (yOffset > pageHeight - margin - 30) {
          doc.addPage();
          yOffset = margin;
        }
      }

      yOffset += 5;
      if (yOffset > pageHeight - margin - 30) {
        doc.addPage();
        yOffset = margin;
      }
    }

    // QR Code on the last page, bottom center
    // Ensure there's enough space
    if (yOffset + 30 > pageHeight - margin) {
      doc.addPage();
      yOffset = margin;
    }

    try {
      const qrCanvas = document.createElement("canvas");
      QRCode.toCanvas(qrCanvas, uploadLink, { width: 100 }, (error) => {
        if (error) console.error(error);
      });
      const qrDataUrl = qrCanvas.toDataURL("image/png");
      const qrWidth = 30; // Adjust size as needed
      const qrHeight = 30;

      // Position QR code at the bottom center
      doc.addImage(
        qrDataUrl,
        "PNG",
        (pageWidth - qrWidth) / 2,
        pageHeight - margin - qrHeight,
        qrWidth,
        qrHeight
      );

      // Optionally, add a label below the QR code
      doc.setFont("Times", "Normal");
      doc.setFontSize(10);
      doc.text(
        "Scan the QR code to track your application",
        pageWidth / 2,
        pageHeight - margin - qrHeight - 5,
        {
          align: "center",
        }
      );
    } catch (error) {
      console.error("Error adding QR code to PDF:", error);
    }

    // Footer with Page Numbers (if multiple pages)
    const pageCount = doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      // Add page number at the bottom center
      doc.setFont("Times", "Normal");
      doc.setFontSize(10);
      doc.text(`Page ${i} of ${pageCount}`, pageWidth / 2, pageHeight - 10, {
        align: "center",
      });
    }

    // Save the PDF
    doc.save(`Application_${userData.applicationId}.pdf`);
  };

  // Loading Screen
  if (progress < 100 && !applicationSubmitted) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-50 z-50">
        <div className="text-center">
          <img
            src={logo}
            alt="Loading Logo"
            className="mx-auto mb-4 w-48 h-auto"
          />
          <div className="relative w-48 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="absolute top-0 h-full bg-blue-600 transition-all"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <p className="mt-2 text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Application Submitted Screen
  if (applicationSubmitted) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 px-4">
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <img
            src={dp}
            alt="Profile"
            className="w-24 h-24 rounded-full mx-auto mb-4"
          />
          <h2 className="text-2xl font-semibold mb-2">{userData.name}</h2>
          <p className="text-gray-700 mb-6">
            Your application has been submitted successfully.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600"
          >
            Back to Application
          </button>
        </div>
      </div>
    );
  }

  // Main Application Content
  return (
    <div className="container mx-auto py-8 px-4">
      {/* First Section: Elegant Header and Basic Information */}
      <div className="bg-white p-8 rounded-lg shadow-md">
        {/* Dynamic Header */}
        <h2 className="text-3xl font-bold mb-6 text-gray-800">
          {getQualificationName(qualification)} for {userData.name} #
          {userData.applicationId}
        </h2>

        {/* Information and Tracker */}
        <div className="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
          {/* Left Side: Basic Information */}
          <div className="w-full lg:w-2/3 mb-6 lg:mb-0">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <p className="text-lg text-gray-700">
                  <span className="font-semibold">Application ID:</span>{" "}
                  {userData.applicationId}
                </p>
                <p className="text-lg text-gray-700">
                  <span className="font-semibold">Name:</span> {userData.name}
                </p>
                <p className="text-lg text-gray-700">
                  <span className="font-semibold">Email:</span>{" "}
                  {userData.emailAddress}
                </p>
                <p className="text-lg text-gray-700">
                  <span className="font-semibold">Address:</span>{" "}
                  {userData.address}
                </p>
                <p className="text-lg text-gray-700">
                  <span className="font-semibold">USI Number:</span>{" "}
                  {userData.usiNumber}
                </p>
                <p className="text-lg text-gray-700">
                  <span className="font-semibold">Phone:</span>{" "}
                  {userData.phoneNumber}
                </p>
              </div>
            </div>
          </div>

          {/* Right Side: Status, QR Code, and Tracker */}
          <div className="w-full lg:w-1/3 flex flex-col items-center lg:items-end space-y-6">
            {/* Status Section */}

            {/* QR Code Section */}
            <div className="flex flex-col items-center">
              <div>
                <p className="text-lg text-gray-700">
                  <span className="font-semibold">{userData.status}</span>{" "}
                </p>
              </div>
              <QRCode value={uploadLink} size={128} />
              <button
                onClick={handleCopyTracker}
                className="mt-2 flex items-center text-blue-500 hover:underline"
              >
                <FontAwesomeIcon icon={faCopy} className="mr-1" />
                Copy Tracker Link
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Second Section: Tabs */}
      <div className="bg-white p-6 rounded-lg shadow-md mt-8">
        {/* Tabs Navigation */}
        <div className="flex border-b">
          {[
            "Documents",
            "Feedbacks",
            "Activity",
            "Deal",
            "Sales Details",
            "Additional Options", // New Tab
          ].map((tab) => (
            <button
              key={tab}
              className={`py-2 px-4 -mb-px border-b-2 font-medium text-sm ${
                activeTab === tab
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-600 hover:text-blue-600 hover:border-blue-500"
              }`}
              onClick={() => handleTabChange(tab)}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Tabs Content */}
        <div className="mt-4">
          {activeTab === "Documents" && (
            <div>
              {documentSections.map((section, idx) => (
                <div key={idx} className="mb-6">
                  <h3 className="text-xl font-semibold mb-4 text-gray-800">
                    {section.title}
                  </h3>
                  <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-4">
                    {section.documents.map((doc, idx) => (
                      <DocumentButton
                        key={idx}
                        title={doc.name}
                        date={doc.uploadedDate}
                        fileLink={doc.fileLink}
                        handleOpenModal={handleOpenModal}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === "Feedbacks" && (
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-800">
                Agent Feedback
              </h3>
              {feedbacks.length > 0 ? (
                <ul className="space-y-2">
                  {feedbacks.map((feedback, idx) => (
                    <li
                      key={idx}
                      className="p-4 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <p className="text-xs text-gray-500">{feedback.date}</p>
                      <p className="text-sm text-gray-800 mt-1">
                        {feedback.message}
                      </p>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500">
                  You have no feedback at this moment.
                </p>
              )}

              {/* Add Feedback Section */}
              <div className="mt-6">
                <h4 className="text-lg font-semibold mb-2 text-gray-800">
                  Add Your Feedback
                </h4>
                <textarea
                  value={newFeedback}
                  onChange={(e) => setNewFeedback(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 resize-none h-24 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your feedback here..."
                ></textarea>
                <button
                  onClick={handleAddFeedback}
                  className="mt-2 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                >
                  Add Feedback
                </button>
              </div>
            </div>
          )}

          {activeTab === "Activity" && (
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-800">
                Activity
              </h3>
              {activities.length > 0 ? (
                <ul className="space-y-2">
                  {activities.map((activity, index) => (
                    <li
                      key={index}
                      className="p-4 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      <p className="text-xs text-gray-500">{activity.date}</p>
                      <p className="text-sm text-gray-800 mt-1">
                        {activity.change}
                      </p>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500">No recent activities.</p>
              )}
            </div>
          )}

          {activeTab === "Deal" && (
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-800">Deal</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Quota Reference Number */}
                <div>
                  <label
                    htmlFor="quotaRef"
                    className="block text-gray-700 font-semibold mb-2"
                  >
                    Quota Reference Number:
                  </label>
                  <input
                    type="text"
                    id="quotaRef"
                    value={quotaRef}
                    onChange={(e) => setQuotaRef(e.target.value)}
                    className="border border-gray-300 rounded-lg p-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter Quota Reference Number"
                  />
                  <button
                    onClick={handleUpdateQuota}
                    className={`mt-2 flex items-center justify-center bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors ${
                      quotaRef.trim() === ""
                        ? "bg-gray-400 cursor-not-allowed"
                        : ""
                    }`}
                    disabled={quotaRef.trim() === ""}
                  >
                    Update Quota
                  </button>
                </div>

                {/* Invoice Reference Number */}
                <div>
                  <label
                    htmlFor="invoiceRef"
                    className="block text-gray-700 font-semibold mb-2"
                  >
                    Invoice Number:
                  </label>
                  <input
                    type="text"
                    id="invoiceRef"
                    value={invoiceRef}
                    onChange={(e) => setInvoiceRef(e.target.value)}
                    className="border border-gray-300 rounded-lg p-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter Invoice Number"
                  />
                  <button
                    onClick={handleUpdateInvoice}
                    className={`mt-2 flex items-center justify-center bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors ${
                      invoiceRef.trim() === ""
                        ? "bg-gray-400 cursor-not-allowed"
                        : ""
                    }`}
                    disabled={invoiceRef.trim() === ""}
                  >
                    Update Invoice
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === "Sales Details" && (
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-800">
                Sales Details
              </h3>
              <div className="bg-gray-50 p-6 rounded-lg shadow-sm">
                <p className="text-lg text-gray-700">
                  <span className="font-semibold">Sales Representative:</span>{" "}
                  {salesDetails.salesRep}
                </p>
                <p className="text-lg text-gray-700 mt-2">
                  <span className="font-semibold">Lead Name:</span>{" "}
                  {salesDetails.leadName}
                </p>
                <p className="text-lg text-gray-700 mt-2">
                  <span className="font-semibold">Application Created At:</span>{" "}
                  {userData.createdAt}
                </p>
              </div>
            </div>
          )}

          {activeTab === "Additional Options" && (
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-800">
                Additional Options
              </h3>
              <button
                onClick={handlePrint}
                className="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors flex items-center"
              >
                Print Application
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Third Section: Dropdowns and Send Application Button */}
      <div className="bg-white p-6 rounded-lg shadow-md mt-8">
        {/* Qualification, Provider, and Email Template Dropdowns */}
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          {/* Qualification Dropdown */}
          <div className="w-full md:w-1/3">
            <label
              htmlFor="qualification"
              className="block text-gray-700 font-semibold mb-2"
            >
              Qualification:
            </label>
            <select
              id="qualification"
              value={qualification}
              onChange={handleQualificationChange}
              className="border border-gray-300 rounded-lg p-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Qualification</option>
              {qualificationsList.map((q) => (
                <option key={q.code} value={q.code}>
                  {q.name}
                </option>
              ))}
            </select>
          </div>

          {/* Provider Dropdown */}
          <div className="w-full md:w-1/3">
            <label
              htmlFor="provider"
              className="block text-gray-700 font-semibold mb-2"
            >
              Provider:
            </label>
            <select
              id="provider"
              value={provider}
              onChange={(e) => setProvider(e.target.value)}
              className="border border-gray-300 rounded-lg p-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Provider</option>
              {providersList.map((prov) => (
                <option key={prov.id} value={prov.name}>
                  {prov.name}
                </option>
              ))}
            </select>
          </div>

          {/* Email Template Dropdown */}
          <div className="w-full md:w-1/3">
            <label
              htmlFor="emailTemplate"
              className="block text-gray-700 font-semibold mb-2"
            >
              Email Template:
            </label>
            <select
              id="emailTemplate"
              value={emailTemplate}
              onChange={(e) => setEmailTemplate(e.target.value)}
              className="border border-gray-300 rounded-lg p-2 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Email Template</option>
              {emailTemplatesList.map((template) => (
                <option key={template.id} value={template.name}>
                  {template.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Terms & Send Application */}
        <div className="flex items-center mt-6">
          <input
            type="checkbox"
            id="terms"
            checked={agreeToTerms}
            onChange={handleCheckboxChange}
            className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="terms" className="text-gray-700 text-sm">
            I understand the{" "}
            <a href="#" className="text-blue-500 underline">
              terms and services
            </a>
            .
          </label>
        </div>
        <div className="mt-4">
          <button
            className={`flex items-center justify-center bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors w-full md:w-auto`}
            disabled={!agreeToTerms || !provider || !emailTemplate}
            onClick={handleSubmit}
          >
            Send Application to Provider
          </button>
        </div>
      </div>

      {/* Document Modal */}
      <DocumentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        document={selectedDocument}
      />
    </div>
  );
};

// Exporting the Application component
export default Application;
