--
-- PostgreSQL database dump
--

-- Dumped from database version 16.6 (Ubuntu 16.6-0ubuntu0.24.10.1)
-- Dumped by pg_dump version 16.6 (Ubuntu 16.6-0ubuntu0.24.10.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: activities; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.activities (
    id bigint NOT NULL,
    created_by character varying(255),
    created_date timestamp(6) without time zone,
    last_modified_by character varying(255),
    last_modified_date timestamp(6) without time zone,
    description character varying(255),
    lead_id bigint
);


ALTER TABLE public.activities OWNER TO postgres;

--
-- Name: activities_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.activities_seq
    START WITH 1
    INCREMENT BY 50
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.activities_seq OWNER TO postgres;

--
-- Name: applications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.applications (
    id bigint NOT NULL,
    created_by character varying(255),
    created_date timestamp(6) without time zone,
    last_modified_by character varying(255),
    last_modified_date timestamp(6) without time zone,
    agent_username character varying(255),
    applicant_address character varying(255),
    applicant_email character varying(255),
    applicant_name character varying(255),
    applicant_phone character varying(255),
    application_id character varying(255),
    invoice_ref_number character varying(255),
    lead_phone character varying(255),
    paid_amount double precision NOT NULL,
    paid_status character varying(255),
    price double precision NOT NULL,
    quote_ref_number character varying(255),
    status character varying(255),
    CONSTRAINT applications_paid_status_check CHECK (((paid_status)::text = ANY ((ARRAY['PENDING'::character varying, 'PARTIALLY_PAID'::character varying, 'FULLY_PAID'::character varying, 'INVOICE_EXPIRED'::character varying])::text[]))),
    CONSTRAINT applications_status_check CHECK (((status)::text = ANY ((ARRAY['DOCUMENT_PENDING'::character varying, 'QUOTE_RAISED'::character varying, 'INVOICE_RAISED'::character varying, 'IN_PROGRESS'::character varying, 'SOFT_COPY_READY'::character varying, 'SOFT_COPY_SENT'::character varying, 'HARD_COPY_READY'::character varying, 'HARD_COPY_SENT'::character varying, 'FALLOUT'::character varying])::text[])))
);


ALTER TABLE public.applications OWNER TO postgres;

--
-- Name: applications_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.applications_seq
    START WITH 1
    INCREMENT BY 50
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.applications_seq OWNER TO postgres;

--
-- Name: comments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.comments (
    id bigint NOT NULL,
    created_by character varying(255),
    created_date timestamp(6) without time zone,
    last_modified_by character varying(255),
    last_modified_date timestamp(6) without time zone,
    content character varying(255),
    lead_id bigint
);


ALTER TABLE public.comments OWNER TO postgres;

--
-- Name: comments_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.comments_seq
    START WITH 1
    INCREMENT BY 50
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.comments_seq OWNER TO postgres;

--
-- Name: company; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.company (
    id bigint NOT NULL,
    created_by character varying(255),
    created_date timestamp(6) without time zone,
    last_modified_by character varying(255),
    last_modified_date timestamp(6) without time zone,
    active boolean,
    address character varying(255),
    email_address character varying(255) NOT NULL,
    full_name character varying(255) NOT NULL,
    gender character varying(255) NOT NULL,
    is_system_admin boolean,
    phone_number character varying(255),
    user_id bigint NOT NULL
);


ALTER TABLE public.company OWNER TO postgres;

--
-- Name: company_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.company_seq
    START WITH 1
    INCREMENT BY 50
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.company_seq OWNER TO postgres;

--
-- Name: lead_sales_agent; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.lead_sales_agent (
    lead_id bigint NOT NULL,
    sales_agent_id bigint NOT NULL
);


ALTER TABLE public.lead_sales_agent OWNER TO postgres;

--
-- Name: leads; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.leads (
    id bigint NOT NULL,
    created_by character varying(255),
    created_date timestamp(6) without time zone,
    last_modified_by character varying(255),
    last_modified_date timestamp(6) without time zone,
    address character varying(255),
    application_count integer NOT NULL,
    company_name character varying(255),
    email character varying(255),
    lead_name character varying(255),
    phone character varying(255),
    status character varying(255),
    CONSTRAINT leads_status_check CHECK (((status)::text = ANY ((ARRAY['HOT'::character varying, 'WARM'::character varying, 'COLD'::character varying, 'FRESH'::character varying])::text[])))
);


ALTER TABLE public.leads OWNER TO postgres;

--
-- Name: leads_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.leads_seq
    START WITH 1
    INCREMENT BY 50
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.leads_seq OWNER TO postgres;

--
-- Name: qualifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.qualifications (
    id bigint NOT NULL,
    enrollment_price double precision NOT NULL,
    notes character varying(10485760),
    offshore_price double precision NOT NULL,
    qualification_id character varying(255) NOT NULL,
    qualification_name character varying(255),
    rpl_price double precision NOT NULL,
    total_applications integer NOT NULL,
    type character varying(255)
);


ALTER TABLE public.qualifications OWNER TO postgres;

--
-- Name: qualifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.qualifications ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.qualifications_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: sales_agent; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.sales_agent (
    id bigint NOT NULL,
    created_by character varying(255),
    created_date timestamp(6) without time zone,
    last_modified_by character varying(255),
    last_modified_date timestamp(6) without time zone,
    active boolean,
    address character varying(255),
    current_week character varying(255),
    email_address character varying(255) NOT NULL,
    full_name character varying(255) NOT NULL,
    gender character varying(255) NOT NULL,
    phone_number character varying(255),
    weekly_target double precision,
    user_id bigint NOT NULL
);


ALTER TABLE public.sales_agent OWNER TO postgres;

--
-- Name: sales_agent_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.sales_agent_seq
    START WITH 1
    INCREMENT BY 50
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.sales_agent_seq OWNER TO postgres;

--
-- Name: sold_qualifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.sold_qualifications (
    id bigint NOT NULL,
    sold_price double precision NOT NULL,
    application_id bigint,
    qualification_id bigint
);


ALTER TABLE public.sold_qualifications OWNER TO postgres;

--
-- Name: sold_qualifications_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.sold_qualifications_seq
    START WITH 1
    INCREMENT BY 50
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.sold_qualifications_seq OWNER TO postgres;

--
-- Name: tracker; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tracker (
    id bigint NOT NULL,
    created_by character varying(255),
    created_date timestamp(6) without time zone,
    last_modified_by character varying(255),
    last_modified_date timestamp(6) without time zone,
    first_login timestamp(6) without time zone,
    last_login timestamp(6) without time zone,
    total_time double precision NOT NULL,
    username character varying(255)
);


ALTER TABLE public.tracker OWNER TO postgres;

--
-- Name: tracker_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tracker_seq
    START WITH 1
    INCREMENT BY 50
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tracker_seq OWNER TO postgres;

--
-- Name: user_authorities; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_authorities (
    user_id bigint NOT NULL,
    authorities character varying(255),
    CONSTRAINT user_authorities_authorities_check CHECK (((authorities)::text = ANY ((ARRAY['ADMIN'::character varying, 'APPLICANT'::character varying, 'SALES'::character varying, 'OPERATIONS'::character varying])::text[])))
);


ALTER TABLE public.user_authorities OWNER TO postgres;

--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    password character varying(255),
    username character varying(255)
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: users_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_seq
    START WITH 1
    INCREMENT BY 50
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_seq OWNER TO postgres;

--
-- Name: weekly_target; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.weekly_target (
    id bigint NOT NULL,
    agent_username character varying(255),
    kpi1 double precision NOT NULL,
    kpi2 double precision NOT NULL,
    weekly_targets_combined_id bigint
);


ALTER TABLE public.weekly_target OWNER TO postgres;

--
-- Name: weekly_target_combined; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.weekly_target_combined (
    id bigint NOT NULL,
    end_date timestamp(6) without time zone,
    start_date timestamp(6) without time zone,
    title character varying(255)
);


ALTER TABLE public.weekly_target_combined OWNER TO postgres;

--
-- Name: weekly_target_combined_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.weekly_target_combined ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.weekly_target_combined_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: weekly_target_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

ALTER TABLE public.weekly_target ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.weekly_target_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Data for Name: activities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.activities (id, created_by, created_date, last_modified_by, last_modified_date, description, lead_id) FROM stdin;
1	<EMAIL>	2025-02-12 00:53:12.20538	<EMAIL>	2025-02-12 00:53:12.20538	<NAME_EMAIL> added a comment: Allied health : Classes query	28
2	<EMAIL>	2025-02-12 01:43:14.34162	<EMAIL>	2025-02-12 01:43:14.34162	<NAME_EMAIL> added a comment: Client made full payment and lodged his application on January, will be released this week or the week after 	38
3	<EMAIL>	2025-02-12 02:34:01.237719	<EMAIL>	2025-02-12 02:34:01.237719	<NAME_EMAIL> added a comment: Client submitted all documents for his wife to proceed with the qualification. Its an urgent requirement and he would need it processed by Friday. 	43
4	<EMAIL>	2025-02-12 05:40:03.544658	<EMAIL>	2025-02-12 05:40:03.544658	<NAME_EMAIL> added a comment: 0424 099 468\n\nPlease contact Joseph when you can to discuss security licence	18
5	<EMAIL>	2025-02-12 05:40:12.696804	<EMAIL>	2025-02-12 05:40:12.696804	<NAME_EMAIL> added a comment: 0424 099 468\n	18
6	<EMAIL>	2025-02-12 05:43:14.800287	<EMAIL>	2025-02-12 05:43:14.800287	<NAME_EMAIL> added a comment: ‭0433 281 659‬\n\nPannu \n\nCPP41419\nCertificate IV in Real Estate Practice\n$1650\n\nCPP51122\nDiploma of Property (Agency Management)\n$2050\n\nPackage : AUD 3600	6
7	<EMAIL>	2025-02-16 02:24:22.556847	<EMAIL>	2025-02-16 02:24:22.556847	<NAME_EMAIL> added a comment: Want to dance\n	57
8	<EMAIL>	2025-02-21 04:29:20.230908	<EMAIL>	2025-02-21 04:29:20.230908	<NAME_EMAIL> added a comment: Add a comment 1\n	58
52	<EMAIL>	2025-03-04 00:13:14.224951	<EMAIL>	2025-03-04 00:13:14.224951	<NAME_EMAIL> added a comment: Certificate 4 in finance and mortgage broking ( Current ) : AUD 1650	106
53	<EMAIL>	2025-03-04 00:13:19.97356	<EMAIL>	2025-03-04 00:13:19.97356	<NAME_EMAIL> added a comment: Diploma of Finance and Mortgage Broking : AUD 1850	106
54	<EMAIL>	2025-03-04 00:17:50.8033	<EMAIL>	2025-03-04 00:17:50.8033	<NAME_EMAIL> added a comment: Certificate III in Individual Support : AUD 1450\n	107
55	<EMAIL>	2025-03-04 00:18:44.25935	<EMAIL>	2025-03-04 00:18:44.25935	<NAME_EMAIL> added a comment: DNP	107
56	<EMAIL>	2025-03-04 00:19:55.519998	<EMAIL>	2025-03-04 00:19:55.519998	<NAME_EMAIL> added a comment: Certificate III in Light Vehicle Mechanical Technology\t $ 1,350	108
\.


--
-- Data for Name: applications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.applications (id, created_by, created_date, last_modified_by, last_modified_date, agent_username, applicant_address, applicant_email, applicant_name, applicant_phone, application_id, invoice_ref_number, lead_phone, paid_amount, paid_status, price, quote_ref_number, status) FROM stdin;
15	<EMAIL>	2025-02-12 01:22:39.235083	<EMAIL>	2025-02-12 01:28:34.922504	<EMAIL>	\N	<EMAIL>	Kiran Chanmanwar	0469316532	KC62HAN	INV-581	0469316532	1350	FULLY_PAID	1350	\N	INVOICE_RAISED
19	<EMAIL>	2025-02-12 02:40:24.269953	<EMAIL>	2025-02-28 02:48:54.986345	<EMAIL>	\N	<EMAIL>	Haotian Zhu	0461431912	HZ62HU	\N	0420888816	2500	FULLY_PAID	2500	\N	SOFT_COPY_SENT
3	<EMAIL>	2025-02-12 00:44:49.907913	<EMAIL>	2025-02-12 04:06:33.731951	<EMAIL>	\N	<EMAIL>	Junshu He	429292811	JH13E	INV-726	0429 ************	FULLY_PAID	2450	\N	INVOICE_RAISED
11	<EMAIL>	2025-02-12 01:01:40.167053	<EMAIL>	2025-02-12 01:29:17.577618	<EMAIL>	\N	<EMAIL>	Mandelli Francesco	429292811	MF92RAN	INV-687	0429 ************	FULLY_PAID	1650	QU-687	INVOICE_RAISED
4	<EMAIL>	2025-02-12 00:47:30.182086	<EMAIL>	2025-02-12 00:47:48.033464	<EMAIL>	\N	<EMAIL>	Rupinder Kaur	0468852786	RK65AUR	\N	0468 852 786	0	PENDING	1650	QU-718	QUOTE_RAISED
7	<EMAIL>	2025-02-12 00:58:27.885689	<EMAIL>	2025-02-12 01:14:44.60219	<EMAIL>	\N	0435853585	Abdulrahman A Al-Raimi	<EMAIL>	AA22	\N	0435853585	0	PENDING	1350	QU-715	QUOTE_RAISED
8	<EMAIL>	2025-02-12 00:59:21.043074	<EMAIL>	2025-02-12 01:15:01.35198	<EMAIL>	\N	<EMAIL>	Imran Sarwar	0435853585	IS15ARW	\N	0435853585	0	PENDING	1850	QU-714	QUOTE_RAISED
5	<EMAIL>	2025-02-12 00:48:54.796314	<EMAIL>	2025-02-12 00:54:21.647656	<EMAIL>	\N	<EMAIL>	Zeyu Li	0498283369	ZL54I	\N	0498283369	0	PENDING	1675	QU-717	QUOTE_RAISED
6	<EMAIL>	2025-02-12 00:52:25.206287	<EMAIL>	2025-02-12 00:54:26.348063	<EMAIL>	\N	<EMAIL>	Zeyu Li	0498283369	ZL5I	\N	0498283369	0	PENDING	1675	QU-717	QUOTE_RAISED
2	<EMAIL>	2025-02-12 00:05:38.450223	<EMAIL>	2025-02-21 04:44:05.809932	<EMAIL>	\N	<EMAIL>	Aman Kapoor	0433864382	AK73APO	\N	0435229828	0	PENDING	2250	QU-722	IN_PROGRESS
16	<EMAIL>	2025-02-12 01:31:35.787791	<EMAIL>	2025-02-12 01:32:01.105453	<EMAIL>	\N	<EMAIL>	Tanveer Singh	0480 166 944	TS31ING	INV-669	+61 ***********	1250	PARTIALLY_PAID	1450	\N	INVOICE_RAISED
13	<EMAIL>	2025-02-12 01:10:52.794645	<EMAIL>	2025-02-12 01:32:43.283185	<EMAIL>	\N	<EMAIL>	Sukhvinder Singh	0493732710	SS72ING	INV-673	0493732710	8000	PARTIALLY_PAID	10000	\N	INVOICE_RAISED
10	<EMAIL>	2025-02-12 01:00:43.366566	<EMAIL>	2025-02-12 01:17:32.122699	<EMAIL>	\N	<EMAIL>	Nasser ADEM	0452301630	NA9DEM	\N	0452301630	0	PENDING	1450	QU-706	QUOTE_RAISED
20	<EMAIL>	2025-02-12 02:54:17.184633	<EMAIL>	2025-03-03 03:15:39.575147	<EMAIL>	\N	<EMAIL>	Bhavyang Trivedi	+61 ***********\t	BT78RIV	\N	+61 ***********\t	2250	PENDING	2250	\N	DOCUMENT_PENDING
12	<EMAIL>	2025-02-12 01:10:21.523672	<EMAIL>	2025-02-12 01:32:54.208163	<EMAIL>	\N	<EMAIL>	Hovard	0493732710	H21OVA	INV-682	0493732710	7000	PARTIALLY_PAID	8500	\N	INVOICE_RAISED
14	<EMAIL>	2025-02-12 01:19:09.478058	<EMAIL>	2025-02-12 01:21:06.359169	<EMAIL>	\N	<EMAIL>	Gaurav Sood	0414239619	GS97OOD	INV-543	0414239619	2550	FULLY_PAID	2550	\N	INVOICE_RAISED
25	<EMAIL>	2025-02-12 06:18:15.615816	<EMAIL>	2025-02-28 02:49:20.339526	<EMAIL>	\N	<EMAIL>	Sanjeev	0415138634	S18ANJ	INV-647	0466829418	2750	FULLY_PAID	2750	\N	IN_PROGRESS
17	<EMAIL>	2025-02-12 01:47:11.340638	<EMAIL>	2025-02-16 02:50:58.322516	<EMAIL>	\N	<EMAIL>	Stephen Ekene Ogbusuo\t	+***********\t	SE36KEN	\N	+***********	0	FULLY_PAID	0	\N	IN_PROGRESS
9	<EMAIL>	2025-02-12 00:59:56.432042	<EMAIL>	2025-02-12 02:26:10.268944	<EMAIL>	\N	<EMAIL>	James Howard Sedgwick	0400368488	JH22OWA	\N	0422283267	1850	FULLY_PAID	1850	QU-713	QUOTE_RAISED
26	<EMAIL>	2025-02-12 23:52:19.033929	<EMAIL>	2025-02-12 23:52:19.038311	<EMAIL>	\N	<EMAIL>	Meenu Sharma	0447345146	MS36HAR	\N	0470756017	0	PENDING	1100	\N	DOCUMENT_PENDING
24	<EMAIL>	2025-02-12 05:45:53.37753	<EMAIL>	2025-02-12 05:46:08.048502	<EMAIL>	\N	<EMAIL>	Patrick Kipkoech\t	+61 ***********\t	PK25IPK	\N	+61 ***********\t	1250	FULLY_PAID	1250	\N	IN_PROGRESS
23	<EMAIL>	2025-02-12 05:21:31.944704	<EMAIL>	2025-02-12 05:21:39.980056	<EMAIL>	\N	0415835188	Jiayi Lin	0415835188	JL20IN	\N	0469742998	1950	FULLY_PAID	1950	\N	IN_PROGRESS
18	<EMAIL>	2025-02-12 02:33:08.741253	<EMAIL>	2025-03-03 03:17:08.386649	<EMAIL>	UNIT 8/72-78 CARDIGAN STREET GUILDFORD NSW 2161	<EMAIL>	Faith Jerop 	+61405536367	FJ12ERO	INV 720	+61 ***********	1200	FULLY_PAID	1200	QU-720	SOFT_COPY_SENT
1	<EMAIL>	2025-02-11 23:27:42.542542	<EMAIL>	2025-02-13 00:59:15.067874	<EMAIL>	\N	N/A	Longjiang Huang	N/A	LH14UAN	INV- 698 	0469742998	3750	FULLY_PAID	3750	\N	INVOICE_RAISED
28	<EMAIL>	2025-02-13 02:08:00.02166	<EMAIL>	2025-02-13 02:08:18.319398	<EMAIL>	\N	<EMAIL>	Saravjit Singh	0416151404	SS80ING	\N	0416151404	0	PENDING	1850	QU-735	QUOTE_RAISED
22	<EMAIL>	2025-02-12 05:19:25.87167	<EMAIL>	2025-02-21 04:44:01.547831	<EMAIL>	\N	<EMAIL>	Cheng-li CHOU	0403929166	CC53HOU	\N	0469742998	2450	FULLY_PAID	2450	\N	IN_PROGRESS
31	<EMAIL>	2025-02-18 04:54:55.495734	<EMAIL>	2025-02-28 02:49:06.097932	<EMAIL>	\N	<EMAIL>	Primrose Kanzara	0418756162	PK29ANZ	\N	**********	1550	FULLY_PAID	1550	QU-759	IN_PROGRESS
32	<EMAIL>	2025-02-26 02:42:22.6971	<EMAIL>	2025-02-26 02:42:30.826233	<EMAIL>	\N	<EMAIL>	Hiago Comper Serqueira	0414678026	HC36OMP	\N	0424570699	1650	FULLY_PAID	1650	\N	IN_PROGRESS
21	<EMAIL>	2025-02-12 04:53:14.964272	<EMAIL>	2025-02-28 02:49:18.52358	<EMAIL>	\N	<EMAIL>	Xiaoli Xu	0420986877	XX50U	\N	0469742998	1800	FULLY_PAID	1800	\N	IN_PROGRESS
33	<EMAIL>	2025-02-26 02:47:12.799743	<EMAIL>	2025-02-26 02:47:32.791174	<EMAIL>	\N	<EMAIL>	Chikanele Chibeneme	0428880103	CC70HIB	\N	***********	825	FULLY_PAID	825	\N	SOFT_COPY_READY
34	<EMAIL>	2025-02-26 02:49:02.031121	<EMAIL>	2025-02-26 02:49:12.938962	<EMAIL>	\N	<EMAIL>	Rehoboth Anenyasha Mabvira	0416528571	RA13NEN	\N	***********	825	FULLY_PAID	825	\N	SOFT_COPY_READY
35	<EMAIL>	2025-02-26 02:54:28.965518	<EMAIL>	2025-02-26 02:54:44.178884	<EMAIL>	\N	<EMAIL>	UCHE VICTOR WILLIAMS	0420915676	UV32ICT	\N	***********	825	FULLY_PAID	825	\N	IN_PROGRESS
36	<EMAIL>	2025-02-26 02:55:41.552156	<EMAIL>	2025-02-26 02:55:53.858465	<EMAIL>	\N	<EMAIL>	SHAMISO SINGIZI	0415343644	SS55ING	\N	***********	825	FULLY_PAID	825	\N	SOFT_COPY_READY
54	<EMAIL>	2025-03-03 04:21:27.660505	<EMAIL>	2025-03-03 04:21:27.666346	<EMAIL>	27 Trumpy St Silkstone QLD 4304	<EMAIL>	Lesinda Robinson  \t	+61 481 154 447	LR81OBI	\N	+61 408 175 516	0	PENDING	1250	\N	DOCUMENT_PENDING
37	<EMAIL>	2025-02-26 03:10:37.497095	<EMAIL>	2025-02-26 03:10:57.543555	<EMAIL>	\N	<EMAIL>	JATIN	0479045553	J3ATI	\N	***********	1100	FULLY_PAID	1100	\N	IN_PROGRESS
60	<EMAIL>	2025-03-04 00:20:42.879038	<EMAIL>	2025-03-04 00:20:56.711434	<EMAIL>		<EMAIL>	Ugyen Dorji	0424777060	UD56ORJ	809	0424777060	0	PENDING	1350	\N	INVOICE_RAISED
38	<EMAIL>	2025-02-26 03:15:18.89233	<EMAIL>	2025-02-26 03:15:36.60111	<EMAIL>	\N		Chencho Gyeltshen		CG41YEL	\N	***********	2350	FULLY_PAID	2350	\N	IN_PROGRESS
55	<EMAIL>	2025-03-03 04:32:19.20598	<EMAIL>	2025-03-03 04:39:03.965071	<EMAIL>		<EMAIL>	DWAYNE RICHARD FULLER	0429292972	DR54ICH	\N	***********	1250	FULLY_PAID	1250	\N	IN_PROGRESS
39	<EMAIL>	2025-02-28 00:12:18.570432	<EMAIL>	2025-02-28 00:12:30.638905	<EMAIL>	\N	<EMAIL>	DWAYNE RICHARD FULLER	0429292972	DR49ICH	\N	***********	1350	FULLY_PAID	1350	\N	IN_PROGRESS
56	<EMAIL>	2025-03-03 04:42:19.114612	<EMAIL>	2025-03-03 04:42:19.11844	<EMAIL>	85 Butler St. Townview QLD 4825	<EMAIL>	Paea He Ofa Tangi	+61 421 165 515	PH71E	\N	+61 408 175 516	0	PENDING	1250	\N	DOCUMENT_PENDING
40	<EMAIL>	2025-02-28 02:50:46.742812	<EMAIL>	2025-02-28 02:51:02.04956	<EMAIL>	\N	<EMAIL>	Dilini Hetti Arachchige Dona	0408489881	DH36ETT	\N	0430701144	1950	FULLY_PAID	1950	\N	IN_PROGRESS
57	<EMAIL>	2025-03-03 05:18:27.982809	<EMAIL>	2025-03-03 05:18:27.997634	<EMAIL>	82 Katandra Crescent Bellbird Park QLD 4300	<EMAIL>	Maria Jose Meza Reales	+61 416 200 948	MJ99OSE	\N	+61 408 175 516	0	PENDING	1250	\N	DOCUMENT_PENDING
41	<EMAIL>	2025-02-28 02:55:11.828116	<EMAIL>	2025-02-28 02:55:19.453511	<EMAIL>	\N	<EMAIL>	Maryam Bahadur	0410799946 	MB8AHA	\N	0435229828	2900	FULLY_PAID	2900	\N	SOFT_COPY_SENT
52	<EMAIL>	2025-03-03 00:56:37.505386	<EMAIL>	2025-03-03 00:56:51.366108	<EMAIL>		<EMAIL>	Tseng Sheng-Ming	0414034262	TS43HEN	\N	0416926512 	0	PENDING	1950	 QU-818	QUOTE_RAISED
58	<EMAIL>	2025-03-04 00:06:18.434282	<EMAIL>	2025-03-04 00:06:18.439219	<EMAIL>		<EMAIL>	Felister Jesang Arusey	0420409322	FJ7ESA	\N	0420409322	0	PENDING	1450	\N	DOCUMENT_PENDING
59	<EMAIL>	2025-03-04 00:08:29.195179	<EMAIL>	2025-03-04 00:09:23.186088	<EMAIL>		<EMAIL>	Mishma Dewni Silva Agampodige	0413 078 268	MD27EWN	802	**********	0	PENDING	2250	\N	INVOICE_RAISED
53	<EMAIL>	2025-03-03 03:20:32.535895	<EMAIL>	2025-03-04 03:00:45.764089	<EMAIL>	<EMAIL>	<EMAIL>	Harsh Jain	**********	HJ68AIN	829	**********	0	PENDING	1450	829	INVOICE_RAISED
\.


--
-- Data for Name: comments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.comments (id, created_by, created_date, last_modified_by, last_modified_date, content, lead_id) FROM stdin;
1	<EMAIL>	2025-02-12 00:53:12.209829	<EMAIL>	2025-02-12 00:53:12.209829	Allied health : Classes query	28
2	<EMAIL>	2025-02-12 01:43:14.342548	<EMAIL>	2025-02-12 01:43:14.342548	Client made full payment and lodged his application on January, will be released this week or the week after 	38
3	<EMAIL>	2025-02-12 02:34:01.239234	<EMAIL>	2025-02-12 02:34:01.239234	Client submitted all documents for his wife to proceed with the qualification. Its an urgent requirement and he would need it processed by Friday. 	43
4	<EMAIL>	2025-02-12 05:40:03.551092	<EMAIL>	2025-02-12 05:40:03.*********** 099 468\n\nPlease contact Joseph when you can to discuss security licence	18
5	<EMAIL>	2025-02-12 05:40:12.697117	<EMAIL>	2025-02-12 05:40:12.*********** 099 468\n	18
6	<EMAIL>	2025-02-12 05:43:14.800603	<EMAIL>	2025-02-12 05:43:14.800603	‭0433 281 659‬\n\nPannu \n\nCPP41419\nCertificate IV in Real Estate Practice\n$1650\n\nCPP51122\nDiploma of Property (Agency Management)\n$2050\n\nPackage : AUD 3600	6
7	<EMAIL>	2025-02-16 02:24:22.557186	<EMAIL>	2025-02-16 02:24:22.557186	Want to dance\n	57
8	<EMAIL>	2025-02-21 04:29:20.231307	<EMAIL>	2025-02-21 04:29:20.231307	Add a comment 1\n	58
52	<EMAIL>	2025-03-04 00:13:14.232049	<EMAIL>	2025-03-04 00:13:14.232049	Certificate 4 in finance and mortgage broking ( Current ) : AUD 1650	106
53	<EMAIL>	2025-03-04 00:13:19.973864	<EMAIL>	2025-03-04 00:13:19.973864	Diploma of Finance and Mortgage Broking : AUD 1850	106
54	<EMAIL>	2025-03-04 00:17:50.803722	<EMAIL>	2025-03-04 00:17:50.803722	Certificate III in Individual Support : AUD 1450\n	107
55	<EMAIL>	2025-03-04 00:18:44.259892	<EMAIL>	2025-03-04 00:18:44.259892	DNP	107
56	<EMAIL>	2025-03-04 00:19:55.520186	<EMAIL>	2025-03-04 00:19:55.520186	Certificate III in Light Vehicle Mechanical Technology\t $ 1,350	108
\.


--
-- Data for Name: company; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.company (id, created_by, created_date, last_modified_by, last_modified_date, active, address, email_address, full_name, gender, is_system_admin, phone_number, user_id) FROM stdin;
1	anonymousUser	2025-02-11 20:38:29.713785	anonymousUser	2025-02-11 20:38:29.713785	f	Dhaka	<EMAIL>	System Admin	Male	f	***********	1
2	<EMAIL>	2025-02-11 21:05:20.953182	<EMAIL>	2025-02-11 21:05:20.953182	f	Australia	<EMAIL>	Abdullah Ahmed	Male	f	+61 ***********	6
3	<EMAIL>	2025-02-11 21:09:01.354012	<EMAIL>	2025-02-11 21:09:01.354012	f	Australia	<EMAIL>	Rakin Hasan	Male	f	+61 ***********	7
\.


--
-- Data for Name: lead_sales_agent; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.lead_sales_agent (lead_id, sales_agent_id) FROM stdin;
1	2
2	2
3	1
4	1
5	1
6	1
7	1
8	1
9	1
10	1
11	1
12	1
13	1
14	1
15	1
16	1
17	1
18	1
19	1
20	1
21	1
22	1
23	1
24	1
25	1
26	1
27	1
28	1
29	1
30	1
31	1
32	1
33	2
34	1
35	1
36	1
37	1
38	4
39	1
41	2
42	2
43	4
45	4
46	2
47	2
48	2
49	4
50	2
51	2
54	3
55	1
56	3
57	2
58	2
59	2
60	2
61	2
62	3
63	3
64	3
65	3
66	3
102	2
40	1
103	4
104	3
105	1
106	1
107	1
108	1
109	2
110	4
\.


--
-- Data for Name: leads; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.leads (id, created_by, created_date, last_modified_by, last_modified_date, address, application_count, company_name, email, lead_name, phone, status) FROM stdin;
25	<EMAIL>	2025-02-12 00:34:30.806389	<EMAIL>	2025-02-12 00:41:39.681529	VIC	0	VK Homes	<EMAIL>	Davinder Randhawa	0432 052 258	HOT
1	<EMAIL>	2025-02-11 23:23:58.059552	<EMAIL>	2025-02-11 23:24:12.693318		0	Yous Group	<EMAIL>	Jason	0469742998	HOT
2	<EMAIL>	2025-02-12 00:02:55.583652	<EMAIL>	2025-02-12 00:02:59.634025		0	Spot on Global	<EMAIL>	Komal Karkra	0435229828	HOT
23	<EMAIL>	2025-02-12 00:33:39.434118	<EMAIL>	2025-02-12 00:41:41.532184	VIC	0	IIE	<EMAIL>	Sudeep Singh	0414697964	HOT
11	<EMAIL>	2025-02-12 00:22:11.630127	<EMAIL>	2025-02-12 00:41:43.354034	VIC	0	SMART Education & Migration	<EMAIL>	Dean Liu	0414587225	HOT
22	<EMAIL>	2025-02-12 00:33:13.904284	<EMAIL>	2025-02-12 00:41:46.48983	VIC	0	MTT Migration	<EMAIL>	Nikesh	0452411515	HOT
7	<EMAIL>	2025-02-12 00:18:13.041485	<EMAIL>	2025-02-12 00:18:56.736923	VIC	0	N/A	<EMAIL>	Waleed	0435853585	HOT
6	<EMAIL>	2025-02-12 00:17:50.240653	<EMAIL>	2025-02-12 00:18:59.765273	VIC	0	New England College	<EMAIL>	Rajbir Singh	0422283267	HOT
5	<EMAIL>	2025-02-12 00:17:06.754979	<EMAIL>	2025-02-12 00:19:02.488678	VIC	0	Hawks Migration	<EMAIL>	Robindeep Singh Karwal / Ridhika	0433110126	HOT
4	<EMAIL>	2025-02-12 00:16:14.055426	<EMAIL>	2025-02-12 00:19:04.246589	VIC	0	N/A	<EMAIL>	Parminder	0498283369	HOT
3	<EMAIL>	2025-02-12 00:15:30.460055	<EMAIL>	2025-02-12 00:19:07.587126	VIC	0	Van Company	-	Ranjot	+61 ***********	HOT
8	<EMAIL>	2025-02-12 00:19:46.47109	<EMAIL>	2025-02-12 00:19:52.076573	VIC	0	EDUVision Global	<EMAIL>	Uka Badia	*********	HOT
30	<EMAIL>	2025-02-12 00:40:32.586096	<EMAIL>	2025-02-12 00:41:13.752033	VIC	0	Zen Migration	<EMAIL>	Shirley	0410046070	HOT
9	<EMAIL>	2025-02-12 00:20:33.031075	<EMAIL>	2025-02-12 00:20:35.862216	VIC	0	Honest Immigration Group	<EMAIL>	Rahul	61430297655	HOT
10	<EMAIL>	2025-02-12 00:21:25.928625	<EMAIL>	2025-02-12 00:21:31.714104	QLD	0	Chiao Shun International Pty Ltd	<EMAIL>	Stevia chen	0430333018	HOT
33	<EMAIL>	2025-02-12 00:46:04.942341	<EMAIL>	2025-02-12 00:46:04.953956		0	N/A	N/A	Sales Rep Mayukh	0432168583	FRESH
14	<EMAIL>	2025-02-12 00:23:43.310632	<EMAIL>	2025-02-12 00:41:48.386004	VIC	0	Newpoint migration	<EMAIL>	Ivy	0426456158	HOT
16	<EMAIL>	2025-02-12 00:24:56.890003	<EMAIL>	2025-02-12 00:41:16.092448	VIC	0	ABC Overseas Consultants	<EMAIL>	Abdur Rahman	0452301630	HOT
12	<EMAIL>	2025-02-12 00:22:36.71692	<EMAIL>	2025-02-12 00:41:53.617236	NSW	0	N/A	<EMAIL>	Ajit Singh	404 378 897	HOT
34	<EMAIL>	2025-02-12 01:09:23.734286	<EMAIL>	2025-02-12 01:11:58.719377	VIC	0	Study Solutions	<EMAIL>	Bella / Jash	0493732710	HOT
18	<EMAIL>	2025-02-12 00:29:05.499396	<EMAIL>	2025-02-12 00:41:18.247741	VIC	0	N/A	<EMAIL>	Peter	0431087642	HOT
36	<EMAIL>	2025-02-12 01:22:02.507595	<EMAIL>	2025-02-12 01:35:52.541335	VIC	0	N/A	<EMAIL>	Kiran Chanmanwar	0469316532	HOT
29	<EMAIL>	2025-02-12 00:39:21.94183	<EMAIL>	2025-02-12 00:41:19.873693	VIC	0	Aussiz	<EMAIL>	Karan	0414066806	HOT
32	<EMAIL>	2025-02-12 00:42:38.923486	<EMAIL>	2025-02-12 00:42:47.536459	QLD	0	Atlantic Clg	<EMAIL>	Sarabjeet	***********	HOT
21	<EMAIL>	2025-02-12 00:32:46.818318	<EMAIL>	2025-02-12 00:41:21.869444	VIC	0	ACME Migration	<EMAIL>	Robin	0435273813	HOT
20	<EMAIL>	2025-02-12 00:31:27.050382	<EMAIL>	2025-02-12 00:41:04.626082	Gold Coast	0	Gold Coast Training College Pty Ltd (Current)	<EMAIL>	Ms Xuelin Fang	0484 619 285	HOT
15	<EMAIL>	2025-02-12 00:24:21.332244	<EMAIL>	2025-02-12 00:41:06.528628	VIC	0	HCI Australia	<EMAIL>	Robert	0429 292 811	HOT
31	<EMAIL>	2025-02-12 00:40:59.402101	<EMAIL>	2025-02-12 00:41:08.374336	VIC	0	OZ study and Migration	<EMAIL>	Silmy	**********	HOT
24	<EMAIL>	2025-02-12 00:34:02.488754	<EMAIL>	2025-02-12 00:41:10.194021	VIC	0	Oceanian Migration	<EMAIL>	bubban	0468368107	HOT
27	<EMAIL>	2025-02-12 00:35:21.796765	<EMAIL>	2025-02-12 00:41:12.115027	VIC	0	Big Brothers Group	<EMAIL>	Arun	0481033481	HOT
17	<EMAIL>	2025-02-12 00:28:38.752186	<EMAIL>	2025-02-12 00:41:23.671248	SA	0	Yogi Group	<EMAIL>	Dinesh Yogi	0405573239	HOT
42	<EMAIL>	2025-02-12 02:28:42.943609	<EMAIL>	2025-02-16 02:33:04.706742	3593 VICTORIA POINT WEST QLD 4165	0	Unicons Migration	<EMAIL>	Miklos	0410867280	HOT
13	<EMAIL>	2025-02-12 00:23:09.739609	<EMAIL>	2025-02-12 00:41:30.990537	VIC	0	Deriva Migration	<EMAIL>	Hiten	0415262167	HOT
19	<EMAIL>	2025-02-12 00:29:47.358848	<EMAIL>	2025-02-12 00:41:33.398171	QLD	0	Eagle Training Pty Ltd	<EMAIL>	Gurdeep Bhullar	0450150095	HOT
26	<EMAIL>	2025-02-12 00:34:56.68056	<EMAIL>	2025-02-12 00:41:35.842611	VIC	0	Visa Pundit	<EMAIL>	Girish Rawat	0421 194 984	HOT
37	<EMAIL>	2025-02-12 01:30:57.32642	<EMAIL>	2025-02-12 01:35:44.938413	VIC	0	N/A	<EMAIL>	Tanveer Singh	+61 ***********	HOT
35	<EMAIL>	2025-02-12 01:18:36.09367	<EMAIL>	2025-02-12 01:35:50.537426	VIC	0	N/A	<EMAIL>	Gaurav Sood 	0414239619	HOT
40	<EMAIL>	2025-02-12 02:25:01.579389	<EMAIL>	2025-03-03 03:05:48.**********- 420 (Basement) Collins St, Melbourne Vic – 3000	0	Gen Institute Australia	<EMAIL>	Vyoma	**********	FRESH
39	<EMAIL>	2025-02-12 02:20:43.110959	<EMAIL>	2025-02-12 02:20:43.121619	VIC	0	Overseas Study & Migration Services	<EMAIL>	Noor MD Limon	0466666514	FRESH
28	<EMAIL>	2025-02-12 00:35:56.154673	<EMAIL>	2025-02-16 02:30:28.476149	VIC	0	N/A	<EMAIL>	Ruby	0468 852 786	WARM
41	<EMAIL>	2025-02-12 02:26:59.234784	<EMAIL>	2025-02-12 02:38:08.993683	Calamvale, QLD 4116	0	Ruifan Education	<EMAIL>	Charlie	0420888816	HOT
44	<EMAIL>	2025-02-12 02:33:19.915267	<EMAIL>	2025-02-12 02:33:19.915267		0	N/A	<EMAIL>	RUCHI GUPTA	***********	FRESH
43	<EMAIL>	2025-02-12 02:31:28.29068	<EMAIL>	2025-02-12 02:31:34.944292	8/72-78 Cardigan St. Guildford NSW 2161	0	N/A	<EMAIL>	Ibrahim Majid 	+61 ***********	HOT
38	<EMAIL>	2025-02-12 01:42:05.754507	<EMAIL>	2025-02-12 02:35:09.536106	4A Erebus Cres. Tregear NSW 2770	0	N/A	<EMAIL>	Stephen Ekene Ogbusuo\t\t	+***********	HOT
45	<EMAIL>	2025-02-12 02:53:19.888244	<EMAIL>	2025-02-12 02:53:19.894721		0	N/A	<EMAIL>	Bhavyang Trivedi\t	+61 ***********\t	FRESH
46	<EMAIL>	2025-02-12 03:29:12.047283	<EMAIL>	2025-02-12 03:29:12.052493	University Ave, Canberra City 2601 ACT	0	Global Consult	<EMAIL>	Basan Chhetri	0484230167	FRESH
47	<EMAIL>	2025-02-12 05:09:38.950479	<EMAIL>	2025-02-12 05:09:38.957315	Brisbane QLD 4000	0	Auspac Visa	<EMAIL>	Teresa Cardona	0415264125 	FRESH
60	<EMAIL>	2025-02-25 23:57:48.052627	<EMAIL>	2025-02-25 23:57:48.061939	QLD 	0	Go Learn Australia	<EMAIL>	Melwyn Dsouzza	0423097246	FRESH
48	<EMAIL>	2025-02-12 05:32:57.927722	<EMAIL>	2025-02-12 05:33:09.877758	Brisbane City QLD 4000	0	Sirus Migration	<EMAIL>	Ashani Yapa	0430701144	HOT
49	<EMAIL>	2025-02-12 05:45:04.546842	<EMAIL>	2025-02-12 05:45:04.573359	99A Ravenswood Dr, Westminister WA 6061	0	N/A	<EMAIL>	Patrick Kipkoech\t	+61 ***********\t	FRESH
50	<EMAIL>	2025-02-12 06:15:20.181653	<EMAIL>	2025-02-12 06:15:20.187566	Inner City Sydney	0	Invicta Technical College	<EMAIL>	Sayeed Parveez	0466829418	FRESH
51	<EMAIL>	2025-02-12 23:51:19.635064	<EMAIL>	2025-02-12 23:51:19.643649	 Bundoora VIC 3083	0	Gate Migration	<EMAIL>	Dimple Singh	0470756017	FRESH
52	<EMAIL>	2025-02-13 01:18:03.193169	<EMAIL>	2025-02-13 01:18:03.193169		0	N/A	<EMAIL>	Keji Yakubu	***********	FRESH
53	<EMAIL>	2025-02-13 01:20:01.514899	<EMAIL>	2025-02-13 01:20:01.514899		0	OZ STUDY & VISA CENTRE	<EMAIL>	Raminder Pal Singh	***********	FRESH
54	<EMAIL>	2025-02-13 01:21:11.256448	<EMAIL>	2025-02-13 01:21:11.261982		0	Vahora & Associates	<EMAIL>	Shoaib Vahora	61423735436	FRESH
55	<EMAIL>	2025-02-13 02:06:26.303372	<EMAIL>	2025-02-13 02:06:26.311035	VIC	0	N/A	<EMAIL>	Saravjit Singh	0416151404	FRESH
61	<EMAIL>	2025-02-26 02:40:54.736291	<EMAIL>	2025-02-26 02:40:54.744986	NSW 	0	Integrity Migration Solutions	<EMAIL>	Thais Dinallo	0424570699	FRESH
57	<EMAIL>	2025-02-16 02:23:39.228637	<EMAIL>	2025-02-16 02:25:35.897524	NSW	0	N/A	<EMAIL>	Hyper	**********	COLD
62	<EMAIL>	2025-02-26 02:44:14.609854	<EMAIL>	2025-02-26 02:44:14.614163		0	SWIVIVA	info@swiviva.<NAME_EMAIL>	Isaiah Uchechukwu Okorie	***********	FRESH
63	<EMAIL>	2025-02-26 03:03:13.729699	<EMAIL>	2025-02-26 03:03:13.740939	550 Lonsdale Street, Level 1, Melbourne Victoria 3000	0	Ghothane	<EMAIL>	Rajaneesh Shah	***********	FRESH
103	<EMAIL>	2025-03-03 04:19:13.411213	<EMAIL>	2025-03-03 04:19:13.419511	Little Learners Child Care Centre 9 - 11 Henty Drive Redbank Plains, QLD, 4301	0	Little Learners 	<EMAIL>	Mayleen Eteaki 	+61 408 175 516	FRESH
58	<EMAIL>	2025-02-18 04:54:11.319919	<EMAIL>	2025-02-18 04:54:11.326835	QLD	0	Springs of Hope Care	<EMAIL>	Faith Gwatidzo	**********	FRESH
59	<EMAIL>	2025-02-20 00:02:56.853767	<EMAIL>	2025-02-20 00:02:56.861541	VIC 	0	OneU Education	<EMAIL>	Hansun	********** 	FRESH
64	<EMAIL>	2025-02-26 03:09:07.185137	<EMAIL>	2025-02-26 03:09:07.192144	Adelaide · Perth	0	ISA MIGRATIONS	<EMAIL>	HARMANDEEP	***********	FRESH
56	<EMAIL>	2025-02-13 04:23:34.821997	<EMAIL>	2025-02-26 03:11:09.843501		0	N/A	<EMAIL>	Benson	***********	HOT
65	<EMAIL>	2025-02-26 03:13:01.169331	<EMAIL>	2025-02-26 03:13:01.174753	99 101 Francis Street, Perth Western Australia 6003	0	Korrylink Migration Consultants Pty Ltd	<EMAIL>	Liang Lu	***********	FRESH
66	<EMAIL>	2025-02-28 00:10:57.393836	<EMAIL>	2025-02-28 00:10:57.40378	9 Rutland Street, Sydney New South Wales 2010	0	OAA Study	<EMAIL>	Matthias Ehizua	***********	FRESH
102	<EMAIL>	2025-03-03 00:53:13.354353	<EMAIL>	2025-03-03 00:53:13.370544	110BeattyRd,Archerfield4108 Queensland Australia	0	Oceania College	<EMAIL>; <EMAIL>	Saurav Singh	0416926512 	FRESH
104	<EMAIL>	2025-03-03 04:35:10.593373	<EMAIL>	2025-03-03 04:35:10.600156	Suite 605, Level 6/32 York St, Sydney NSW 2000, Australia	0	VISAWISE PTY LTD	<EMAIL>.<NAME_EMAIL>	Walson Mathew	***********	FRESH
105	<EMAIL>	2025-03-04 00:05:50.514281	<EMAIL>	2025-03-04 00:05:50.526315		0	N/A	<EMAIL>	Samuel	0420409322	FRESH
106	<EMAIL>	2025-03-04 00:12:21.068573	<EMAIL>	2025-03-04 00:12:21.076283		0	Y-Axis Migration	<EMAIL>	Somya Reddy	0468346046	FRESH
107	<EMAIL>	2025-03-04 00:16:56.969172	<EMAIL>	2025-03-04 00:16:56.975577		0	N/A	<EMAIL>	Jonah	0430615622	FRESH
108	<EMAIL>	2025-03-04 00:19:30.72404	<EMAIL>	2025-03-04 00:19:30.732448		0	Visa Empire	<EMAIL>	Thupten	0424777060	FRESH
109	<EMAIL>	2025-03-04 01:26:24.049691	<EMAIL>	2025-03-04 01:26:24.055558	1/33 May Road Lalor VIC 3075 Australia	0	RS GLOBAL IMMIGRATION PTY LTD	<EMAIL> ; <EMAIL>	Ginni Kocher	0452299777	FRESH
110	<EMAIL>	2025-03-04 02:09:49.678127	<EMAIL>	2025-03-04 02:09:49.686708		0	N/A	<EMAIL>	Collins Kiprotich\t	+61 ***********\t	FRESH
\.


--
-- Data for Name: qualifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.qualifications (id, enrollment_price, notes, offshore_price, qualification_id, qualification_name, rpl_price, total_applications, type) FROM stdin;
1	2450	Important Note: 1 year + Experience = Direct RPL / Enrolment Process	0	CHC30121	Certificate III in Early Childhood Education and Care	1650	0	Education
2	0	\N	0	CHC30221	Certificate III in School Based Education Support	1150	0	Education
3	2450	Pre-requisite: Certificate III in Early Childhood Education & Care Important Note: 1 year + Experience = Direct RPL / Enrolment Process This is also the latest package.	0	CHC50121	Diploma of Early Childhood Education and Care	1650	0	Education
4	2450	Pre-requisite: Certificate III in Individual Support\nImportant Note: No Experience = Enrolment Process	3750	CHC43121	Certificate IV in Disability Support	1350	0	Health
5	0	\N	0	CHC43315	Certificate IV in Mental Health	1650	0	Health
6	2450	\N	0	CHC33021	Certificate III in Individual Support	1350	0	Health
7	2450	\N	3750	CHC52021	Diploma of Community Service	1350	0	Health
8	0	\N	0	CHC51015	Diploma of Counselling	1850	0	Health
9	0	\N	3750	CHC62015	Advanced Diploma of Community Sector management	1650	0	Health
10	0	\N	0	CPC10120	Certificate I in Construction	1750	0	Trade
11	0	Important Note: Must have white card to proceed	3750	CPC30620	Certificate III in painting and decorating	1250	0	Trade
12	0	Important Note: Must have white card to proceed	3750	CPC31020	Certificate III in Solid Plastering	1650	0	Trade
13	0	Important Note: Must have white card to proceed	3750	CPC30220	Certificate III in Carpentry	1250	0	Trade
14	0	Important Note: Confirm with Management before taking Files	0	CPC30320	Certificate III in Concreting	1450	0	Trade
15	0	Important Note: Must have white card to proceed	0	RII30820	Certificate III in Civil Construction Plant Operations	2650	0	Trade
16	0	Important Note: 2-3 Weeks to proceed	0	CPC31220	Certificate III in Wall and Ceiling Lining	2450	0	Trade
17	0	Important Note: Confirm with Management before taking Files	0	CPC33020	Certificate III in Bricklaying and Blocklaying	1650	0	Trade
18	0	Important Note: Confirm with Management before taking Files	0	CPC31320	Certificate III in Wall and Floor Tiling	1450	0	Trade
19	0	Important Note: Confirm with Management before taking Files	0	CPC31420	Certificate III in Construction Waterproofing	1650	0	Trade
20	0	Important Note: Must have white card to proceed	3750	CPC32420	Certificate III in Plumbing	1650	0	Trade
21	0	Important Note: Must have white card to proceed	3750	CPC32620	Certificate III in Roof Plumbing	1450	0	Trade
22	0	Important Note: Must have white card to proceed	3750	CPC30820	Certificate III in Roof Tiling	2750	0	Trade
23	0	Important Note: Must have white card to proceed	0	CPC32320	Certificate III in Stonemasonry	2450	0	Trade
24	0	Important Note: Must have white card to proceed	0	MSF30422	Certificate III in Glass and Glazing	2750	0	Trade
25	0	Important Note: Must have white card to proceed	3750	MSF30322	Certificate III in Cabinet Making and Timber Technology	2750	0	Trade
26	0	Important Note: Must have white card to proceed	0	CPC31920	Certificate III in Joinery	1450	0	Trade
27	0	Important Note: Must have white card to proceed	3750	CPC40120	Certificate IV in Building and Construction ( Building )	1250	0	Trade
28	0	Important Note: Must have white card to proceed	3750	CPC40920	Certificate IV in Plumbing and Services	1850	0	Trade
29	0	Important Note: JAN 2024 Issue date only & Must have white card to proceed	0	CPC41020	Certificate IV in Demolition	2450	0	Trade
30	0	Important Note: Must have white card to proceed	3750	CPC50220	Diploma of Building & Construction ( Building )	1450	0	Trade
31	0	Important Note: Must have white card to proceed	0	CPC60220	Advanced Diploma of Building and Construction (Management)	3500	0	Trade
32	0	Important Note: Charge Extra if no photos & Videos	4500	AUR30620	Certificate III in Light Vehicle Mechanical Technology	1450	0	Automotive
33	0	\N	4750	AUR31120	Certificate III in Heavy Commercial Vehicle Mechanical Technology	2650	0	Automotive
34	0	\N	4750	AUR32120	Certificate III in Automotive Body Repair Technology	2750	0	Automotive
35	0	\N	4750	AUR30320	Certificate III in Automotive Electrical Technology	2750	0	Automotive
36	0	\N	4750	AUR32420	Certificate III in Automotive Refinishing Technology	2750	0	Automotive
37	0	Important Note: Confirm with Management before taking Files	0	AHC30716	Certificate III in Horticulture	3250	0	Automotive
38	0	\N	4750	AUR31520	Certificate III in Automotive Diesel Engine Technology	3250	0	Automotive
39	0	Pre-requisite: An automotive mechanical Certificate III Qualification, or be able to demonstrate equivalent competency.	4750	AUR40216	Certificate IV in Automotive Mechanical Diagnosis	2450	0	Automotive
40	0	Pre-requisite: An automotive mechanical Certificate III Qualification, or be able to demonstrate equivalent competency.	4750	AUR40720	Certificate IV in Automotive Body Repair Technology	2450	0	Automotive
41	0	Pre-requisite AUR30320 Certificate III in Automotive Electrical Technology or be able to demonstrate equivalent competency.	4750	AUR40620	Certificate IV in Automotive Electrical Technology	2450	0	Automotive
42	0	Pre-requisite: An automotive mechanical Certificate III qualification, or be able to demonstrate equivalent competency.	4750	AUR40820	Certificate IV in Automotive Mechanical Overhauling	3750	0	Automotive
43	0	Pre-requisite:\n- An automotive Certificate IV qualification in one of the following disciplines, or be able to demonstrate equivalent competency.\n- AUR40216 Certificate IV in Automotive Mechanical Diagnosis\n- AUR40816 Certificate IV in Automotive Mechanical Overhauling	4750	AUR50216	Diploma of Automotive Technology	2750	0	Automotive
44	0	\N	0	BSB30120	Certificate III in Business	1000	0	Business
45	0	\N	0	SIR30216	Certificate III in Retail	1950	0	Business
46	0	\N	2000	BSB40120	Certificate IV in Business	1000	0	Business
47	0	\N	2000	BSB40220	Certificate IV in Business	1450	0	Business
48	0	\N	2000	BSB40520	Certificate IV in Leadership and Management	1000	0	Business
49	0	\N	2000	BSB40820	Certificate IV in Marketing and Communication	1450	0	Business
50	0	\N	0	SIT50322	Diploma of Event Management	3250	0	Business
51	0	\N	2000	BSB50120	Diploma of Business	1000	0	Business
52	0	\N	2000	BSB50420	Diploma of Leadership & management	1000	0	Business
53	0	\N	0	BSB50820	Diploma of Project Management	1250	0	Business
54	0	\N	0	BSB50920	Diploma of Quality Auditing	1850	0	Business
55	0	Pre-requisite:\n- Have completed BSB42415 Certificate IV in Marketing and Communication, or\n- Have completed the following units (or equivalent competencies):\n(i). BSBCMM411 Make presentations;\n(ii). BSBCRT412 Articulate, present and debate ideas;\n(iii). BSBMKG433 Undertake marketing activities;\n(iv). BSBMKG435 Analyse consumer behaviour;\n(v). BSBMKG439 Develop and apply knowledge of communications industry; and\n(vi). BSBWRT411 Write complex documents. Equivalent competencies are predecessors to these units, which have been mapped as equivalent, or\n- Have two years equivalent full-time relevant work experience.	0	BSB50620	Diploma of Marketing and Communication	1450	0	Business
56	0	Pre-requisite:\n- Have completed a Diploma or Advanced Diploma from the BSB Training Package (current or superseded equivalent versions), or\n- Have two years equivalent full-time relevant workplace experience in an operational or leadership role in an enterprise.	2000	BSB60420	Advanced Diploma of Leadership and Management	1250	0	Business
57	0	Pre-requisite:\n- Have completed BSB52415 Diploma of Marketing and Communication, or\n- Have completed the following units (or equivalent competencies):\n(i). BSBMKG541 Identify and evaluate marketing opportunities;\n(ii). BSBMKG542 Establish and monitor the marketing mix;\n(iii). BSBMKG552 Design and develop marketing communication plans;\n(iv). BSBMKG555 Write persuasive copy; and\n(v). BSBPMG430 Undertake project work.\nEquivalent competencies are predecessors to these units, which have been mapped as equivalent, or\n- Have four years equivalent full-time relevant work experience.	0	BSB60520	Advanced Diploma of Marketing and Communication	2500	0	Business
58	0	Pre-requisite:\n- Have completed one of the following qualifications:\n- BSB50820 Diploma of Project Management; or\n- BSB51415 Diploma of Project Management (or a superseded equivalent version),\nor\n- Have completed two years equivalent full-time relevant workplace experience at a significant level within a project or program environment within an enterprise.	0	BSB60720	Advanced Diploma of Program Management	1750	0	Business
59	0	\N	0	BSB80120	Graduate Diploma of Management (Learning)	1450	0	Business
60	0	Pre-requisite:\n- Have completed a Diploma or Advanced Diploma qualification in related fields of study and 3 years equivalent full-time relevant workplace experience at a significant level of leadership and management responsibility and/or complexity in an enterprise, or\n- Have completed a Bachelor degree in related fields of study and 2 years equivalent full-time relevant workplace experience at a significant level of leadership and management responsibility and/or complexity in an enterprise, or\n- Have five years equivalent full-time relevant workplace experience at a significant level of leadership and management responsibility and/or complexity in an enterprise.	0	BSB80320	Graduate Diploma of Strategic Leadership	2450	0	Business
61	0	Pre-requisite: Entry to this qualification is limited to those who:\n- Have completed a Diploma or Advanced Diploma qualification in related fields of study and 3 years equivalent full-time relevant workplace experience at a significant level of project or program leadership and management responsibility and/or complexity in an enterprise, or\n- Have completed a Bachelor degree in related fields of study and 2 years equivalent full-time relevant workplace experience at a significant level of project or program leadership and management responsibility and/or complexity in an enterprise, or\n- Have five years equivalent full-time relevant workplace experience at a significant level of leadership and management responsibility and/or complexity in an enterprise.	0	BSB80220	Graduate Diploma of Portfolio Management	2450	0	Business
62	0	\N	0	TAE40122	Certificate IV in Training & Assessment ( TAE )	1850	0	Training & Assessment
63	0	\N	0	FNS40821	Certificate IV in Finance and Mortgage Broking	1450	0	Accounting & Finance
64	0	Pre-requisite:\n(i). FNSACC321 Process financial transactions and extract interim reports\n(ii). FNSACC322 Administer subsidiary accounts and ledgers\n(iii). FNSACC418 Work effectively in the accounting and bookkeeping industry\n(iv). FNSACC421 Prepare financial reports (this unit is the equivalent version of BSBFIA401 Prepare financial reports).\nThese competencies may have been achieved through completion of the following	0	FNS50222	Diploma of Accounting	1450	0	Accounting & Finance
65	0	\N	0	FNS50322	Diploma of Finance and Mortgage Broking Management	1850	0	Accounting & Finance
66	0	Important Note: Add $300 for Student Visa	3750	AHC30921	Certificate III in Landscape Construction	1950	0	Property & Real Estate
67	0	\N	2750	CPP41419	Certificate IV in Real Estate Practice	1650	0	Property & Real Estate
68	0	\N	4500	CPP51122	Diploma of Property (Agency Management)	1950	0	Property & Real Estate
69	0	Important Note: Must allow 4 weeks + for certificate after docs & payment	0	CPP20218	Certificate II in Security Operations	1750	0	Security
70	0	\N	0	CPP31318	Certificate III in Security Operations	1950	0	Security
71	0	\N	0	CPP31418	Certificate III in Close Protection Operations	2450	0	Security
72	0	Important Note: Process of obtaining the certification are as follows -\nStep 1 - Collect Documents as usual Including photos, videos white card etc. (Minimum 2 years experience)\nStep 2 - We send it to the college and wait for the class schedule\nStep 3 - They assign class time for 4 weeks in NSW Sydney (Students will need to attend for capstone which is 2 units)\nStep 4 - They make a full payment once they get the class timetables Step 5 - They attend the class\nStep 6 - The college will release the certificate 2 weeks after classes finish\nClass Location will be in Sydney Punchbowl area in AIBT Campus, students all over Australia can do it but they need to go to the campus during class time tables.	0	UEE30820	Certificate III in Electrotechnology Electrician	12500	0	Engineering
73	0	Important Note: Student Visa Add extra $300	6500	MEM31922	Certificate III in Engineering - Fabrication Trade	1950	0	Engineering
74	0	\N	0	SHB30321	Certificate III in Nail Technology	2450	0	Engineering
75	0	\N	0	ICT50220	Diploma of Information Technology	1650	0	Technology
76	0	\N	0	ICT60220	Advanced Diploma of Information Technology	1850	0	Technology
77	0	\N	0	PSP40416	Certificate IV in Government Investigations	3450	0	Vocational
78	0	\N	0	SHB30516	Certificate III in Barbering	2250	0	Fitness, Beauty & Hairdressing
79	0	\N	0	SHB30416	Certificate III in Hairdressing	1650	0	Fitness, Beauty & Hairdressing
80	0	\N	0	SIS30321	Certificate III in Fitness	1950	0	Fitness, Beauty & Hairdressing
81	0	Pre-requisite:\nEntry to this qualification is open to individuals who hold the following units of competency or units that have been superseded by these units:\n- HLTAID011 - Provide First Aid (or a unit that supersedes this unit)\n- HLTWHS001 - Participate in workplace health and safety\n- SISFFIT032 - Complete pre-exercise screening and service orientation\n- SISFFIT033 - Complete client fitness assessments\n- SISFFIT035 - Plan group exercise sessions\n- SISFFIT036 - Instruct group exercise sessions\n- SISFFIT040 - Develop and instruct gym-based exercise programs for individual clients\n- SISFFIT047 - Use anatomy and physiology knowledge to support safe and effective exercise\n- SISFFIT052 - Provide healthy eating information	0	SIS40221	Certificate IV in Fitness	2250	0	Fitness, Beauty & Hairdressing
82	0	Pre-requisite: Entry to this qualification is open to individuals who have-\n1. Achieved a Certificate III in Hairdressing (or equivalent); and\n2. At least one year post-qualification full time employment experience as a hairdresser in a salon environment where they have applied the skills and knowledge covered in the above (or equivalent) qualification.	0	SHB40216	Certificate IV in Hairdressing	2250	0	Fitness, Beauty & Hairdressing
83	0	\N	0	SHB40121	Certificate IV in Beauty Therapy	1650	0	Fitness, Beauty & Hairdressing
84	0	\N	0	SHB50121	Diploma of Beauty Therapy	1950	0	Fitness, Beauty & Hairdressing
85	2450	Important Note: The main difference between a Diploma of Remedial Massage and a Diploma of Remedial Massage (with rebate) lies in whether the course and its graduates meet the standards required to access private health insurance rebates for clients. Here's a breakdown:\n1. Diploma of Remedial Massage (Standard):\n- This is the standard qualification covering all the required skills and knowledge to work as a remedial massage therapist.\n- Graduates can work in various settings, including clinics, spas, or self- employment.\n- No access to private health insurance rebates for clients unless the program or training provider meets additional requirements.\n2. Diploma of Remedial Massage (With Rebate):\n- This course meets specific compliance requirements set by health funds and accrediting bodies (e.g., Australian health funds like Medibank, Bupa, HCF, etc.).\n- Graduates of this version can register with approved associations (e.g., ATMS, ANTA, etc.) and meet criteria for private health insurance rebates for their clients.\n- Training providers must meet stricter guidelines, often including:\n- Extra clinical practice hours.\n- Adherence to higher standards of delivery.\n- Approval from industry bodies and private health insurers.\nKey Considerations for Students:\nClient Attraction: A "rebate-approved" diploma makes your services more attractive to clients who want to claim treatments through their private health insurance.\nAccreditation of the Provider: Not all training organizations offer the rebate- compliant version. Ensure your provider is accredited by associations recognized by private health funds.\nCost & Duration: Rebate-compliant programs may have slightly higher fees or longer durations to include additional training or clinical practice requirements.	0	HLT52015	Diploma of Remedial Massage	1650	0	Fitness, Beauty & Hairdressing
86	0	\N	0	SHB50216	Diploma of Salon Management	2250	0	Fitness, Beauty & Hairdressing
87	0	\N	0	SHB60118	Advanced Diploma of Intense Pulsed Light and Laser for Hair Reduction	2250	0	Fitness, Beauty & Hairdressing
88	0	\N	0	CPP30119	Certificate III in Urban Pest Management	1650	0	Horticulture
89	0	\N	0	AHC40422	Certificate IV in Horticulture	3250	0	Horticulture
90	0	Important Note: Add $300 for Student Visa	6500	MEM30219	Certificate III in Engineering - Mechanical Trade	2250	0	Engineering
91	0	Important Note: UEE32220 Certificate III in Air Conditioning and Refrigeration $8500 Lowest Price Step 1 - Send all usual docs Step 2 - Make payment Step 3 - LMS needs to be logged in Step 4 - Finish 6 Units via LMS ( College will help student to pass ) Step 5 - Rest all units RPL Step 6 - Certificate Delivered	0	UEE32220	Certificate III in Air Conditioning and Refrigeration	8500	0	Engineering
\.


--
-- Data for Name: sales_agent; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.sales_agent (id, created_by, created_date, last_modified_by, last_modified_date, active, address, current_week, email_address, full_name, gender, phone_number, weekly_target, user_id) FROM stdin;
1	<EMAIL>	2025-02-11 20:57:58.325405	<EMAIL>	2025-02-11 20:57:58.325405	t	Australia	\N	<EMAIL>	Sadman Shabab	Male	+**************	0	2
2	<EMAIL>	2025-02-11 21:00:35.745893	<EMAIL>	2025-02-11 21:00:35.745893	t	Philippines	\N	<EMAIL>	Quennee SkillSync	Female	+61 ***********	0	3
3	<EMAIL>	2025-02-11 21:02:01.523358	<EMAIL>	2025-02-11 21:02:01.523358	t	Philippines	\N	<EMAIL>	Joe SkillSync	Female	+63 ************	0	4
4	<EMAIL>	2025-02-11 21:03:47.767814	<EMAIL>	2025-02-11 21:03:47.767814	t	Australia	\N	<EMAIL>	Kawsar SkillSync	Male	+61 ***********	0	5
52	<EMAIL>	2025-03-02 19:27:39.03852	<EMAIL>	2025-03-02 19:27:39.03852	t	Australia	\N	<EMAIL>	Mansib SkillSync	Male	+00000000	0	52
\.


--
-- Data for Name: sold_qualifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.sold_qualifications (id, sold_price, application_id, qualification_id) FROM stdin;
1	3750	1	30
2	2250	2	84
3	2450	3	52
4	1650	4	9
5	1675	5	27
6	1675	6	30
7	1350	7	6
8	1850	8	69
9	1850	9	62
10	1450	10	7
11	1650	11	5
12	8500	12	30
13	10000	13	30
14	2550	14	30
15	1350	15	6
16	1450	16	30
17	1200	18	7
18	2500	19	16
19	2250	20	62
20	1800	21	30
21	2450	22	85
22	1950	23	68
23	1250	24	6
24	2750	25	40
25	1100	26	6
27	1850	28	69
30	1550	31	6
31	1650	32	17
32	825	33	7
33	825	34	7
34	825	35	6
35	825	36	7
36	1100	37	51
37	2350	38	73
38	1350	39	59
39	1950	40	1
40	1650	41	6
41	1250	41	7
52	1950	52	67
54	1250	54	8
55	1250	55	10
56	1250	56	8
57	1250	57	8
58	1450	58	7
59	2250	59	49
60	1350	60	33
61	1450	53	17
\.


--
-- Data for Name: tracker; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tracker (id, created_by, created_date, last_modified_by, last_modified_date, first_login, last_login, total_time, username) FROM stdin;
3	<EMAIL>	2025-02-11 21:37:48.795646	<EMAIL>	2025-02-11 23:58:33.364397	2025-02-11 21:37:48.795162	2025-02-11 23:58:33.363765	2620	<EMAIL>
5	<EMAIL>	2025-02-11 23:46:02.573117	<EMAIL>	2025-02-11 23:59:52.705385	2025-02-11 23:46:02.572578	2025-02-11 23:59:52.704771	123	<EMAIL>
26	<EMAIL>	2025-02-19 00:01:55.216152	<EMAIL>	2025-02-19 23:07:35.86444	2025-02-19 00:01:55.215893	2025-02-19 23:07:35.864131	25838	<EMAIL>
23	<EMAIL>	2025-02-18 00:05:34.237831	<EMAIL>	2025-02-18 07:23:15.38081	2025-02-18 00:05:34.237438	2025-02-18 07:23:15.380603	1038	<EMAIL>
14	<EMAIL>	2025-02-14 00:00:41.087029	<EMAIL>	2025-02-14 01:15:41.034507	2025-02-14 00:00:41.086354	2025-02-14 01:15:41.034262	4479	<EMAIL>
22	<EMAIL>	2025-02-17 00:20:17.246729	<EMAIL>	2025-02-17 23:50:06.087822	2025-02-17 00:20:17.24643	2025-02-17 23:50:06.087509	3303	<EMAIL>
18	<EMAIL>	2025-02-16 02:12:20.310358	<EMAIL>	2025-02-16 10:52:39.370605	2025-02-16 02:12:20.310063	2025-02-16 10:52:39.370415	19432	<EMAIL>
2	<EMAIL>	2025-02-11 21:27:30.514474	<EMAIL>	2025-02-11 21:35:54.161172	2025-02-11 21:27:30.51285	2025-02-11 21:35:54.15967	502	<EMAIL>
30	<EMAIL>	2025-02-24 02:25:52.819294	<EMAIL>	2025-02-24 23:59:21.927453	2025-02-24 02:25:52.817891	2025-02-24 23:59:21.927257	20617	<EMAIL>
1	<EMAIL>	2025-02-11 20:39:13.024763	<EMAIL>	2025-02-11 21:38:22.891396	2025-02-11 20:39:13.015378	2025-02-11 21:38:22.889747	1958	<EMAIL>
7	<EMAIL>	2025-02-12 00:02:22.681762	<EMAIL>	2025-02-12 23:45:29.313601	2025-02-12 00:02:22.681252	2025-02-12 23:45:29.313362	22471	<EMAIL>
4	<EMAIL>	2025-02-11 23:16:21.890413	<EMAIL>	2025-02-11 23:18:29.538855	2025-02-11 23:16:21.889833	2025-02-11 23:18:29.537346	127	<EMAIL>
12	<EMAIL>	2025-02-13 00:11:09.662183	<EMAIL>	2025-02-13 06:41:23.369774	2025-02-13 00:11:09.661917	2025-02-13 06:41:23.36958	11033	<EMAIL>
29	<EMAIL>	2025-02-21 04:01:53.020551	<EMAIL>	2025-02-21 08:35:49.639155	2025-02-21 04:01:53.020217	2025-02-21 08:35:49.638938	3631	<EMAIL>
24	<EMAIL>	2025-02-18 01:11:21.761365	<EMAIL>	2025-02-18 23:59:25.218759	2025-02-18 01:11:21.761113	2025-02-18 23:59:25.21858	25388	<EMAIL>
15	<EMAIL>	2025-02-14 00:49:59.962573	<EMAIL>	2025-02-14 07:26:33.430904	2025-02-14 00:49:59.962307	2025-02-14 07:26:33.430731	2608	<EMAIL>
11	<EMAIL>	2025-02-13 00:00:32.144478	<EMAIL>	2025-02-13 23:50:10.84239	2025-02-13 00:00:32.144086	2025-02-13 23:50:10.842181	28217	<EMAIL>
16	<EMAIL>	2025-02-15 08:32:02.804715	<EMAIL>	2025-02-15 08:36:09.309859	2025-02-15 08:32:02.804355	2025-02-15 08:36:09.309647	246	<EMAIL>
9	<EMAIL>	2025-02-12 01:24:15.488314	<EMAIL>	2025-02-12 23:57:49.572114	2025-02-12 01:24:15.487815	2025-02-12 23:57:49.571928	10803	<EMAIL>
6	<EMAIL>	2025-02-12 00:01:03.379705	<EMAIL>	2025-02-12 23:57:54.974239	2025-02-12 00:01:03.379005	2025-02-12 23:57:54.974029	26523	<EMAIL>
8	<EMAIL>	2025-02-12 00:14:06.022079	<EMAIL>	2025-02-12 23:58:41.025218	2025-02-12 00:14:06.020963	2025-02-12 23:58:41.025018	20653	<EMAIL>
13	<EMAIL>	2025-02-13 00:56:53.521564	<EMAIL>	2025-02-13 07:22:01.043768	2025-02-13 00:56:53.521309	2025-02-13 07:22:01.043507	23038	<EMAIL>
10	<EMAIL>	2025-02-13 00:00:19.581039	<EMAIL>	2025-02-13 23:58:11.094881	2025-02-13 00:00:19.580606	2025-02-13 23:58:11.094712	28950	<EMAIL>
32	<EMAIL>	2025-02-26 00:01:47.297237	<EMAIL>	2025-02-26 23:15:02.727552	2025-02-26 00:01:47.296819	2025-02-26 23:15:02.725877	25842	<EMAIL>
19	<EMAIL>	2025-02-16 02:20:09.236181	<EMAIL>	2025-02-16 23:52:16.951964	2025-02-16 02:20:09.23581	2025-02-16 23:52:16.951767	3605	<EMAIL>
20	<EMAIL>	2025-02-16 23:01:18.339694	<EMAIL>	2025-02-16 23:58:41.335577	2025-02-16 23:01:18.339489	2025-02-16 23:58:41.335359	3429	<EMAIL>
35	<EMAIL>	2025-02-27 02:56:35.319028	<EMAIL>	2025-02-27 23:58:10.086943	2025-02-27 02:56:35.318703	2025-02-27 23:58:10.086729	18437	<EMAIL>
17	<EMAIL>	2025-02-15 16:56:16.287906	<EMAIL>	2025-02-15 17:14:43.830478	2025-02-15 16:56:16.287623	2025-02-15 17:14:43.830188	1106	<EMAIL>
27	<EMAIL>	2025-02-20 00:01:51.675902	<EMAIL>	2025-02-20 23:58:50.235559	2025-02-20 00:01:51.675552	2025-02-20 23:58:50.235351	29031	<EMAIL>
21	<EMAIL>	2025-02-17 00:01:11.333446	<EMAIL>	2025-02-17 23:00:57.118691	2025-02-17 00:01:11.333191	2025-02-17 23:00:57.118482	25838	<EMAIL>
28	<EMAIL>	2025-02-21 00:01:20.241113	<EMAIL>	2025-02-21 00:01:20.241113	2025-02-21 00:01:20.240869	2025-02-21 00:01:20.240869	0	<EMAIL>
34	<EMAIL>	2025-02-27 01:21:44.661794	<EMAIL>	2025-02-27 02:39:38.571256	2025-02-27 01:21:44.661064	2025-02-27 02:39:38.571009	4660	<EMAIL>
25	<EMAIL>	2025-02-18 07:24:11.306629	<EMAIL>	2025-02-18 23:40:39.993683	2025-02-18 07:24:11.306374	2025-02-18 23:40:39.993451	853	<EMAIL>
33	<EMAIL>	2025-02-26 02:42:03.426414	<EMAIL>	2025-02-26 06:13:37.822138	2025-02-26 02:42:03.42591	2025-02-26 06:13:37.821914	12663	<EMAIL>
104	<EMAIL>	2025-03-03 00:50:25.484619	<EMAIL>	2025-03-03 08:07:47.310368	2025-03-03 00:50:25.483438	2025-03-03 08:07:47.310068	7034	<EMAIL>
31	<EMAIL>	2025-02-25 00:01:51.899982	<EMAIL>	2025-02-25 23:59:17.408008	2025-02-25 00:01:51.899405	2025-02-25 23:59:17.407765	11082	<EMAIL>
39	<EMAIL>	2025-03-01 14:43:08.840698	<EMAIL>	2025-03-01 14:46:01.306357	2025-03-01 14:43:08.838885	2025-03-01 14:46:01.306062	172	<EMAIL>
36	<EMAIL>	2025-02-28 00:00:40.099294	<EMAIL>	2025-02-28 02:55:39.51143	2025-02-28 00:00:40.098558	2025-02-28 02:55:39.511187	10458	<EMAIL>
52	<EMAIL>	2025-03-02 19:23:10.841355	<EMAIL>	2025-03-02 19:39:55.827871	2025-03-02 19:23:10.785851	2025-03-02 19:39:55.82657	1002	<EMAIL>
37	<EMAIL>	2025-02-28 00:06:13.136712	<EMAIL>	2025-02-28 07:18:04.757742	2025-02-28 00:06:13.135531	2025-02-28 07:18:04.757494	22316	<EMAIL>
38	<EMAIL>	2025-02-28 07:31:04.98047	<EMAIL>	2025-02-28 22:51:07.613969	2025-02-28 07:31:04.979988	2025-02-28 22:51:07.613732	590	<EMAIL>
102	<EMAIL>	2025-03-02 19:40:16.788729	<EMAIL>	2025-03-02 19:40:16.788729	2025-03-02 19:40:16.786038	2025-03-02 19:40:16.786038	0	<EMAIL>
107	<EMAIL>	2025-03-03 03:19:04.105617	<EMAIL>	2025-03-03 23:58:05.919534	2025-03-03 03:19:04.105063	2025-03-03 23:58:05.919186	16418	<EMAIL>
103	<EMAIL>	2025-03-03 00:49:30.061057	<EMAIL>	2025-03-03 23:59:32.797864	2025-03-03 00:49:30.059099	2025-03-03 23:59:32.797595	26288	<EMAIL>
105	<EMAIL>	2025-03-03 01:51:49.183164	<EMAIL>	2025-03-03 08:43:14.735294	2025-03-03 01:51:49.182216	2025-03-03 08:43:14.735031	11404	<EMAIL>
106	<EMAIL>	2025-03-03 03:10:17.458058	<EMAIL>	2025-03-03 23:57:36.432689	2025-03-03 03:10:17.457532	2025-03-03 23:57:36.432474	18159	<EMAIL>
109	<EMAIL>	2025-03-04 00:00:35.92162	<EMAIL>	2025-03-04 00:13:05.98371	2025-03-04 00:00:35.921173	2025-03-04 00:13:05.983505	748	<EMAIL>
111	<EMAIL>	2025-03-04 00:02:23.852445	<EMAIL>	2025-03-04 03:14:57.609579	2025-03-04 00:02:23.852142	2025-03-04 03:14:57.609324	6012	<EMAIL>
110	<EMAIL>	2025-03-04 00:02:02.791403	<EMAIL>	2025-03-04 03:29:32.470786	2025-03-04 00:02:02.790997	2025-03-04 03:29:32.470501	12401	<EMAIL>
108	<EMAIL>	2025-03-04 00:00:06.479792	<EMAIL>	2025-03-04 03:30:05.044233	2025-03-04 00:00:06.479292	2025-03-04 03:30:05.043842	10244	<EMAIL>
\.


--
-- Data for Name: user_authorities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_authorities (user_id, authorities) FROM stdin;
1	ADMIN
2	SALES
3	SALES
4	SALES
5	SALES
6	ADMIN
7	ADMIN
52	SALES
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, password, username) FROM stdin;
1	$2a$10$FVEENszg2gO8J.S0FQDHz.NXqCnzk3OzeQBpE4OZtahvGx5aWYQO2	<EMAIL>
2	$2a$10$cAUxKILw0WhW5bkMAqu2M.pZ93nrwRZkyTAMwMNm0hITWEFXeYs6u	<EMAIL>
3	$2a$10$OLij.cX3jRzz/qkfhGNgoOmWOowIamS0L3uAIGMJwbGdLcJSi/g2C	<EMAIL>
4	$2a$10$595Il.0oXE9KAcZwYb.iHOb1W6MORND9WBJ3VLoR/vQiFexfaSTxK	<EMAIL>
5	$2a$10$eGn///3kiDvNkso5SaoPGuqN0juips2vfgmAvVdkLGZCd6GI8OpDa	<EMAIL>
6	$2a$10$9Zzsv.VKrfnY8MNa/4AIpuT3dNMLWnuRrBOTGwnLG.1U/fy7jH9Ee	<EMAIL>
7	$2a$10$fq7t2610BET9mpkeRKQhFuqGFM3cOxAVYNMO.j/EQ3pw4aNZECaTW	<EMAIL>
52	$2a$10$siffTNgGGgWy2qEwep3fCuIZkxsM3EmL4UYz1OvmSk12AzeDxiTrC	<EMAIL>
\.


--
-- Data for Name: weekly_target; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.weekly_target (id, agent_username, kpi1, kpi2, weekly_targets_combined_id) FROM stdin;
1	<EMAIL>	13000	9000	1
2	<EMAIL>	17000	10500	1
3	<EMAIL>	8000	3000	1
4	<EMAIL>	19000	12000	1
5	<EMAIL>	12000	10000	2
6	<EMAIL>	14000	10000	2
7	<EMAIL>	4000	4000	2
8	<EMAIL>	15000	10000	2
9	<EMAIL>	1000	1	2
10	<EMAIL>	14000	10000	3
11	<EMAIL>	15000	15000	3
12	<EMAIL>	5000	5000	3
13	<EMAIL>	15000	15000	3
14	<EMAIL>	1000	1	3
15	<EMAIL>	14000	10000	4
16	<EMAIL>	15000	15000	4
17	<EMAIL>	7000	5000	4
18	<EMAIL>	17000	15000	4
19	<EMAIL>	1000	1000	4
20	<EMAIL>	15000	10000	5
21	<EMAIL>	13000	10000	5
22	<EMAIL>	6000	4000	5
23	<EMAIL>	15000	10000	5
24	<EMAIL>	1000	1000	5
\.


--
-- Data for Name: weekly_target_combined; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.weekly_target_combined (id, end_date, start_date, title) FROM stdin;
1	2025-02-16 18:00:00	2025-02-10 18:00:00	Week 2, February 2025
2	2025-03-09 18:00:00	2025-03-03 18:00:00	Week 1, March 2025
3	2025-03-16 18:00:00	2025-03-10 18:00:00	Week 2, March 2025
4	2025-03-23 18:00:00	2025-03-17 18:00:00	Week 3, March 2025
5	2025-03-30 18:00:00	2025-03-24 18:00:00	Week 4, March 2025
\.


--
-- Name: activities_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.activities_seq', 101, true);


--
-- Name: applications_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.applications_seq', 101, true);


--
-- Name: comments_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.comments_seq', 101, true);


--
-- Name: company_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.company_seq', 51, true);


--
-- Name: leads_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.leads_seq', 151, true);


--
-- Name: qualifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.qualifications_id_seq', 1, false);


--
-- Name: sales_agent_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.sales_agent_seq', 101, true);


--
-- Name: sold_qualifications_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.sold_qualifications_seq', 101, true);


--
-- Name: tracker_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.tracker_seq', 151, true);


--
-- Name: users_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_seq', 101, true);


--
-- Name: weekly_target_combined_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.weekly_target_combined_id_seq', 5, true);


--
-- Name: weekly_target_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.weekly_target_id_seq', 24, true);


--
-- Name: activities activities_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_pkey PRIMARY KEY (id);


--
-- Name: applications applications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.applications
    ADD CONSTRAINT applications_pkey PRIMARY KEY (id);


--
-- Name: comments comments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_pkey PRIMARY KEY (id);


--
-- Name: company company_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_pkey PRIMARY KEY (id);


--
-- Name: lead_sales_agent lead_sales_agent_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.lead_sales_agent
    ADD CONSTRAINT lead_sales_agent_pkey PRIMARY KEY (lead_id, sales_agent_id);


--
-- Name: leads leads_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.leads
    ADD CONSTRAINT leads_pkey PRIMARY KEY (id);


--
-- Name: qualifications qualifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.qualifications
    ADD CONSTRAINT qualifications_pkey PRIMARY KEY (id);


--
-- Name: sales_agent sales_agent_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sales_agent
    ADD CONSTRAINT sales_agent_pkey PRIMARY KEY (id);


--
-- Name: sold_qualifications sold_qualifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sold_qualifications
    ADD CONSTRAINT sold_qualifications_pkey PRIMARY KEY (id);


--
-- Name: tracker tracker_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tracker
    ADD CONSTRAINT tracker_pkey PRIMARY KEY (id);


--
-- Name: qualifications uk4lrsom7ss5q8iv8pqgqiivfj2; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.qualifications
    ADD CONSTRAINT uk4lrsom7ss5q8iv8pqgqiivfj2 UNIQUE (qualification_id);


--
-- Name: sales_agent ukcrrs5on9gsums6v6w6d1qvi1l; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sales_agent
    ADD CONSTRAINT ukcrrs5on9gsums6v6w6d1qvi1l UNIQUE (user_id);


--
-- Name: sales_agent ukfdhmbhdouk7babgx7noa64uc; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sales_agent
    ADD CONSTRAINT ukfdhmbhdouk7babgx7noa64uc UNIQUE (email_address);


--
-- Name: company ukg82ixrst2tc542u5s214ggpdf; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT ukg82ixrst2tc542u5s214ggpdf UNIQUE (user_id);


--
-- Name: company uknmqpablkawf3c2oaqvfhswhmk; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT uknmqpablkawf3c2oaqvfhswhmk UNIQUE (email_address);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: weekly_target_combined weekly_target_combined_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.weekly_target_combined
    ADD CONSTRAINT weekly_target_combined_pkey PRIMARY KEY (id);


--
-- Name: weekly_target weekly_target_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.weekly_target
    ADD CONSTRAINT weekly_target_pkey PRIMARY KEY (id);


--
-- Name: lead_sales_agent fk2b3mi00aqml3jve185y163g9o; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.lead_sales_agent
    ADD CONSTRAINT fk2b3mi00aqml3jve185y163g9o FOREIGN KEY (lead_id) REFERENCES public.leads(id);


--
-- Name: activities fkf7dwdqdh5opaejcemm2r16t0m; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.activities
    ADD CONSTRAINT fkf7dwdqdh5opaejcemm2r16t0m FOREIGN KEY (lead_id) REFERENCES public.leads(id);


--
-- Name: weekly_target fkg7nr2u4pacqolm95hbwchmcuc; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.weekly_target
    ADD CONSTRAINT fkg7nr2u4pacqolm95hbwchmcuc FOREIGN KEY (weekly_targets_combined_id) REFERENCES public.weekly_target_combined(id);


--
-- Name: user_authorities fkhiiib540jf74gksgb87oofni; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_authorities
    ADD CONSTRAINT fkhiiib540jf74gksgb87oofni FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: sales_agent fkidlr38g7b64gh63nr8eud92j4; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sales_agent
    ADD CONSTRAINT fkidlr38g7b64gh63nr8eud92j4 FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: comments fklyn46rm9594y7bphtcjpnn1qc; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT fklyn46rm9594y7bphtcjpnn1qc FOREIGN KEY (lead_id) REFERENCES public.leads(id);


--
-- Name: sold_qualifications fknfvppioopdnjs0ojptesns6a; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sold_qualifications
    ADD CONSTRAINT fknfvppioopdnjs0ojptesns6a FOREIGN KEY (qualification_id) REFERENCES public.qualifications(id);


--
-- Name: sold_qualifications fkp07mcddqiylpkw27a2g416pa; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sold_qualifications
    ADD CONSTRAINT fkp07mcddqiylpkw27a2g416pa FOREIGN KEY (application_id) REFERENCES public.applications(id);


--
-- Name: lead_sales_agent fkre056e1krsd0vmbq5mikb5h1s; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.lead_sales_agent
    ADD CONSTRAINT fkre056e1krsd0vmbq5mikb5h1s FOREIGN KEY (sales_agent_id) REFERENCES public.sales_agent(id);


--
-- Name: company fksxe9t9istcdt2mtdbvgh83a9g; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT fksxe9t9istcdt2mtdbvgh83a9g FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

