<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b0ab5e6f-9dfb-456e-90dd-d101d75816c1" name="Changes" comment="addded source of truth">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/core/models/entities/Qualification.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/core/models/entities/Qualification.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/controller/CompanyController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/controller/CompanyController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/controller/GenericController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/controller/GenericController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/controller/SourceOfTrueController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/controller/SourceOfTrueController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/models/CommentDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/models/CommentDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/repositories/LeadsRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/repositories/LeadsRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/repositories/TrueXeroInBankRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/repositories/TrueXeroInBankRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/repositories/TrueXeroInvoiceRespository.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/repositories/TrueXeroInvoiceRespository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/CompanyServices.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/CompanyServices.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/DashboardService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/DashboardService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/LeadsService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/LeadsService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/QualificationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/QualificationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/RTOServices.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/RTOServices.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/SourceOfTruthServices.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/company/services/SourceOfTruthServices.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/sales/controller/SalesController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/sales/controller/SalesController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/sales/services/SalesServices.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/skillsync/applyr/modules/sales/services/SalesServices.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../before_deployment_backup.custom" beforeDir="false" afterPath="$PROJECT_DIR$/../before_deployment_backup.custom" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/.env" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/components/modal/MultiStepCreateApplicationModal.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/components/modal/MultiStepCreateApplicationModal.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/components/modal/SingleLeadModal.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/components/modal/SingleLeadModal.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/components/sidemenu/SideMenuAdmin.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/components/sidemenu/SideMenuAdmin.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/components/sidemenu/SideMenuAgent.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/components/sidemenu/SideMenuAgent.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/main.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/main.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/admin/applications/ApplicationProfile.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/admin/applications/ApplicationProfile.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/admin/applications/ApplicationsScreen.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/admin/applications/ApplicationsScreen.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/admin/dashboard/AdminDashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/admin/dashboard/AdminDashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/admin/leads/LeadProfile.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/admin/leads/LeadProfile.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/admin/leads/leads.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/admin/leads/leads.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/admin/sourceoftruth/dashboard/SourceOfTruthDashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/admin/sourceoftruth/dashboard/SourceOfTruthDashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/admin/troubleshoot/TroubleShoot.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/admin/troubleshoot/TroubleShoot.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/admin/troubleshoot/TroubleTable.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/admin/troubleshoot/TroubleTable.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/agent/AgentApplications.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/agent/AgentApplications.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/agent/AgentDashboard.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/agent/AgentDashboard.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/agent/AgentLeadProfile.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/agent/AgentLeadProfile.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/agent/AgentLeads.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/agent/AgentLeads.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/agent/TroubleShootAgent.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/agent/TroubleShootAgent.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/pages/agent/troubleshoot/TroubleTable.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/pages/agent/troubleshoot/TroubleTable.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/routes.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/routes.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/services/AdminAPIService.js" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/services/AdminAPIService.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/services/CompanyAPIService.js" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/services/CompanyAPIService.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front-end/src/services/SourceOfTruthApiService.js" beforeDir="false" afterPath="$PROJECT_DIR$/../front-end/src/services/SourceOfTruthApiService.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <excluded-from-favorite>
      <branch-storage>
        <map>
          <entry type="REMOTE">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$/.." source="origin/master" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </excluded-from-favorite>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/.." value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2u7HHKgrsUMzotZTxsvwCRKj4wL" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ApplyrApplication.executor&quot;: &quot;Run&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.skillsync.applyr.modules.company.services" />
      <recent name="com.skillsync.applyr.modules.company.controller" />
      <recent name="com.skillsync.applyr.core.models.entities" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="ApplyrApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="applyr" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.skillsync.applyr.ApplyrApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.21565.193" />
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-IU-243.21565.193" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b0ab5e6f-9dfb-456e-90dd-d101d75816c1" name="Changes" comment="" />
      <created>1741592604497</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741592604497</updated>
      <workItem from="1741592606192" duration="7213000" />
      <workItem from="1741628080142" duration="4018000" />
      <workItem from="1741707414400" duration="9218000" />
      <workItem from="1741837176290" duration="1651000" />
      <workItem from="1741848331089" duration="33000" />
      <workItem from="1742016463257" duration="1276000" />
      <workItem from="1742438785568" duration="5784000" />
      <workItem from="1742487753621" duration="2299000" />
      <workItem from="1742632511240" duration="1278000" />
      <workItem from="1742713360118" duration="4426000" />
      <workItem from="1742792921082" duration="1550000" />
      <workItem from="1742956144736" duration="1887000" />
      <workItem from="1743053913153" duration="6872000" />
      <workItem from="1743231677381" duration="4846000" />
      <workItem from="1743440690705" duration="7833000" />
      <workItem from="1743570616979" duration="1150000" />
      <workItem from="1744008232107" duration="1399000" />
      <workItem from="1744080073005" duration="12666000" />
      <workItem from="1744229687394" duration="621000" />
      <workItem from="1744314059606" duration="7213000" />
      <workItem from="1744458698218" duration="1000" />
      <workItem from="1744460306462" duration="1479000" />
      <workItem from="1744476406381" duration="5185000" />
      <workItem from="1744517114971" duration="613000" />
      <workItem from="1744530684576" duration="4635000" />
      <workItem from="1744554349399" duration="1121000" />
      <workItem from="1744555879606" duration="73000" />
      <workItem from="1744555971862" duration="23658000" />
      <workItem from="1744633505487" duration="18955000" />
      <workItem from="1744684414952" duration="9099000" />
      <workItem from="1744781954061" duration="607000" />
      <workItem from="1744860394839" duration="2919000" />
      <workItem from="1745291179628" duration="2379000" />
      <workItem from="1745380607837" duration="3497000" />
      <workItem from="1745808254655" duration="19000" />
      <workItem from="1745808293607" duration="2303000" />
      <workItem from="1746072768042" duration="1957000" />
      <workItem from="1746162332273" duration="622000" />
      <workItem from="1746385168545" duration="5936000" />
      <workItem from="1746478325461" duration="1224000" />
      <workItem from="1746591482940" duration="2518000" />
      <workItem from="1746648894602" duration="567000" />
      <workItem from="1746670860898" duration="1655000" />
      <workItem from="1746719201790" duration="2000" />
      <workItem from="1746987298861" duration="2860000" />
      <workItem from="1747015357736" duration="2646000" />
      <workItem from="1747662292670" duration="3563000" />
      <workItem from="1747681618218" duration="1271000" />
      <workItem from="1747718720653" duration="7430000" />
      <workItem from="1747755956355" duration="749000" />
      <workItem from="1747756723034" duration="4440000" />
      <workItem from="1747803921183" duration="4869000" />
      <workItem from="1747878086248" duration="4156000" />
      <workItem from="1747900223433" duration="8957000" />
      <workItem from="1747988241673" duration="1537000" />
      <workItem from="1748235672210" duration="1328000" />
      <workItem from="1748253176934" duration="45000" />
      <workItem from="1748402428026" duration="9671000" />
      <workItem from="1748801094750" duration="6507000" />
      <workItem from="1748829113951" duration="11888000" />
      <workItem from="1749051556125" duration="7009000" />
      <workItem from="1749134201099" duration="5680000" />
      <workItem from="1749180434251" duration="1300000" />
      <workItem from="1749404025623" duration="1784000" />
      <workItem from="1749443830173" duration="5489000" />
      <workItem from="1749494225854" duration="4465000" />
      <workItem from="1749619620939" duration="4104000" />
      <workItem from="1749632838236" duration="2544000" />
      <workItem from="1749656201832" duration="2794000" />
      <workItem from="1749706677674" duration="2142000" />
      <workItem from="1750129941296" duration="3961000" />
      <workItem from="1750186222826" duration="627000" />
      <workItem from="1750265738404" duration="1076000" />
      <workItem from="1750348347299" duration="2827000" />
    </task>
    <task id="LOCAL-00001" summary="Added a lot of things">
      <option name="closed" value="true" />
      <created>1741712316863</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1741712316863</updated>
    </task>
    <task id="LOCAL-00002" summary="Added a lot of things 2">
      <option name="closed" value="true" />
      <created>1743441070170</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1743441070171</updated>
    </task>
    <task id="LOCAL-00003" summary="Added a lot of things 3">
      <option name="closed" value="true" />
      <created>1744314157053</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1744314157053</updated>
    </task>
    <task id="LOCAL-00004" summary="revamp of Admin Panel whole. Design">
      <option name="closed" value="true" />
      <created>1744544898109</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1744544898109</updated>
    </task>
    <task id="LOCAL-00005" summary="Made design changes to Xero Intregation and admin dashboard">
      <option name="closed" value="true" />
      <created>1744555613795</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1744555613795</updated>
    </task>
    <task id="LOCAL-00006" summary="Added Xero KPI 1 Import">
      <option name="closed" value="true" />
      <created>1744566174748</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1744566174748</updated>
    </task>
    <task id="LOCAL-00007" summary="Completed Xero integration via import">
      <option name="closed" value="true" />
      <created>1744572016780</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1744572016780</updated>
    </task>
    <task id="LOCAL-00008" summary="Completed File Status but ned updates">
      <option name="closed" value="true" />
      <created>1744697442486</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1744697442486</updated>
    </task>
    <task id="LOCAL-00009" summary="addded a lot">
      <option name="closed" value="true" />
      <created>1746987882099</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746987882099</updated>
    </task>
    <task id="LOCAL-00010" summary="addded source of truth">
      <option name="closed" value="true" />
      <created>1747906418758</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1747906418758</updated>
    </task>
    <option name="localTasksCounter" value="11" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="main" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Added a lot of things" />
    <MESSAGE value="Added a lot of things 2" />
    <MESSAGE value="Added a lot of things 3" />
    <MESSAGE value="Added a lot of things 4" />
    <MESSAGE value="revamp of Admin Panel whole. Design" />
    <MESSAGE value="Made design changes to Xero Intregation and admin dashboard" />
    <MESSAGE value="Added Xero KPI 1 Import" />
    <MESSAGE value="Completed Xero integration via import" />
    <MESSAGE value="Completed File Status but ned updates" />
    <MESSAGE value="addded a lot" />
    <MESSAGE value="addded source of truth" />
    <option name="LAST_COMMIT_MESSAGE" value="addded source of truth" />
  </component>
</project>