import React, { useState, useEffect } from "react";
import { useEditQualificationMutation } from "../../services/AdminAPIService";

const EditQualificationModal = ({
  isOpen,
  onClose,
  qualification,
  onSuccess
}) => {
  const [formData, setFormData] = useState({
    qualificationId: "",
    qualificationName: "",
    rplPrice: 0,
    rtoPriceHigh: 0,
    enrollmentPrice: 0,
    offshorePrice: 0,
    notes: "",
    type: "",
    processingTime: "",
    demand: "",
    checklist: []
  });

  const [editQualification, { isLoading }] = useEditQualificationMutation();

  // Populate form with qualification data when modal is opened
  useEffect(() => {
    if (qualification) {
      setFormData({
        qualificationId: qualification.qualificationId || "",
        qualificationName: qualification.qualificationName || "",
        rplPrice: qualification.rplPrice || 0,
        rtoPriceHigh: qualification.rtoPriceHigh || 0,
        enrollmentPrice: qualification.enrollmentPrice || 0,
        offshorePrice: qualification.offshorePrice || 0,
        notes: qualification.notes || "",
        type: qualification.type || "",
        processingTime: qualification.processingTime || "",
        demand: qualification.demand || "",
        checklist: qualification.checklist || []
      });
    }
  }, [qualification]);

  if (!isOpen) return null;

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Convert numeric fields from strings if necessary
      const payload = {
        ...formData,
        rplPrice: parseFloat(formData.rplPrice),
        enrollmentPrice: parseFloat(formData.enrollmentPrice),
        offshorePrice: parseFloat(formData.offshorePrice)
      };

      await editQualification(payload).unwrap();
      
      // Call onSuccess callback if provided
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess();
      }
      
      onClose();
    } catch (error) {
      console.error("Error editing qualification:", error);
      // You might show an error toast or message here
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black opacity-50"
        onClick={onClose}
      ></div>
      {/* Modal content */}
      <div className="bg-white rounded-lg shadow-xl z-10 p-6 w-full max-w-xl relative">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">Edit Qualification</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label
              htmlFor="qualificationId"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Qualification ID
            </label>
            <input
              type="text"
              id="qualificationId"
              name="qualificationId"
              value={formData.qualificationId}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              required
              readOnly // ID should not be editable
            />
          </div>

          <div>
            <label
              htmlFor="qualificationName"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Qualification Name
            </label>
            <input
              type="text"
              id="qualificationName"
              name="qualificationName"
              value={formData.qualificationName}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              required
            />
          </div>

          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
              Type
            </label>
            <input
              type="text"
              id="type"
              name="type"
              value={formData.type}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              required
            />
          </div>

          <div>
            <label htmlFor="rplPrice" className="block text-sm font-medium text-gray-700 mb-2">
              RPL Low Price
            </label>
            <input
              type="number"
              step="0.01"
              id="rplPrice"
              name="rplPrice"
              value={formData.rplPrice}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
          </div>

          <div>
            <label htmlFor="rtoPriceHigh" className="block text-sm font-medium text-gray-700 mb-2">
              RPL High Price
            </label>
            <input
              type="number"
              step="0.01"
              id="rtoPriceHigh"
              name="rtoPriceHigh"
              value={formData.rtoPriceHigh}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
          </div>

          <div>
            <label
              htmlFor="enrollmentPrice"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Enrollment Price
            </label>
            <input
              type="number"
              step="0.01"
              id="enrollmentPrice"
              name="enrollmentPrice"
              value={formData.enrollmentPrice}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
          </div>

          <div>
            <label
              htmlFor="offshorePrice"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Offshore Price
            </label>
            <input
              type="number"
              step="0.01"
              id="offshorePrice"
              name="offshorePrice"
              value={formData.offshorePrice}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            />
          </div>

          <div>
            <label htmlFor="processingTime" className="block text-sm font-medium text-gray-700 mb-2">
              Processing Time
            </label>
            <input
              type="text"
              id="processingTime"
              name="processingTime"
              value={formData.processingTime}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              placeholder="e.g., 6-8 weeks"
            />
          </div>

          <div>
            <label htmlFor="demand" className="block text-sm font-medium text-gray-700 mb-2">
              Demand
            </label>
            <input
              type="text"
              id="demand"
              name="demand"
              value={formData.demand}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              placeholder="e.g., High, Medium, Low"
            />
          </div>

          <div className="col-span-1 md:col-span-2">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              className="border border-gray-200 rounded-lg w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              rows="3"
            />
          </div>

          <div className="col-span-1 md:col-span-2 flex justify-end gap-3 mt-4">
            <button
              type="button"
              onClick={onClose}
              className="border border-gray-300 text-gray-700 px-5 py-2.5 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="bg-[#6E39CB] text-white px-5 py-2.5 rounded-lg hover:bg-[#5E2CB8] transition-colors flex items-center"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditQualificationModal;
