package com.skillsync.applyr.modules.company.models;

import com.skillsync.applyr.core.models.enums.CommissionPaymentStatus;
import com.skillsync.applyr.core.models.enums.FileStatus;
import com.skillsync.applyr.core.models.enums.RTOPaymentStatus;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FileStatusDTO {
    private ApplicationResponseDTO application;
    private String fileSource;
    private String visaStatus;

    private String qualificationCode;
    private String qualificationName;

    private FileStatus fileStatus;

    private RTODataDTO rtoData;
    private double rtoCharge;
    private RTOPaymentStatus rtoPaymentStatus;
    private boolean lodgedToRTO;
    private LocalDateTime lodgedDate;
    private LocalDateTime rtoPaymentDate;

    private String hardCopyTrackingNumber;

    private LocalDateTime documentReceivedDate;
    private LocalDateTime softCopyReceivedDate;
    private LocalDateTime softCopyReleasedDate;
    private LocalDateTime hardCopyReceivedDate;
    private LocalDateTime hardCopyMailedDate;

    private String externalCommissionName;
    private String externalCommissionContactInfo;
    private double commissionAmount;
    private CommissionPaymentStatus commissionPaymentStatus;
    private LocalDateTime commissionPaymentDate;


}
