package com.skillsync.applyr.modules.sales.models;

import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.models.entities.SoldQualifications;
import com.skillsync.applyr.modules.company.models.SoldQualificationDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationDTO {
    private String fullName;
    private String applicationId;
    private String applicationStatus;
    private List<SoldQualificationDTO> qualificationName;
    private String createdAt;


    public ApplicationDTO(Application application) {
        this.fullName = application.getApplicantName();
        this.applicationId = application.getApplicationId();
        this.applicationStatus = application.getStatus().toString();
        this.qualificationName = new ArrayList<>();
        this.createdAt = application.getCreatedDate().toString();

        for(SoldQualifications soldQualification : application.getSoldQualificationsList()) {
            SoldQualificationDTO soldQualificationDTO = new SoldQualificationDTO();
            soldQualificationDTO.setQualificationId(soldQualification.getQualification().getQualificationId());
            soldQualificationDTO.setQualificationName(soldQualification.getQualification().getQualificationName());
            soldQualificationDTO.setPrice(soldQualification.getSoldPrice());
            this.qualificationName.add(soldQualificationDTO);
        }
    }
}
