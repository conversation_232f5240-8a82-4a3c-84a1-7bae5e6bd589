package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.models.entities.Application;
import com.skillsync.applyr.core.models.entities.PaymentInstallment;
import com.skillsync.applyr.core.response.SuccessResponse;

import com.skillsync.applyr.modules.company.repositories.ApplicationRepository;
import com.skillsync.applyr.modules.sales.repositories.PaymentInstallmentRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MigrationService {

    private final ApplicationRepository applicationRepository;
    private final PaymentInstallmentRepository paidInstallmentRepository;

    public MigrationService(ApplicationRepository applicationRepository,
                            PaymentInstallmentRepository paidInstallmentRepository) {
        this.applicationRepository = applicationRepository;
        this.paidInstallmentRepository = paidInstallmentRepository;
    }

    public SuccessResponse migrate() {
        List<Application> applications = applicationRepository.findAll();
        for (Application application : applications) {
            if (application.getPaidAmount() > 0) {
                PaymentInstallment installment = new PaymentInstallment();
                installment.setAmount(application.getPaidAmount());
                installment.setApplication(application);
                installment = paidInstallmentRepository.save(installment);
                installment.setCreatedDate(application.getLastModifiedDate());
                paidInstallmentRepository.save(installment);
                application.getInstallments().add(installment);
                applicationRepository.save(application);
            }
        }
        return new SuccessResponse("Successfully migrated");
    }
}
