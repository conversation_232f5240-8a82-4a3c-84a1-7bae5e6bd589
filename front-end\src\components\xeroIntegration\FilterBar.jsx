import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faFilter, faCalendarAlt } from "@fortawesome/free-solid-svg-icons";

const FilterBar = ({
  filter,
  setFilter,
  selectedStatusFilter,
  setSelectedStatusFilter,
  selectedPaymentStatusFilter,
  setSelectedPaymentStatusFilter,
  dateFilterType,
  setDateFilterType,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  statusOptions,
  invoiceSentOptions,
  tabType
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-5 mb-6 border border-[#DBDCDE]">
      <div className="flex flex-col md:flex-row gap-4 flex-wrap">
        {/* Search */}
        <div className="relative md:w-1/3">
          <input
            type="text"
            placeholder="Search..."
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="w-full h-11 rounded-lg border border-[#DBDCDE] pl-10 pr-4 text-sm focus:border-[#6E39CB] focus:outline-none"
          />
          <FontAwesomeIcon
            icon={faSearch}
            className="absolute left-3 top-3.5 text-[#89868D]"
          />
        </div>

        <div className="flex flex-wrap gap-4 md:ml-auto">
          {/* Status Filter */}
          <div className="relative">
            <select
              value={selectedStatusFilter}
              onChange={(e) => setSelectedStatusFilter(e.target.value)}
              className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
            >
              <option value="">All Statuses</option>
              {statusOptions.map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </select>
            <FontAwesomeIcon
              icon={faFilter}
              className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none"
            />
          </div>

          {/* Payment Status Filter - Only for KPI1 */}
          {tabType === "KPI1" && (
            <div className="relative">
              <select
                value={selectedPaymentStatusFilter}
                onChange={(e) => setSelectedPaymentStatusFilter(e.target.value)}
                className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
              >
                <option value="">All Payment Status</option>
                {invoiceSentOptions.map((status) => (
                  <option key={status} value={status}>
                    {status}
                  </option>
                ))}
              </select>
              <FontAwesomeIcon
                icon={faFilter}
                className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none"
              />
            </div>
          )}

          {/* Date Filter */}
          <div className="relative">
            <select
              value={dateFilterType}
              onChange={(e) => setDateFilterType(e.target.value)}
              className="h-11 appearance-none rounded-lg border border-[#DBDCDE] bg-white pl-4 pr-10 text-sm focus:border-[#6E39CB] focus:outline-none"
            >
              <option value="thisWeek">This Week</option>
              <option value="lastWeek">Last Week</option>
              <option value="thisMonth">This Month</option>
              <option value="custom">Custom Date</option>
              <option value="all">All Dates</option>
            </select>
            <FontAwesomeIcon
              icon={faCalendarAlt}
              className="absolute right-3 top-3.5 text-[#89868D] pointer-events-none"
            />
          </div>
        </div>
      </div>

      {/* Custom Date Range */}
      {dateFilterType === "custom" && (
        <div className="flex items-center gap-2 mt-4">
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="h-11 rounded-lg border border-[#DBDCDE] px-4 text-sm focus:border-[#6E39CB] focus:outline-none"
          />
          <span className="text-[#89868D]">to</span>
          <input
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            className="h-11 rounded-lg border border-[#DBDCDE] px-4 text-sm focus:border-[#6E39CB] focus:outline-none"
          />
        </div>
      )}
    </div>
  );
};

export default FilterBar;
