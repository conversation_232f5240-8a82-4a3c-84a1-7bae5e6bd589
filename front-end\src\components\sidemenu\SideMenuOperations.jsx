import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faHouse,
  faFileInvoice,
  faFileExcel,
  faClipboardList,
  faGear,
  faSignOutAlt,
  faChevronLeft,
  faChevronRight,
  faBars,
  faAngleDown,
  faAngleRight,
  faFileInvoiceDollar,
  faFileContract,
  faFolderOpen,
  faBuilding,
  faSearchPlus,
  faSearchMinus,
  faCertificate
} from "@fortawesome/free-solid-svg-icons";
import { NavLink, useNavigate, useLocation } from "react-router-dom";
import { getToken, removeToken } from "../../services/LocalStorageService";
import logo from "../../assets/Logo.png";
import { useGetProfileQuery } from "../../services/AgentAPIService";
import routes from "../../routes";

const SideMenuOperations = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1); // Default zoom level
  const [expandedCategories, setExpandedCategories] = useState({
    Main: true,
    Operations: true,
    System: true
  });
  const navigate = useNavigate();
  const location = useLocation();
  const token = getToken();
  const { data: userData } = useGetProfileQuery(token);

  // Zoom levels: 0.8 (small), 1 (normal), 1.2 (large)
  const zoomLevels = [0.8, 1, 1.2];

  // Function to handle zoom in
  const handleZoomIn = () => {
    setZoomLevel(prevZoom => {
      const currentIndex = zoomLevels.indexOf(prevZoom);
      const nextIndex = Math.min(currentIndex + 1, zoomLevels.length - 1);
      const newZoom = zoomLevels[nextIndex];

      // Apply zoom to document root
      document.documentElement.style.fontSize = `${newZoom * 100}%`;

      return newZoom;
    });
  };

  // Function to handle zoom out
  const handleZoomOut = () => {
    setZoomLevel(prevZoom => {
      const currentIndex = zoomLevels.indexOf(prevZoom);
      const nextIndex = Math.max(currentIndex - 1, 0);
      const newZoom = zoomLevels[nextIndex];

      // Apply zoom to document root
      document.documentElement.style.fontSize = `${newZoom * 100}%`;

      return newZoom;
    });
  };

  // Close mobile menu when route changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  // Initialize zoom level from localStorage or set default
  useEffect(() => {
    const savedZoom = parseFloat(localStorage.getItem('appZoomLevel'));
    if (savedZoom && zoomLevels.includes(savedZoom)) {
      setZoomLevel(savedZoom);
      document.documentElement.style.fontSize = `${savedZoom * 100}%`;
    } else {
      // Set default zoom
      document.documentElement.style.fontSize = `${zoomLevel * 100}%`;
    }
  }, []);

  // Save zoom level to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('appZoomLevel', zoomLevel.toString());
  }, [zoomLevel]);

  // Check if screen is mobile and set collapsed state accordingly
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setCollapsed(true);
      }
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = () => {
    removeToken("token");
    navigate("/");
  };

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const toggleCategory = (category) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Navigation items grouped by category
  const navItems = [
    {
      category: "Main",
      items: [
        { name: "Dashboard", icon: faHouse, path: "/operations/dashboard" },
      ],
    },
    {
      category: "Operations",
      items: [
        { name: "Quote & Invoice Requests", icon: faFileInvoiceDollar, path: "/operations/quoterequests" },
        { name: "Drafted Quotes & Invoices", icon: faFileContract, path: "/operations/draftedquotes" },
        { name: "Applications", icon: faFolderOpen, path: "/operations/applications" },
        { name: "File Status", icon: faFileExcel, path: "/operations/filestatus" },
        { name: "Qualifications", icon: faCertificate, path: "/operations/qualifications" },
        { name: "RTO Management", icon: faBuilding, path: "/operations/rto" },
        { name: "External Commissions", icon: faFileInvoiceDollar, path: "/operations/commissions" },
      ],
    },
    {
      category: "System",
      items: [
        { name: "Settings", icon: faGear, path: "/operations/settings" },
        { name: "Log out", icon: faSignOutAlt, path: null, onClick: handleLogout, className: "text-red-500" },
      ],
    },
  ];

  // Check if a nav item is active
  const isItemActive = (path) => {
    if (!path) return false;
    return location.pathname === path;
  };

  // Render a navigation item
  const renderNavItem = (item, sectionExpanded) => {
    const isActive = isItemActive(item.path);

    // If section is not expanded and sidebar is not collapsed, don't render
    if (!sectionExpanded && !collapsed) return null;

    const content = (
      <div className="flex items-center w-full">
        <div className={`flex items-center justify-center ${collapsed ? 'w-full' : 'w-8'} h-8 ${isActive ? 'text-[#6E39CB]' : 'text-gray-600'}`}>
          <FontAwesomeIcon
            icon={item.icon}
            className={`${item.className || ''} ${isActive ? 'text-[#6E39CB]' : ''} w-5 h-5`}
          />
        </div>
        {!collapsed && (
          <span className={`ml-3 text-sm font-medium whitespace-nowrap transition-all duration-200 ${isActive ? 'text-[#6E39CB]' : 'text-gray-700'}`}>
            {item.name}
          </span>
        )}
      </div>
    );

    if (item.onClick) {
      return (
        <button
          key={item.name}
          onClick={item.onClick}
          className={`flex items-center w-full rounded-md ${collapsed ? 'justify-center p-2' : 'p-2'}
            ${isActive ? 'bg-[#F4F5F9]' : 'hover:bg-[#F4F5F9]'} transition-all duration-200`}
          title={collapsed ? item.name : ""}
        >
          {content}
        </button>
      );
    }

    return (
      <NavLink
        key={item.name}
        to={item.path}
        className={`flex items-center w-full rounded-md ${collapsed ? 'justify-center p-2' : 'p-2'}
          ${isActive ? 'bg-[#F4F5F9]' : 'hover:bg-[#F4F5F9]'} transition-all duration-200`}
        title={collapsed ? item.name : ""}
      >
        {content}
      </NavLink>
    );
  };

  return (
    <>
      {/* Mobile menu overlay */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Mobile menu toggle button */}
      <button
        className="md:hidden fixed top-4 left-4 z-30 p-2 rounded-md bg-white shadow-md text-gray-700"
        onClick={toggleMobileMenu}
      >
        <FontAwesomeIcon icon={faBars} className="w-5 h-5" />
      </button>

      {/* Sidebar */}
      <div
        className={`${
          mobileMenuOpen ? "translate-x-0" : "-translate-x-full"
        } md:translate-x-0 fixed md:sticky top-0 left-0 z-20 flex flex-col h-screen ${
          collapsed ? "w-16" : "w-64"
        } bg-white border-r border-gray-100 shadow-md transition-all duration-300 ease-in-out`}
      >
        {/* Logo and toggle button */}
        <div className="relative flex items-center justify-between py-5 px-4 border-b border-gray-100">
          {!collapsed && (
            <div className="flex items-center">
              <img src={logo} alt="Logo" className="h-6 w-auto object-contain" />
            </div>
          )}
          {collapsed && (
            <div className="flex flex-col justify-center items-center w-full">
              <img src={logo} alt="Logo" className="h-8 w-auto object-contain" />

              {/* Zoom controls for collapsed mode */}
              <div className="flex items-center space-x-1 mt-2">
                <button
                  onClick={handleZoomOut}
                  disabled={zoomLevel === zoomLevels[0]}
                  className={`flex items-center justify-center w-6 h-6 rounded-full bg-white border border-gray-200 shadow-sm focus:outline-none ${
                    zoomLevel === zoomLevels[0] ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-[#6E39CB]'
                  }`}
                  title="Zoom out"
                >
                  <FontAwesomeIcon icon={faSearchMinus} className="w-3 h-3" />
                </button>
                <button
                  onClick={handleZoomIn}
                  disabled={zoomLevel === zoomLevels[zoomLevels.length - 1]}
                  className={`flex items-center justify-center w-6 h-6 rounded-full bg-white border border-gray-200 shadow-sm focus:outline-none ${
                    zoomLevel === zoomLevels[zoomLevels.length - 1] ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-[#6E39CB]'
                  }`}
                  title="Zoom in"
                >
                  <FontAwesomeIcon icon={faSearchPlus} className="w-3 h-3" />
                </button>
              </div>
            </div>
          )}

          {/* Toggle button - positioned absolutely to avoid affecting layout */}
          <button
            onClick={toggleSidebar}
            className={`absolute -right-3 top-5 flex items-center justify-center w-6 h-6 rounded-full bg-white border border-gray-200 shadow-sm text-gray-500 hover:text-[#6E39CB] focus:outline-none hidden md:flex`}
          >
            <FontAwesomeIcon icon={collapsed ? faChevronRight : faChevronLeft} className="w-3 h-3" />
          </button>

          {/* Zoom controls - only show when not collapsed */}
          {!collapsed && (
            <div className="flex items-center space-x-1">
              <button
                onClick={handleZoomOut}
                disabled={zoomLevel === zoomLevels[0]}
                className={`flex items-center justify-center w-6 h-6 rounded-full bg-white border border-gray-200 shadow-sm focus:outline-none ${
                  zoomLevel === zoomLevels[0] ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-[#6E39CB]'
                }`}
                title="Zoom out"
              >
                <FontAwesomeIcon icon={faSearchMinus} className="w-3 h-3" />
              </button>
              <button
                onClick={handleZoomIn}
                disabled={zoomLevel === zoomLevels[zoomLevels.length - 1]}
                className={`flex items-center justify-center w-6 h-6 rounded-full bg-white border border-gray-200 shadow-sm focus:outline-none ${
                  zoomLevel === zoomLevels[zoomLevels.length - 1] ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-[#6E39CB]'
                }`}
                title="Zoom in"
              >
                <FontAwesomeIcon icon={faSearchPlus} className="w-3 h-3" />
              </button>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex-grow flex flex-col py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
          {navItems.map((section) => {
            const isExpanded = expandedCategories[section.category];
            const hasActiveItem = section.items.some(item => isItemActive(item.path));

            return (
              <div key={section.category} className="mb-2 px-3">
                {!collapsed && (
                  <div
                    className="flex items-center justify-between py-2 cursor-pointer group"
                    onClick={() => toggleCategory(section.category)}
                  >
                    <h2 className="text-xs uppercase font-semibold text-gray-400 tracking-wider group-hover:text-gray-600">
                      {section.category}
                    </h2>
                    <FontAwesomeIcon
                      icon={isExpanded ? faAngleDown : faAngleRight}
                      className="w-3 h-3 text-gray-400 group-hover:text-gray-600"
                    />
                  </div>
                )}
                {collapsed && (
                  <div className="flex justify-center">
                    <div className={`w-8 h-0.5 ${hasActiveItem ? 'bg-[#6E39CB]' : 'bg-gray-200'} my-2`}></div>
                  </div>
                )}
                <div className={`space-y-1 ${!isExpanded && !collapsed ? 'hidden' : ''}`}>
                  {section.items.map((item) => renderNavItem(item, isExpanded))}
                </div>
              </div>
            );
          })}
        </div>

        {/* User profile */}
        {userData && !collapsed && (
          <div className="p-4 border-t border-gray-100 flex items-center">
            <div className="w-8 h-8 rounded-full bg-[#6E39CB] flex items-center justify-center text-white font-medium">
              {userData.fullName?.charAt(0) || "U"}
            </div>
            <div className="ml-3 overflow-hidden">
              <p className="text-sm font-medium text-gray-800 truncate">
                {userData.fullName || "User"}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {userData.email || ""}
              </p>
            </div>
            <button className="ml-auto p-1 text-gray-400 hover:text-gray-600">
              <FontAwesomeIcon icon={faGear} className="w-4 h-4" />
            </button>
          </div>
        )}

        {/* Collapsed user profile */}
        {userData && collapsed && (
          <div className="p-2 border-t border-gray-100 flex justify-center">
            <div className="w-8 h-8 rounded-full bg-[#6E39CB] flex items-center justify-center text-white font-medium" title={userData.fullName || "User"}>
              {userData.fullName?.charAt(0) || "U"}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default SideMenuOperations;
