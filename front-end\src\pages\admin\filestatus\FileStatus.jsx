import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faFilter, faSearch, faTable, faThLarge } from "@fortawesome/free-solid-svg-icons";
import { useGetAllApplicationFilesQuery } from "../../../services/CompanyAPIService";
import FileStatusCard from "../../../components/card/FileStatusCard";
import routes from "../../../routes";

const FileStatus = () => {
  const navigate = useNavigate();
  const { data: fileStatusData = [], isLoading, error, refetch } = useGetAllApplicationFilesQuery();

  // View mode state
  const [viewMode, setViewMode] = useState("table");

  // Filter states
  const [search, setSearch] = useState("");
  const [selectedAgentFilter, setSelectedAgentFilter] = useState("");
  const [selectedStatusFilter, setSelectedStatusFilter] = useState("");
  const [dateFilterType, setDateFilterType] = useState("thisWeek");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [filteredData, setFilteredData] = useState([]);

  // Status filter options based on FileStatus enum
  const statusOptions = [
    "DOCUMENTS_PENDING",
    "DOCUMENTS_RECEIVED",
    "LODGED_AND_PROCESSING",
    "SOFT_COPY_RECEIVED",
    "SOFT_COPY_RELEASED",
    "HARD_COPY_RECEIVED",
    "HARD_COPY_MAILED_AND_CLOSED",
    "PENDING_EVIDENCE",
    "CANCELLED",
    "STATUS_UNAVAILABLE",
    "HOLD",
  ];

  // Format status for display
  const formatStatus = (status) => {
    if (!status) return "";
    return status.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, (c) => c.toUpperCase());
  };

  // Date filtering logic
  const applyDateFilter = (dateString) => {
    if (!dateString) return true; // If no date, do not filter

    const dateToCompare = new Date(dateString);
    let filterStartDate, filterEndDate;

    const today = new Date();
    const thisWeekStart = new Date(today);
    thisWeekStart.setDate(today.getDate() - today.getDay()); // Start of current week (Sunday)

    const thisWeekEnd = new Date(thisWeekStart);
    thisWeekEnd.setDate(thisWeekStart.getDate() + 6); // End of current week (Saturday)

    const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);

    switch (dateFilterType) {
      case "today":
        filterStartDate = new Date(today.setHours(0, 0, 0, 0));
        filterEndDate = new Date(today.setHours(23, 59, 59, 999));
        break;
      case "thisWeek":
        filterStartDate = new Date(thisWeekStart.setHours(0, 0, 0, 0));
        filterEndDate = new Date(thisWeekEnd.setHours(23, 59, 59, 999));
        break;
      case "thisMonth":
        filterStartDate = new Date(thisMonthStart.setHours(0, 0, 0, 0));
        filterEndDate = new Date(thisMonthEnd.setHours(23, 59, 59, 999));
        break;
      case "lastMonth":
        filterStartDate = new Date(lastMonthStart.setHours(0, 0, 0, 0));
        filterEndDate = new Date(lastMonthEnd.setHours(23, 59, 59, 999));
        break;
      case "custom":
        if (startDate && endDate) {
          filterStartDate = new Date(startDate);
          filterStartDate.setHours(0, 0, 0, 0);
          filterEndDate = new Date(endDate);
          filterEndDate.setHours(23, 59, 59, 999);
        }
        break;
      default:
        return true; // No date filter applied
    }

    return dateToCompare >= filterStartDate && dateToCompare <= filterEndDate;
  };

  // Apply filters
  useEffect(() => {
    if (!fileStatusData) return;

    const filtered = fileStatusData.filter((file) => {
      const clientName = file.application?.lead?.companyName || "";
      const candidateName = file.application?.applicantName || "";
      const agentName = file.application?.createdBy?.fullName || "";
      const fileStatus = file.fileStatus || "";
      const lodgedDate = file.lodgedDate;
      const softCopyReleasedDate = file.softCopyReleasedDate;

      const searchMatch =
        clientName.toLowerCase().includes(search.toLowerCase()) ||
        candidateName.toLowerCase().includes(search.toLowerCase());

      const agentMatch = selectedAgentFilter ? agentName === selectedAgentFilter : true;
      const statusMatch = selectedStatusFilter ? fileStatus === selectedStatusFilter : true;
      const dateMatch =
        applyDateFilter(lodgedDate) ||
        applyDateFilter(softCopyReleasedDate);

      return searchMatch && agentMatch && statusMatch && dateMatch;
    });

    setFilteredData(filtered);
  }, [fileStatusData, search, selectedAgentFilter, selectedStatusFilter, dateFilterType, startDate, endDate]);

  // Get unique agent names for filter
  const agentOptions = fileStatusData
    ? [...new Set(fileStatusData.map(file => file.application?.createdBy?.fullName).filter(Boolean))]
    : [];

  // Handle view details click
  const handleViewDetails = (applicationId, qualificationId) => {
    navigate(`/admin/filestatus/${applicationId}/${qualificationId}`);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">File Status</h1>
          <p className="text-sm text-gray-500">View and manage file status information</p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode("card")}
            className={`p-2 rounded ${viewMode === "card" ? "bg-[#6E39CB] text-white" : "bg-gray-200 text-gray-600"}`}
          >
            <FontAwesomeIcon icon={faThLarge} />
          </button>
          <button
            onClick={() => setViewMode("table")}
            className={`p-2 rounded ${viewMode === "table" ? "bg-[#6E39CB] text-white" : "bg-gray-200 text-gray-600"}`}
          >
            <FontAwesomeIcon icon={faTable} />
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          {/* Search */}
          <div className="relative flex-grow max-w-md">
            <input
              type="text"
              placeholder="Search by client or candidate name..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
            />
            <FontAwesomeIcon
              icon={faSearch}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
          </div>

          {/* Filter Button */}
          <button
            onClick={() => document.getElementById("filterDrawer").classList.toggle("hidden")}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50"
          >
            <FontAwesomeIcon icon={faFilter} className="mr-2" />
            Filters
          </button>
        </div>

        {/* Filter Drawer */}
        <div id="filterDrawer" className="hidden mt-4 pt-4 border-t border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Agent Filter */}
            <div>
              <label htmlFor="agentFilter" className="block text-sm font-medium text-gray-700 mb-1">
                Agent
              </label>
              <select
                id="agentFilter"
                value={selectedAgentFilter}
                onChange={(e) => setSelectedAgentFilter(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
              >
                <option value="">All Agents</option>
                {agentOptions.map((agent) => (
                  <option key={agent} value={agent}>
                    {agent}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="statusFilter"
                value={selectedStatusFilter}
                onChange={(e) => setSelectedStatusFilter(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
              >
                <option value="">All Statuses</option>
                {statusOptions.map((status) => (
                  <option key={status} value={status}>
                    {formatStatus(status)}
                  </option>
                ))}
              </select>
            </div>

            {/* Date Filter */}
            <div>
              <label htmlFor="dateFilter" className="block text-sm font-medium text-gray-700 mb-1">
                Date Range
              </label>
              <select
                id="dateFilter"
                value={dateFilterType}
                onChange={(e) => setDateFilterType(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
              >
                <option value="today">Today</option>
                <option value="thisWeek">This Week</option>
                <option value="thisMonth">This Month</option>
                <option value="lastMonth">Last Month</option>
                <option value="custom">Custom Range</option>
              </select>
            </div>

            {/* Custom Date Range */}
            {dateFilterType === "custom" && (
              <div className="md:col-span-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    id="startDate"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
                  />
                </div>
                <div>
                  <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    id="endDate"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-transparent"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Filter Actions */}
          <div className="flex justify-end mt-4">
            <button
              onClick={() => {
                setSelectedAgentFilter("");
                setSelectedStatusFilter("");
                setDateFilterType("thisWeek");
                setStartDate("");
                setEndDate("");
                setSearch("");
              }}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 mr-2"
            >
              Reset
            </button>
            <button
              onClick={() => document.getElementById("filterDrawer").classList.add("hidden")}
              className="px-4 py-2 bg-[#6E39CB] text-white rounded-lg hover:bg-[#5E2CB8]"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-10">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#6E39CB]"></div>
          <p className="mt-2 text-gray-500">Loading file status data...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          <p>Error loading file status data. Please try again later.</p>
        </div>
      )}

      {/* No Results */}
      {!isLoading && !error && filteredData.length === 0 && (
        <div className="bg-white border border-gray-100 rounded-lg shadow-sm p-10 text-center">
          <p className="text-gray-500">No file status records found matching your filters.</p>
        </div>
      )}

      {/* Card View */}
      {!isLoading && !error && viewMode === "card" && filteredData.length > 0 && (
        <div className="grid grid-cols-1 gap-4">
          {filteredData.map((file) => (
            <div key={`${file.application?.applicationId}-${file.qualificationCode}`} className="relative">
              <FileStatusCard
                clientName={file.application?.lead?.companyName || "N/A"}
                agentName={file.application?.createdBy?.fullName || "N/A"}
                invoiceNumber={file.application?.invoiceRefNumber || "N/A"}
                certificationName={file.qualificationName || "N/A"}
                status={file.fileStatus}
                lodgementDate={file.lodgedDate ? new Date(file.lodgedDate).toISOString().split('T')[0] : "N/A"}
                softCopyReleaseDate={file.softCopyReleasedDate ? new Date(file.softCopyReleasedDate).toISOString().split('T')[0] : "N/A"}
                hardCopyReleaseDate={file.hardCopyMailedDate ? new Date(file.hardCopyMailedDate).toISOString().split('T')[0] : "N/A"}
              />
              <button
                onClick={() => handleViewDetails(file.application?.applicationId, file.qualificationCode)}
                className="absolute top-4 right-4 bg-blue-500 text-white p-2 rounded-full hover:bg-blue-600 transition-colors"
                title="View Details"
              >
                <FontAwesomeIcon icon={faEye} />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Table View */}
      {!isLoading && !error && viewMode === "table" && filteredData.length > 0 && (
        <div className="overflow-x-auto bg-white rounded-lg border border-gray-100 shadow-sm">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-[#F4F5F9]">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client/Candidate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Agent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Qualification
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Lodged Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredData.map((file) => (
                <tr key={`${file.application?.applicationId}-${file.qualificationCode}`} className="hover:bg-[#F4F5F9]">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-col">
                      <div className="text-sm font-medium text-gray-900">{file.application?.lead?.companyName || "N/A"}</div>
                      <div className="text-sm text-gray-500">{file.application?.applicantName || "N/A"}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{file.application?.createdBy?.fullName || "N/A"}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-normal">
                    <div className="text-sm text-gray-900">{file.qualificationName || "N/A"}</div>
                    <div className="text-xs text-gray-500">{file.qualificationCode || "N/A"}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      {formatStatus(file.fileStatus)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {file.lodgedDate ? new Date(file.lodgedDate).toLocaleDateString() : "N/A"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleViewDetails(file.application?.applicationId, file.qualificationCode)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default FileStatus;
