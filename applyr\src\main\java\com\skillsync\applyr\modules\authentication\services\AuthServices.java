package com.skillsync.applyr.modules.authentication.services;

import com.skillsync.applyr.core.config.JwtUtil;
import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.User;
import com.skillsync.applyr.core.models.enums.Roles;
import com.skillsync.applyr.core.utills.UserUtils;
import com.skillsync.applyr.modules.authentication.models.AuthRequestDTO;
import com.skillsync.applyr.modules.authentication.models.AuthResponseDTO;
import com.skillsync.applyr.modules.authentication.models.ChangePassDTO;
import com.skillsync.applyr.modules.authentication.models.RegisterRequestDTO;
import com.skillsync.applyr.modules.authentication.repositories.UserRepository;
import com.skillsync.applyr.modules.emailing.services.EmailServices;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;
import java.util.function.Function;

@Service
public class AuthServices implements UserDetailsService {

    private final UserRepository userRepository;
    private final AuthenticationManager authenticationManager;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;


    public AuthServices(UserRepository userRepository, @Lazy AuthenticationManager authenticationManager, JwtUtil jwtUtil, PasswordEncoder passwordEncoder, EmailServices emailServices) {
        this.userRepository = userRepository;
        this.authenticationManager = authenticationManager;
        this.jwtUtil = jwtUtil;
        this.passwordEncoder = passwordEncoder;
    }


    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }


    public String authenticate(AuthRequestDTO authRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        authRequest.getUsername(), authRequest.getPassword()));
        SecurityContextHolder.getContext().setAuthentication(authentication);
        return jwtUtil.generateToken((UserDetails) authentication.getPrincipal());
    }

    public AuthResponseDTO register(RegisterRequestDTO registerRequest, Function<User, Boolean> savedUser) {


        Optional<User> existingUser = userRepository.findByUsername(registerRequest.getUsername());
        if (existingUser.isPresent()) {
            throw new IllegalArgumentException("Username already exists");
        }


        if(!savedUser.apply(convertRegDataToUser(registerRequest))) {
            throw new AppRTException("Unable to register user, please try again!", HttpStatus.BAD_REQUEST);
        }



        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        registerRequest.getUsername(), registerRequest.getPassword()));

        return new AuthResponseDTO(jwtUtil.generateToken((UserDetails) authentication.getPrincipal()),
                registerRequest.getUsername());
    }

    private User convertRegDataToUser(RegisterRequestDTO registerRequest) {
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));

        Roles role = Roles.valueOf(registerRequest.getRole().toString().toUpperCase());
        user.setAuthorities(Collections.singleton(role));

        return user;
    }

    public String changePassword(ChangePassDTO changePassDTO) {
        String username = UserUtils.getUsernameFromToken();
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new AppRTException("Unauthorized user! Please login and try again", HttpStatus.UNAUTHORIZED));

        if (!passwordEncoder.matches(changePassDTO.getOldPassword(), user.getPassword())) {
            throw new RuntimeException("Old password is incorrect, Please try again!");
        }

        String encodedNewPassword = passwordEncoder.encode(changePassDTO.getNewPassword());
        user.setPassword(encodedNewPassword);
        userRepository.save(user);

        return "Password updated successfully!";

    }

}
