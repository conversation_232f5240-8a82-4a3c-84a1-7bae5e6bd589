package com.skillsync.applyr.modules.company.repositories;

import com.skillsync.applyr.core.models.entities.Lead;
import com.skillsync.applyr.core.models.entities.SalesAgent;
import com.skillsync.applyr.core.models.enums.LeadsStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface LeadsRepository extends JpaRepository<Lead, Long> {
    Optional<Lead> getLeadByPhone(String uniqueId);

    Lead findLeadByPhone(String phone);

    int countLeadByAssignedAgentsAndStatusAndCreatedDateAfter(Set<SalesAgent> salesAgents, LeadsStatus leadsStatus, LocalDateTime weekStart);

    Optional<Lead> findByEmail(String email);

    List<Lead> findAllLeadByPhoneOrEmail(String phone, String email);

    // Simple JPA methods for filtering
    Page<Lead> findByStatus(LeadsStatus status, Pageable pageable);

    Page<Lead> findByCreatedDateBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    Page<Lead> findByStatusAndCreatedDateBetween(LeadsStatus status, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    @Query("SELECT l FROM Lead l WHERE " +
           "(:search IS NULL OR :search = '' OR " +
           "LOWER(l.companyName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(l.leadName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(l.email) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<Lead> findBySearchTerm(@Param("search") String search, Pageable pageable);
}
