import { createApi } from '@reduxjs/toolkit/query/react';
import { createCustomBaseQuery } from './customBaseQuery';

export const agentIDApi = createApi({
  reducerPath: 'agentIDApi',
  baseQuery: createCustomBaseQuery('/api/student'),
  endpoints: (builder) => ({
    submitAgentId: builder.mutation({
      query: (agentIdData) => ({
        url: `/update/agent/${agentIdData}`, 
        method: 'PUT', 
      }),
    }),
  }),
});

export const { useSubmitAgentIdMutation } = agentIDApi;
