package com.skillsync.applyr.core.models.entities;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "trouble_answers")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TroubleAnswer extends Auditable<String> {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    private String answer;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trouble_id")
    private Troubles trouble;
}
