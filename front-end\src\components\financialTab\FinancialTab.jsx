import React, { useState } from "react";
import { <PERSON>, <PERSON> } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const FinancialTab = () => {
  const [dateRange, setDateRange] = useState("weekly");
  const [customRange, setCustomRange] = useState({ from: "", to: "" });

  // Dummy data
  const data = {
    weekTitle: null,
    kpi1Target: 100000.0,
    kpi2Target: 150000.0,
    kpi1Actual: 80000.0,
    kpi2Actual: 120000.0,
    quotes: 200,
    invoices: 150,
    agentInfos: [
      {
        profile: { fullName: "Agent 1" },
        kpi1Target: 20000.0,
        kpi1Actual: 15000.0,
        kpi2Target: 25000.0,
        kpi2Actual: 22000.0,
      },
      {
        profile: { fullName: "Agent 2" },
        kpi1Target: 25000.0,
        kpi1Actual: 20000.0,
        kpi2Target: 30000.0,
        kpi2Actual: 28000.0,
      },
    ],
    topPerformer: "Agent 1",
  };

  const totalRevenue = data.kpi1Actual + data.kpi2Actual;

  const kpi1BarData = {
    labels: data.agentInfos.map((a) => a.profile.fullName),
    datasets: [
      {
        label: "KPI1 Actual ($)",
        data: data.agentInfos.map((a) =>
          a.kpi1Actual > a.kpi1Target ? a.kpi1Target : a.kpi1Actual
        ),
        backgroundColor: "rgba(16, 185, 129, 0.7)",
      },
      {
        label: "Remaining ($)",
        data: data.agentInfos.map((a) => {
          const diff = a.kpi1Target - a.kpi1Actual;
          return diff > 0 ? diff : 0;
        }),
        backgroundColor: "rgba(229, 231, 235, 0.8)",
      },
    ],
  };

  const kpi1BarOptions = {
    indexAxis: "y",
    responsive: true,
    scales: {
      x: { stacked: true, beginAtZero: true },
      y: { stacked: true },
    },
    plugins: {
      legend: { position: "bottom" },
      title: { display: false },
    },
  };

  const kpi2BarData = {
    labels: data.agentInfos.map((a) => a.profile.fullName),
    datasets: [
      {
        label: "KPI2 Actual ($)",
        data: data.agentInfos.map((a) =>
          a.kpi2Actual > a.kpi2Target ? a.kpi2Target : a.kpi2Actual
        ),
        backgroundColor: "rgba(139, 92, 246, 0.7)",
      },
      {
        label: "Remaining ($)",
        data: data.agentInfos.map((a) => {
          const diff = a.kpi2Target - a.kpi2Actual;
          return diff > 0 ? diff : 0;
        }),
        backgroundColor: "rgba(229, 231, 235, 0.8)",
      },
    ],
  };

  const kpi2BarOptions = {
    indexAxis: "y",
    responsive: true,
    scales: {
      x: { stacked: true, beginAtZero: true },
      y: { stacked: true },
    },
    plugins: {
      legend: { position: "bottom" },
      title: { display: false },
    },
  };

  const quoteInvoicePieData = {
    labels: ["Quotes Raised", "Invoices Raised"],
    datasets: [
      {
        data: [data.quotes, data.invoices],
        backgroundColor: ["#3B82F6", "#EF4444"],
        hoverBackgroundColor: ["#2563EB", "#DC2626"],
      },
    ],
  };

  const kpi1PieData = {
    labels: ["KPI1 Actual", "Remaining"],
    datasets: [
      {
        data: [
          data.kpi1Actual,
          data.kpi1Target - data.kpi1Actual > 0
            ? data.kpi1Target - data.kpi1Actual
            : 0,
        ],
        backgroundColor: [
          "rgba(16, 185, 129, 0.7)",
          "rgba(229, 231, 235, 0.8)",
        ],
        hoverBackgroundColor: [
          "rgba(16, 185, 129, 1)",
          "rgba(229, 231, 235, 1)",
        ],
      },
    ],
  };

  const kpi2PieData = {
    labels: ["KPI2 Actual", "Remaining"],
    datasets: [
      {
        data: [
          data.kpi2Actual,
          data.kpi2Target - data.kpi2Actual > 0
            ? data.kpi2Target - data.kpi2Actual
            : 0,
        ],
        backgroundColor: [
          "rgba(139, 92, 246, 0.7)",
          "rgba(229, 231, 235, 0.8)",
        ],
        hoverBackgroundColor: [
          "rgba(139, 92, 246, 1)",
          "rgba(229, 231, 235, 1)",
        ],
      },
    ],
  };

  return (
    <div className="p-4 space-y-6 w-full">
      <h1 className="text-2xl font-bold text-gray-800">Financial Dashboard</h1>
      {data.weekTitle && (
        <p className="text-sm text-gray-600">
          Current period: <strong>{data.weekTitle}</strong>
        </p>
      )}

      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-white rounded shadow p-4 text-center">
          <p className="text-sm text-gray-500">Total Revenue</p>
          <p className="text-xl font-bold text-blue-600">
            ${totalRevenue.toLocaleString()}
          </p>
        </div>

        <div className="bg-white rounded shadow p-4 text-center">
          <p className="text-sm text-gray-500">KPI1 Target</p>
          <p className="text-xl font-bold text-indigo-600">
            ${data.kpi1Target.toLocaleString()}
          </p>
        </div>

        <div className="bg-white rounded shadow p-4 text-center">
          <p className="text-sm text-gray-500">KPI1 Actual (Invoices)</p>
          <p className="text-xl font-bold text-green-600">
            ${data.kpi1Actual.toLocaleString()}
          </p>
        </div>

        <div className="bg-white rounded shadow p-4 text-center">
          <p className="text-sm text-gray-500">KPI2 Target</p>
          <p className="text-xl font-bold text-purple-600">
            ${data.kpi2Target.toLocaleString()}
          </p>
        </div>

        <div className="bg-white rounded shadow p-4 text-center">
          <p className="text-sm text-gray-500">KPI2 Money in Bank</p>
          <p className="text-xl font-bold text-yellow-600">
            ${data.kpi2Actual.toLocaleString()}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div className="bg-white rounded shadow p-4 text-center">
          <h2 className="text-base font-semibold mb-2">
            KPI1: Target vs Actual
          </h2>
          <div style={{ height: "300px", margin: "0 auto" }}>
            <Pie data={kpi1PieData} options={{ maintainAspectRatio: false }} />
          </div>
        </div>

        <div className="bg-white rounded shadow p-4 text-center">
          <h2 className="text-base font-semibold mb-2">
            KPI2: Target vs Money in Bank
          </h2>
          <div style={{ height: "300px", margin: "0 auto" }}>
            <Pie data={kpi2PieData} options={{ maintainAspectRatio: false }} />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="bg-white rounded shadow p-4">
          <h2 className="text-base font-semibold mb-2">KPI1 (Invoice)</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm text-left">
              <thead>
                <tr className="border-b bg-gray-50 text-gray-600">
                  <th className="py-2 px-4">Agent</th>
                  <th className="py-2 px-4 text-right">Target ($)</th>
                  <th className="py-2 px-4 text-right">Actual ($)</th>
                  <th className="py-2 px-4 text-right">Difference ($)</th>
                </tr>
              </thead>
              <tbody>
                {data.agentInfos.map((agent, idx) => {
                  const diff = agent.kpi1Target - agent.kpi1Actual;
                  return (
                    <tr
                      key={idx}
                      className="border-b hover:bg-gray-50 transition-colors"
                    >
                      <td className="py-2 px-4 font-medium">
                        {agent.profile.fullName}
                      </td>
                      <td className="py-2 px-4 text-right">
                        {agent.kpi1Target.toLocaleString()}
                      </td>
                      <td className="py-2 px-4 text-right">
                        {agent.kpi1Actual.toLocaleString()}
                      </td>
                      <td
                        className={`py-2 px-4 text-right ${
                          diff >= 0 ? "text-red-500" : "text-green-500"
                        }`}
                      >
                        {diff.toLocaleString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-white rounded shadow p-4">
          <h2 className="text-base font-semibold mb-2">KPI1 Progress</h2>
          <div className="h-72">
            <Bar data={kpi1BarData} options={kpi1BarOptions} />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="bg-white rounded shadow p-4">
          <h2 className="text-base font-semibold mb-2">
            KPI2 (Money In The Bank)
          </h2>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm text-left">
              <thead>
                <tr className="border-b bg-gray-50 text-gray-600">
                  <th className="py-2 px-4">Agent</th>
                  <th className="py-2 px-4 text-right">Target ($)</th>
                  <th className="py-2 px-4 text-right">Actual ($)</th>
                  <th className="py-2 px-4 text-right">Difference ($)</th>
                </tr>
              </thead>
              <tbody>
                {data.agentInfos.map((agent, idx) => {
                  const diff = agent.kpi2Target - agent.kpi2Actual;
                  return (
                    <tr
                      key={idx}
                      className="border-b hover:bg-gray-50 transition-colors"
                    >
                      <td className="py-2 px-4 font-medium">
                        {agent.profile.fullName}
                      </td>
                      <td className="py-2 px-4 text-right">
                        {agent.kpi2Target.toLocaleString()}
                      </td>
                      <td className="py-2 px-4 text-right">
                        {agent.kpi2Actual.toLocaleString()}
                      </td>
                      <td
                        className={`py-2 px-4 text-right ${
                          diff >= 0 ? "text-red-500" : "text-green-500"
                        }`}
                      >
                        {diff.toLocaleString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-white rounded shadow p-4">
          <h2 className="text-base font-semibold mb-2">KPI2 Progress</h2>
          <div className="h-72">
            <Bar data={kpi2BarData} options={kpi2BarOptions} />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="bg-white rounded shadow p-4">
          <h2 className="text-base font-semibold mb-2">
            Total Quote vs Invoice
          </h2>
          <div className="max-w-xs mx-auto">
            <Pie data={quoteInvoicePieData} />
          </div>
        </div>

        <div className="bg-white rounded shadow p-4">
          <h2 className="text-base font-semibold mb-2">
            Agent Quote & Invoice
          </h2>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm text-left">
              <thead>
                <tr className="border-b bg-gray-50 text-gray-600">
                  <th className="py-2 px-4">Agent</th>
                  <th className="py-2 px-4 text-right">Quotes Raised</th>
                  <th className="py-2 px-4 text-right">Invoices Raised</th>
                </tr>
              </thead>
              <tbody>
                {data.agentInfos.map((agent, idx) => (
                  <tr
                    key={idx}
                    className="border-b hover:bg-gray-50 transition-colors"
                  >
                    <td className="py-2 px-4 font-medium">
                      {agent.profile.fullName}
                    </td>
                    <td className="py-2 px-4 text-right">
                      {agent.kpi1Target.toLocaleString()}
                    </td>
                    <td className="py-2 px-4 text-right">
                      {agent.kpi2Target.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialTab;
