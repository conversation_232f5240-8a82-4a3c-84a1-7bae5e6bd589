import React, { useState, useMemo } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowUp,
  faCheckCircle,
  faTimesCircle,
  faUsers,
  faMoneyBillWave,
  faFileInvoiceDollar,
  faRobot,
  faSpinner,
  faExclamationTriangle,
} from "@fortawesome/free-solid-svg-icons";

// Import the Google Generative AI library
import { GoogleGenerativeAI } from "@google/generative-ai";

const AgentsTab = ({
  targetData,
}) => {
  // Add state for AI analysis
  const [aiInsights, setAiInsights] = useState(null);
  const [isLoadingInsights, setIsLoadingInsights] = useState(false);
  const [insightError, setInsightError] = useState(null);

  // Calculate stats for summary cards
  const stats = useMemo(() => {
    const totalAgents = targetData.targets.length;
    const activeAgents = targetData.targets.filter(a => a.kpi1Actual > 0 || a.kpi2Actual > 0).length;
    const kpi1TargetMet = targetData.targets.filter(a => a.kpi1Actual >= a.kpi1Target).length;
    const kpi2TargetMet = targetData.targets.filter(a => a.kpi2Actual >= a.kpi2Target).length;
    const bothTargetsMet = targetData.targets.filter(a => a.kpi1Actual >= a.kpi1Target && a.kpi2Actual >= a.kpi2Target).length;

    return {
      totalAgents,
      activeAgents,
      kpi1TargetMet,
      kpi2TargetMet,
      bothTargetsMet,
      kpi1Percentage: Math.round((kpi1TargetMet / totalAgents) * 100),
      kpi2Percentage: Math.round((kpi2TargetMet / totalAgents) * 100),
      bothPercentage: Math.round((bothTargetsMet / totalAgents) * 100),
      activePercentage: Math.round((activeAgents / totalAgents) * 100)
    };
  }, [targetData]);

  // Function to generate performance insights using Google's Gemini API
  const generatePerformanceInsights = async () => {
    setIsLoadingInsights(true);
    setInsightError(null);

    try {
      // Prepare the data for analysis
      const performanceData = {
        summary: {
          totalAgents: stats.totalAgents,
          kpi1TargetMet: stats.kpi1TargetMet,
          kpi2TargetMet: stats.kpi2TargetMet,
          bothTargetsMet: stats.bothTargetsMet,
          kpi1Percentage: stats.kpi1Percentage,
          kpi2Percentage: stats.kpi2Percentage,
        },
        agents: targetData.targets.map(agent => ({
          name: agent.profile?.fullName || agent.profile?.username || "Agent",
          kpi1Target: agent.kpi1Target,
          kpi1Actual: agent.kpi1Actual,
          kpi2Target: agent.kpi2Target,
          kpi2Actual: agent.kpi2Actual,
          kpi1Met: agent.kpi1Actual >= agent.kpi1Target,
          kpi2Met: agent.kpi2Actual >= agent.kpi2Target,
          kpi1Progress: agent.kpi1Target > 0 ? Math.round((agent.kpi1Actual / agent.kpi1Target) * 100) : 0,
          kpi2Progress: agent.kpi2Target > 0 ? Math.round((agent.kpi2Actual / agent.kpi2Target) * 100) : 0,
        }))
      };

      // API key for Google's Gemini API
      const API_KEY = "AIzaSyBfHT1ajPGzXXakJl8y88Vgb9f8piHg2c4";

      // Initialize the Gemini API client
      const genAI = new GoogleGenerativeAI(API_KEY);

      // Get the model
      const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp-image-generation" });

      // Prepare the prompt for the AI
      const prompt = `
        Analyze this performance data for a sales team and provide insights:
        1. Identify top performers and what makes them successful
        2. Identify underperforming agents and suggest improvement strategies
        3. Provide 3-5 actionable recommendations for management
        4. Highlight any patterns or trends in the data

        Format your response as HTML with inline CSS. Use the following guidelines:
        - Use <div style="font-size: 16px; line-height: 1.5; color: #333;"> as the main container
        - Use <h1 style="color: #6E39CB; font-size: 24px; margin-bottom: 16px;"> for main headings
        - Use <h2 style="color: #4A5568; font-size: 20px; margin-top: 20px; margin-bottom: 12px;"> for subheadings
        - Use <p style="margin-bottom: 12px; font-size: 16px;"> for paragraphs
        - Use <ul style="margin-left: 20px; margin-bottom: 16px;"> for unordered lists
        - Use <li style="margin-bottom: 8px;"> for list items
        - Use <span style="color: #2F855A; font-weight: bold;"> for highlighting positive metrics
        - Use <span style="color: #C53030; font-weight: bold;"> for highlighting negative metrics
        - Use <div style="background-color: #F7FAFC; border-left: 4px solid #6E39CB; padding: 12px; margin: 16px 0;"> for important insights

        Keep your analysis concise and focused on actionable insights.

        KPI 1 is Invoices that were raised and accepted. KPI 2 is Money in Bank.

        Performance Data: ${JSON.stringify(performanceData)}
      `;
      

      // Generate content using the proper API
      const result = await model.generateContent(prompt);
      const response = result.response;
      const insightText = response.text();

      // The LLM now returns HTML with inline CSS, so we can use it directly
      setAiInsights(insightText);
    } catch (error) {
      console.error('Error generating insights:', error);
      setInsightError(error.message || 'Failed to generate insights');
    } finally {
      setIsLoadingInsights(false);
    }
  };
  return (
    <>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4">
          <div className="flex items-center mb-2">
            <div className="bg-[#F4F5F9] p-2 rounded-full mr-2">
              <FontAwesomeIcon icon={faUsers} className="h-4 w-4 text-[#6E39CB]" />
            </div>
            <h3 className="text-sm font-medium text-gray-700">Total Agents</h3>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-xl font-bold text-gray-900">{stats.totalAgents}</p>
            <p className="text-xs text-gray-500">
              {stats.activeAgents} active ({stats.activePercentage}%)
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4">
          <div className="flex items-center mb-2">
            <div className="bg-[#F4F5F9] p-2 rounded-full mr-2">
              <FontAwesomeIcon icon={faCheckCircle} className="h-4 w-4 text-green-600" />
            </div>
            <h3 className="text-sm font-medium text-gray-700">Both Targets Met</h3>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-xl font-bold text-gray-900">{stats.bothTargetsMet}</p>
            <p className="text-xs text-gray-500">
              {stats.bothPercentage}% of agents
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4">
          <div className="flex items-center mb-2">
            <div className="bg-[#F4F5F9] p-2 rounded-full mr-2">
              <FontAwesomeIcon icon={faMoneyBillWave} className="h-4 w-4 text-blue-600" />
            </div>
            <h3 className="text-sm font-medium text-gray-700">KPI1 Target Met</h3>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-xl font-bold text-gray-900">{stats.kpi1TargetMet}</p>
            <p className="text-xs text-gray-500">
              {stats.kpi1Percentage}% of agents
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4">
          <div className="flex items-center mb-2">
            <div className="bg-[#F4F5F9] p-2 rounded-full mr-2">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-4 w-4 text-purple-600" />
            </div>
            <h3 className="text-sm font-medium text-gray-700">KPI2 Target Met</h3>
          </div>
          <div className="flex items-end justify-between">
            <p className="text-xl font-bold text-gray-900">{stats.kpi2TargetMet}</p>
            <p className="text-xs text-gray-500">
              {stats.kpi2Percentage}% of agents
            </p>
          </div>
        </div>
      </div>

      {/* KPI1 Leaderboard */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h5 className="text-md font-medium text-gray-900">KPI1 Performance Leaderboard</h5>
            <p className="text-xs text-gray-500 mt-1">
              Revenue Generation - Ranked by achievement percentage
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <FontAwesomeIcon icon={faCheckCircle} className="mr-1 h-3 w-3" /> Target Achieved
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              <FontAwesomeIcon icon={faArrowUp} className="mr-1 h-3 w-3" /> Target Pending
            </span>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-[#F4F5F9]">
              <tr>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                  Rank
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Agent
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Target
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actual
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {[...targetData.targets]
                .sort((a, b) => {
                  const aPercentage = a.kpi1Target > 0 ? (a.kpi1Actual / a.kpi1Target) : 0;
                  const bPercentage = b.kpi1Target > 0 ? (b.kpi1Actual / b.kpi1Target) : 0;
                  return bPercentage - aPercentage;
                })
                .map((agent, idx) => {
                  const kpi1Met = agent.kpi1Actual >= agent.kpi1Target;
                  const kpi1Progress = agent.kpi1Target > 0 ? Math.round((agent.kpi1Actual / agent.kpi1Target) * 100) : 0;

                  // Determine row styling based on rank and achievement
                  let rowClass = "transition-colors";
                  let rankClass = "";

                  if (kpi1Met) {
                    rowClass += " bg-green-50 hover:bg-green-100";
                  } else if (idx >= targetData.targets.length - 3) {
                    // Bottom 3 performers who didn't meet target
                    rowClass += " bg-gray-50 hover:bg-gray-100";
                  } else {
                    rowClass += " hover:bg-gray-50";
                  }

                  // Special styling for top 3
                  if (idx === 0) {
                    rankClass = "bg-yellow-100 text-yellow-800 border border-yellow-300";
                  } else if (idx === 1) {
                    rankClass = "bg-gray-200 text-gray-800 border border-gray-300";
                  } else if (idx === 2) {
                    rankClass = "bg-orange-100 text-orange-800 border border-orange-300";
                  } else {
                    rankClass = "bg-gray-100 text-gray-600";
                  }

                  return (
                    <tr key={idx} className={rowClass}>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        <div className="flex justify-center">
                          <span className={`h-8 w-8 rounded-full flex items-center justify-center text-sm font-bold ${rankClass}`}>
                            {idx + 1}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                            <span className="text-[#6E39CB] font-medium">
                              {agent.profile?.fullName
                                ? agent.profile.fullName.charAt(0).toUpperCase()
                                : "?"}
                            </span>
                          </div>
                          <span className="font-medium text-sm">
                            {agent.profile?.fullName || agent.profile?.username || "Agent"}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                        ${agent.kpi1Target.toLocaleString()}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                        <span className={kpi1Met ? 'font-medium text-green-600' : 'font-normal'}>
                          ${agent.kpi1Actual.toLocaleString()}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center justify-center">
                          <div className="w-full max-w-xs bg-gray-200 rounded-full h-2.5">
                            <div
                              className={`h-2.5 rounded-full ${kpi1Met ? 'bg-green-500' : kpi1Progress >= 75 ? 'bg-blue-500' : kpi1Progress >= 50 ? 'bg-yellow-500' : 'bg-orange-500'}`}
                              style={{ width: `${Math.min(100, kpi1Progress)}%` }}
                            ></div>
                          </div>
                          <span className={`ml-2 text-xs font-medium ${kpi1Met ? 'text-green-600' : kpi1Progress >= 75 ? 'text-blue-600' : kpi1Progress >= 50 ? 'text-yellow-600' : 'text-orange-500'}`}>
                            {kpi1Progress}%
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        {kpi1Met ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> Achieved
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <FontAwesomeIcon icon={faArrowUp} className="mr-1" /> {100 - kpi1Progress}% to go
                          </span>
                        )}
                      </td>
                    </tr>
                  );
                })}
            </tbody>
          </table>
        </div>
      </div>

      {/* KPI2 Leaderboard */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h5 className="text-md font-medium text-gray-900">KPI2 Performance Leaderboard</h5>
            <p className="text-xs text-gray-500 mt-1">
              Invoicing - Ranked by achievement percentage
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <FontAwesomeIcon icon={faCheckCircle} className="mr-1 h-3 w-3" /> Target Achieved
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              <FontAwesomeIcon icon={faArrowUp} className="mr-1 h-3 w-3" /> Target Pending
            </span>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-[#F4F5F9]">
              <tr>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                  Rank
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Agent
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Target
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actual
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progress
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {[...targetData.targets]
                .sort((a, b) => {
                  const aPercentage = a.kpi2Target > 0 ? (a.kpi2Actual / a.kpi2Target) : 0;
                  const bPercentage = b.kpi2Target > 0 ? (b.kpi2Actual / b.kpi2Target) : 0;
                  return bPercentage - aPercentage;
                })
                .map((agent, idx) => {
                  const kpi2Met = agent.kpi2Actual >= agent.kpi2Target;
                  const kpi2Progress = agent.kpi2Target > 0 ? Math.round((agent.kpi2Actual / agent.kpi2Target) * 100) : 0;

                  // Determine row styling based on rank and achievement
                  let rowClass = "transition-colors";
                  let rankClass = "";

                  if (kpi2Met) {
                    rowClass += " bg-green-50 hover:bg-green-100";
                  } else if (idx >= targetData.targets.length - 3) {
                    // Bottom 3 performers who didn't meet target
                    rowClass += " bg-gray-50 hover:bg-gray-100";
                  } else {
                    rowClass += " hover:bg-gray-50";
                  }

                  // Special styling for top 3
                  if (idx === 0) {
                    rankClass = "bg-yellow-100 text-yellow-800 border border-yellow-300";
                  } else if (idx === 1) {
                    rankClass = "bg-gray-200 text-gray-800 border border-gray-300";
                  } else if (idx === 2) {
                    rankClass = "bg-orange-100 text-orange-800 border border-orange-300";
                  } else {
                    rankClass = "bg-gray-100 text-gray-600";
                  }

                  return (
                    <tr key={idx} className={rowClass}>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        <div className="flex justify-center">
                          <span className={`h-8 w-8 rounded-full flex items-center justify-center text-sm font-bold ${rankClass}`}>
                            {idx + 1}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                            <span className="text-[#6E39CB] font-medium">
                              {agent.profile?.fullName
                                ? agent.profile.fullName.charAt(0).toUpperCase()
                                : "?"}
                            </span>
                          </div>
                          <span className="font-medium text-sm">
                            {agent.profile?.fullName || agent.profile?.username || "Agent"}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                        ${agent.kpi2Target.toLocaleString()}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm">
                        <span className={kpi2Met ? 'font-medium text-green-600' : 'font-normal'}>
                          ${agent.kpi2Actual.toLocaleString()}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center justify-center">
                          <div className="w-full max-w-xs bg-gray-200 rounded-full h-2.5">
                            <div
                              className={`h-2.5 rounded-full ${kpi2Met ? 'bg-green-500' : kpi2Progress >= 75 ? 'bg-blue-500' : kpi2Progress >= 50 ? 'bg-yellow-500' : 'bg-orange-500'}`}
                              style={{ width: `${Math.min(100, kpi2Progress)}%` }}
                            ></div>
                          </div>
                          <span className={`ml-2 text-xs font-medium ${kpi2Met ? 'text-green-600' : kpi2Progress >= 75 ? 'text-blue-600' : kpi2Progress >= 50 ? 'text-yellow-600' : 'text-orange-500'}`}>
                            {kpi2Progress}%
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-center">
                        {kpi2Met ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> Achieved
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <FontAwesomeIcon icon={faArrowUp} className="mr-1" /> {100 - kpi2Progress}% to go
                          </span>
                        )}
                      </td>
                    </tr>
                  );
                })}
            </tbody>
          </table>
        </div>
      </div>

                

      {/* AI Performance Analysis */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h5 className="text-md font-medium text-gray-900">AI Performance Analysis</h5>
            <p className="text-xs text-gray-500 mt-1">
              Insights and recommendations based on current performance data
            </p>
          </div>
          <button
            onClick={generatePerformanceInsights}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            disabled={isLoadingInsights}
          >
            {isLoadingInsights ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faRobot} className="mr-2" />
                Generate Insights
              </>
            )}
          </button>
        </div>
        <div className="bg-gray-50 rounded-lg p-4 min-h-[200px]">
          {isLoadingInsights && (
            <div className="flex flex-col items-center justify-center py-8">
              <FontAwesomeIcon icon={faSpinner} className="text-[#6E39CB] text-3xl animate-spin mb-4" />
              <p className="text-gray-600">Analyzing performance data with AI...</p>
            </div>
          )}

          {!isLoadingInsights && !aiInsights && !insightError && (
            <div className="text-center text-gray-500 py-8">
              <FontAwesomeIcon icon={faRobot} className="text-[#6E39CB] text-3xl mb-4" />
              <p>Click "Generate Insights" to analyze team performance data with AI</p>
              <p className="text-xs mt-2">This will use a Large Language Modal to provide actionable recommendations</p>
            </div>
          )}

          {!isLoadingInsights && insightError && (
            <div className="text-center text-red-500 py-8">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-3xl mb-4" />
              <p>Error generating insights: {insightError}</p>
              <button
                onClick={generatePerformanceInsights}
                className="mt-4 px-3 py-1 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
              >
                Try Again
              </button>
            </div>
          )}

          {!isLoadingInsights && aiInsights && (
            <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: aiInsights }}></div>
          )}
        </div>
      </div>

      {/* Simple Agent Performance Table */}
      <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h5 className="text-md font-medium text-gray-900">Agent Performance Summary</h5>
            <p className="text-xs text-gray-500 mt-1">
              Simple overview of all agents and their target achievement status
            </p>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-[#F4F5F9]">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Agent
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  KPI1 Status
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  KPI2 Status
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Overall
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {targetData.targets.map((agent, idx) => {
                const kpi1Met = agent.kpi1Actual >= agent.kpi1Target;
                const kpi2Met = agent.kpi2Actual >= agent.kpi2Target;

                // Determine row highlight class
                let rowClass = "hover:bg-gray-50 transition-colors";
                if (kpi1Met && kpi2Met) {
                  rowClass += " bg-green-50";
                } else if (kpi1Met) {
                  rowClass += " bg-blue-50";
                } else if (kpi2Met) {
                  rowClass += " bg-purple-50";
                }

                return (
                  <tr key={idx} className={rowClass}>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="bg-[#F4F5F9] rounded-full h-8 w-8 flex items-center justify-center mr-3 flex-shrink-0">
                          <span className="text-[#6E39CB] font-medium">
                            {agent.profile?.fullName
                              ? agent.profile.fullName.charAt(0).toUpperCase()
                              : "?"}
                          </span>
                        </div>
                        <span className="font-medium">
                          {agent.profile?.fullName || agent.profile?.username || "Agent"}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      {kpi1Met ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> Met
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <FontAwesomeIcon icon={faTimesCircle} className="mr-1" /> Not Met
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      {kpi2Met ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> Met
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <FontAwesomeIcon icon={faTimesCircle} className="mr-1" /> Not Met
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      {kpi1Met && kpi2Met ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> Both Met
                        </span>
                      ) : kpi1Met ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> KPI1 Only
                        </span>
                      ) : kpi2Met ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> KPI2 Only
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <FontAwesomeIcon icon={faTimesCircle} className="mr-1" /> None Met
                        </span>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
};

export default AgentsTab;
