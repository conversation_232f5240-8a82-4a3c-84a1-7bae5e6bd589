import React, { useState, useEffect } from "react";
import XeroIntegrationHeader from "../../../components/xeroIntegration/XeroIntegrationHeader";
import FilterBar from "../../../components/xeroIntegration/FilterBar";
import XeroImportModal from "../../../components/xeroIntegration/XeroImportModal";
import KPI1InvoicingTable from "../../../components/xeroIntegration/KPI1InvoicingTable";
import KPI2InvoicingTable from "../../../components/xeroIntegration/KPI2InvoicingTable";
import {
  useUploadXeroInvoiceMutation,
  useGetAllKPI1XeroDataQuery,
  useUploadXeroInBankMutation,
  useGetAllKPI2XeroDataQuery
} from "../../../services/AdminAPIService";
import { toast } from "react-toastify";

const XeroIntegration = () => {
  // Tab state
  const [selectedTab, setSelectedTab] = useState("KPI1");

  // Filter states
  const [filter, setFilter] = useState("");
  const [selectedStatusFilter, setSelectedStatusFilter] = useState("");
  const [selectedPaymentStatusFilter, setSelectedPaymentStatusFilter] = useState("");
  const [dateFilterType, setDateFilterType] = useState("thisWeek");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  // Modal and file handling states
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [uploadFile, setUploadFile] = useState(null);
  const [importTab, setImportTab] = useState(null);

  // API hooks for Xero data
  const [uploadXeroInvoice, { isLoading: isUploadingKPI1 }] = useUploadXeroInvoiceMutation();
  const { data: kpi1ApiData, isLoading: isLoadingKPI1, refetch: refetchKPI1 } = useGetAllKPI1XeroDataQuery();
  const [uploadXeroInBank, { isLoading: isUploadingKPI2 }] = useUploadXeroInBankMutation();
  const { data: kpi2ApiData, isLoading: isLoadingKPI2, refetch: refetchKPI2 } = useGetAllKPI2XeroDataQuery();

  // State for KPI1 and KPI2 data
  const [kpi1Data, setKpi1Data] = useState([]);
  const [kpi2Data, setKpi2Data] = useState([]);

  // No dropdown options needed as fields are read-only

  // Filter logic for KPI1 data
  const filteredKpi1Data = kpi1Data.filter((item) => {
    const searchMatch =
      String(item.invoiceNumber || "").toLowerCase().includes(filter.toLowerCase()) ||
      String(item.contact || "").toLowerCase().includes(filter.toLowerCase()) ||
      String(item.invoiceDate || "").toLowerCase().includes(filter.toLowerCase()) ||
      String(item.reference || "").toLowerCase().includes(filter.toLowerCase());

    const statusMatch = selectedStatusFilter ? item.status === selectedStatusFilter : true;
    const paymentStatusMatch = selectedPaymentStatusFilter
      ? item.invoiceSent === selectedPaymentStatusFilter
      : true;

    return searchMatch && statusMatch && paymentStatusMatch;
  });

  // Filter logic for KPI2 data
  const filteredKpi2Data = kpi2Data.filter((item) => {
    const searchMatch =
      String(item.reference || "").toLowerCase().includes(filter.toLowerCase()) ||
      String(item.contact || "").toLowerCase().includes(filter.toLowerCase()) ||
      String(item.description || "").toLowerCase().includes(filter.toLowerCase());

    const statusMatch = selectedStatusFilter ? String(item.source || "") === selectedStatusFilter : true;

    return searchMatch && statusMatch;
  });

  // Load KPI1 data when component mounts or when data is refreshed
  useEffect(() => {
    if (kpi1ApiData) {
      const formattedData = kpi1ApiData.map(item => ({
        applicationId: item.application?.applicationId || '',
        invoiceNumber: item.application?.invoiceRefNumber || '',
        contact: item.application?.applicantName || '',
        invoiceDate: item.invoiceDate ? new Date(item.invoiceDate).toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "short",
          year: "numeric",
        }) : '',
        dueDate: item.invoiceExpiryDate ? new Date(item.invoiceExpiryDate).toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "short",
          year: "numeric",
        }) : '',
        reference: item.application?.quoteRefNumber || '',
        gross: item.application?.totalPrice || 0,
        balance: item.application?.paidAmount || 0,
        status: item.status || '',
        source: item.source || '',
        invoiceSent: item.invoiceStatus || 'NOT_SENT',
        agentCommission: item.application?.createdBy?.fullName || '',
      }));

      setKpi1Data(formattedData);
    }
  }, [kpi1ApiData]);

  // Load KPI2 data when component mounts or when data is refreshed
  useEffect(() => {
    if (kpi2ApiData) {
      const formattedData = kpi2ApiData.map(item => ({
        applicationId: item.applicationResponse?.applicationId || '',
        date: item.dateInserted ? new Date(item.dateInserted).toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "short",
          year: "numeric",
        }) : '',
        agentCommission: item.agentCommission?.fullName || '',
        reference: item.reference || '',
        contact: item.applicationResponse?.applicantName || '',
        description: item.description || '',
        debit: item.debitAmount || 0,
        credit: item.creditAmount || 0,
        net: item.netAmount || 0,
        source: item.source || '',
      }));

      setKpi2Data(formattedData);
    }
  }, [kpi2ApiData]);

  // Event handlers
  const handleTabChange = (tab) => {
    setSelectedTab(tab);
    // Reset filters when changing tabs
    setFilter("");
    setSelectedStatusFilter("");
    setSelectedPaymentStatusFilter("");

    // Refresh data when switching tabs
    if (tab === "KPI1") {
      refetchKPI1();
    } else if (tab === "KPI2") {
      refetchKPI2();
    }
  };

  // No handlers needed for status, invoice sent, or agent commission as they are read-only

  const closeModal = () => {
    setIsModalOpen(false);
    setUploadFile(null);
    setImportTab(null);
  };

  // Handle file upload
  const handleUpload = async () => {
    if (uploadFile) {
      if (importTab === "KPI1") {
        try {
          await uploadXeroInvoice(uploadFile).unwrap();
          toast.success("KPI1 data imported successfully");
          refetchKPI1(); // Refresh the data after upload
        } catch (error) {
          console.error("Error uploading KPI1 data:", error);
          toast.error("Failed to import KPI1 data: " + (error.data?.message || "Unknown error"));
        }
      } else if (importTab === "KPI2") {
        try {
          await uploadXeroInBank(uploadFile).unwrap();
          toast.success("KPI2 data imported successfully");
          refetchKPI2(); // Refresh the data after upload
        } catch (error) {
          console.error("Error uploading KPI2 data:", error);
          toast.error("Failed to import KPI2 data: " + (error.data?.message || "Unknown error"));
        }
      }
    }
    closeModal();
  };

  return (
    <div className="container mx-auto py-6 px-4">
      {/* Header with tabs */}
      <XeroIntegrationHeader
        selectedTab={selectedTab}
        handleTabChange={handleTabChange}
      />

      {/* Filters */}
      <FilterBar
        filter={filter}
        setFilter={setFilter}
        selectedStatusFilter={selectedStatusFilter}
        setSelectedStatusFilter={setSelectedStatusFilter}
        selectedPaymentStatusFilter={selectedPaymentStatusFilter}
        setSelectedPaymentStatusFilter={setSelectedPaymentStatusFilter}
        dateFilterType={dateFilterType}
        setDateFilterType={setDateFilterType}
        startDate={startDate}
        setStartDate={setStartDate}
        endDate={endDate}
        setEndDate={setEndDate}
        statusOptions={["Paid", "Approved"]}
        invoiceSentOptions={["Sent", "Not Sent"]}
        tabType={selectedTab}
      />

      {/* KPI1 Table - Invoicing */}
      {selectedTab === "KPI1" && (
        <KPI1InvoicingTable
          filteredData={filteredKpi1Data}
          setImportTab={setImportTab}
          setIsModalOpen={setIsModalOpen}
          isLoading={isLoadingKPI1}
        />
      )}

      {/* KPI2 Table - Money IN the Bank */}
      {selectedTab === "KPI2" && (
        <KPI2InvoicingTable
          filteredData={filteredKpi2Data}
          setImportTab={setImportTab}
          setIsModalOpen={setIsModalOpen}
          isLoading={isLoadingKPI2}
        />
      )}

      {/* Modal for file upload */}
      <XeroImportModal
        isOpen={isModalOpen}
        closeModal={closeModal}
        uploadFile={uploadFile}
        setUploadFile={setUploadFile}
        handleUpload={handleUpload}
        importTab={importTab}
        isUploading={isUploadingKPI1 || isUploadingKPI2}
      />
    </div>
  );
};

export default XeroIntegration;
