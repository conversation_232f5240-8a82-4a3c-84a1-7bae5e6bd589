import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faSearch,
  faFilter,
  faTable,
  faTh,
  faSync,
  faFileInvoiceDollar,
  faEdit,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";
import {
  useGetAllCommissionsQuery,
  useUpdateCommissionStatusMutation,
  useDeleteCommissionMutation,
  useCreateCommissionMutation,
  useEditCommissionMutation,
} from "../../../services/CompanyAPIService";
import CommissionCard from "../../../components/commissions/CommissionCard";
import AddCommissionModal from "../../../components/commissions/AddCommissionModal";
import LoadingSpinner from "../../../components/common/LoadingSpinner";
import EmptyState from "../../../components/common/EmptyState";
import ConfirmationModal from "../../../components/modal/ConfirmationModal";
import { formatCurrency } from "../../../utils/formatters";
import { showErrorToast, showSuccessToast } from "../../../utils/toastUtils";

const OperationsExternalCommissions = () => {
  // State
  const [viewMode, setViewMode] = useState("table");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCommission, setSelectedCommission] = useState(null);
  const [selectedApplicationId, setSelectedApplicationId] = useState(null);

  // Queries and Mutations
  const { data: commissions = [], isLoading, refetch } = useGetAllCommissionsQuery();
  const [updateCommissionStatus] = useUpdateCommissionStatusMutation();
  const [deleteCommission, { isLoading: isDeleting }] = useDeleteCommissionMutation();
  const [createCommission, { isLoading: isCreating }] = useCreateCommissionMutation();
  const [editCommission, { isLoading: isEditing }] = useEditCommissionMutation();

  // Filter commissions
  const filteredCommissions = commissions.filter((commission) => {
    const matchesSearch =
      searchTerm === "" ||
      commission.externalCommissionName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      commission.application.applicantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (commission.application.invoiceRefNumber &&
        commission.application.invoiceRefNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
      commission.application.createdBy.fullName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "ALL" || commission.paymentStatus === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Handlers
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleStatusChange = async (applicationId, newStatus) => {
    try {
      await updateCommissionStatus({ applicationId, status: newStatus }).unwrap();
      showSuccessToast("Commission status updated successfully!");
      refetch();
    } catch (error) {
      console.error("Failed to update status:", error);
      showErrorToast(error, "Failed to update commission status. Please try again.");
    }
  };

  const handleAddCommission = async (commissionData) => {
    try {
      await createCommission(commissionData).unwrap();
      setIsAddModalOpen(false);
      showSuccessToast("Commission created successfully!");
      refetch();
    } catch (error) {
      console.error("Failed to create commission:", error);
      showErrorToast(error, "Failed to create commission. Please try again.");
    }
  };

  const handleEditClick = (commission) => {
    setSelectedCommission(commission);
    setIsEditModalOpen(true);
  };

  const handleEditCommission = async (commissionData) => {
    try {
      await editCommission({
        applicationId: selectedCommission.application.applicationId,
        commissionDTO: commissionData,
      }).unwrap();
      setIsEditModalOpen(false);
      setSelectedCommission(null);
      showSuccessToast("Commission updated successfully!");
      refetch();
    } catch (error) {
      console.error("Failed to edit commission:", error);
      showErrorToast(error, "Failed to edit commission. Please try again.");
    }
  };

  const handleDeleteClick = (applicationId) => {
    setSelectedApplicationId(applicationId);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteCommission(selectedApplicationId).unwrap();
      setIsDeleteModalOpen(false);
      setSelectedApplicationId(null);
      showSuccessToast("Commission deleted successfully!");
      refetch();
    } catch (error) {
      console.error("Failed to delete commission:", error);
      showErrorToast(error, "Failed to delete commission. Please try again.");
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <div className="flex items-center">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="text-blue-600 text-xl mr-3" />
              <h1 className="text-2xl font-bold text-gray-800">External Commissions</h1>
            </div>
            <p className="mt-2 text-gray-600">
              Manage external commission payments for referrals
            </p>
          </div>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200"
          >
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Add Commission
          </button>
        </div>
      </div>

      {/* Filters and View Controls */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="w-full md:w-auto flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative w-full sm:w-80">
              <input
                type="text"
                placeholder="Search commissions..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <FontAwesomeIcon
                icon={faSearch}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
            </div>

            {/* Status Filter */}
            <div className="relative w-full sm:w-auto">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              >
                <option value="ALL">All Statuses</option>
                <option value="PENDING_CHECKED">Pending Checked</option>
                <option value="CHECKED_AND_PAID">Checked & Paid</option>
                <option value="RECONCILED_AND_COMPLETED">Reconciled & Completed</option>
              </select>
              <FontAwesomeIcon
                icon={faFilter}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* View Toggle */}
            <div className="flex border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode("table")}
                className={`px-3 py-2 flex items-center ${
                  viewMode === "table" ? "bg-blue-50 text-blue-600" : "bg-white text-gray-600"
                }`}
                title="Table View"
              >
                <FontAwesomeIcon icon={faTable} />
              </button>
              <button
                onClick={() => setViewMode("card")}
                className={`px-3 py-2 flex items-center ${
                  viewMode === "card" ? "bg-blue-50 text-blue-600" : "bg-white text-gray-600"
                }`}
                title="Card View"
              >
                <FontAwesomeIcon icon={faTh} />
              </button>
            </div>

            {/* Refresh Button */}
            <button
              onClick={refetch}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg flex items-center transition-colors duration-200"
              title="Refresh"
            >
              <FontAwesomeIcon icon={faSync} />
            </button>

            {/* Add Commission Button (Mobile) */}
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="md:hidden bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200"
            >
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Add
            </button>
          </div>
        </div>

        {/* Content */}
        {isLoading ? (
          <LoadingSpinner />
        ) : filteredCommissions.length === 0 ? (
          <EmptyState
            message={
              searchTerm || statusFilter !== "ALL"
                ? "No commissions found matching your search criteria."
                : "No external commissions have been added yet."
            }
            icon={faFileInvoiceDollar}
          />
        ) : viewMode === "card" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredCommissions.map((commission) => (
              <CommissionCard
                key={commission.application.applicationId}
                commission={commission}
                onStatusChange={handleStatusChange}
                onEdit={handleEditClick}
                onDelete={handleDeleteClick}
              />
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm overflow-x-auto">
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900">Commission List</h2>
            </div>
            <table className="min-w-full">
              <thead>
                <tr className="bg-[#F4F5F9]">
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Internal Team Member
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Applicant Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    External Referral
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact Info
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Commission Amount
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredCommissions.map((commission, index) => (
                  <tr key={commission.application.applicationId} className={index % 2 === 0 ? "bg-white" : "bg-[#F4F5F9]"}>

                    <td className="px-6 py-3 whitespace-nowrap">
                      <span className="text-sm font-medium text-[#6E39CB]">
                        {commission.application.invoiceRefNumber || "No Invoice"}
                      </span>
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">
                      {commission.application.createdBy.fullName}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">
                      {commission.application.applicantName}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">
                      {commission.externalCommissionName}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">
                      {commission.externalCommissionContactInfo}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-700">
                      {formatCurrency(commission.commissionAmount)}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap">
                      <select
                        value={commission.paymentStatus}
                        onChange={(e) => handleStatusChange(commission.application.applicationId, e.target.value)}
                        className="bg-white border border-gray-300 text-gray-700 text-sm rounded-md focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] block w-full p-1.5"
                      >
                        <option value="PENDING_CHECKED">Pending Checked</option>
                        <option value="CHECKED_AND_PAID">Checked & Paid</option>
                        <option value="RECONCILED_AND_COMPLETED">Reconciled & Completed</option>
                      </select>
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">
                      {commission.paymentDate
                        ? new Date(commission.paymentDate).toLocaleDateString()
                        : "Not paid yet"}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={() => handleEditClick(commission)}
                          className="text-[#6E39CB] hover:text-[#5E2CB8]"
                          title="Edit Commission"
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(commission.application.applicationId)}
                          className="text-red-600 hover:text-red-700"
                          title="Delete Commission"
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add Commission Modal */}
      <AddCommissionModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddCommission}
      />

      {/* Edit Commission Modal */}
      {selectedCommission && (
        <AddCommissionModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedCommission(null);
          }}
          onSubmit={handleEditCommission}
          initialData={{
            name: selectedCommission.externalCommissionName,
            contactInfo: selectedCommission.externalCommissionContactInfo,
            commissionAmount: selectedCommission.commissionAmount,
            applicationId: selectedCommission.application.applicationId,
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Commission"
        message="Are you sure you want to delete this commission? This action cannot be undone."
        confirmText="Delete"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
        isLoading={isDeleting}
      />
    </div>
  );
};

export default OperationsExternalCommissions;
