package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.exceptions.AppRTException;
import com.skillsync.applyr.core.models.entities.Company;
import com.skillsync.applyr.core.models.entities.OperationsAgent;
import com.skillsync.applyr.core.models.entities.SalesAgent;
import com.skillsync.applyr.core.response.SuccessResponse;
import com.skillsync.applyr.core.utills.UserUtils;
import com.skillsync.applyr.modules.authentication.services.AuthServices;
import com.skillsync.applyr.modules.company.models.AdminRegisterDTO;
import com.skillsync.applyr.modules.company.repositories.CompanyRepository;
import com.skillsync.applyr.modules.company.repositories.SalesAgentRepository;
import com.skillsync.applyr.modules.emailing.services.EmailServices;
import com.skillsync.applyr.core.models.enums.Roles;
import com.skillsync.applyr.modules.sales.repositories.OperationsAgentRepository;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class RegistrationService {

    private final CompanyRepository companyRepository;
    private final SalesAgentRepository salesAgentRepository;
    private final OperationsAgentRepository operationsAgentRepository;
    private final AuthServices authServices;
    private final EmailServices emailServices;

    public RegistrationService(CompanyRepository companyRepository,
                               SalesAgentRepository salesAgentRepository,
                               OperationsAgentRepository operationsAgentRepository,
                               AuthServices authServices,
                               EmailServices emailServices) {
        this.companyRepository = companyRepository;
        this.salesAgentRepository = salesAgentRepository;
        this.operationsAgentRepository = operationsAgentRepository;
        this.authServices = authServices;
        this.emailServices = emailServices;
    }

    public SuccessResponse registerAdmin(AdminRegisterDTO adminInfo) {
        try {
            String tempPassword = UserUtils.generatePassword();
            adminInfo.getRegisterRequestDTO().setPassword(tempPassword);
            Company company = new Company(adminInfo);
            adminInfo.getRegisterRequestDTO().setUsername(adminInfo.getEmail());
            authServices.register(adminInfo.getRegisterRequestDTO(), user -> {
                company.setUser(user);
                companyRepository.save(company);

                String subject = "Welcome to Skill Sync CRM System!";
                List<String> recipients = Collections.singletonList(adminInfo.getEmail());
                List<String> cc = Collections.emptyList(); // No CC
                String body = UserUtils.generateAccountCreationEmailBody(
                        adminInfo.getFullName(),
                        adminInfo.getRegisterRequestDTO().getUsername(),
                        adminInfo.getRegisterRequestDTO().getPassword()
                );
                emailServices.sendEmail(subject, recipients, cc, body);
                return true;
            });
            return new SuccessResponse("Admin added successfully");
        } catch (Exception e) {
            throw new AppRTException("Error registering with Exception: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse registerOperations(AdminRegisterDTO info) {
        try {
            String tempPassword = UserUtils.generatePassword();
            info.getRegisterRequestDTO().setPassword(tempPassword);

            OperationsAgent operationsAgent = new OperationsAgent(info);
            info.getRegisterRequestDTO().setUsername(info.getEmail());
            authServices.register(info.getRegisterRequestDTO(), user -> {
                operationsAgent.setUser(user);
                operationsAgentRepository.save(operationsAgent);

                String subject = "Welcome to Skill Sync CRM System!";
                List<String> recipients = Collections.singletonList(info.getEmail());
                List<String> cc = Collections.emptyList();
                String body = UserUtils.generateAccountCreationEmailBody(
                        info.getFullName(),
                        info.getRegisterRequestDTO().getUsername(),
                        info.getRegisterRequestDTO().getPassword()
                );
                emailServices.sendEmail(subject, recipients, cc, body);
                return true;
            });
            return new SuccessResponse("Operations Agent added successfully");
        } catch (Exception e) {
            throw new AppRTException("Error registering with Exception: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public SuccessResponse registerSalesAgent(AdminRegisterDTO salesInfo) {
        try {
            String tempPassword = UserUtils.generatePassword();
            salesInfo.getRegisterRequestDTO().setPassword(tempPassword);
            SalesAgent salesAgent = new SalesAgent(salesInfo);
            salesInfo.getRegisterRequestDTO().setUsername(salesInfo.getEmail());
            authServices.register(salesInfo.getRegisterRequestDTO(), user -> {
                salesAgent.setUser(user);
                salesAgentRepository.save(salesAgent);

                String subject = "Welcome to Skill Sync CRM System!";
                List<String> recipients = Collections.singletonList(salesInfo.getEmail());
                List<String> cc = Collections.emptyList();
                String body = UserUtils.generateAccountCreationEmailBody(
                        salesInfo.getFullName(),
                        salesInfo.getRegisterRequestDTO().getUsername(),
                        salesInfo.getRegisterRequestDTO().getPassword()
                );
                emailServices.sendEmail(subject, recipients, cc, body);
                return true;
            });
            return new SuccessResponse("Sales Agent added successfully");
        } catch (Exception e) {
            throw new AppRTException("Error registering with Exception: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
