package com.skillsync.applyr.modules.company.services;

import com.skillsync.applyr.core.models.entities.Company;
import com.skillsync.applyr.core.models.entities.SalesAgent;
import com.skillsync.applyr.modules.company.models.ProfileDTO;

public class EmployeeProfileServiceHelper {
    public static ProfileDTO fromAgentToProfileDTO(SalesAgent agent) {
        return new ProfileDTO(
                agent.getUser().getUsername(),
                agent.getFullName(),
                agent.getEmailAddress(),
                agent.getPhoneNumber(),
                agent.getAddress(),
                agent.getGender(),
                agent.getWeeklyTarget(),
                "SALES AGENT"
        );
    }

    public static ProfileDTO fromCompanyToProfileDTO(Company company) {
        return new ProfileDTO(
                company.getUser().getUsername(),
                company.getFullName(),
                company.getEmailAddress(),
                company.getPhoneNumber(),
                company.getAddress(),
                company.getGender(),
                0,
                "ADMIN");
    }
}
