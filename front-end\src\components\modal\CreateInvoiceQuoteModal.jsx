import React, { useState } from "react";
import { useRaiseQuoteAndInvoiceMutation } from "../../services/CompanyAPIService";

const CreateInvoiceQuoteModal = ({ isOpen, onClose, applicationId, onSuccess }) => {
  const [quoteNumber, setQuoteNumber] = useState("");
  const [quoteStatus, setQuoteStatus] = useState("DRAFTED");
  const [invoiceNumber, setInvoiceNumber] = useState("");
  const [invoiceStatus, setInvoiceStatus] = useState("DRAFTED");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // Use the mutation hook
  const [raiseQuoteAndInvoice, { isLoading }] = useRaiseQuoteAndInvoiceMutation();

  if (!isOpen) return null;

  const handleSubmit = async () => {
    if (!quoteNumber || !invoiceNumber) {
      setError("Please fill in all fields.");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const requestDTO = {
        applicationId,
        quoteRefNumber: quoteNumber,
        quoteRequestStatus: quoteStatus,
        invoiceRefNumber: invoiceNumber,
        invoiceRequestStatus: invoiceStatus
      };

      await raiseQuoteAndInvoice(requestDTO).unwrap();

      // Call onSuccess callback if provided
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess();
      }

      onClose();
    } catch (err) {
      console.error("Failed to raise quote and invoice:", err);
      setError(err.data?.message || "Failed to raise quote and invoice. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white w-full max-w-lg p-6 rounded shadow-lg">
        <div className="sticky top-0 bg-white z-10 p-4">
          <h2 className="text-xl font-bold mb-4">Raise Quote & Invoice</h2>
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
        </div>

        {/* Quote Number */}
        <div className="mb-3">
          <label className="block font-semibold text-sm mb-1">
            Quote Number
          </label>
          <input
            type="text"
            value={quoteNumber}
            onChange={(e) => setQuoteNumber(e.target.value)}
            className="border p-2 rounded w-full"
            placeholder="Enter quote number"
          />
        </div>

        {/* Quote Status */}
        <div className="mb-3">
          <label className="block font-semibold text-sm mb-1">Status</label>
          <select
            value={quoteStatus}
            onChange={(e) => setQuoteStatus(e.target.value)}
            className="border p-2 rounded w-full"
          >
            <option value="SENT">Sent</option>
            <option value="DRAFTED">Drafted</option>
            <option value="ACCEPTED">Accepted</option>
          </select>
        </div>

        {/* Invoice Number */}
        <div className="mb-3">
          <label className="block font-semibold text-sm mb-1">
            Invoice Number
          </label>
          <input
            type="text"
            value={invoiceNumber}
            onChange={(e) => setInvoiceNumber(e.target.value)}
            className="border p-2 rounded w-full"
            placeholder="Enter invoice number"
          />
        </div>

        {/* Invoice Status */}
        <div className="mb-3">
          <label className="block font-semibold text-sm mb-1">Status</label>
          <select
            value={invoiceStatus}
            onChange={(e) => setInvoiceStatus(e.target.value)}
            className="border p-2 rounded w-full"
          >
            <option value="SENT">Sent</option>
            <option value="DRAFTED">Drafted</option>
            <option value="ACCEPTED">Accepted</option>
          </select>
        </div>

        <div className="flex justify-end mt-4">
          <button
            onClick={onClose}
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded mr-2"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || isSubmitting}
            className={`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded ${(isLoading || isSubmitting) ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading || isSubmitting ? 'Processing...' : 'Raise Quote & Invoice'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateInvoiceQuoteModal;
