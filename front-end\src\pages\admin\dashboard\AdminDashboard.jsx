import React, { useState, useEffect } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";
import { Bar, Pie } from "react-chartjs-2";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faUsers,
  faFileInvoiceDollar,
  faMoneyBillWave,
  faCalendarAlt,
  faAward,
  faUser
} from "@fortawesome/free-solid-svg-icons";

import {
  useGetCompanyDashboardQuery,
  useGetCompanyDashboardThisMonthQuery,
  useGetCompanyDashboardThisYearQuery
} from "../../../services/AdminAPIService";
import { useGetAllTargetsQuery } from "../../../services/CompanyAPIService";
import AgentPerformanceSummary from "../../../components/dashboard/AgentPerformanceSummary";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

// ----------------------------------
// 1) Utility: Compute date range
// ----------------------------------
function getDateRangeValues(dateRange, customRange) {
  // We'll produce "YYYY-MM-DDTHH:mm:ss"
  // so that Spring can parse it as LocalDateTime
  const now = new Date();

  // Helper to format JS Date as "YYYY-MM-DDTHH:mm:ss"
  const toLocalDateTime = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  };

  let start, end;

  switch (dateRange) {
    case "today":
      // Start of today
      start = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
      // End of today
      end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
      break;

    case "weekly": {
      // By default, let's assume week starts Monday
      const dayOfWeek = now.getDay(); // Sunday=0, Monday=1, ...
      const mondayOffset = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      // Start: last Monday
      start = new Date(now);
      start.setDate(now.getDate() - mondayOffset);
      start.setHours(0, 0, 0, 0);
      // End: Sunday
      end = new Date(start);
      end.setDate(start.getDate() + 6);
      end.setHours(23, 59, 59, 999);
      break;
    }

    case "monthly":
      // 1st day of this month
      start = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
      // last day of this month
      end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
      break;

    case "yearly":
      // Jan 1 of this year
      start = new Date(now.getFullYear(), 0, 1, 0, 0, 0);
      // Dec 31 of this year
      end = new Date(now.getFullYear(), 11, 31, 23, 59, 59);
      break;

    case "custom":
      // customRange.from / to => 'YYYY-MM-DD' from <input type="date">
      if (customRange.from) {
        // parse the date
        start = new Date(customRange.from + "T00:00:00");
      } else {
        start = now;
      }
      if (customRange.to) {
        end = new Date(customRange.to + "T23:59:59");
      } else {
        end = now;
      }
      break;

    default:
      // fallback to "today"
      start = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
      end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
  }

  return {
    startDate: toLocalDateTime(start), // "YYYY-MM-DDTHH:mm:ss"
    endDate: toLocalDateTime(end),     // "YYYY-MM-DDTHH:mm:ss"
  };
}

const AdminDashboard = () => {
  // 2) DATE RANGE FILTER - Enhanced with multiple options
  const [dateRange, setDateRange] = useState("thisMonth");
  const [selectedWeeklyTarget, setSelectedWeeklyTarget] = useState("");
  const [selectedWeeklyTargets, setSelectedWeeklyTargets] = useState([]);
  const [customRange, setCustomRange] = useState({ from: "", to: "" });

  // Fetch weekly targets
  const { data: weeklyTargets, isLoading: targetsLoading } = useGetAllTargetsQuery();

  // Auto-select current weekly target if available
  useEffect(() => {
    if (weeklyTargets && weeklyTargets.length > 0 && !selectedWeeklyTarget) {
      const now = new Date();
      // Find a target where current date falls between start and end dates
      const currentTarget = weeklyTargets.find(target => {
        const startDate = new Date(target.startDate);
        const endDate = new Date(target.endDate);
        return now >= startDate && now <= endDate;
      });

      if (currentTarget) {
        setSelectedWeeklyTarget(currentTarget.id.toString());
      }
    }
  }, [weeklyTargets, selectedWeeklyTarget]);

  const handleWeeklyTargetChange = (e) => {
    setSelectedWeeklyTarget(e.target.value);
  };

  // 3) Compute start/end
  let computedStartDate, computedEndDate;
  let selectedTargetId = null;

  if (dateRange === "weeklyTarget" && selectedWeeklyTarget && weeklyTargets) {
    // Find the selected weekly target
    const target = weeklyTargets.find(t => t.id === parseInt(selectedWeeklyTarget));
    if (target) {
      // Use the weekly target's start and end dates directly from the API response
      // These are already in the correct format for the API
      computedStartDate = target.startDate;
      computedEndDate = target.endDate;
      selectedTargetId = target.id;
    }
  }

  // If not using weekly target or if weekly target is not found, use the date range values
  if (!computedStartDate || !computedEndDate) {
    const { startDate, endDate } = getDateRangeValues(dateRange, customRange);
    computedStartDate = startDate;
    computedEndDate = endDate;
  }

  // 4) Fetch data - Call all hooks unconditionally
  const monthQuery = useGetCompanyDashboardThisMonthQuery({
    skip: dateRange !== "thisMonth"
  });

  const yearQuery = useGetCompanyDashboardThisYearQuery({
    skip: dateRange !== "thisYear"
  });

  const multiQuery = useGetCompanyDashboardQuery({
    startDate: computedStartDate,
    endDate: computedEndDate,
    targetIds: selectedWeeklyTargets
  }, {
    skip: dateRange !== "multipleTargets" || selectedWeeklyTargets.length === 0
  });

  const skipDefaultQuery = (dateRange === "weeklyTarget" && !selectedWeeklyTarget) ||
                           (dateRange === "custom" && (!customRange.from || !customRange.to));

  const defaultQuery = useGetCompanyDashboardQuery({
    startDate: computedStartDate,
    endDate: computedEndDate,
    targetId: selectedTargetId
  }, {
    skip: skipDefaultQuery || dateRange === "thisMonth" || dateRange === "thisYear" || dateRange === "multipleTargets"
  });

  // Select the appropriate data based on dateRange
  let data, isLoading, isError, error;

  if (dateRange === "thisMonth") {
    data = monthQuery.data;
    isLoading = monthQuery.isLoading;
    isError = monthQuery.isError;
    error = monthQuery.error;
  } else if (dateRange === "thisYear") {
    data = yearQuery.data;
    isLoading = yearQuery.isLoading;
    isError = yearQuery.isError;
    error = yearQuery.error;
  } else if (dateRange === "multipleTargets") {
    data = multiQuery.data;
    isLoading = multiQuery.isLoading;
    isError = multiQuery.isError;
    error = multiQuery.error;
  } else {
    data = defaultQuery.data;
    isLoading = defaultQuery.isLoading;
    isError = defaultQuery.isError;
    error = defaultQuery.error;
  }

  // Enhanced header with multiple selection options
  const renderHeader = () => (
    <div className="flex flex-col mb-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
          {data?.weekTitle ? (
            <p className="mt-1 text-sm text-gray-500">
              Current period: <span className="font-medium">{data.weekTitle}</span>
            </p>
          ) : (
            <p className="mt-1 text-sm text-gray-500">
              Welcome to admin dashboard
            </p>
          )}
        </div>
      </div>

      {/* Enhanced Date Range Selection */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-50 p-4">
        <div className="flex flex-col lg:flex-row lg:items-center gap-4">
          {/* Date Range Type Selector */}
          <div className="flex items-center gap-2">
            <label className="font-semibold text-sm text-gray-700">
              Time Period:
            </label>
            <select
              value={dateRange}
              onChange={(e) => {
                setDateRange(e.target.value);
                // Reset selections when changing type
                setSelectedWeeklyTarget("");
                setSelectedWeeklyTargets([]);
                setCustomRange({ from: "", to: "" });
              }}
              className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
            >
              <option value="weeklyTarget">Single Weekly Target</option>
              <option value="multipleTargets">Multiple Weekly Targets</option>
              <option value="thisMonth">This Month</option>
              <option value="thisYear">This Year</option>
              <option value="custom">Custom Date Range</option>
            </select>
          </div>

          {/* Conditional Selection Based on Type */}
          {dateRange === "weeklyTarget" && (
            <div className="flex items-center gap-2">
              <label className="font-medium text-sm text-gray-600">
                Select Target:
              </label>
              <select
                value={selectedWeeklyTarget}
                onChange={handleWeeklyTargetChange}
                className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB] min-w-[200px]"
                disabled={targetsLoading}
              >
                {targetsLoading ? (
                  <option value="">Loading...</option>
                ) : (
                  <>
                    <option value="">Select a target</option>
                    {weeklyTargets?.map((target) => {
                      const startDate = new Date(target.startDate);
                      const endDate = new Date(target.endDate);
                      const formattedStart = startDate.toLocaleDateString();
                      const formattedEnd = endDate.toLocaleDateString();
                      const now = new Date();
                      const isCurrent = now >= startDate && now <= endDate;

                      return (
                        <option key={target.id} value={target.id}>
                          {target.title} ({formattedStart} - {formattedEnd}) {isCurrent ? "• Current" : ""}
                        </option>
                      );
                    })}
                  </>
                )}
              </select>
            </div>
          )}

          {dateRange === "multipleTargets" && (
            <div className="flex flex-col gap-3 min-w-[400px]">
              <div className="flex items-center justify-between">
                <label className="font-medium text-sm text-gray-700">
                  Select Weekly Targets:
                </label>
                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    onClick={() => setSelectedWeeklyTargets(weeklyTargets?.map(t => t.id.toString()) || [])}
                    className="text-xs text-[#6E39CB] hover:text-[#5B2FA3] font-medium"
                  >
                    Select All
                  </button>
                  <span className="text-gray-300">|</span>
                  <button
                    type="button"
                    onClick={() => setSelectedWeeklyTargets([])}
                    className="text-xs text-gray-500 hover:text-gray-700 font-medium"
                  >
                    Clear All
                  </button>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg bg-white shadow-sm">
                <div className="max-h-64 overflow-y-auto">
                  {targetsLoading ? (
                    <div className="p-4 text-center text-sm text-gray-500">
                      <div className="animate-pulse">Loading targets...</div>
                    </div>
                  ) : (
                    <div className="divide-y divide-gray-100">
                      {weeklyTargets?.map((target, index) => {
                        const startDate = new Date(target.startDate);
                        const endDate = new Date(target.endDate);
                        const formattedStart = startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                        const formattedEnd = endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                        const now = new Date();
                        const isCurrent = now >= startDate && now <= endDate;
                        const isSelected = selectedWeeklyTargets.includes(target.id.toString());

                        return (
                          <label
                            key={target.id}
                            className={`flex items-center p-3 cursor-pointer transition-all duration-200 ${
                              isSelected
                                ? 'bg-[#6E39CB]/5 border-l-4 border-l-[#6E39CB]'
                                : 'hover:bg-gray-50'
                            } ${index === 0 ? 'rounded-t-lg' : ''} ${index === weeklyTargets.length - 1 ? 'rounded-b-lg' : ''}`}
                          >
                            <div className="flex items-center space-x-3 w-full">
                              <input
                                type="checkbox"
                                value={target.id}
                                checked={isSelected}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSelectedWeeklyTargets(prev => [...prev, target.id.toString()]);
                                  } else {
                                    setSelectedWeeklyTargets(prev => prev.filter(id => id !== target.id.toString()));
                                  }
                                }}
                                className="h-4 w-4 text-[#6E39CB] focus:ring-[#6E39CB] border-gray-300 rounded transition-colors"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <span className={`font-medium text-sm ${isSelected ? 'text-[#6E39CB]' : 'text-gray-900'}`}>
                                    {target.title}
                                  </span>
                                  {isCurrent && (
                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-[#6E39CB] text-white">
                                      Current
                                    </span>
                                  )}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  {formattedStart} - {formattedEnd}
                                </div>
                              </div>
                            </div>
                          </label>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>

              {selectedWeeklyTargets.length > 0 && (
                <div className="flex items-center justify-between bg-[#6E39CB]/10 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-[#6E39CB] rounded-full"></div>
                    <span className="text-sm font-medium text-[#6E39CB]">
                      {selectedWeeklyTargets.length} target{selectedWeeklyTargets.length > 1 ? 's' : ''} selected
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => setSelectedWeeklyTargets([])}
                    className="text-xs text-gray-500 hover:text-gray-700"
                  >
                    Clear selection
                  </button>
                </div>
              )}
            </div>
          )}

          {dateRange === "custom" && (
            <div className="flex items-center gap-2">
              <label className="font-medium text-sm text-gray-600">From:</label>
              <input
                type="date"
                value={customRange.from}
                onChange={(e) => setCustomRange(prev => ({ ...prev, from: e.target.value }))}
                className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
              <label className="font-medium text-sm text-gray-600">To:</label>
              <input
                type="date"
                value={customRange.to}
                onChange={(e) => setCustomRange(prev => ({ ...prev, to: e.target.value }))}
                className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-[#6E39CB] focus:border-[#6E39CB]"
              />
            </div>
          )}

          {(dateRange === "thisMonth" || dateRange === "thisYear") && (
            <div className="text-sm text-gray-600 font-medium">
              {dateRange === "thisMonth" ? "Showing data for current month" : "Showing data for current year"}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // Check if we should show a message instead of loading
  const shouldShowMessage = () => {
    if (dateRange === "weeklyTarget" && !selectedWeeklyTarget) return "Please select a weekly target to view the dashboard data.";
    if (dateRange === "multipleTargets" && selectedWeeklyTargets.length === 0) return "Please select one or more weekly targets to view the dashboard data.";
    if (dateRange === "custom" && (!customRange.from || !customRange.to)) return "Please select both start and end dates for custom range.";
    return null;
  };

  const message = shouldShowMessage();
  if (message) {
    return (
      <div className="w-full">
        {renderHeader()}
        <div className="p-4 text-gray-600 bg-white rounded-lg shadow-sm border border-gray-50">
          {message}
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="w-full">
        {renderHeader()}
        <div className="p-4 text-gray-600 bg-white rounded-lg shadow-sm border border-gray-50">
          Loading dashboard data...
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="w-full">
        {renderHeader()}
        <div className="p-4 text-red-500 bg-white rounded-lg shadow-sm border border-gray-50">
          Error fetching dashboard: {error?.data?.message || error?.error}
        </div>
      </div>
    );
  }

  // 5) Extract data
  const totalRevenue = (data?.kpi1Actual ?? 0) + (data?.kpi2Actual ?? 0);

  // We used to have totalInvoices = data?.invoices ?? 0
  // But now we want to show KPI1 Target and KPI2 Target
  const kpi1Target = data?.kpi1Target ?? 0;
  const kpi1ActualInvoice = data?.kpi1Actual ?? 0; // same as before
  const kpi2Target = data?.kpi2Target ?? 0;
  const kpi2MoneyBank = data?.kpi2Actual ?? 0;

  const kpi1Agents = data?.agentInfos?.map((agent) => ({
    name: agent?.profile?.fullName || agent?.profile?.username,
    target: agent?.kpi1Target ?? 0,
    actual: agent?.kpi1Actual ?? 0,
  })) ?? [];

  const kpi1BarData = {
    labels: kpi1Agents.map((a) => a.name),
    datasets: [
      {
        label: "KPI1 Actual ($)",
        data: kpi1Agents.map((a) => (a.actual > a.target ? a.target : a.actual)),
        backgroundColor: "rgba(16, 185, 129, 0.7)",
      },
      {
        label: "Remaining ($)",
        data: kpi1Agents.map((a) => {
          const diff = a.target - a.actual;
          return diff > 0 ? diff : 0;
        }),
        backgroundColor: "rgba(229, 231, 235, 0.8)",
      },
    ],
  };

  const kpi1BarOptions = {
    indexAxis: "y",
    responsive: true,
    scales: {
      x: { stacked: true, beginAtZero: true },
      y: { stacked: true },
    },
    plugins: {
      legend: { position: "bottom" },
      title: { display: false },
    },
  };

  const kpi2Agents = data?.agentInfos?.map((agent) => ({
    name: agent?.profile?.fullName || agent?.profile?.username,
    target: agent?.kpi2Target ?? 0,
    actual: agent?.kpi2Actual ?? 0,
  })) ?? [];

  const kpi2BarData = {
    labels: kpi2Agents.map((a) => a.name),
    datasets: [
      {
        label: "KPI2 Actual ($)",
        data: kpi2Agents.map((a) => (a.actual > a.target ? a.target : a.actual)),
        backgroundColor: "rgba(139, 92, 246, 0.7)",
      },
      {
        label: "Remaining ($)",
        data: kpi2Agents.map((a) => {
          const diff = a.target - a.actual;
          return diff > 0 ? diff : 0;
        }),
        backgroundColor: "rgba(229, 231, 235, 0.8)",
      },
    ],
  };

  const kpi2BarOptions = {
    indexAxis: "y",
    responsive: true,
    scales: {
      x: { stacked: true, beginAtZero: true },
      y: { stacked: true },
    },
    plugins: {
      legend: { position: "bottom" },
      title: { display: false },
    },
  };

  const totalQuotesRaised = data?.quotes ?? 0;
  const totalInvoicesRaised = data?.invoices ?? 0;

  const quoteInvoicePieData = {
    labels: ["Quotes Raised", "Invoices Raised"],
    datasets: [
      {
        data: [totalQuotesRaised, totalInvoicesRaised],
        backgroundColor: ["#3B82F6", "#EF4444"],
        hoverBackgroundColor: ["#2563EB", "#DC2626"],
      },
    ],
  };

  const agentQuoteInvoiceData = data?.agentInfos?.map((agent) => ({
    name: agent?.profile?.fullName || agent?.profile?.username,
    quotes: agent?.quotesRaised ?? 0,
    invoices: agent?.invoiceRaised ?? 0,
  })) ?? [];

  // --------------
// New Pie Chart Data
// --------------
const kpi1PieData = {
  labels: ["KPI1 Actual", "Remaining"],
  datasets: [
    {
      data: [
        kpi1ActualInvoice,
        kpi1Target - kpi1ActualInvoice > 0 ? kpi1Target - kpi1ActualInvoice : 0,
      ],
      backgroundColor: ["rgba(16, 185, 129, 0.7)", "rgba(229, 231, 235, 0.8)"],
      hoverBackgroundColor: ["rgba(16, 185, 129, 1)", "rgba(229, 231, 235, 1)"],
    },
  ],
};

const kpi2PieData = {
  labels: ["KPI2 Money in Bank", "Remaining"],
  datasets: [
    {
      data: [
        kpi2MoneyBank,
        kpi2Target - kpi2MoneyBank > 0 ? kpi2Target - kpi2MoneyBank : 0,
      ],
      backgroundColor: ["rgba(139, 92, 246, 0.7)", "rgba(229, 231, 235, 0.8)"],
      hoverBackgroundColor: ["rgba(139, 92, 246, 1)", "rgba(229, 231, 235, 1)"],
    },
  ],
};


  // 6) Render the dashboard UI
  return (
    <div className="w-full">
      {/* Header Section */}
      {renderHeader()}

      {/* TOP ROW - 5 KPI CARDS */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
        {/* 1) KPI1 TARGET */}
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">KPI1 Target</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            ${kpi1Target.toLocaleString()}
          </p>
          <div className="flex items-center mt-2 text-xs">
            <span className="text-gray-500">Invoice Revenue Target</span>
          </div>
        </div>

        {/* 2) KPI1 ACTUAL */}
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">KPI1 Actual</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className={`text-2xl font-bold ${kpi1ActualInvoice >= kpi1Target ? 'text-green-600' : 'text-gray-900'}`}>
            ${kpi1ActualInvoice.toLocaleString()}
          </p>
          <div className="flex items-center mt-2 text-xs">
            <span className={`${kpi1ActualInvoice >= kpi1Target ? 'text-green-600' : 'text-orange-500'} font-medium`}>
              {Math.round((kpi1ActualInvoice / kpi1Target) * 100)}% of Target
            </span>
          </div>
        </div>

        {/* 3) KPI2 TARGET */}
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">KPI2 Target</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faMoneyBillWave} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            ${kpi2Target.toLocaleString()}
          </p>
          <div className="flex items-center mt-2 text-xs">
            <span className="text-gray-500">Money in Bank Target</span>
          </div>
        </div>

        {/* 4) KPI2 ACTUAL */}
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">KPI2 Actual</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faMoneyBillWave} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className={`text-2xl font-bold ${kpi2MoneyBank >= kpi2Target ? 'text-green-600' : 'text-gray-900'}`}>
            ${kpi2MoneyBank.toLocaleString()}
          </p>
          <div className="flex items-center mt-2 text-xs">
            <span className={`${kpi2MoneyBank >= kpi2Target ? 'text-green-600' : 'text-orange-500'} font-medium`}>
              {Math.round((kpi2MoneyBank / kpi2Target) * 100)}% of Target
            </span>
          </div>
        </div>

        {/* 5) TEAM OVERVIEW */}
        <div className="bg-white rounded-lg shadow-sm p-5 border border-gray-50">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm font-medium text-gray-500">Team Overview</p>
            <div className="p-2 bg-[#F4F5F9] rounded-md">
              <FontAwesomeIcon icon={faUsers} className="h-5 w-5 text-[#6E39CB]" />
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {data?.agentInfos?.length || 0} Agents
          </p>
          <div className="flex items-center mt-2 text-xs">
            <FontAwesomeIcon icon={faCalendarAlt} className="text-blue-500 mr-1" />
            <span className="text-blue-500 font-medium">{data?.weekTitle || 'This Week'}</span>
          </div>
        </div>
      </div>
      {/* AGENT PERFORMANCE SUMMARY */}
      <div className="mb-6">
        <AgentPerformanceSummary agentInfos={data?.agentInfos || []} />
      </div>

      {/* Two side-by-side Pie Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">KPI1: Target vs Actual</h2>
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-[#F4F5F9] rounded-md cursor-pointer hover:bg-[#EAECF2]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </div>
            </div>
          </div>
          <div style={{ height: '300px', margin: '0 auto' }}>
            <Pie
              data={kpi1PieData}
              options={{
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: {
                      usePointStyle: true,
                      padding: 20
                    }
                  }
                }
              }}
            />
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">KPI2: Target vs Money in Bank</h2>
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-[#F4F5F9] rounded-md cursor-pointer hover:bg-[#EAECF2]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </div>
            </div>
          </div>
          <div style={{ height: '300px', margin: '0 auto' }}>
            <Pie
              data={kpi2PieData}
              options={{
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: {
                      usePointStyle: true,
                      padding: 20
                    }
                  }
                }
              }}
            />
          </div>
        </div>
      </div>



      {/* KPI1: TABLE + PROGRESS BAR CHART */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">

        {/* TABLE */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">KPI1 (Invoice)</h2>
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-[#F4F5F9] rounded-md cursor-pointer hover:bg-[#EAECF2]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </div>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="py-3 px-4 text-left font-medium text-gray-500">Agent</th>
                  <th className="py-3 px-4 text-right font-medium text-gray-500">Target ($)</th>
                  <th className="py-3 px-4 text-right font-medium text-gray-500">Actual ($)</th>
                  <th className="py-3 px-4 text-right font-medium text-gray-500">Difference ($)</th>
                </tr>
              </thead>
              <tbody>
                {kpi1Agents.map((agent, idx) => {
                  const diff = agent.target - agent.actual;
                  return (
                    <tr
                      key={idx}
                      className="border-b border-gray-100 hover:bg-[#F4F5F9] transition-colors"
                    >
                      <td className="py-3 px-4 font-medium text-gray-900">{agent.name}</td>
                      <td className="py-3 px-4 text-right text-gray-700">
                        {agent.target.toLocaleString()}
                      </td>
                      <td className="py-3 px-4 text-right text-gray-700">
                        {agent.actual.toLocaleString()}
                      </td>
                      <td
                        className={`py-3 px-4 text-right font-medium ${
                          diff >= 0 ? "text-red-500" : "text-green-500"
                        }`}
                      >
                        {diff.toLocaleString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* HORIZONTAL STACKED BAR CHART */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">KPI1 Progress</h2>
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-[#F4F5F9] rounded-md cursor-pointer hover:bg-[#EAECF2]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </div>
            </div>
          </div>
          <div className="h-72">
            <Bar data={kpi1BarData} options={kpi1BarOptions} />
          </div>
        </div>
      </div>

      {/* KPI2: TABLE + PROGRESS BAR CHART */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* TABLE */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              KPI2 (Money In The Bank)
            </h2>
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-[#F4F5F9] rounded-md cursor-pointer hover:bg-[#EAECF2]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </div>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="py-3 px-4 text-left font-medium text-gray-500">Agent</th>
                  <th className="py-3 px-4 text-right font-medium text-gray-500">Target ($)</th>
                  <th className="py-3 px-4 text-right font-medium text-gray-500">Actual ($)</th>
                  <th className="py-3 px-4 text-right font-medium text-gray-500">Difference ($)</th>
                </tr>
              </thead>
              <tbody>
                {kpi2Agents.map((agent, idx) => {
                  const diff = agent.target - agent.actual;
                  return (
                    <tr
                      key={idx}
                      className="border-b border-gray-100 hover:bg-[#F4F5F9] transition-colors"
                    >
                      <td className="py-3 px-4 font-medium text-gray-900">{agent.name}</td>
                      <td className="py-3 px-4 text-right text-gray-700">
                        {agent.target.toLocaleString()}
                      </td>
                      <td className="py-3 px-4 text-right text-gray-700">
                        {agent.actual.toLocaleString()}
                      </td>
                      <td
                        className={`py-3 px-4 text-right font-medium ${
                          diff >= 0 ? "text-red-500" : "text-green-500"
                        }`}
                      >
                        {diff.toLocaleString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* HORIZONTAL STACKED BAR CHART */}
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">KPI2 Progress</h2>
            <div className="flex items-center space-x-2">
              <div className="p-1 bg-[#F4F5F9] rounded-md cursor-pointer hover:bg-[#EAECF2]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </div>
            </div>
          </div>
          <div className="h-72">
            <Bar data={kpi2BarData} options={kpi2BarOptions} />
          </div>
        </div>
      </div>



      {/* TOP PERFORMER SECTION */}
      {data?.topPerformer && (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-50 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Top Performer</h2>
            <div className="p-2 bg-green-100 rounded-full">
              <FontAwesomeIcon icon={faAward} className="h-5 w-5 text-green-600" />
            </div>
          </div>
          <div className="flex items-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-[#F0ECF6]">
              <FontAwesomeIcon icon={faUser} className="text-[#6E39CB]" />
            </div>
            <div className="ml-4">
              <p className="text-xl font-bold text-gray-900">{data.topPerformer}</p>
              <p className="text-sm text-gray-500">Congratulations on the outstanding performance!</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
